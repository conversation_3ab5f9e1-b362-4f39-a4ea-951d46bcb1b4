cmake_minimum_required(VERSION 3.2 FATAL_ERROR)
#set(CMAKE_CXX_STANDARD 11)

project(maprenderservice)
set(CMAKE_VERBOSE_MAKEFILE ON)
# Add .lib files
#link_directories(${CMAKE_SOURCE_DIR}/thirdparty/lib)


set(PROJECT_ROOT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/../../)
set(MAP_ROOT_DIR ${CMAKE_CURRENT_SOURCE_DIR})
SET(THIRD_PARTY_DIR ${PROJECT_ROOT_PATH}/third_party)
# Skia
set(SKIA_DIR "${THIRD_PARTY_DIR}/skia")
# 新增：检测系统架构并映射到对应目录
if(CMAKE_SYSTEM_PROCESSOR STREQUAL "x86_64" OR CMAKE_SYSTEM_PROCESSOR STREQUAL "amd64")
    set(SKIA_ARCH_DIR "x86_64")
elseif(CMAKE_SYSTEM_PROCESSOR STREQUAL "aarch64" OR CMAKE_SYSTEM_PROCESSOR STREQUAL "arm64")
    set(SKIA_ARCH_DIR "arm64")
else()
    message(WARNING "Unsupported architecture ${CMAKE_SYSTEM_PROCESSOR}, using default lib directory")
    set(SKIA_ARCH_DIR "")  # 未识别架构时使用根目录
endif()
# 修改原路径为架构相关路径
set(SKIA_LIBRARY "${SKIA_DIR}/lib/${SKIA_ARCH_DIR}/libskia.a")

set(SKIA_INCLUDE_DIR "${SKIA_DIR}/")
if ("${FREETYPE_INCLUDE_DIR}" STREQUAL "")
    set(FREETYPE_INCLUDE_DIR "${THIRD_PARTY_DIR}/freetype2/include")
endif()
# map parser
set(PARSER_INCLUDE_DIR "${PROJECT_ROOT_PATH}/src/data_provider/include")
if (BUILD_ASAN STREQUAL "ON")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fsanitize=address -fPIE")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsanitize=address -fPIE")
set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -pie")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g")
endif()

# Add source files
file(GLOB_RECURSE SOURCE_FILES 
    ${MAP_ROOT_DIR}/*.h
    ${MAP_ROOT_DIR}/*.hpp
	${MAP_ROOT_DIR}/*.c
    ${MAP_ROOT_DIR}/*.cc
	${MAP_ROOT_DIR}/*.cpp)

# 用于存储需要移除的文件
set(FILES_TO_REMOVE "")

# 遍历所有找到的文件
foreach(FILE ${SOURCE_FILES})
    # 读取文件内容
    file(STRINGS ${FILE} FILE_CONTENTS)
    foreach(LINE ${FILE_CONTENTS})
        if(LINE MATCHES " main\\s*\\(")
            list(APPEND FILES_TO_REMOVE ${FILE})
            break()
        endif()
    endforeach()
endforeach()



# 从 SOURCE_FILES 列表中移除包含 main 函数的文件
list(REMOVE_ITEM SOURCE_FILES ${FILES_TO_REMOVE})

# 将当前目录下的main.cpp加进去
message("${SOURCE_FILES}")
 Set(TARGET_SOURCE "${MAP_ROOT_DIR}/test/test_map_render_service.cpp")

add_library(${PROJECT_NAME} SHARED ${SOURCE_FILES})
# Define the executable
SET(TEST_BIN_NAME "${PROJECT_NAME}_test")
add_executable("${TEST_BIN_NAME}" ${HEADER_FILES} ${TARGET_SOURCE})
# 为可执行文件目标添加 DEBUG 定义
if (DEBUG_LOG STREQUAL "ON")
 target_compile_definitions("${TEST_BIN_NAME}" PRIVATE DEBUG)
 target_compile_definitions("${PROJECT_NAME}" PRIVATE DEBUG)
endif()

# We need a CMAKE_DIR with some code to find external dependencies
set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} "${CMAKE_SOURCE_DIR}/cmake/")

#######################################
# LOOK for the packages that we need! #
#######################################

# OpenGL
set(OpenGL_GL_PREFERENCE GLVND)
find_package(OpenGL REQUIRED)

# GLFW
# find_package(GLFW3 REQUIRED)
# message(STATUS "Found GLFW3 in ${GLFW3_INCLUDE_DIR}")


#Put all libraries into a variable
set(LIBS ${GLFW3_LIBRARY} ${OPENGL_LIBRARY})


# Define the include DIRs
include_directories(
    "${PROJECT_ROOT_PATH}/src/base/include"
	"${PROJECT_ROOT_PATH}/thirdparty"
    "${SKIA_INCLUDE_DIR}"
	"${SKIA_INCLUDE_DIR}/include/core"
    "${SKIA_INCLUDE_DIR}/include/config"
    "${MAP_ROOT_DIR}/include"
    "${PARSER_INCLUDE_DIR}"
    "${MAP_ROOT_DIR}/src/map_render_service"
    "${MAP_ROOT_DIR}/map_render_data"
    "${MAP_ROOT_DIR}/map_render_data/render_data_tile"
    "${MAP_ROOT_DIR}/map_render_data/render_data_cache"
    "${MAP_ROOT_DIR}/map_render"
    "${MAP_ROOT_DIR}/map_render/render_backend"
    "${MAP_ROOT_DIR}/map_render/render_gl"
    "${MAP_ROOT_DIR}/map_render/render_layer"
    "${MAP_ROOT_DIR}/src/util"
    "${MAP_ROOT_DIR}/src/data/cache"
    "${MAP_ROOT_DIR}/map_theme"
    "${MAP_ROOT_DIR}/src/data"
    "${MAP_ROOT_DIR}/src/layout"
    "${MAP_ROOT_DIR}/src/render"
    "${MAP_ROOT_DIR}/src/render/backend"
    "${MAP_ROOT_DIR}/src/render/backend/gl"
    "${MAP_ROOT_DIR}/src/render/backend/gl/text"
     "${MAP_ROOT_DIR}/src/render/backend/skia"
     "${MAP_ROOT_DIR}/src/render/backend/skia/text"
    "${MAP_ROOT_DIR}/src/render/font"
    "${MAP_ROOT_DIR}/src/render/gl"
    "${MAP_ROOT_DIR}/src/render/layer"
    "${MAP_ROOT_DIR}/src/render/scene"
     "${MAP_ROOT_DIR}/src/render/text"
    "${MAP_ROOT_DIR}/src/theme"
    "${MAP_ROOT_DIR}/src/util"
    "${FREETYPE_INCLUDE_DIR}"
    "${CMAKE_INSTALL_INCLUDEDIR}"
)

# add skia
add_library(Skia::Skia STATIC IMPORTED)
set_target_properties(Skia::Skia PROPERTIES
    IMPORTED_LOCATION "${SKIA_LIBRARY}"
    INTERFACE_INCLUDE_DIRECTORIES "${SKIA_INCLUDE_DIR}"
)

# add parser lib


# 在链接库列表中添加Skia及其所有依赖
set(LIBS 
    ${OPENGL_LIBRARY} 
    Skia::Skia
    png
    jpeg
    pthread 
    data_provider
    freetype
)

SET(TEST_BIN_LIBS
    ${GLFW3_LIBRARY} 
    ${LIBS}
    glfw
    )

if (BUILD_FREETYPE)
add_dependencies(${PROJECT_NAME} freetype)
endif(
    BUILD_FREETYPE
)
if (BUILD_PNG)
add_dependencies(${PROJECT_NAME} png_shared)
endif(
    BUILD_PNG
)
if (BUILD_JPEG)
add_dependencies(${PROJECT_NAME} libjpeg-turbo)
endif(
    BUILD_JPEG
)

add_dependencies(${PROJECT_NAME} data_provider )
add_dependencies(${TEST_BIN_NAME} ${PROJECT_NAME})

# Define the link libraries
target_link_libraries(${PROJECT_NAME} ${LIBS})

target_link_libraries(${TEST_BIN_NAME} ${TEST_BIN_LIBS} ${PROJECT_NAME})
# 为共享库添加 rpath，指向自身所在目录（$ORIGIN 表示当前库的目录）
target_link_options(${PROJECT_NAME} PRIVATE "-Wl,-rpath,$ORIGIN")

# 为测试可执行文件添加 rpath，指向共享库所在目录（假设共享库与测试程序在同一目录）
target_link_options("${TEST_BIN_NAME}" PRIVATE "-Wl,-rpath,$ORIGIN")
