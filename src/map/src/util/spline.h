#ifndef MAP_UTIL_SPLINE_H
#define MAP_UTIL_SPLINE_H

#include "vector2.h"

namespace aurora {

class Spline
{
public:
  class iterator
  {
  public:
    Vector2d pos_;
    Vector2d dir_;
    Vector2d avr_dir_;

    iterator();

    void Attach(Spline const & spl);
    void Advance(double step);
    bool BeginAgain() const;
    double GetFullLength() const;

    size_t GetIndex() const;
    bool IsAttached() const;

  private:
    friend class Spline;
    double GetDistance() const;

    void AdvanceForward(double step);
    void AdvanceBackward(double step);

  private:
    bool checker_;
    Spline const * spl_;
    size_t index_;
    double dist_;
  };

public:
  Spline() = default;
  explicit Spline(size_t reservedSize);
  explicit Spline(std::vector<Vector2d> const & path);
  explicit Spline(std::vector<Vector2d> && path);

  void AddPoint(Vector2d const & pt);
  void ReplacePoint(Vector2d const & pt);
  bool IsProlonging(Vector2d const & pt) const;

  size_t GetSize() const;
  std::vector<Vector2d> const & GetPath() const { return position_; }
  void Clear();

  iterator GetPoint(double step) const;

  template <typename TFunctor>
  void ForEachNode(iterator const & begin, iterator const & end, TFunctor const & f) const
  {
    f(begin.pos_);

    for (size_t i = begin.GetIndex() + 1; i <= end.GetIndex(); ++i)
      f(position_[i]);

    f(end.pos_);
  }

  bool IsEmpty() const;
  bool IsValid() const;

  double GetLength() const;
  double GetLastLength() const;
  /// @return for (i) -> (i + 1) section.
  std::pair<Vector2d, double> GetTangentAndLength(size_t i) const;

protected:
  void InitDirections();

  std::vector<Vector2d> position_;
  std::vector<Vector2d> direction_;
  std::vector<double> length_;
};

}
#endif // MAP_UTIL_SPLINE_H
