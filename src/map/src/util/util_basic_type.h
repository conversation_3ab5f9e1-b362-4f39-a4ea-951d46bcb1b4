/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#ifndef UTIL_BASEIC_TYPE_DEFINE_H_
#define UTIL_BASEIC_TYPE_DEFINE_H_
#include <cstdint>
#include "point2.h"
#include "aabb2.h"

namespace aurora {

inline constexpr int32_t kTileSize = 512;

    enum MapLayerID {
        LAYERID_INVALID = -1,
        LAYERID_TILE = 0,
        LAYERID_BUILDING,
        LAYERID_ROAD,
        LAYERID_BACKBROUND,
        // TileLayer,
        // PoiLayer,              //!< POI Icons
        // TrafficIncident,       //!< Traffic Icons
        // TrafficSpeedTrap,      //!< Speed trap Icons
        // RouteWaypoint,         //!< Begin/End/Waypoints of route
        // Vehicle,               //!< Vehicle annotation
        // UserDefined=32,        //!< First custom annotation layer
        // MaxUserDefined = 127   //!< Last custom annotation layer
    };

    enum MapRenderScale
    {
        MAPRENDER_SCALE_INVALID   = -1,
        MAPRENDER_SCALE_1_2500    = 0,     ///< 1/25百图
        MAPRENDER_SCALE_1_5000    = 1,     ///< 1/5千图
        MAPRENDER_SCALE_1_1W      = 2,     ///< 1/1万图
        MAPRENDER_SCALE_1_2W      = 3,     ///< 1/2万图
        MAPRENDER_SCALE_1_4W      = 4,     ///< 1/4万图
        MAPRENDER_SCALE_1_8W      = 5,     ///< 1/8万图
        MAPRENDER_SCALE_1_16W     = 6,     ///< 1/16万图
        MAPRENDER_SCALE_1_32W     = 7,     ///< 1/32万图
        MAPRENDER_SCALE_1_64W     = 8,     ///< 1/64万图
        MAPRENDER_SCALE_1_128W    = 9,     ///< 1/128万图
        MAPRENDER_SCALE_1_256W    = 10,    ///< 1/256万图
        MAPRENDER_SCALE_1_512W    = 11,    ///< 1/512万图
        MAPRENDER_SCALE_1_1024W   = 12,    ///< 1/1024万图
        MAPRENDER_SCALE_1_2048W   = 13,    ///< 1/2048万图
        MAPRENDER_SCALE_1_4096W   = 14,    ///< 1/4096万图
    };

    enum FillStyle
    {
        STYLE_FILE   = 0,
        STYLE_STROKE = 1,
        STYLE_FILE_AND_STROKE = 2
    };

    /* Line join styles. */
    enum LineJoin {
        JOIN_MITER = 0,    // sharp angle（skia default）
        JOIN_ROUND = 1,    // rounded corners
        JOIN_BEVEL = 2     // bevel angle
    };

    /* Line cap styles. */
    enum LineCap {
        CAP_BUTT     = 0,
        CAP_ROUNT    = 1,
        CAP_SQUARE   = 2
    };

    enum FontWeight {
        WEIGHT_INVISIBLE   =    0,
        WEIGHT_THIN        =  100,
        WEIGHT_EXTRALIGHT  =  200,
        WEIGHT_LIGHT       =  300,
        WEIGHT_NORMAL      =  400,
        WEIGHT_MEDIUM      =  500,
        WEIGHT_SEMIBOLD    =  600,
        WEIGHT_BOLD        =  700,
        WEIGHT_EXTRABOLD   =  800,
        WEIGHT_BLACK       =  900,
        WEIGHT_EXTRABLACK  = 1000,
    };

    enum FontWidth {
        WIDTH_ULTRACONDENSED   = 1,
        WIDTH_EXTRACONDENSED   = 2,
        WIDTH_CONDENSED        = 3,
        WIDTH_SEMICONDENSED    = 4,
        WIDTH_NORMAL           = 5,
        WIDTH_SEMIEXPANDED     = 6,
        WIDTH_EXPANDED         = 7,
        WIDTH_EXTRAEXPANDED    = 8,
        WIDTH_ULTRAEXPANDED    = 9,
    };

    enum FontSlant : uint8_t {
        SLANT_UPRIGHT,
        SLANT_ITALIC,
        SLANT_OBLIQUE,
    };

    enum FontStyleType
     {
        FONT_NORMAL = 0,
        FONT_BOLD,
        FONT_ITALIC,
        FONT_BOLD_ITALIC,
     };
     
    enum PitchAngleMode {
        PITCH_ANGLE_MODE_CUSTOM = 0, // 用户设置
        PITCH_ANGLE_MODE_FIXED = 1, // 内部预定义：2D模式 固定俯仰角 0， 3D模式 俯仰角默认根据当前地图比例尺、投影中心按照最大显示角度度

    };

    enum AnchorType {
        kAnchorCenter = 0,
        kAnchorLeftTop,
        kAnchorLeftMiddle,
        kAnchorLeftBottom,
        kAnchorMiddleBottom,
        kAnchorRightBottom,
        kAnchorRightMiddle,
        kAnchorRightTop,
        kAnchorMiddleTop,
        kAnchorFree,
    };

    using BoundingBox = AABB2<PointXY<float>>;

} //namespace 

#endif //UTIL_BASEIC_TYPE_DEFINE_H_
/* EOF */
