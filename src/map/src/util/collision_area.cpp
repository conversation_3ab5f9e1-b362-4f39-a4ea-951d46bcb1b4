#include "collision_area.h"

namespace aurora {
CollisionArea::CollisionArea() : mbr_(), boxes_() {}
void CollisionArea::Reset(int32_t w, int32_t h) {
  mbr_.Create({{0, 0}, {w, h}});
  boxes_.reserve(256);
}
void CollisionArea::Clear() { boxes_.clear(); }
bool CollisionArea::IsInScreen(int32_t min_x, int32_t min_y, int32_t max_x, int32_t max_y) {
  AABB2<PointXY<int32_t>> box(min_x, min_y, max_x, max_y);
  return mbr_.Intersects(box);
}
CollisionResult CollisionArea::CanPlace(int32_t min_x, int32_t min_y, int32_t max_x,
                                        int32_t max_y) {
  AABB2<PointXY<int32_t>> box(min_x, min_y, max_x, max_y);
  if (!mbr_.Intersects(box)) {
    return kCollisionOutOfScreen;
  }
  for (AABB2<PointXY<int32_t>>& bbox : boxes_) {
    if (bbox.Intersects(box)) {
      return kCollisionHide;
    }
  }
  return kCollisionShow;
}
CollisionResult CollisionArea::Place(int32_t min_x, int32_t min_y, int32_t max_x, int32_t max_y) {
  AABB2<PointXY<int32_t>> box(min_x, min_y, max_x, max_y);
  if (!mbr_.Intersects(box)) {
    return kCollisionOutOfScreen;
  }
  for (AABB2<PointXY<int32_t>>& bbox : boxes_) {
    if (bbox.Intersects(box)) {
      return kCollisionHide;
    }
  }
  boxes_.push_back(box);
  return kCollisionShow;
}
void CollisionArea::ForcePlace(int32_t min_x, int32_t min_y, int32_t max_x, int32_t max_y) {
  AABB2<PointXY<int32_t>> box(min_x, min_y, max_x, max_y);
  if (!mbr_.Intersects(box)) {
    return;
  }
  boxes_.push_back(box);
}
}  // namespace aurora
