#include "spline.h"

#include <numeric>
#include <limits>

namespace aurora
{
Spline::Spline(std::vector<Vector2d> const & path)
  : position_(path)
{
  InitDirections();
}

Spline::Spline(std::vector<Vector2d>&& path)
  : position_(std::move(path))
{
  InitDirections();
}
Spline::Spline(size_t reservedSize)
{
  position_.reserve(reservedSize);
  direction_.reserve(reservedSize - 1);
  length_.reserve(reservedSize - 1);
}

void Spline::AddPoint(Vector2d const & pt)
{
  if (!IsEmpty())
  {
    Vector2d const dir = pt - position_.back();
    if (abs(dir.x()) < 0.01 && abs(dir.y()) < 0.01) {
      return;
    }

    double const len = dir.Norm();
    length_.push_back(len);
    direction_.push_back(dir / len);
  }

  position_.push_back(pt);
}

void Spline::ReplacePoint(Vector2d const & pt)
{
  position_.pop_back();
  length_.pop_back();
  direction_.pop_back();
  AddPoint(pt);
}

bool Spline::IsProlonging(Vector2d const & pt) const
{
  size_t const sz = position_.size();
  if (sz < 2)
    return false;

  Vector2d dir = pt - position_.back();
  if (abs(dir.x()) < 1 && abs(dir.y()) < 1) {
    return false;
  }
  dir = dir.Normalize();

  return std::fabs(dir.Dot(direction_.back())) > 0.995;
}

size_t Spline::GetSize() const
{
  return position_.size();
}

void Spline::Clear()
{
  position_.clear();
  direction_.clear();
  length_.clear();
}

bool Spline::IsEmpty() const
{
  return position_.empty();
}

bool Spline::IsValid() const
{
  return position_.size() > 1;
}

Spline::iterator Spline::GetPoint(double step) const
{
  iterator it;
  it.Attach(*this);
  it.Advance(step);
  return it;
}

double Spline::GetLength() const
{
  return std::accumulate(length_.begin(), length_.end(), 0.0);
}

double Spline::GetLastLength() const
{
  return length_.back();
}

std::pair<Vector2d, double> Spline::GetTangentAndLength(size_t i) const
{
  return { direction_[i], length_[i] };
}

void Spline::InitDirections()
{
  size_t const sz = position_.size() - 1;

  direction_.resize(sz);
  length_.resize(sz);

  for (size_t i = 0; i < sz; ++i)
  {
    direction_[i] = position_[i + 1] - position_[i];
    double const len = direction_[i].Norm();
    length_[i] = len;
    direction_[i] = direction_[i] / len;
  }
}

Spline::iterator::iterator()
  : checker_(false)
  , spl_(NULL)
  , index_(0)
  , dist_(0)
{
}

void Spline::iterator::Attach(Spline const & spl)
{
  spl_ = &spl;
  index_ = 0;
  dist_ = 0;
  checker_ = false;
  dir_ = spl_->direction_[index_];
  avr_dir_ = spl_->direction_[index_];
  pos_ = spl_->position_[index_] + dir_ * dist_;
}

bool Spline::iterator::IsAttached() const
{
  return spl_ != nullptr;
}

void Spline::iterator::Advance(double step)
{
  if (step < 0.0)
    AdvanceBackward(step);
  else
    AdvanceForward(step);
}

double Spline::iterator::GetFullLength() const
{
  return spl_->GetLength();
}

bool Spline::iterator::BeginAgain() const
{
  return checker_;
}

double Spline::iterator::GetDistance() const
{
  return dist_;
}

size_t Spline::iterator::GetIndex() const
{
  return index_;
}

void Spline::iterator::AdvanceBackward(double step)
{
  dist_ += step;
  while (dist_ < 0.0f)
  {
    if (index_ == 0)
    {
      checker_ = true;
      pos_ = spl_->position_[index_];
      dir_ = spl_->direction_[index_];
      avr_dir_ = {0, 0};
      dist_ = 0.0;
      return;
    }

    --index_;

    dist_ += spl_->length_[index_];
  }

  dir_ = spl_->direction_[index_];
  avr_dir_ = {-pos_.x(), -pos_.y()};
  pos_ = spl_->position_[index_] + dir_ * dist_;
  avr_dir_ += pos_;
}

void Spline::iterator::AdvanceForward(double step)
{
  dist_ += step;
  if (checker_)
  {
    pos_ = spl_->position_[index_] + dir_ * dist_;
    return;
  }

  while (dist_ > spl_->length_[index_])
  {
    dist_ -= spl_->length_[index_];
    index_++;
    if (index_ >= spl_->direction_.size())
    {
      index_--;
      dist_ += spl_->length_[index_];
      checker_ = true;
      break;
    }
  }
  dir_ = spl_->direction_[index_];
  avr_dir_ = {-pos_.x(), -pos_.y()};
  pos_ = spl_->position_[index_] + dir_ * dist_;
  avr_dir_ += pos_;
}

} // namespace m2
