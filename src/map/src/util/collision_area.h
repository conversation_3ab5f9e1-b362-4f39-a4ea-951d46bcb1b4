#ifndef MAP_UTIL_COLLISION_AREA_H
#define MAP_UTIL_COLLISION_AREA_H
#include <cstdint>
#include <vector>

#include "aabb2.h"
#include "point2.h"

namespace aurora {
enum CollisionResult {
  kCollisionOutOfScreen = 0,
  kColl<PERSON>Hide,
  kCollisionShow,
};

class CollisionArea {
public:
  CollisionArea();
  void Reset(int32_t w, int32_t h);
  void Clear();
  bool IsInScreen(int32_t min_x, int32_t min_y, int32_t max_x, int32_t max_y);
  CollisionResult CanPlace(int32_t min_x, int32_t min_y, int32_t max_x, int32_t max_y);
  CollisionResult Place(int32_t min_x, int32_t min_y, int32_t max_x, int32_t max_y);
  void ForcePlace(int32_t min_x, int32_t min_y, int32_t max_x, int32_t max_y);

private:
  AABB2<PointXY<int32_t>> mbr_;
  std::vector<AABB2<PointXY<int32_t>>> boxes_;
};

}  // namespace aurora
#endif  // MAP_UTIL_COLLISION_AREA_H
