/**
 * Copyright @ 2025
 * All Rights Reserved.
 */
#include "map_render_state.h"
#include "map_render_theme.h"
#include "map_render_manager.h"
#include "map_render_data_manager.h"
#include "map_render_scene.h"
#include "render_raster_tile.h"
#include "include/core/SkPixmap.h"
#include <chrono>
#include <thread>
#include "map_render_skfontmanager.h"
#include "map_render_skfontface.h"
 #include "gpu_shader_cache.h"
#include "render_data_cache_lru.h"
#include "path/path_manager.h"
#include "mark/mark_manager.h"
#include "pin_mark/pin_mark_manager.h"
#include "gl_texture2d.h"


namespace aurora {
    
    constexpr auto kFrameTime = std::chrono::milliseconds(33); // 40->25fps; 33->30fps; 16->60fps; 
    static std::chrono::time_point<std::chrono::high_resolution_clock> last_frame_time;
    const int kMaxDealedTileCount = 100;
    MapRenderManager::MapRenderManager(const std::string& default_font_path, const std::string& default_font_name)
    :  render_theme_(nullptr)
    , data_manager_(nullptr)
    , default_fontPath_(default_font_path)
    , default_fontName_(default_font_name)
    {

    }
             
    MapRenderManager::~MapRenderManager()
    {
         
    }


    bool MapRenderManager::Init(MapDataProviderPtr provider, MapRenderThemeIF* theme)
    {
        render_theme_  = theme;

        scene_ = std::make_shared<MapRenderScene>();
        scene_->Init();
        
        render_theme_->SetCamera(scene_->GetCamera());
        
        // create data manager
        data_manager_ = new MapRenderDataManager();
        data_manager_->SetRenderTheme(theme);
        data_manager_->AddDataProvider(provider);
        // init font
        if (default_fontPath_.empty() ) {
            printf("default_fontPath_ is empty\n");
            default_fontPath_ = "./fonts/";
        }
        SKFontManager::Instance().Init(default_fontPath_, default_fontName_);

        // load shader
        GpuShaderCache::Instance().LoadPrograms();

        // overlay
        // overlayer_.Init(w, h);

        return true;
    }

    void MapRenderManager::Destory()
    {
        if (scene_ != nullptr)
        {
            scene_->Destory();
        }

        if (data_manager_ != nullptr)
        {
            delete data_manager_;
            data_manager_ = nullptr;
        }

        current_render_tile_datas_.clear();
    }

    void MapRenderManager::RenderMap()
    {
        static int once = 0;
        if (once < 1)
        {
            last_frame_time  = std::chrono::high_resolution_clock::now();
            once = 1;
        }

        auto now = std::chrono::high_resolution_clock::now();
        auto elapsed = now - last_frame_time;
        auto sleep_time = kFrameTime - elapsed;
        uint32_t cur_frame_z = 0;
        {
            std::lock_guard<std::recursive_mutex> lock(cache_tile_mutex_);
            cur_frame_z = current_render_tile_ids_.empty()? 0:current_render_tile_ids_.begin()->z_; 
        }
        
        if (scene_ != nullptr)
        {
            TileImagePtr tileImage = nullptr;
            // clock_t sttime = clock();
            int deal_count = 0;
            while (MapRenderScene::PopRasterTileToQueue(tileImage))
            {
                if (tileImage == nullptr || tileImage->image_ == nullptr)
                {
                    continue;
                }
                std::lock_guard<std::recursive_mutex> lock(cache_tile_mutex_);
                if ( tileImage->tile_id_.z_ == cur_frame_z) 
                {// todo: only cache current logic level tiles
                    // if (tileImage->image_ != nullptr)
                    {
                        // tile 缓存里先查找，如果已经存在，更新下使用，让它放到cache的最前面
                        std::shared_ptr<CacheTile> cache_tiled = nullptr;
                        if (data_manager_!= nullptr && data_manager_->GetRenderTileDataById(tileImage->tile_id_, cache_tiled))
                        {
                            #ifdef DEBUG
                            printf("tick:%d tile: %d, %d, %d already in cache\n", 
                                std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count(), 
                                tileImage->tile_id_.x_, tileImage->tile_id_.y_, tileImage->tile_id_.z_);
                            #endif
                            continue;
                        }
                       
                        deal_count++;
                        auto cache_tile = ProcessAndCacheTile(tileImage, cur_frame_z);                        
                        // tile需要渲染
                        // 1. 检查tile是否在渲染列表中（O(1)）
                        if (current_render_tile_ids_.count(tileImage->tile_id_)) {
                            // 2. 检查数据是否已存在（O(1)）
                            if (current_render_tile_datas_.count(tileImage->tile_id_) == 0) {
                                // 3. 异步生成的tile数据添加到渲染数据
                                printf("tick:%d add pop tile:%d %d %d to render data list\n", 
                                    std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count(),
                                    tileImage->tile_id_.x_, tileImage->tile_id_.y_, tileImage->tile_id_.z_);
                                current_render_tile_datas_[tileImage->tile_id_] = cache_tile;
                            }
                        }
                        // 将tile加到cache缓存里
                        if (data_manager_!= nullptr)
                        {
                            data_manager_->AddRenderTileCache(tileImage->tile_id_, cache_tile, cur_frame_z == tileImage->tile_id_.z_);
                        }
                    }
                }
                else 
                {
                    background_cached_tileImage_.push_back(tileImage);
                }
            }
            // 再在background_cached_tileImage_里找z_是当前level的数据处理，然后再看deal_count是否有空余
            
            if ( !background_cached_tileImage_.empty()) {
                auto iter = background_cached_tileImage_.begin();
                while( iter != background_cached_tileImage_.end()) {
                    if (*iter == nullptr) {
                        iter++;
                        continue;
                    }
                    if ((*iter)->tile_id_.z_ == cur_frame_z) {
                        if ((*iter)->image_ != nullptr) {
                            deal_count++;
                            ProcessAndCacheTile(*iter, cur_frame_z);
                            iter = background_cached_tileImage_.erase(iter);
                            continue;
                        }
                        
                    }
                    iter++;
                }
            }
            
            if (deal_count < kMaxDealedTileCount && !background_cached_tileImage_.empty()) {
                
                auto to_deal = background_cached_tileImage_.begin();
                
                auto to_deal_count = std::min(background_cached_tileImage_.size(), (size_t)(kMaxDealedTileCount - deal_count));
                #ifdef DEBUG
                printf("tick:%d deal_count:%d , deal back cache:%d\n", 
                    std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count(), 
                    deal_count, to_deal_count);
                    #endif
                std::advance(to_deal, to_deal_count);
                for (auto iter = background_cached_tileImage_.begin(); iter != to_deal; iter++) {
                    if (*iter != nullptr) {
                        if ((*iter)->image_ != nullptr) {
                            deal_count++;
                            ProcessAndCacheTile(*iter, cur_frame_z);
                        }
                    }
                }
                background_cached_tileImage_.erase(background_cached_tileImage_.begin(), to_deal);
            }
            // clock_t edtime = clock();
            // if (double(edtime-sttime)/1000 > 100.0)
            // {
            //     printf("lintime to skia peekPixels:( %f ms)\n", double(edtime-sttime)/1000);
            //     printf("lintime to skia peekPixels:( %f s)\n", double(edtime-sttime)/CLOCKS_PER_SEC); 
            // }
            

            if (current_render_tile_datas_.empty() || current_render_tile_ids_.empty() ||
                  (scene_->GetCamera() && !scene_->GetCamera()->IsAnimationFinished())
                ) 
            {
                // 先将缓存好的tile数据存入current_render_tile_datas_中, 如果当前是动画过程，则tile要重算，不能直接用之前设置的tile
                // 如比例尺变换则前一半动画时间用原始层级的tile，后一半用目标层级的tile，移图动画比例尺没关系
                UpdateCurrentRenderTiles();
            }


            {
                std::lock_guard<std::recursive_mutex> lock(cache_tile_mutex_);
                
                std::vector<TileID> tile_ids(current_render_tile_ids_.begin(), current_render_tile_ids_.end());
                std::vector<std::shared_ptr<CacheTile>> tile_datas;
                for (auto& [id, data] : current_render_tile_datas_) {
                    tile_datas.push_back(data);
                }
                scene_->SetCurRenderMapTiles(tile_datas, tile_ids);
            }
           
 
            scene_->RenderMap();

             effect_layer_.BeginRender();
             // draw effect here
             // ...
             effect_layer_.Render();
        }

        
        if(sleep_time > sleep_time.zero()) {
            std::this_thread::sleep_for(sleep_time);
        }
        
        last_frame_time = last_frame_time + kFrameTime;  // 新逻辑：期望开始时间递增
    }

    void MapRenderManager::SetMapScale(float scale, const uint32_t animation_duration_ms)
    {   
        if (scene_ != nullptr)
        {
            printf("  MapRenderManager::SetMapScale  %f\n", scale);
            scene_->GetRenderState().SetMapScale(scale, animation_duration_ms);
            if (data_manager_!= nullptr) {
                 // 先将缓存好的tile数据存入current_render_tile_datas_中
                UpdateCurrentRenderTiles();
            }
        }
    }

    void MapRenderManager::SetMapCenter(double lon, double lat, const uint32_t animation_duration_ms)
    {
        // printf("  MapRenderManager::SetMapCenter  \n");
        if (scene_ != nullptr)
        {
            scene_->GetRenderState().SetMapCenter(lon, lat, animation_duration_ms);
        }

        // get render data from data manager
        // calc current screen tiles
        UpdateCurrentRenderTiles();
    }

    void MapRenderManager::SetMapRotation(float rot, const uint32_t animation_duration_ms)
    {
        if (scene_ != nullptr)
        {
            scene_->GetRenderState().SetMapRotation(rot, animation_duration_ms);
        }
        UpdateCurrentRenderTiles();
    }

    float MapRenderManager::GetMapScale()
    {
        if (scene_ != nullptr)
        {
            return scene_->GetRenderState().GetMapScale();
        }

        return 0;
    }

    Lonlat MapRenderManager::GetMapCenter()
    {
        if (scene_ != nullptr)
        {
            return scene_->GetRenderState().GetMapCenter();
        }

        return Lonlat(0.,0.); 
    }

    float MapRenderManager::GetMapRotation()
    {
        if (scene_ != nullptr)
        {
            return scene_->GetRenderState().GetMapRotation();
        }

        return 0.f;
    }

    Vector2<double> MapRenderManager::ScreenToMap(const Vector2<int32_t>& pt)
    {
        double lon = 0.0;
        double lat = 0.0;
        if (scene_ != nullptr && scene_->GetCamera() != nullptr)
        {
            scene_->GetCamera()->Screen2World(pt.x, pt.y, lon, lat);
        }
        return Vector2<double>(lon, lat);
    }

    
    Vector2<int32_t> MapRenderManager::MapToScreen(const Vector2<double>& geo)
    {
        double x = 0.0;
        double y = 0.0;
        if (scene_ != nullptr && scene_->GetCamera() != nullptr)
        {
            scene_->GetCamera()->World2Screen(geo.x, geo.y, x, y);
        }
        return Vector2<int32_t>((int32_t)x, (int32_t)y);
       
    }

    void MapRenderManager::SetMapThemeByLayerId()
    {
        if (render_theme_ != nullptr)
        {
            render_theme_->SetRenderThemeByLayerID();
        }
    }

    void MapRenderManager::RenderEmbedGeoObj()
    {

    }

     void MapRenderManager::SetScreenSize(int32_t width, int32_t height) 
     {
         if (scene_!= nullptr)
         {
             scene_->GetRenderState().SetScreenSize(width, height);
             UpdateCurrentRenderTiles();

         }  
     }

     void MapRenderManager::MoveMap(double delta_x, double delta_y, const uint32_t animation_duration_ms)
     {
         if (scene_!= nullptr)
         {
             scene_->MoveMap(delta_x, delta_y, animation_duration_ms);
             UpdateCurrentRenderTiles();
         }
     }

     void MapRenderManager::SetMapPitch(float pitch, const uint32_t animation_duration_ms)
     {
         if (scene_!= nullptr)
         {
             scene_->SetMapPitch(pitch, animation_duration_ms);
         }
         UpdateCurrentRenderTiles();
     }

     float MapRenderManager::GetMapPitch()
     {
         if (scene_!= nullptr)
         {
             return scene_->GetMapPitch();
         }
         return 0;
     }

     //  flyto 到指定经纬度和比例尺，默认最少5s动画
     void MapRenderManager::FlyTo(double lon, double lat, uint32_t dest_scale, uint32_t animation_duration_ms)
     {
        if (scene_!= nullptr && scene_->GetCamera() != nullptr)
        {
            auto camera = scene_->GetCamera();
            AnimationOptions animation(Milliseconds(animation_duration_ms), 
            {camera->GetMapCenter().longitude_, camera->GetMapCenter().latitude_}, {lon, lat}, AnimationType::kAnimationTypeFlyTo);
            scene_->GetCamera()->FlyTo(lon, lat, dest_scale, animation);
        }
     }

     void MapRenderManager::UpdateCurrentRenderTiles()
     {
        if (data_manager_!= nullptr)
        {
            std::vector<TileID> current_render_tile_ids;
            std::vector<std::shared_ptr<CacheTile>> current_render_tile_datas;
            data_manager_->GetRenderTileData(scene_->GetCamera(), current_render_tile_ids, current_render_tile_datas);
            std::lock_guard<std::recursive_mutex> lock(cache_tile_mutex_);
            // 清空旧数据
            current_render_tile_ids_.clear();
            current_render_tile_datas_.clear();
            // 填充新数据（渲染列表和数据关联）
            for (const auto& id : current_render_tile_ids) {
                current_render_tile_ids_.insert(id);
            }
            for (const auto& data : current_render_tile_datas) {
                if(data && data->raster_tile) {
                    current_render_tile_datas_[data->raster_tile->GetTileID()] = data;
                }
            }
        }
     }

     void MapRenderManager::SetPath(uint32_t type, std::vector<Point2d>& path)
     {
         PathManager::Instance().SetRoute(type, path);
     }

     void MapRenderManager::ClearPath(uint32_t type)
     {
         PathManager::Instance().ClearRoute(type);
     }

     int32_t MapRenderManager::SetMarkInfo(uint16_t mark_type, Point2d mark_lnglat_pos, Point2d mark_anchor, std::string mark_name)
     {
         return -1;
         auto info = MarkManager::Instance().GetMark(mark_name, mark_anchor);
         return PinMarkManager::Instance().Add(mark_type, info, mark_lnglat_pos, 0);
     }

     void MapRenderManager::UpdateMarkInfo(uint16_t mark_type, int32_t mark_id,  Point2d mark_anchor, std::string mark_name)
     {
         auto info = MarkManager::Instance().GetMark(mark_name, mark_anchor);
         PinMarkManager::Instance().Update(mark_type, mark_id, info);
     }

     void MapRenderManager::UpdateMarkInfo(uint16_t mark_type, int32_t mark_id, Point2d mark_lnglat_pos, float degree)
     {
         PinMarkManager::Instance().Update(mark_type, mark_id, mark_lnglat_pos, degree);
     }

     void MapRenderManager::ClearMark(uint16_t mark_type, int32_t mark_id)
     {
         PinMarkManager::Instance().Delete(mark_type, mark_id);
     }

    void MapRenderManager::GenerateBuildingData( std::shared_ptr<CacheTile> cache_tile)
    {
        if (cache_tile == nullptr) {
            return;
        }
        // 预加载固定纹理（仅首次调用时加载）
        static std::shared_ptr<GLTexture2D> building_texture = nullptr;
        if (!building_texture) {
            building_texture = std::make_shared<GLTexture2D>();
            std::vector<uint8_t> buf;
            int32_t w = 0, h = 0;
            // if (MarkManager::Instance().ReadMarkFromFile("../data/mark/brick.png", buf, w, h)) 
            // {
            //     // 加载纹理并设置参数（复用 GLTexture2D 的 Set 方法）
            //     building_texture->Set(buf.data(), aurora::FORMAT_RGBA, w, h);
            //     building_texture->SetTextureParameter(
            //         TextureFilterType::FILTER_LINEAR,  // 最小过滤
            //         TextureFilterType::FILTER_LINEAR,  // 最大过滤
            //         TextureWrapType::WRAP_REPEAT,      // S 轴环绕
            //         TextureWrapType::WRAP_REPEAT       // T 轴环绕
            //     );
            // }
        }
    
        for (auto& building : cache_tile->building_list) {
            // 跳过已生成的资源（避免重复生成）
            if (building.vao != 0 && building.texture_id != 0) continue;
    
            // 生成 VAO/VBO/EBO（渲染线程，拥有 OpenGL 上下文）
            glGenVertexArrays(1, &building.vao);
            glGenBuffers(1, &building.vbo);
            glGenBuffers(1, &building.ebo);
    
            // 复用预加载的纹理 ID（关键修改）
            if (building.texture_id == 0 && building_texture->IsValid()) {
                building.texture_id = building_texture->GetTexID(); // 直接复用纹理 ID
            }
    
            // 绑定 VAO 和 VBO/EBO（原有逻辑不变）
            glBindVertexArray(building.vao);
            glBindBuffer(GL_ARRAY_BUFFER, building.vbo);
            glBufferData(GL_ARRAY_BUFFER, building.vertices.size() * sizeof(float), 
                        building.vertices.data(), GL_STATIC_DRAW);
            glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, building.ebo);
            glBufferData(GL_ELEMENT_ARRAY_BUFFER, building.indices.size() * sizeof(uint32_t), 
                        building.indices.data(), GL_STATIC_DRAW);
    
            // 设置顶点属性（与着色器匹配，原有逻辑不变）
            glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 8 * sizeof(float), (void*)0);
            glEnableVertexAttribArray(0);
            glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, 8 * sizeof(float), (void*)(3 * sizeof(float)));
            glEnableVertexAttribArray(1);
            glVertexAttribPointer(2, 2, GL_FLOAT, GL_FALSE, 8 * sizeof(float), (void*)(6 * sizeof(float)));
            glEnableVertexAttribArray(2);
    
            glBindVertexArray(0);
        }
    }

     CacheTilePtr MapRenderManager::ProcessAndCacheTile(TileImagePtr tileImage, uint32_t cur_frame_z) {
        
        if (!tileImage || !tileImage->image_) return nullptr;
        CacheTilePtr cache_tile;
        // 创建并初始化 RasterTile
        auto raster_tile = std::make_shared<RasterTile>();
        if (raster_tile) {
            raster_tile->Init(tileImage->pixmap_.addr(), aurora::FORMAT_RGBA, 
                             tileImage->pixmap_.width(), tileImage->pixmap_.height());
            raster_tile->SetTileID(tileImage->tile_id_, tileImage->tile_bbox_);
        }

        // 创建 CacheTile 并添加到缓存
        if (data_manager_) {
            #ifdef DEBUG
            printf("tick:%d add back cache tile:%d, %d, %d\n", 
                std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::high_resolution_clock::now().time_since_epoch()).count(),
                tileImage->tile_id_.x_, tileImage->tile_id_.y_, tileImage->tile_id_.z_);
            #endif

            cache_tile = std::make_shared<CacheTile>();
            cache_tile->raster_tile = raster_tile;
            cache_tile->collision_tile = tileImage->collision_;
            {
                cache_tile->building_list = tileImage->building_list_;
                // 生成vbo，fbo等数据
                GenerateBuildingData(cache_tile);
            }
            data_manager_->AddRenderTileCache(tileImage->tile_id_, cache_tile, cur_frame_z == tileImage->tile_id_.z_);
        }
        return cache_tile;
    }

} //namespace 
