#include "mark/skia/sk_image_holder.h"

namespace aurora {
void SkImageHolder::CreateImage() {
  if (Width() == 0 || Height() == 0 || GetBuf().size() == 0) {
    return;
  }
  CreateBitmapFromBuff(GetBuf(), Width(), Height());
  image_ = SkImages::RasterFromBitmap(bitmap_);
}

void SkImageHolder::CreateBitmapFromBuff(std::vector<uint8_t>& bitmap_buf, int32_t w, int32_t h) {
  SkImageInfo info = SkImageInfo::Make(w, h, kRGBA_8888_SkColorType, kPremul_SkAlphaType);
  size_t row_bytes = w * 4;
  SkPixmap pixmap;
  pixmap.reset(info, bitmap_buf.data(), row_bytes);
  bitmap_.installPixels(pixmap);
}
}  // namespace aurora
