#ifndef MAP_RENDER_MARK_SKIA_SK_IMAGE_HOLDER_H
#define MAP_RENDER_MARK_SKIA_SK_IMAGE_HOLDER_H

#include "SkBitmap.h"
#include "SkImage.h"
#include "SkPixmap.h"
#include "mark/mark_def.h"

namespace aurora {
class SkImageHolder : public ImageHolder {
public:
  virtual void CreateImage();

  const sk_sp<SkImage>& GetImage() { return image_; }

private:
  void CreateBitmapFromBuff(std::vector<uint8_t>& bitmap_buf, int32_t w, int32_t h);

  SkBitmap bitmap_;
  sk_sp<SkImage> image_;
};
}  // namespace aurora
#endif  // MAP_RENDER_MARK_SKIA_SK_IMAGE_HOLDER_H
