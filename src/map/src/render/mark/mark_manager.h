#ifndef MAP_RENDER_MARK_MARK_MANAGER_H
#define MAP_RENDER_MARK_MARK_MANAGER_H

#include <cstdint>
#include <mutex>
#include <vector>

#include "mark/mark_def.h"

namespace aurora {

class MarkManager {
public:
  static MarkManager& Instance() {
    static MarkManager instance;
    return instance;
  }

  MarkManager();

  bool Init(const char* file);

  ImageHolderPtr& GetResHolder();

  UserMarkInfoPtr GetMark(const std::string& file, Point2d& anchor);

  UserMarkInfoPtr GetMark(const std::string& file);

private:
  UserMarkInfoPtr GetUserMarkFromCache(const std::string& file, Point2d& anchor);

  bool ReadMarkFromFile(const char* file, std::vector<uint8_t>& buf, int32_t& w, int32_t& h);

private:
  ImageHolderPtr res_holder_;
  UserMarkCache mark_cache_;
  std::mutex mark_mtx_;
};
}  // namespace aurora
#endif  // MAP_RENDER_MARK_MARK_MANAGER_H
