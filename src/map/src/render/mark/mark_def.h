#ifndef MAP_RENDER_MARK_MARK_DEF_H
#define MAP_RENDER_MARK_MARK_DEF_H

#include <cstdint>
#include <map>
#include <memory>
#include <string>
#include <vector>

#include "aabb2.h"
#include "point2.h"

namespace aurora {
using MarkRect = AABB2<PointXY<uint32_t>>;

enum MarkID {
  kMarkAttraction = 0,
  kMarkAirport,
  kMarkCatering,
  kMarkParking,

  kMarkOthers
};

// temp code
const std::vector<MarkRect> mark_info = {{{2, 3}, {46, 47}},
                                         {{145, 3}, {189, 47}},
                                         {{288, 3}, {332, 47}},
                                         {{431, 3}, {475, 47}},
                                         {{574, 3}, {618, 47}}};

class ImageHolder {
public:
  virtual void CreateImage() {}
  void SetSize(int32_t w, int32_t h) {
    width_ = w;
    height_ = h;
  }
  std::vector<uint8_t>& GetBuf() { return buf_; }
  int32_t Width() { return width_; }
  int32_t Height() { return height_; }

private:
  std::vector<uint8_t> buf_{};
  int32_t width_{0};
  int32_t height_{0};
};

using ImageHolderPtr = std::shared_ptr<ImageHolder>;

struct UserMarkInfo {
  Point2d anchor;
  ImageHolderPtr image_holder;
};
using UserMarkInfoPtr = std::shared_ptr<UserMarkInfo>;
using UserMarkCache = std::map<std::string, UserMarkInfoPtr>;
struct DrawMarkCommand {
  std::string mark_file;
  Point2d lnglat;
  float degree;
  UserMarkInfoPtr ptr{nullptr};
};
using DrawMarkCommandPtr = std::shared_ptr<DrawMarkCommand>;
using DrawMarkCommandSet = std::vector<DrawMarkCommandPtr>;
using DrawMarkCommandCache = std::map<uint16_t, DrawMarkCommandSet>;

}  // namespace aurora
#endif  // MAP_RENDER_MARK_MARK_DEF_H
