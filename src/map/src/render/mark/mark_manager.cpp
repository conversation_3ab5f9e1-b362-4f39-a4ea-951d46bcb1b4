#include "mark_manager.h"

#include <cstdio>
#include <iostream>
#include <string>

#include "mark/skia/sk_image_holder.h"
#include "png.h"

namespace aurora {
MarkManager::MarkManager() {}

bool MarkManager::Init(const char* file) {
  res_holder_ = std::make_shared<SkImageHolder>();
  int32_t w = 0;
  int32_t h = 0;
  if (ReadMarkFromFile(file, res_holder_->GetBuf(), w, h)) {
    res_holder_->SetSize(w, h);
    res_holder_->CreateImage();
    return true;
  }
  return false;
}

ImageHolderPtr& MarkManager::GetResHolder() { return res_holder_; }

UserMarkInfoPtr MarkManager::GetMark(const std::string& file, Point2d& anchor) {
  std::lock_guard<std::mutex> lock(mark_mtx_);
  return GetUserMarkFromCache(file, anchor);
}

UserMarkInfoPtr MarkManager::GetMark(const std::string& file) {
  std::lock_guard<std::mutex> lock(mark_mtx_);
  auto itr = mark_cache_.find(file);
  if (itr == mark_cache_.end()) {
    return nullptr;
  }
  return itr->second;
}

UserMarkInfoPtr MarkManager::GetUserMarkFromCache(const std::string& file, Point2d& anchor) {
  auto itr = mark_cache_.find(file);
  if (itr == mark_cache_.end()) {
    ImageHolderPtr img_holder = std::make_shared<SkImageHolder>();
    int32_t w = 0;
    int32_t h = 0;
    if (!ReadMarkFromFile(file.c_str(), img_holder->GetBuf(), w, h)) {
      return nullptr;
    }
    UserMarkInfoPtr ptr = std::make_shared<UserMarkInfo>();
    ptr->anchor = anchor;
    ptr->image_holder = img_holder;
    ptr->image_holder->SetSize(w, h);
    ptr->image_holder->CreateImage();
    mark_cache_.emplace(file, ptr);
    return ptr;
  }
  return itr->second;
}

bool MarkManager::ReadMarkFromFile(const char* file, std::vector<uint8_t>& buf, int32_t& w,
                                   int32_t& h) {
  FILE* fptr = fopen(file, "rb");
  if (fptr == nullptr) {
    return false;
  }
  png_structp png = png_create_read_struct(PNG_LIBPNG_VER_STRING, NULL, NULL, NULL);
  if (!png) {
    fclose(fptr);
    return false;
  }
  png_infop info = png_create_info_struct(png);
  if (!info) {
    png_destroy_read_struct(&png, NULL, NULL);
    fclose(fptr);
    return false;
  }

  if (setjmp(png_jmpbuf(png))) {
    png_destroy_read_struct(&png, &info, NULL);
    fclose(fptr);
    return false;
  }
  png_init_io(png, fptr);
  png_read_info(png, info);
  int width = png_get_image_width(png, info);
  int height = png_get_image_height(png, info);
  int color_type = png_get_color_type(png, info);
  int bit_depth = png_get_bit_depth(png, info);

  if (color_type == PNG_COLOR_TYPE_PALETTE) {
    png_set_expand(png);
  }
  if (color_type == PNG_COLOR_TYPE_GRAY && bit_depth < 8) {
    png_set_expand(png);
  }
  if (png_get_valid(png, info, PNG_INFO_tRNS)) {
    png_set_expand(png);
  }
  if (color_type == PNG_COLOR_TYPE_GRAY || color_type == PNG_COLOR_TYPE_GRAY_ALPHA) {
    png_set_gray_to_rgb(png);
  }
  if (bit_depth < 8) {
    png_set_expand(png);
  }
  png_read_update_info(png, info);

  int rowbytes = png_get_rowbytes(png, info);
  png_bytep* row_pointers = (png_bytep*)malloc(sizeof(png_bytep) * height);

  for (int y = 0; y < height; y++) {
    row_pointers[y] = (png_bytep)malloc(rowbytes);
    png_read_rows(png, row_pointers + y, nullptr, 1);
  }

  buf.resize(width * height * 4, 0);
  w = width;
  h = height;
  for (int y = 0; y < height; y++) {
    png_bytep row = row_pointers[y];
    png_bytep rgba = &buf[y * width * 4];
    for (int x = 0; x < width; x++) {
      png_bytep pixel = row + x * 4;
      rgba[0] = pixel[2];  // R
      rgba[1] = pixel[1];  // G
      rgba[2] = pixel[0];  // B
      rgba[3] = pixel[3];  // A
      rgba += 4;
    }
  }
  for (int y = 0; y < height; y++) {
    free(row_pointers[y]);
  }
  png_free(png, row_pointers);

  png_destroy_read_struct(&png, &info, nullptr);
  fclose(fptr);

  return true;
}
}  // namespace aurora
