/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file map_render_backend.h
 * @brief Declaration file of class MapRenderBackend.
 * @attention used for C/C++ only.
 */

#ifndef MAPRENDERBACKEND_BACKEND_H_
#define MAPRENDERBACKEND_BACKEND_H_

#include <vector>
#include <memory>
#include <string>
#include "util_basic_type.h"
#include "render_geometry_type.h"
#include "util_color.h"
#include "util_tile_define.h"
#include "map_render_poi_style.h"
#include "text_def.h"
#include "aabb2.h"
#include "util_vector3.h"
#include "util_matrix4.h"
#include "building_data.h"

namespace aurora {

class MapRenderPaint;
class FontStyle;
class MapRenderBackend;
class MapRenderContext;
class CollisionTile;

/// @brief The active graphics API/backend type.
enum class BackendAPIType : uint8_t {
    API_SKIA,   ///< The Skia API backend
    API_GLES,  ///< The OpenGLES API backend
    API_VULKAN, ///< The Vulkan API backend
    API_METEL, ///< The Metal(MacOS) API backend
    API_MAX,   ///< Not a valid backend type, used to determine the number
};

/// Backend Shared Ptr
using BackendPtr = std::shared_ptr<MapRenderBackend>;
using CollisionTilePtr = std::shared_ptr<CollisionTile>;

/**
* class breif description
*
* MapRenderBackend Interface
*
*/
class MapRenderBackend {
public:         
    virtual ~MapRenderBackend() = default;

    virtual bool Init(uint32_t w, uint32_t h, Color background) = 0;

    virtual void Destory()= 0;

    virtual void BuildProMatrix() = 0;

    virtual void SetLineStyle(LineCap cap, LineJoin join) = 0;

    virtual void SetFillStyle(FillStyle style) = 0;

    virtual void SetFontStyle(const FontStyle& style) = 0;

    virtual void DrawPolyline(const std::vector<Point>& points) = 0;

    virtual void DrawPolygon(const std::vector<Point>& points) = 0;

    virtual void DrawText(const std::string& str, const aurora::Point& pt, float degree, const TextStyle& style, TextHolderPtr& text_holder) = 0;

    virtual void DrawImage() = 0;

    virtual void SetColor(Color clr) = 0;

    virtual void BeginDraw() = 0;

    virtual void EndDraw(TileID id, const AABB2<Point2d>& tile_mbr, CollisionTilePtr ptr, const std::vector<BuildingData>& building_list) = 0;

    virtual void SetAntiAlias(bool anti) = 0;

    virtual void SetLineWidth(float width) = 0;

    virtual MapRenderContext* GetContext() = 0;

    virtual BackendAPIType GetBackendType() const = 0;

    virtual void DrawBuildings(const std::vector<BuildingData>& buildings,
                               const Vector3<float>& light_dir,  // 全局光照方向
                               const Vector3<float>& ambient,   // 全局环境光
                               const Matrix4<double>& proj_view_matrix ) = 0;

public:
    static BackendPtr CreateBackend(BackendAPIType type);

// protected:
//     virtual bool CreateContext(Color background) = 0;
};

} //namespace 

#endif //RENDER_BACKEND_MAP_RENDER_BACKEND_H_
/* EOF */
