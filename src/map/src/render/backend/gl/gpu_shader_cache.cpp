/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

 #include "gpu_shader_cache.h"

 namespace aurora {
    
    const char* GpuShaderCache::GetVShader(glshader::GPUProgramID id)
    {
        switch (id)
        {
        case glshader::PROGRAM_TILE:
            return glshader::kVertexDefault;
        case glshader::PROGRAM_BUILDING:  // 新增：处理 3D 建筑顶点着色器
            return glshader::kBuildingVertexShader;
        case glshader::PROGRAM_2D_PROJECTION:
            return glshader::k2DVertexShader;
        default:
            return glshader::kVertexDefault;
    }
}

// 新增：获取片段着色器的方法
const char* GpuShaderCache::GetFShader(glshader::GPUProgramID id) {
    switch (id) {
        case glshader::PROGRAM_TILE:
            return glshader::kFragmentDefault;
        case glshader::PROGRAM_BUILDING:  // 新增：处理 3D 建筑片段着色器
            return glshader::kBuildingFragmentShader;
        case glshader::PROGRAM_2D_PROJECTION:
            return glshader::k2DFragmentShader;
        default:
            return glshader::kFragmentDefault;
    }
}

// 新增：创建并缓存着色器程序的逻辑（示例）
GPUProgramPtr GpuShaderCache::CreateProgram(glshader::GPUProgramID id) {
    const char* vShader = GetVShader(id);
    const char* fShader = GetFShader(id);

    GPUProgramPtr gpuProgram = std::make_shared<GPUProgram>();
    gpuProgram->CreateProgram(vShader, fShader);

    // 缓存程序
    programs_[id] = gpuProgram;
    return gpuProgram;
}

// 修改 GetProgram 方法，若不存在则创建
GPUProgramPtr GpuShaderCache::GetProgram(const glshader::GPUProgramID id) {
    auto iter = programs_.find(id);
    if (iter != programs_.end()) {
        return iter->second;
    } else {
        // 若不存在则创建（实际项目中需添加错误检查）
        return CreateProgram(id);
    }
}

    GpuShaderCache::GpuShaderCache()
    {
    }

    GpuShaderCache::~GpuShaderCache()
    {
        Destory();
    }

    void GpuShaderCache::Destory()
    {
        programs_.clear();
    }

    GpuShaderCache& GpuShaderCache::Instance()
    {
        static GpuShaderCache shader_cache_;
        return shader_cache_;
    }

    int32_t GpuShaderCache::LoadPrograms() {
        {// tile
            GPUProgramPtr gpuProgram = std::make_shared<GPUProgram>();
            bool ret = gpuProgram->CreateProgram(GetVShader(glshader::PROGRAM_TILE), GetFShader(glshader::PROGRAM_TILE));
            programs_[glshader::PROGRAM_TILE] = gpuProgram;
            printf("Load PROGRAM_TILE: %s\n", ret ? "Success" : "Failed");  // 新增加载状态日志
        }
    
        {// overlay 
            // overlay will require an independent shader later
            programs_[glshader::PROGRAM_OVERLAYER] = GetProgram(glshader::PROGRAM_TILE);
            printf("Load PROGRAM_OVERLAYER: Aliased to PROGRAM_TILE\n");  // 明确提示复用关系
        }
    
        {// 2D projection（新增）
            GPUProgramPtr gpuProgram = std::make_shared<GPUProgram>();
            bool ret = gpuProgram->CreateProgram(GetVShader(glshader::PROGRAM_2D_PROJECTION), GetFShader(glshader::PROGRAM_2D_PROJECTION));
            programs_[glshader::PROGRAM_2D_PROJECTION] = gpuProgram;
            printf("Load PROGRAM_2D_PROJECTION: %s\n", ret ? "Success" : "Failed");  // 关键新增：2D投影着色器加载日志
        }
        {
        // 2D projection（新增）
            GPUProgramPtr gpuProgram = std::make_shared<GPUProgram>();
            bool ret = gpuProgram->CreateProgram(GetVShader(glshader::PROGRAM_BUILDING), GetFShader(glshader::PROGRAM_BUILDING));
            programs_[glshader::PROGRAM_BUILDING] = gpuProgram;
            printf("Load PROGRAM_BUILDING: %s\n", ret ? "Success" : "Failed");  // 关键新增：2D投影着色器加载日志
        }
    
    
        return 0; 
    }


} 
