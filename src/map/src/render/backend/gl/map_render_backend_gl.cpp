/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

 #include "map_render_backend_gl.h"
 #include "gl_context.h"
 #include <GLES3/gl3.h>
 #include "gpu_shader_cache.h"

 namespace aurora {
 
    RenderBackendGL::RenderBackendGL()
    {

    }

    RenderBackendGL::~RenderBackendGL()
    {

    }

    bool RenderBackendGL::Init(uint32_t w, uint32_t h, Color background) {   
        if (!CreateContext(background)) {
            return false;
        }
    
        context_->SetViewPort(0, 0, w, h);
        width_ = w;
        height_ = h;
    
        //  2D 初始化逻辑 ...
    
        // 修正：使用枚举中定义的 PROGRAM_BUILDING
        building_shader_ = GpuShaderCache::Instance().GetProgram(glshader::PROGRAM_BUILDING);
        if (building_shader_ == nullptr) {
            return false;
        }
    
        // 新增：初始化 2D 投影着色器
        proj_2d_shader_ = GpuShaderCache::Instance().GetProgram(glshader::PROGRAM_2D_PROJECTION);
        if (proj_2d_shader_ == nullptr) {
            return false;
        }
    
        // 新增：创建白色纹理（关键修改）
        glGenTextures(1, &white_texture_id_);
        glBindTexture(GL_TEXTURE_2D, white_texture_id_);
        // 设置纹理参数（线性过滤、边缘不重复）
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
        glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
        // 生成 1x1 白色纹理（所有像素为 RGBA(255,255,255,255)）
        const uint8_t white_pixel[4] = {255, 255, 255, 255};
        glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, 1, 1, 0, GL_RGBA, GL_UNSIGNED_BYTE, white_pixel);
        glBindTexture(GL_TEXTURE_2D, 0);  // 解绑
    
        // 原有状态初始化 ...
        glEnable(GL_DEPTH_TEST);
        glDepthFunc(GL_LEQUAL);
        glEnable(GL_BLEND);
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
    
        return true;
    }

    void RenderBackendGL::Destory() {
        // 释放白色纹理
        if (white_texture_id_ != 0) {
            glDeleteTextures(1, &white_texture_id_);
            white_texture_id_ = 0;
        }
    }

    void RenderBackendGL::BuildProMatrix()
    {
        
    }

    void RenderBackendGL::DrawPolyline(const std::vector<Point>& points)
    {
        
    }

    void RenderBackendGL::DrawPolygon(const std::vector<Point>& points)
    {

    }

    void RenderBackendGL::DrawText(const std::string& str, const aurora::Point& pt, float degree, const TextStyle& style, TextHolderPtr& text_holder)
    {

    }

    void RenderBackendGL::DrawImage()
    {

    }

    void RenderBackendGL::SetLineStyle(LineCap cap, LineJoin join)
    {
    
    }

    void RenderBackendGL::SetFillStyle(FillStyle style)
    {
    
    }

    void RenderBackendGL::SetFontStyle(const FontStyle& style)
    {
        
    }

    void RenderBackendGL::SetColor(Color clr)
    {

    }

    void RenderBackendGL::SetAntiAlias(bool anti)
    {

    }

    void RenderBackendGL::SetLineWidth(float width)
    {
    
    }

    bool RenderBackendGL::CreateContext(Color background)
    {
        context_ = std::make_unique<GLContext>();
        return context_->Init(background) == 0;
    }

    void RenderBackendGL::BeginDraw()
    {
        if (context_ != nullptr) {
            context_->Clear();
        }
    }

    void RenderBackendGL::EndDraw(TileID id, const AABB2<Point2d>& tile_mbr, CollisionTilePtr ptr, const std::vector<BuildingData>& buildings)
    {

    }


void RenderBackendGL::DrawBuildings(const std::vector<BuildingData>& buildings,
                                   const Vector3<float>& light_dir,
                                   const Vector3<float>& ambient,
                                   const Matrix4<double>& proj_view_matrix) {
    if (building_shader_ == nullptr || buildings.empty()) {
        printf("building_shader_ == %p || buildings.empty %d\n", building_shader_.get(), buildings.size());
        return;
    }
    DrawTriangle(buildings, proj_view_matrix);
    return;
    building_shader_->UseProgram();

    // 全局光照参数只需设置一次（原逻辑保留）
    GLuint light_dir_loc =  building_shader_->GetUniformLocation("u_lightDir");
    GLfloat light_dir_f[3] = {light_dir[0], light_dir[1], light_dir[2]};
    glUniform3fv(light_dir_loc, 1, light_dir_f);
    GLuint ambient_loc = building_shader_->GetUniformLocation("u_ambient");
    GLfloat ambient_f[3] = {ambient[0], ambient[1], ambient[2]};
    glUniform3fv(ambient_loc, 1, ambient_f);

    // 纯色设置（原逻辑保留）
    GLuint diffuse_loc = building_shader_->GetUniformLocation("u_diffuse");
    glUniform3f(diffuse_loc, 1.0f, 0.0f, 0.0f);

    // 新增：合并所有建筑的顶点和索引到临时缓冲区
    std::vector<float> merged_vertices;
    std::vector<uint32_t> merged_indices;
    uint32_t index_offset = 0;  // 记录索引偏移量

    for (const auto& building : buildings) {
        if (building.vao == 0 || building.texture_id == 0) {
            printf("Skip invalid building: vao=%d, texture_id=%d\n", building.vao, building.texture_id);
            continue;
        }

        // 新增：打印顶点和索引数量（关键验证点）
        printf("Building info: vao=%d, vertices_size=%zu, indices_size=%zu\n", 
               building.vao, building.vertices.size(), building.indices.size());

        // 新增：打印前3个顶点的位置（验证坐标是否合理）
        if (!building.vertices.empty()) {
            printf("First 3 vertices (x,y,z):\n");
            for (size_t i = 0; (i/8) < 3 && i < building.vertices.size(); i += 8) {  // 步长8（3位置+3法线+2纹理）
                float x = building.vertices[i];
                float y = building.vertices[i+1];
                float z = building.vertices[i+2];
                printf("  Vertex %zu: (%.2f, %.2f, %.2f)\n", i/8, x, y, z);
                // 打印这几个点mvp矩阵*完后的坐标
                Vector4<float> pt = Vector4<float>(x, y, z, 1.0f);
                pt = proj_view_matrix * building.model_matrix * pt;
                printf("  Vertex %zu after MVP: (%.2f, %.2f, %.2f)\n", i/8, pt.x, pt.y, pt.z);
                pt = Vector4<float>(x, y, 0.0f, 1.0f);
                 pt = proj_view_matrix * building.model_matrix * pt;
                  printf(" 0.0z Vertex %zu after MVP: (%.2f, %.2f, %.2f)\n", i/8, pt.x, pt.y, pt.z);

            }
        }

        // 设置 MVP 和模型矩阵
        Matrix4<float> mvp = proj_view_matrix * building.model_matrix;
        GLuint mvp_loc = building_shader_->GetUniformLocation("u_mvp");
        glUniformMatrix4fv(mvp_loc, 1, GL_FALSE, mvp.getRawPointer());

        GLuint model_loc = building_shader_->GetUniformLocation("u_model");
        glUniformMatrix4fv(model_loc, 1, GL_FALSE, building.model_matrix.getRawPointer());

        // 绑定当前建筑的纹理和 VAO（预先生成的资源）
        // glBindTexture(GL_TEXTURE_2D, building.texture_id);
        glBindVertexArray(building.vao);  // 直接使用建筑自己的 VAO

        // 绘制（VAO 已包含顶点属性配置，无需重复设置）
        // glDrawElements(GL_TRIANGLES, building.indices.size(), 
        //               GL_UNSIGNED_INT, 0);
        glDisable(GL_DEPTH_TEST);  // 关闭深度测试
glDrawElements(GL_TRIANGLES, building.indices.size(), 
              GL_UNSIGNED_INT, 0);
glEnable(GL_DEPTH_TEST);   // 恢复深度测试

                      
    }
 
    glBindVertexArray(0);
    glBindTexture(GL_TEXTURE_2D, 0);
    glUseProgram(0);
}

void RenderBackendGL::DrawTriangle(const std::vector<BuildingData>& buildings, const Matrix4<double>& proj_view_matrix) {
    if (building_shader_ == nullptr || buildings.empty()) {
        printf("No valid buildings or shader for triangle drawing\n");
        return;
    }

    // 新增：合并所有建筑的顶点和索引
    std::vector<float> merged_vertices;
    std::vector<uint32_t> merged_indices;
    uint32_t index_offset = 0;

    for (const auto& building : buildings) {
        if (building.vao == 0 || building.vertices.size() < 3 * 8) {  // 至少3个顶点（每个顶点8float）
            printf("Skip invalid building: vao=%d, vertices_size=%zu\n", building.vao, building.vertices.size());
            continue;
        }

        // 合并顶点数据（直接追加）
        merged_vertices.insert(merged_vertices.end(), building.vertices.begin(), building.vertices.end());
        
        // 合并索引数据（调整偏移量）
        for (auto idx : building.indices) {
            merged_indices.push_back(idx + index_offset);
        }
        index_offset += building.vertices.size() / 8;  // 每个顶点占8float
    }

    if (merged_vertices.empty() || merged_indices.empty()) {
        printf("No valid vertices/indices to draw triangle\n");
        return;
    }

    // 创建共享VAO/VBO/EBO
    GLuint vao, vbo, ebo;
    glGenVertexArrays(1, &vao);
    glGenBuffers(1, &vbo);
    glGenBuffers(1, &ebo);
    glBindVertexArray(vao);

    // 填充顶点缓冲
    glBindBuffer(GL_ARRAY_BUFFER, vbo);
    glBufferData(GL_ARRAY_BUFFER, merged_vertices.size() * sizeof(float), merged_vertices.data(), GL_STATIC_DRAW);

    // 填充索引缓冲
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, ebo);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, merged_indices.size() * sizeof(uint32_t), merged_indices.data(), GL_STATIC_DRAW);

    // 顶点属性配置（与3D着色器要求一致）
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 8 * sizeof(float), (void*)0);
    glEnableVertexAttribArray(0);
    glVertexAttribPointer(1, 3, GL_FLOAT, GL_FALSE, 8 * sizeof(float), (void*)(3 * sizeof(float)));
    glEnableVertexAttribArray(1);
    glVertexAttribPointer(2, 2, GL_FLOAT, GL_FALSE, 8 * sizeof(float), (void*)(6 * sizeof(float)));
    glEnableVertexAttribArray(2);

    // 使用建筑着色器
    building_shader_->UseProgram();

    // 统一设置矩阵（假设所有三角形使用相同投影视图矩阵）
    Matrix4<float> mvp = proj_view_matrix;  // 需根据实际类型转换
    GLuint mvp_loc = building_shader_->GetUniformLocation("u_mvp");
    if (mvp_loc != -1) {
        glUniformMatrix4fv(mvp_loc, 1, GL_FALSE, mvp.getTranspose().getRawPointer());
    }

    // 统一设置光照参数（与DrawBuildings逻辑一致）
    GLuint diffuse_loc = building_shader_->GetUniformLocation("u_diffuse");
    if (diffuse_loc != -1) glUniform3f(diffuse_loc, 1.0f, 0.0f, 0.0f);  // 红色

    // 绑定白色纹理（统一使用预加载纹理）
    GLuint tex_loc = building_shader_->GetUniformLocation("u_texture");
    if (tex_loc != -1) {
        glUniform1i(tex_loc, 0);
        glActiveTexture(GL_TEXTURE0);
        glBindTexture(GL_TEXTURE_2D, white_texture_id_);
    }

    // 绘制合并后的所有三角形（仅一次调用）
    glDisable(GL_DEPTH_TEST);
    glDrawElements(GL_TRIANGLES, merged_indices.size(), GL_UNSIGNED_INT, 0);
    glEnable(GL_DEPTH_TEST);

    // 清理临时资源
    glDeleteVertexArrays(1, &vao);
    glDeleteBuffers(1, &vbo);
    glDeleteBuffers(1, &ebo);
    glBindVertexArray(0);
    glUseProgram(0);
}


     
 } //namespace 

