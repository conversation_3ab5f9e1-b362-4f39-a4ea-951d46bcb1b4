/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file gpu_shader_chache.h
 * @brief Declaration file of class GpuShaderCache.
 * @attention used for C/C++ only.
 */

#ifndef MPA_SRC_RENDER_BACKEND_GL_GPUSHADERCACHE_H_
#define MPA_SRC_RENDER_BACKEND_GL_GPUSHADERCACHE_H_

#include <map>
#include "gpu_program.h"
#include "gpu_program_id.h"

namespace aurora {

 /**
 * class breif description
 *
 * GpuShaderCache: cache all runtime shader
 * When the program starts, load the shaders to avoid dynamic loading during runtime. 
 * In the later stage, the shader can be precompiled into binary,
 * which only needs to be loaded at startup without dynamic compilation.
 *
 */
 
class GpuShaderCache 
{
public:
    ~GpuShaderCache();

    void Destory();

    /// @brief The compilation and loading of shaders take time. 
    //  When the program starts, load the shaders to avoid dynamic loading during runtime. 
    /// In the later stage, there will be optimization, and the shader can be precompiled into binary,
    //  which only needs to be loaded at startup without dynamic compilation.
    /// @return Returns successful 0, false otherwise
    int32_t LoadPrograms();

    GPUProgramPtr GetProgram(const glshader::GPUProgramID id);

    static GpuShaderCache& Instance();

private:
    GpuShaderCache();
    const char* GetFShader(glshader::GPUProgramID id);
    const char* GetVShader(glshader::GPUProgramID id);
    GPUProgramPtr CreateProgram(glshader::GPUProgramID id);

private:
    std::map<int32_t, GPUProgramPtr> programs_;
};

} //namespace 

#endif // MPA_SRC_RENDER_BACKEND_GL_GPUSHADERCACHE_H_
/* EOF */