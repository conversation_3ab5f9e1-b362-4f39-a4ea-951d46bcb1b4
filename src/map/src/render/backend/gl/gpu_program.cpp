/**
 * Copyright @ 2025
 * All Rights Reserved.
 */


 #include "gpu_program.h"

 namespace aurora {
 
    GPUProgram::GPUProgram()
    : attributes_()
    , uniforms_()
    {
        
    }  

    GPUProgram::~GPUProgram()
    {
        Release();
    }
 
    bool GPUProgram::CreateProgram(const char* vshader, const char* fshader) {
        program_ = glCreateProgram();
        if (program_ == 0) {
            printf("glCreateProgram failed\n");  // 新增程序创建失败日志
            return false;
        }
    
        vert_id_ = CompileShader(GL_VERTEX_SHADER, vshader);
        frag_id_ = CompileShader(GL_FRAGMENT_SHADER, fshader);
    
        if (vert_id_ == 0 || frag_id_ == 0) {
            printf("Program creation aborted: vertex/fragment shader compilation failed\n");  // 新增依赖检查日志
            Release();
            return false;
        }
    
        glAttachShader(program_, vert_id_);
        glAttachShader(program_, frag_id_);
        glLinkProgram(program_);
    
        GLint success;
        glGetProgramiv(program_, GL_LINK_STATUS, &success);
        if (!success) {
            char info_log[512];
            glGetProgramInfoLog(program_, 512, NULL, info_log);
            printf("Program linking failed (ID=%d): %s\n", program_, info_log);  // 新增程序ID信息
            glDeleteShader(vert_id_);
            glDeleteShader(frag_id_);
            return false;
        }
    
        printf("Program created successfully (ID=%d)\n", program_);  // 新增成功日志
        return true;
    }

    GLuint GPUProgram::CompileShader(GLenum type, const char* shader) {
        const char* type_str = (type == GL_VERTEX_SHADER) ? "Vertex" : "Fragment";
        
        GLuint shaderid = glCreateShader(type);
        if (shaderid == 0) {
            printf("[%s Shader] glCreateShader failed\n", type_str);  // 新增类型信息
            return 0;
        }
    
        glShaderSource(shaderid, 1, &shader, NULL);
        glCompileShader(shaderid);
    
        GLint success;
        glGetShaderiv(shaderid, GL_COMPILE_STATUS, &success);
        if (!success) {
            char info_log[512];
            glGetShaderInfoLog(shaderid, 512, NULL, info_log);
            printf("[%s Shader] Compilation failed (ID=%d): %s\n", type_str, shaderid, info_log);  // 新增ID信息
            glDeleteShader(shaderid);
            return 0;
        }
    
        return shaderid;
    }

    GLuint GPUProgram::GetAttributeLocation(const char* atrribute) const
    {
        return glGetAttribLocation(program_, atrribute);
    }

    GLuint GPUProgram::GetUniformLocation(const char* uniform) const
    {
        return glGetUniformLocation(program_, uniform);
    }

    bool GPUProgram::UseProgram()
    {
        if (0 != program_) {
            glUseProgram(program_);
            return true;
        }

        return false;
    }

    bool GPUProgram::IsValid()
    {
        return (program_ != 0);
    }

    void GPUProgram::Release()
    {
        if (0 != program_) {
            glDeleteShader(vert_id_);
            glDeleteShader(frag_id_);
            glDetachShader(program_, vert_id_);
            glDetachShader(program_, frag_id_);
            glDeleteProgram(program_);
            vert_id_ = 0;
            frag_id_ = 0;
            program_ = 0;
    
            attributes_.clear();
            uniforms_.clear();
        }
    }
} 