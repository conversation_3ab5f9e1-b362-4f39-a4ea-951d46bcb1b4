/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file render_backend_gl.h
 * @brief Declaration file of class RenderBackendGL.
 * @attention used for C/C++ only.
 */

 #ifndef RENDERBACKEND_MAPRENDERBACKEND_GL_H_
 #define RENDERBACKEND_MAPRENDERBACKEND_GL_H_
 
 #include <memory>
 #include "map_render_backend.h"
 #include "building_data.h"
 #include "gpu_program.h"
 
 namespace aurora {
 
 class GLContext;

 /**
 * class breif description
 *
 * RenderBackendGL
 *
 */
 class RenderBackendGL : public MapRenderBackend {
public:
     RenderBackendGL ();              
     virtual ~RenderBackendGL();   
 
     virtual bool Init(uint32_t w, uint32_t h, Color background);
 
     virtual void Destory();
 
     virtual void BuildProMatrix();
 
     virtual void DrawPolyline(const std::vector<Point>& points);
 
     virtual void DrawPolygon(const std::vector<Point>& points);
 
     virtual void DrawText(const std::string& str, const aurora::Point& pt, float degree, const TextStyle& style, TextHolderPtr& text_holder);

     virtual void DrawImage();
 
     virtual void SetLineStyle(LineCap cap, LineJoin join);
 
     virtual void SetFillStyle(FillStyle style);
 
     virtual void SetFontStyle(const FontStyle& style);

     virtual void SetColor(Color clr);

     virtual void SetAntiAlias(bool anti);
 
     virtual void SetLineWidth(float width);
     
     virtual void BeginDraw();

     virtual void EndDraw(TileID id, const AABB2<Point2d>& tile_mbr, CollisionTilePtr ptr, const std::vector<BuildingData>& buildings);

     virtual BackendAPIType GetBackendType() const { return BackendAPIType::API_GLES; }

     virtual MapRenderContext* GetContext() {return context_.get();}

     virtual void DrawTriangle(const std::vector<BuildingData>& buildings, const Matrix4<double>& proj_view_matrix );

     // 新增：批量绘制建筑数组
    void DrawBuildings(const std::vector<BuildingData>& buildings,
                       const Vector3<float>& light_dir,  // 全局光照方向
                       const Vector3<float>& ambient,   // 全局环境光
                       const Matrix4<double>& proj_view_matrix );  
 
protected:
     bool CreateContext(Color background);
     std::unique_ptr<MapRenderContext> context_;



    
                       
private:

    // 新增：3D 建筑着色器程序
    GPUProgramPtr building_shader_ = nullptr;    
    GPUProgramPtr  proj_2d_shader_ = nullptr;
     GLuint white_texture_id_ = 0;  // 新增：白色纹理ID
    int32_t width_ = 0;
    int32_t height_  = 0;
};

    
 } //namespace 
 
 #endif //RENDERBACKEND_MAPRENDERBACKEND_GL_H_
 /* EOF */
