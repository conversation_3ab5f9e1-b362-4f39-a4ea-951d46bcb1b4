/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file render_gl_texture2d.h
 * @brief Declaration file of class GLTexture2D.
 * @attention used for C/C++ only.
 */

 #ifndef RENDER_BACKEND_GL_TEXTURE2D_H_
 #define RENDER_BACKEND_GL_TEXTURE2D_H_
 
 #include "gl_texture.h"
 #include <GLES3/gl3.h>

 namespace aurora {
 
 /**
 * class breif description
 *
 * GLTexture2D
 *
 */
 class GLTexture2D : public GLTexture {
 public:
     GLTexture2D();        
     virtual ~GLTexture2D();
 
     virtual bool Set(const void* pixeldata, TextureFormat format, uint32_t w, uint32_t h) noexcept;
 
     virtual bool SetMipmap(const void* pixeldata, TextureFormat format, uint32_t w, uint32_t  h, uint32_t level) noexcept;
 
     virtual bool Upload(const void* pixeldata, TextureFormat format, uint32_t w, uint32_t h) noexcept;
 
     virtual void GenerateMipmap() noexcept;
 
     virtual void Release() noexcept;
 
     virtual bool IsValid() const noexcept;
 
     virtual bool Bind() const noexcept;
 
     virtual TextureFormat GetTextureFormat() const noexcept { return format_; }
 
     virtual uint32_t GetSize() const noexcept { return size_; }
 
     virtual uint32_t GetWidth() const noexcept { return width_; }
 
     virtual uint32_t GetHeight() const noexcept { return height_; }
 
     virtual void SetTextureParameter(TextureFilterType minfilter,
                         TextureFilterType magfilter,
                         TextureWrapType swrap,
                         TextureWrapType twrap) noexcept;

     virtual GLuint GetTexID() {
        return tex_id_;
     }
 
 private:
         uint32_t size_{0};    // texture size
         uint32_t width_{0};   // texture width
         uint32_t height_{0};  // texture height
         GLuint   tex_id_{0};      // texture handle ID
         TextureFormat format_{TextureFormat::FORMAT_RGBA}; // texture format
         TextureFilterType min_filter_type_{TextureFilterType::FILTER_LINEAR}; // min filter
         TextureFilterType mag_filter_type_{TextureFilterType::FILTER_LINEAR}; // mag filter
         TextureWrapType s_wrap_{TextureWrapType::WRAP_CLAMP};            // s wrap
         TextureWrapType t_wrap_{TextureWrapType::WRAP_CLAMP};            // t wrap
 };
     
 } //namespace 
 
 #endif //RENDER_BACKEND_GL_TEXTURE2D_H_
 /* EOF */