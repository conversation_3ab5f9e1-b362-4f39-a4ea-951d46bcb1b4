/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "map_render_backend_2d.h"
#include "map_render_canvas.h"
#include "map_render_paint.h"
#include "map_render_proj.h"
#include "concurrentqueue.h"
#include "include/core/SkCanvas.h"
#include "map_render_scene.h"
#include "map_render_skfontmanager.h"
#include "map_render_skfontface.h"
#include "building_data.h"

namespace aurora {

    RenderBackend2D::RenderBackend2D()
    {
    }

    RenderBackend2D::~RenderBackend2D()
    {
        Destory();
    }

    void RenderBackend2D::Clear(Color background) {
        render_canvas_->Clear(background);
    }

    void RenderBackend2D::DrawMark(ImageHolderPtr img, const AABB2<Point2d>& src, const AABB2<Point2d>& dst, float degree) {
        render_canvas_->DrawMark(img, src, dst, degree);
    }

    void RenderBackend2D::DrawMark(ImageHolderPtr img, Point2 anchor, double x, double y, float degree) {
        render_canvas_->DrawMark(img, anchor, x, y, degree);
    }

    bool RenderBackend2D::Init(uint32_t w, uint32_t h, Color background)
    {   
        // create Skia Surface
        SkImageInfo info = SkImageInfo::MakeN32Premul(w, h);
        surface_ = SkSurfaces::Raster(info);

        if (!surface_) {
            printf("RenderBackend2D::Init failed, create surface failed, w:%d, h:%d\n", w, h);
            return false;
        }

        CreateCanvas();
        BuildPaint();

        render_canvas_->Clear(background); // #FFEAF4F6
        // render_paint_->SetAntiAlias(false);

        return true;
    }

    void RenderBackend2D::Destory()
    {
        if (render_canvas_ != nullptr)
        {
            delete render_canvas_;
            render_canvas_ = nullptr;
        }

        if (render_paint_ != nullptr)
        {
            delete render_paint_;
            render_paint_ = nullptr;
        }

        if (render_proj_ != nullptr)
        {
            delete render_proj_;
            render_proj_ = nullptr;
        }
        if (surface_) {
            // surface_->reset();
        }
        // if (font_manager_ != nullptr)
        // {
        //     delete font_manager_;
        //     font_manager_ = nullptr;
        // }
    }

    void RenderBackend2D::CreateCanvas()
    {
        render_canvas_ = new MapRenderCanvas();

        if (surface_ != nullptr)
        {
            render_canvas_->Init(surface_->getCanvas());
        }
    }

    void RenderBackend2D::BuildProMatrix()
    {
        render_proj_ = new MapRenderProj();
    }

    void RenderBackend2D::BuildPaint()
    {
        render_paint_ = new MapRenderPaint();
    }

    void RenderBackend2D::DrawPolyline(const std::vector<Point>& points)
    {
        if (render_canvas_ != nullptr)
        {
            render_canvas_->DrawPolyline(points, render_paint_);
        }
    }

    void RenderBackend2D::DrawPolygon(const std::vector<Point>& points)
    {
        if (render_canvas_ != nullptr)
        {
            render_canvas_->DrawPolygon(points, render_paint_);
        }
    }

    void RenderBackend2D::DrawText(const std::string& str, const aurora::Point& pt, float degree, const TextStyle& style, TextHolderPtr& text_holder)
    { 
        render_canvas_->DrawText(str, pt, degree, style, text_holder);
    }

    void RenderBackend2D::BeginDraw()
    {
    }

    void RenderBackend2D::EndDraw(TileID id, const AABB2<Point2d>& tile_mbr, CollisionTilePtr ptr, const std::vector<BuildingData>& building_list)
    {  
        TileImagePtr tile = std::make_shared<TileImage>();
        tile->tile_id_.x_ = id.x_;
        tile->tile_id_.y_ = id.y_;
        tile->tile_id_.z_ = id.z_;
        tile->tile_bbox_ = tile_mbr;
        tile->image_ = surface_->makeImageSnapshot();
        if (tile->image_) {
            tile->image_->peekPixels(&tile->pixmap_);
        }
        tile->collision_ = ptr;
        tile->building_list_ = building_list;
        MapRenderScene::AddRasterTileToQueue(tile);
    }

    void RenderBackend2D::DrawImage()
    {
        render_canvas_->DrawImage();
    }

    void RenderBackend2D::SetLineStyle(LineCap cap, LineJoin join)
    {
        if (render_paint_ != nullptr)
        {
            render_paint_->SetLineStyle(cap, join);
        }
    }

    void RenderBackend2D::SetFillStyle(FillStyle style)
    {
        if (render_paint_ != nullptr)
        {
            render_paint_->SetFillStyle(style);
        }
    }

    void RenderBackend2D::SetFontStyle(const FontStyle& style)
    {
        if (render_paint_ != nullptr)
        {
            render_paint_->SetFontStyle(style);
        }
    }

    void RenderBackend2D::SetColor(Color clr)
    {
        if (render_paint_ != nullptr)
        {
            render_paint_->SetColor(clr);
        }
    }  
    
    void RenderBackend2D::SetLineWidth(float width)
    {
        if (render_paint_ != nullptr)
        {
            render_paint_->SetLineWidth(width);
        }
    }

    void RenderBackend2D::SetAntiAlias(bool anti)
    {
        if (render_paint_ != nullptr)
        {   
            render_paint_->SetAntiAlias(anti);
        }
    }

    void RenderBackend2D::DrawBuildings(const std::vector<BuildingData>& buildings,
                                       const Vector3<float>& light_dir,  // 全局光照方向
                                       const Vector3<float>& ambient,   // 全局环境光
                                       const Matrix4<double>& proj_view_matrix )
    {
        
    }

    
} //namespace 
