/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file map_render_backend_2d.h
 * @brief Declaration file of class RenderBackend2D.
 * @attention used for C/C++ only.
 */

#ifndef MAPRENDERBACKEND_RENDERBACKE_2D_H_
#define MAPRENDERBACKEND_RENDERBACKE_2D_H_

#include "include/core/SkSurface.h"
#include "map_render_backend.h"
#include "mark/mark_def.h"

namespace aurora {

class MapRenderCanvas;
class MapRenderProj;
class SKFontManager;

/**
* class breif description
*
* RenderBackend2D
*
*/
class RenderBackend2D : public  MapRenderBackend
{
public:
    RenderBackend2D();              
    virtual ~RenderBackend2D();  

    virtual bool Init(uint32_t w, uint32_t h, Color background);

    virtual void Destory();

    virtual void BuildProMatrix();

    virtual void DrawPolyline(const std::vector<Point>& points);

    virtual void DrawPolygon(const std::vector<Point>& points);

    virtual void DrawText(const std::string& str, const aurora::Point& left_bottom, float degree, const TextStyle& style, TextHolderPtr& text_holder);

    virtual void DrawImage();

    virtual void SetLineStyle(LineCap cap, LineJoin join);

    virtual void SetFillStyle(FillStyle style);

    virtual void SetFontStyle(const FontStyle& style);

    virtual void SetColor(Color clr);

    virtual void BeginDraw();

    virtual void EndDraw(TileID id, const AABB2<Point2d>& tile_mbr, CollisionTilePtr ptr, const std::vector<BuildingData>& building_list);

    virtual void SetAntiAlias(bool anti);

    virtual void SetLineWidth(float width);

    virtual MapRenderContext* GetContext() {return nullptr;}

    virtual BackendAPIType GetBackendType() const { return BackendAPIType::API_SKIA; }

    void Clear(Color background);

    void DrawMark(ImageHolderPtr img, const AABB2<Point2d>& src, const AABB2<Point2d>& dst, float degree = 0);

    void DrawMark(ImageHolderPtr img, Point2 anchor, double x, double y, float degree = 0);

    sk_sp<SkImage> MakeImageSnapshot() {
        if (surface_) {
            return surface_->makeImageSnapshot();
        }
        return nullptr;
    }

    void DrawBuildings(const std::vector<BuildingData>& buildings,
                       const Vector3<float>& light_dir,  // 全局光照方向
                       const Vector3<float>& ambient,   // 全局环境光
                       const Matrix4<double>& proj_view_matrix );
    
// private:
//     virtual bool CreateContext(Color background) {return false; }

private:
    void CreateCanvas();
    void BuildPaint();

private:
    sk_sp<SkSurface>    surface_;
    MapRenderPaint*     render_paint_{nullptr};
    MapRenderCanvas*    render_canvas_{nullptr};
    MapRenderProj*      render_proj_{nullptr};
    // SKFontManager*      font_manager_{nullptr};
};
    
} //namespace 

#endif //MAPRENDERBACKEND_RENDERBACKE_2D_H_
/* EOF */
