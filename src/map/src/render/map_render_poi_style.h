/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file map_render_poi_style.h
 * @brief Declaration file of class TextStyle.
 * @attention used for C/C++ only.
 */

#ifndef MAP_SRC_RENDER_POI_STYLE_H_
#define MAP_SRC_RENDER_POI_STYLE_H_

#include <string>
#include "util_color.h"
#include "util_basic_type.h"

namespace aurora {

 /**
 *  Define poi name render sytle and icon render style
 */

/**
* TextStyle for render text
*/
struct TextStyle {
public:
    TextStyle(int32_t font_size, int32_t outline, Color fore, Color back, const std::string& font_name) 
    : font_size_(font_size)
    , outline_(outline)
    , fore_color_(fore)
    , back_color_(back)
    , font_name_(font_name)
    {}

    TextStyle() {}

public:
    int32_t font_size_{0};
    int32_t outline_{0};
    Color fore_color_{0};
    Color back_color_{0};
    std::string font_name_{};
    AnchorType type_{kAnchorLeftBottom};
};


    
} //namespace 

#endif // MAP_SRC_RENDER_TEXT_STYLE_H_
/* EOF */
