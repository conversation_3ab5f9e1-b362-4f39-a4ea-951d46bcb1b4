/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "map_render_camera.h"
#include <shared_mutex> // 引入读写锁头文件
#include "util.h"
#include "scale_converter.h"
#include <mutex>
#include <math.h>
#include <thread>  // 新增线程ID支持


namespace aurora {

    
   
    const double CAMERA_DEFAULT_HEIGHT = 50.0; // 相机默认高度，改为double
    const double SCREEN_DEFAULT_DPI = 96.0; // 默认dpi, 取个中间值，最好外部设置实际dpi值，改为double
    const double CM_PER_INCH = 2.54;  // cm/英寸
    const double DEFAULT_SCALE_IN_METER = 100.0;  // 默认的比例尺，单位：米/cm
    const uint16_t kDefaultTilePixelSize = 512; // 一个数据块描画的默认像素大小512*512
    

    void printMatrix(const Matrix4<double>& matrix) {  // 改为double
        for (int i = 0; i < 4; ++i) {
            for (int j = 0; j < 4; ++j) {
                printf("%f ", matrix.m[i][j]);
            }
            printf("\n");
        }
        printf("=========\n");
    }

    
// 计算网格的经纬度跨度
void calcGridLonLatSpan_zFix(int z, double& lonSpan, double& latSpan) {
    // 计算经度跨度
    lonSpan = 360.0 / std::pow(2.0, z);

    // 计算每个网格在墨卡托 y 坐标上的跨度
    double mercatorYSpan = 2 * M_PI / std::pow(2.0, z);

    // 以第一个网格为例，计算其 y 坐标范围
    double yMin = -M_PI;
    double yMax = yMin + mercatorYSpan;

    // 将墨卡托 y 坐标转换为纬度
    auto mercatorYToLat = [](double y) {
        return (180.0 / M_PI) * (2 * std::atan(std::exp(y)) - M_PI / 2);
    };

    double latMin = mercatorYToLat(yMin);
    double latMax = mercatorYToLat(yMax);

    // 计算纬度跨度
    latSpan = latMax - latMin;
    // printf("z:%d, lonSpan:%f, latSpan:%f\n", z, lonSpan, latSpan);
}

void calcGridLonLatSpan(double z, double& lonSpan, double& latSpan) {
    // 分解浮点缩放级别为整数基础层级和小数插值系数
    int base_z = static_cast<int>(std::floor(z));  // 基础层级（向下取整，如 z=14.3 则 base_z=14）
    double t = z - base_z;                         // 插值系数（0~1 之间，如 z=14.3 则 t=0.3）
    
    // 递归计算基础层级和下一层级的经纬度跨度
    double base_lon, base_lat, next_lon, next_lat;
    calcGridLonLatSpan_zFix(base_z, base_lon, base_lat);       // 计算基础层级（整数）的经纬度跨度
    calcGridLonLatSpan_zFix(base_z + 1, next_lon, next_lat);   // 计算下一层级（base_z+1）的经纬度跨度
    
    // 线性插值：根据基础层级和下一层级的跨度，计算当前浮点 z 对应的经纬度跨度
    lonSpan = base_lon * (1 - t) + next_lon * t;  // 经度跨度插值（基础层级占比 1-t，下一层级占比 t）
    latSpan = base_lat * (1 - t) + next_lat * t;  // 纬度跨度插值（基础层级占比 1-t，下一层级占比 t）
}

    MapRenderCamera::MapRenderCamera()
    : max_zoom_level_(TILE_LOGIC_LEVEL_17)
    , min_zoom_level_(TILE_LOGIC_LEVEL_3)
    , map_scale_(TILE_LOGIC_LEVEL_15)
    {
        // 初始化监控列表：scale 操作关联 model、view、proj 的 dirty 标志
        dirty_watch_list_["scale"] = {
            &modelMatrixDirty_,
            &viewMatrixDirty_,
            &projMatrixDirty_,
            &cameraHeightDirty_
        };
        dirty_watch_list_["center"] = {
            &modelMatrixDirty_
        };
        dirty_watch_list_["screen"] = {
            &modelMatrixDirty_,
            &viewMatrixDirty_,
            &projMatrixDirty_,
            &cameraHeightDirty_
        };
        dirty_watch_list_["rotate"] = {
            &modelMatrixDirty_,
            &viewMatrixDirty_
        };
        dirty_watch_list_["pitch"] = {
            &modelMatrixDirty_,
            &viewMatrixDirty_,
            &projMatrixDirty_,
            &cameraHeightDirty_
        };
        // TODO: dpi 变化后，需要重新计算的类型
        dirty_watch_list_["dpi"] = {
            // &modelMatrixDirty_,
            // &vewMatrixDirty_,
            // &projMatrixDirty_i
        };
        
    }

    // 新增：根据操作类型触发所有关联的 dirty 标志
    void MapRenderCamera::MarkDirtyByKey(const std::string& key) {
        if (dirty_watch_list_.find(key) != dirty_watch_list_.end()) {
            for (auto flag_ptr : dirty_watch_list_[key]) {
                flag_ptr->store(true);
            }
        }

    }



 

    MapRenderCamera::~MapRenderCamera()
    {

    }
    
    void MapRenderCamera::SetViewport(int left, int top, int w, int h)
    {
        std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 独占锁，同一时间只允许一个写操作
        if (view_left_ == left && view_top_ == top && view_width_ == w && view_height_ == h) {
            return;
        }
        view_left_ = left;
        view_top_ = top;
        view_width_ = w;
        view_height_ = h;
        if (screen_height_ <= 0 ) {
            screen_height_ = view_height_;
            screen_width_ = view_width_;
        }
        // 通过监控列表触发所有关联的 dirty 标志（替代手动设置）
        MarkDirtyByKey("screen");
    }

    bool MapRenderCamera::GetProjViewMatrix(Matrix4<double>& projViewMatrix, const TimePoint& now) {  // 改为double
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_);
        if (view_width_ <= 0 || view_height_ <= 0)
        {
            return false; 
        }
        if (projMatrixDirty_.load() || viewMatrixDirty_.load() || modelMatrixDirty_.load() || animationDirty_.load()) 
        {
            // 更新投影矩阵逻辑
            UpdateMatrixes(now);
        }
       
        projViewMatrix =  m_viewProj_; 
        
        return true;
    }

    bool MapRenderCamera::GetOrthViewMatrix(Matrix4<double>& orthViewMatrix, const TimePoint& now)
    {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_);
        // 如果camera的参数异常，返回false
        if (view_width_ <= 0 || view_height_ <= 0) 
        {
            return false; 
        }
        if (orthMatrixDirty_.load() || viewMatrixDirty_.load() || modelMatrixDirty_.load() || animationDirty_.load()) 
        {
            // 更新正投影矩阵逻辑
            // printf("****************** orthMatrixDirty_.load() || viewMatrixDirty_.load() || modelMatrixDirty_.load() ******************\n");
             UpdateMatrixes(now);
        }
        
 
        orthViewMatrix = m_viewProjorth_;

        // printMatrix(orthViewMatrix);
        
        return true;
    }

    bool MapRenderCamera::CalcRenderTiles(std::vector<TileID>& tiles)
    {
        return true;
    }

    float MapRenderCamera::GetAccurateScale(const TimePoint& now) 
    {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 共享锁，允许多个读操作同时进行
        if (animationDirty_.load()) {
            double cur_scale;
            if (animation_options_.GetAnimateScale(cur_scale, now)) {
                // cur_scale 简单点用4舍5入的方式
                cur_scale = std::clamp(cur_scale, (double)min_zoom_level_, (double)max_zoom_level_);
                return cur_scale;
            }
        }
        return map_scale_;
    }
        
    float MapRenderCamera::GetMapScale(const TimePoint& now) 
    
    {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 共享锁，允许多个读操作同时进行
        if (animationDirty_.load()) {
            double cur_scale;
            if (animation_options_.GetAnimateScale(cur_scale, now)) {
                // cur_scale 简单点用4舍5入的方式
                // if (cur_scale > 0) {
                //     cur_scale = std::round(cur_scale);
                // }
                cur_scale = std::clamp(cur_scale, (double)min_zoom_level_, (double)max_zoom_level_);
                return cur_scale;
            }
        }
        return map_scale_;
    }

     Lonlat MapRenderCamera::GetMapCenter(const TimePoint& now) {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 共享锁，允许多个读操作同时进行
        if (animationDirty_.load() && 
            (animation_options_.type == AnimationType::kAnimationTypeMoveTo ||
                animation_options_.type == AnimationType::kAnimationTypeFlyTo)
        ) {
            PointXY<double> cur_pos;
            if (animation_options_.GetAnimatePos(cur_pos, now)) {
                return Lonlat(cur_pos.x(), cur_pos.y());
            }
        }
        return map_center_;
     }

     bool MapRenderCamera::GetMapCenter(Lonlat& mapCenter, const TimePoint& now) {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 共享锁，允许多个读操作同时进行
        if (animationDirty_.load()) {
            PointXY<double> cur_pos;
            if (animation_options_.GetAnimatePos(cur_pos, now)) {
                mapCenter = Lonlat(cur_pos.x(), cur_pos.y());
                return true;
            }
        }
        mapCenter = map_center_;
        return true;
     }

    // Change parameter from const reference to value to enable move semantics
    void MapRenderCamera::SetMapScale(float scale, const AnimationOptions& options) 
    {
        CancelAnimation();
        std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_);
        if (max_zoom_level_ < scale ) {
            scale = max_zoom_level_;
        }
        // TODO: level 1
        if (min_zoom_level_ > scale) {
            scale = min_zoom_level_;
        }
        map_scale_ = scale;
        data_scale_ = GetDataLevel(std::floor(map_scale_));
       
        // logic_data_scale = GetLogicDataScale(map_scale_, data_scale_);
    
    
        // 更新tile经度跨度范围
        double latSpan = 0.0;
        calcGridLonLatSpan(map_scale_ , lonSpan_, latSpan);
        printf("SetMapScale map_scale_:%f, data_scale_:%d lonSpan_:%lf\n", map_scale_, data_scale_, lonSpan_);
        // 触发动画相关
        if (options.type == AnimationType::kAnimationTypeZoomTo) {
            animationDirty_.store(true);
            animation_options_ = options;
        }
    
        // 标记dirty
        MarkDirtyByKey("scale");
    
    }

    void MapRenderCamera::UpdateCameraHeight(const TimePoint& now)
    {
        if (cameraHeightDirty_.load() || animationDirty_.load()) {
            // printf("****************** cameraHeightDirty_.load() ******************\n");

            auto cur_scale = GetMapScale(now);
            worldTileSize_ = 2 * kPiD * kEarthRadiusMeters * exp2((double)0-cur_scale);  // 地图周长/层级对应的份数

            // viewport height in world space is such that each tile is [m_pixelsPerTile] px square in screen space
            double screenTileSize = tile_size_pix_ * m_pixelScale_;
            m_height = (float)screen_height_ * worldTileSize_ / screenTileSize;
            m_width = m_height * m_aspect_;
             double fovy = m_fov_;

            // we assume portrait orientation by default, so in landscape
            // mode we scale the vertical FOV such that the wider dimension
            // gets the intended FOV
            if (m_width > m_height) {
                fovy /= m_aspect_;
            }

            // 新增：根据俯仰角调整相机高度（示例逻辑）
            float pitch_rad = GetMapPitchAngle(now) * M_PI / 180.0;
            // 俯仰角越大（向下倾斜），相机高度越低（保持地面区域可见）
            double pitch_factor = cos(pitch_rad); // 俯仰角为0时，factor=1；俯仰角90度时，factor=0（极端情况）
            camera_heigth_ = (m_height * 0.5 / tan(fovy * 0.5)) * pitch_factor;
            cameraHeightDirty_.store(false);
        }
    }

    void MapRenderCamera::SetMapCenter(double lon, double lat, const AnimationOptions& options) 
    {
        CancelAnimation();
        std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_);
        if (map_center_.longitude_ == lon && map_center_.latitude_ == lat) {
            printf("SetMapCenter same lon:%f lat:%f\n", lon, lat);
            if (options.type == AnimationType::kAnimationTypeMoveTo) {
                if (options.transitionFinishFn) {
                    options.transitionFinishFn();
                }
                animationDirty_.store(false);
            }
            return;
        }
        

        // 触发动画相关
        if (options.type == AnimationType::kAnimationTypeMoveTo) {
            printf("SetMapCenter  kAnimationTypeMoveTo before lon:%f lat:%f\n", map_center_.longitude_, map_center_.latitude_);
            animationDirty_.store(true);
            animation_options_ = options;
        }
        
        map_center_.longitude_ = lon;
        map_center_.latitude_ = lat; 
        MarkDirtyByKey("center");
    }

    void MapRenderCamera::FlyTo(double lon, double lat, unsigned int dest_scale, AnimationOptions& options)
    {
        
        if ((options.type & kAnimationTypeFlyTo) == kAnimationTypeFlyTo) {
            // 初始化FlyTo状态
            flyto_state_.target_center = {lon, lat};
            double target_scale = GetMapScale();
            // options.GetAnimateScale(target_scale, Clock::time_point::max());
            // printf("+++++++++FlyTo pahse 1 start_scale: %f, end_scale: %f\n", GetMapScale(), target_scale);
            // target_scale = std::round(target_scale);
            flyto_state_.target_scale = dest_scale;  
            uint32_t zoom_scale = min_zoom_level_;
            GetFlyToZoomOutScale(GetMapCenter(), {lon, lat}, dest_scale, zoom_scale);
            flyto_state_.max_zoom = std::max(zoom_scale, min_zoom_level_);         // 从类成员获取最大比例尺

            // 阶段1：上升到最小比例尺
            AnimationOptions zoom_out_options;
            zoom_out_options.type = kAnimationTypeZoomTo;
            zoom_out_options.duration = options.duration.value()/3;  // 可调整为总时长的1/3
            zoom_out_options.easing = options.easing;
            zoom_out_options.SetAnimateScale(GetMapScale(), flyto_state_.max_zoom);
            
            // 阶段1完成后触发移动阶段
            zoom_out_options.transitionFinishFn = [this, options]() {
                // 阶段2：移动到目标位置
                printf("+++++++++FlyTo pahse 2+++++++++++++++++\n");
                AnimationOptions move_options;
                move_options.type = kAnimationTypeMoveTo;
                move_options.duration = options.duration.value()/3;  // 可调整为总时长的1/3
                move_options.easing =  options.easing;
                auto start_pos = GetMapCenter(Clock::now());
                printf("+++++++++FlyTo pahse 2 start_pos: %f, %f target_center: %f, %f\n", 
                    start_pos.longitude_, start_pos.latitude_,
                    flyto_state_.target_center.x(), flyto_state_.target_center.y());
                move_options.SetAnimatePos(
                    {start_pos.longitude_, start_pos.latitude_},  // 当前中心点（缩小后的位置）
                    flyto_state_.target_center
                );

                // 阶段2完成后触发放大阶段
                move_options.transitionFinishFn = [this, options]() {
                     
                    // 阶段3：放大到目标比例尺
                    AnimationOptions zoom_in_options;
                    zoom_in_options.type = kAnimationTypeZoomTo;
                    zoom_in_options.duration = options.duration.value() /3;
                    zoom_in_options.easing = options.easing;
                    zoom_in_options.SetAnimateScale(
                        flyto_state_.max_zoom,
                        flyto_state_.target_scale
                    );
                    printf("+++++++++FlyTo pahse 3 start_scale: %f, end_scale: %f\n", 
                        flyto_state_.max_zoom, flyto_state_.target_scale);
                    SetMapScale(flyto_state_.target_scale, zoom_in_options);
                };

                SetMapCenter(flyto_state_.target_center.x(), flyto_state_.target_center.y(), move_options);
            };

            // 启动阶段1：放大比例尺
            SetMapScale(flyto_state_.max_zoom, zoom_out_options);
        }
        
    }   

    void MapRenderCamera::SetMapRotation(float rot, const AnimationOptions& options) 
    {
        CancelAnimation();
        std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_);
        if (roll_angle_ == rot) {
            return;
        }
        roll_angle_ = rot; 
        MarkDirtyByKey("rotate");

        // 触发动画相关
        if (options.type == AnimationType::kAnimationTypeRotateTo) {
            animationDirty_.store(true);
            animation_options_ = options;
        }
    }

    float MapRenderCamera::GetMapRotation(const TimePoint& now) 
    {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 共享锁，允许多个读操作同时进行
        if (animationDirty_.load()) {
            double cur_rot;
            if (animation_options_.GetAnimateRotate(cur_rot, now)) {
                return cur_rot;
            }
        }
        return roll_angle_;
    }

    void MapRenderCamera::SetYawAngle(float yaw_angle, const AnimationOptions& options) 
    {
        CancelAnimation();
        std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_);
        if (yaw_angle_ == yaw_angle) {
            return;
        }
        yaw_angle_ = yaw_angle; 
        MarkDirtyByKey("yaw");

        // 触发动画相关
        if (options.type == AnimationType::kAnimationTypeRotateTo) {
            animationDirty_.store(true);
            animation_options_ = options;
        }
    }

    void MapRenderCamera::SetPitchAngle(float pitch_angle, const AnimationOptions& options) 
    {
        CancelAnimation();
        std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_);
        if (pitch_angle_ == pitch_angle) {
            return;
        }
        pitch_angle_ = pitch_angle; 
        MarkDirtyByKey("pitch");

        // 触发动画相关
        if (options.type == AnimationType::kAnimationTypeRollTo) {
            animationDirty_.store(true);
            animation_options_ = options;
        }
    }

    void MapRenderCamera::SetRollAngle(float roll_angle, const AnimationOptions& options) 
    {
        CancelAnimation();
        std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_);
        if (roll_angle_ == roll_angle) {
            return;
        }
        roll_angle_ = roll_angle;
        MarkDirtyByKey("rotate");

        // 触发动画相关
        if (options.type == AnimationType::kAnimationTypeRotateTo) {
            animationDirty_.store(true);
            animation_options_ = options;
        }
    }

    void MapRenderCamera::SetPitchMode(const PitchAngleMode& pitch_mode, const AnimationOptions& options) 
    {
        CancelAnimation();
        std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_);
        if (pitch_mode_ == pitch_mode) {
            return;
        }
        pitch_mode_ = pitch_mode;  
        // TODO： 每个pitchmode设置对应的pitch角度
        MarkDirtyByKey("pitch");

        // 触发动画相关
        if (options.type == AnimationType::kAnimationTypeRollTo) {
            animationDirty_.store(true);
            animation_options_ = options;
        }
    }

    float MapRenderCamera::GetMapPitchAngle(const TimePoint& now) 
    {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 共享锁，允许多个读操作同时进行
        if (animationDirty_.load()) {
            double cur_pitch;
            if (animation_options_.GetAnimateRoll(cur_pitch, now)) {
                return cur_pitch;
            }
        }
        return pitch_angle_;
    }

    double MapRenderCamera::GetLonSpan(const TimePoint& now) 
    {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 共享锁，允许多个读操作同时进行
        if (animationDirty_.load()) {
            double cur_lonSpan;
            auto progress = animation_options_.GetProgress(now);
             double latSpan = 0.0;
             auto accurate_scale = GetAccurateScale(now);
            // auto cur_scale = std::round(accurate_scale);
            #ifdef DEBUG
            printf("=============GetLonSpan accurate_scale :%f cur_scale:%d \n", accurate_scale, (int)accurate_scale);
            #endif
            calcGridLonLatSpan(accurate_scale, cur_lonSpan, latSpan);
            // if (cur_scale > accurate_scale) { // 缩小, 13.8 的round是14,14->13 缩小2倍，tile经纬度间隔放大2倍
            //     cur_lonSpan = cur_lonSpan  * (1.0 + 0.5 * abs(cur_scale - accurate_scale));
            // } else if (cur_scale < accurate_scale) { // 放大，如14-》15级，比例尺放大2倍，14.3时round是14，tile间隔缩小2倍
            //     cur_lonSpan = cur_lonSpan  * (1.0 - 0.5 * abs(cur_scale - accurate_scale));
            // }
            return cur_lonSpan ;
        }
        return lonSpan_;
    }

    void MapRenderCamera::Update() 
    {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); 
        // 执行更新操作，重算matrix,可能多线程在操作，前面设置的dirty标志可能会被其他线程修改清除掉了，强制设置dirty标志
        // forceupdate
        UpdateMatrixes();
    }
   
   

    void MapRenderCamera::UpdateModelMatrix(const TimePoint& now)
    {
        // 计算模型矩阵逻辑， 地图中心点变化，比例尺变换，roll角度，pitch角度变化的时候需要更新
        if (modelMatrixDirty_.load() || animationDirty_.load() ) {
            // printf("****************** modelMatrixDirty_.load() ******************\n");
            m_model_ = Matrix4<double>::identity();
   
            // 正交投影用的-w/2,w/2,-h/2,h/2
            //  m_model_ *= Matrix4<double>::translation({-2.0, -2.0, 0.0f});
            auto center = GetMapCenter(now);
            
            auto rotateZ = Matrix4<double>::rotationZ(GetMapRotation(now) * M_PI / 180.0);
            auto rotateX = Matrix4<double>::rotationX(GetMapPitchAngle(now) * M_PI / 180.0);
            // auto rotateY = Matrix4<double>::rotationY(yaw_angle_ * M_PI / 180.0);
            m_model_ =   rotateX  * rotateZ *  m_model_;
            auto lonSpan = GetLonSpan(now);
           
            m_model_ =   Matrix4<double>::scaling({kDefaultTilePixelSize / (lonSpan * kDM5), kDefaultTilePixelSize /(lonSpan* kDM5), 1.0f}) * m_model_; // 先缩放
            m_model_ *= rotateX; // 再选择X
            m_model_ *= rotateZ; // 最后选择
            m_model_ *= Matrix4<double>::translation({-center.longitude_ * kDM5, -center.latitude_* kDM5, 0.0});
             #ifdef DEBUG
            printf("=============UpdateModelMatrix lonSpan:%f\n", lonSpan);
            #endif
             
            modelMatrixDirty_.store(false);
        }

    }

    void MapRenderCamera::UpdateViewMatrix(const TimePoint& now) {
        if (view_width_ <= 0 || view_height_ <= 0) {
            return; 
        }
            
        if (viewMatrixDirty_.load() || animationDirty_.load()) {
            // printf("****************** viewMatrixDirty_.load() ******************\n");
            // 计算视口矩阵逻辑
             double fovy = m_fov_;

            // we assume portrait orientation by default, so in landscape
            // mode we scale the vertical FOV such that the wider dimension
            // gets the intended FOV
            if (m_width > m_height) {
                fovy /= m_aspect_;
            }
            auto lonSpan = GetLonSpan(now);
            // set camera z to produce desired viewable area
            camera_heigth_ = m_height * 0.5 / tan(fovy * 0.5);
            Matrix4<double> eyeMatrix = Matrix4<double>::translation({0,  0, camera_heigth_});
            auto rotateZ = Matrix4<double>::rotationZ(GetMapRotation(now) * M_PI / 180.0);
            auto rotateX = Matrix4<double>::rotationX(GetMapPitchAngle(now) * M_PI / 180.0);
            eyeMatrix = Matrix4<double>::scaling({kDefaultTilePixelSize / (lonSpan * kDM5), kDefaultTilePixelSize /(lonSpan* kDM5), 1.0f}) * eyeMatrix;
            eyeMatrix = rotateX * rotateZ * eyeMatrix;

            // eyeMatrix = Matrix4<double>::translation({view_width_/2.0, view_height_/2.0, 0.0f}) * eyeMatrix;

            // printMatrix(eyeMatrix);
            Vector3<double> eye(eyeMatrix.m[0][3], eyeMatrix.m[1][3], eyeMatrix.m[2][3]);
            Vector3<double> at = { eyeMatrix.m[0][3], eyeMatrix.m[1][3], 0.f };
            // 仅应用俯仰旋转（绕 X 轴）到 up 向量
            Vector3<double> up(0.0, 1.0, 0.0);  // 初始为世界 Y 轴方向
            Vector4<double> rotated_up = rotateX * Vector4<double>(up.x, up.y, up.z, 0.0);  // 仅绕 X 轴旋转（俯仰）
            up = Vector3<double>(rotated_up.x, rotated_up.y, rotated_up.z);  // 更新 up 方向
            // 生成视图矩阵（无需绕 Z 轴旋转 up）
            m_view_ = Matrix4<double>::lookAtRH(eye, at, up);
            viewMatrixDirty_.store(false);
        }
    }

    void MapRenderCamera::UpdateProjMatrix(const TimePoint& now) {
        if (projMatrixDirty_.load() || animationDirty_.load()) {
            // printf("****************** projMatrixDirty_.load() ******************\n");
            // 计算投影矩阵逻辑
            double maxTileDistance = worldTileSize_ * (exp2(7) - 1.0);
            double near = camera_heigth_ / 50.f;
            double far = 1;
            double hw = 0.5 * m_width;
            double hh = 0.5 * m_height;
            double fovy = m_fov_;
            auto cur_tick = Clock::now();

            // we assume portrait orientation by default, so in landscape
            // mode we scale the vertical FOV such that the wider dimension
            // gets the intended FOV
            if (m_width > m_height) {
                fovy /= m_aspect_;
            }
            // Vector2<float> viewportSize = { screen_width_, screen_height_ };
            // Vector2<float> paddingOffset = { m_padding.right - m_padding.left, m_padding.top - m_padding.bottom };
            // Vector2<float> centerOffset = paddingOffset / viewportSize;

            // Generate projection matrix based on camera type
            {
                far = 2. * camera_heigth_/ std::max(0., cos(GetMapPitchAngle(now)* M_PI / 180.0 + 0.5 * fovy));
                far = std::min(far, maxTileDistance);
                m_proj_ =  Matrix4<double>::perspectiveRH(fovy, m_aspect_, near, far);
                // Adjust projection center for edge padding.
                // m_proj_[2][0] = centerOffset.x;
                // m_proj_[2][1] = centerOffset.y;
                // // Adjust for vanishing point.
                // m_proj_[2][0] -= m_vanishingPoint.x / getWidth();
                // m_proj_[2][1] -= m_vanishingPoint.y / getHeight();
            }
                
                    
            {
                    far = 2. * ( camera_heigth_ + hh * std::abs(tan(GetMapPitchAngle(now)* M_PI / 180.0)));
                    far = std::min(far, maxTileDistance);
                    // m_proj_orth_ = Matrix4<double>::orthoRH(-hw, hw, -hh, hh, near, far);
                    m_proj_orth_ = Matrix4<double>::orthoRH(-view_width_/2.0, view_width_/2.0, -view_height_/2.0, view_height_/2.0, near, far);
                    #ifdef DEBUG
                    printf("=========m_proj_orth_====== near:%f, far:%f, camera_heigth_:%f\n", near, far, camera_heigth_);
                    #endif
                    // m_proj_orth_ = Matrix4<double>::orthoRH(0, view_width_, 0, view_height_, near, far);
                    // Adjust projection center for edge padding.
                    // m_proj[3][0] -= centerOffset.x;
                    // m_proj[3][1] -= centerOffset.y;
            }


            projMatrixDirty_.store(false);
        }
    }

    void MapRenderCamera::UpdateMatrixes(const TimePoint& now) {
        // find dimensions of tiles in world space at new zoom level
        // double worldTileSize = 2 * kPiD * kEarthRadiusMeters * exp2((double)0-map_scale_);  // 地图周长/层级对应的份数

        // // viewport height in world space is such that each tile is [m_pixelsPerTile] px square in screen space
        // double screenTileSize = tile_size_pix_ * m_pixelScale_;
        // m_height = (float)screen_height_ * worldTileSize / screenTileSize;
        // m_width = m_height * m_aspect_;
        bool need_update = modelMatrixDirty_.load() || viewMatrixDirty_.load() || projMatrixDirty_.load() || animationDirty_.load();
        if (animationDirty_.load()) {
            
            if (animation_options_.IsFinished(now)) {
                // 新增：动画完成时触发回调
                printf("+++++++++++++++animation_options_.IsFinished\n");
                if (animation_options_.transitionFinishFn) {
                    // 为了防止finish函数里用了读写锁，导致死锁，finish调用切个线程调用
                    auto finish_fun = animation_options_.transitionFinishFn;
                    std::thread([this, finish_fun]() {
                        finish_fun();
                    }).detach();
                    animation_options_.transitionFinishFn = nullptr; // 防止重复触发
                }
                animationDirty_.store(false);
            }
        }

        UpdateCameraHeight(now);

        UpdateModelMatrix(now);

        UpdateViewMatrix(now);

        UpdateProjMatrix(now);

        
        // if (m_type == CameraType::isometric) {
        //     glm::mat4 shear = m_view;

        //     // Add the oblique projection scaling factors to the shear matrix
        //     shear[2][0] += m_obliqueAxis.x;
        //     shear[2][1] += m_obliqueAxis.y;

        //     // Remove the view from the shear matrix so we don't apply it two times
        //     shear *= glm::inverse(m_view);

        //     // Inject the shear in the projection matrix
        //     m_proj *= shear;
        // }
        // printf("=========m_proj_======\n");
        // 针对无极缩放需要设置这个缩放系数
        // auto logic_data_scale = GetLogicDataScale(map_scale_, data_scale_);
        // auto level_scale = GetLogicDataScale(map_scale_, data_scale_);
        // Matrix4<double> scaleMatrix = Matrix4<double>::scaling(level_scale * kDefaultTilePixelSize/2, level_scale * kDefaultTilePixelSize/2, 1.0);
    //    printMatrix(m_proj_);
        if (need_update) {
            m_viewProj_ = m_proj_ * m_view_ * m_model_ ;
            m_viewProjorth_ =  m_proj_orth_* m_view_ * m_model_;
        }
        
       
        
        #ifdef DEBUG
        printf("=========m_viewProjorth_======\n");
         printMatrix(m_viewProjorth_);
        printf("=========m_model_======\n");
         printMatrix(m_model_);
          printf("=========m_view_======\n");
         printMatrix(m_view_);
         printf("=========m_proj_orth_======\n");
         printMatrix(m_proj_orth_);
         #endif

    }


    void MapRenderCamera::UpdateProjViewMatrix(const TimePoint& now) 
    {
        // 更新投影矩阵逻辑 
        // 将经纬度转换为墨卡托坐标
        // PointXY<double> centerMercator = LonLat2Mercator( map_center_.longitude_, map_center_.latitude_);
        
        // // 获取当前比例尺的scale值, 这里获取逻辑level对应的实际比例尺
        // double scale = Level2Scale(map_scale_);
        // // double scale = map_scale_; // map_scale_是已经转为m的比例尺数值，如果是逻辑比例尺，需要转换为实际比例尺
        // if (scale <= 0) {
        //     scale = DEFAULT_SCALE_IN_METER; // 防止scale为0导致计算错误
        // }

        // // 计算每个像素代表的实际长度（米）
        //  double meterPerPixel = (CM_PER_INCH / dpi_) * scale;
       
        // double real_scale = data_scale_in_pixel_/meterPerPixel;
        // printf("============meterPerPixel: %f=== datameterPerPixel：%f========\n", meterPerPixel, data_scale_in_pixel_);
        // printf("============real_scale: %f===========\n", real_scale);
        // printf("============scale: %f===========\n", scale);


        // // 计算屏幕在地图上的实际宽度和高度（米）
        // double actualWidthMeters = view_width_ * meterPerPixel;
        // // double actualHeightMeters = view_height_ * meterPerPixel;
        // double actualHeightMeters = 1376.0;

        // // 构建 MVP 矩阵
        // // 模型矩阵
        // // Matrix4<float> model  = Matrix4<float>::scaling( 1222.0/512 , 611.0/512  , 1);
        // //  Matrix4<float> model = Matrix4<float>::translation(centerMercator.x(), centerMercator.y(), 0);
        // Matrix4<float> model = Matrix4<float>::translation(centerMercator.x(), centerMercator.y(), 0);
        // // model *= Matrix4<float>::translation(1.35031e+07, 1.73543e+06, 0);
        // printf("centerMercator.x() - leftbottom=%f-%f\n", centerMercator.x()- 1.35031e+07, centerMercator.y()-1.73543e+06);
        // double width_mercator = 1222;
        // double hedith_mercator = 611;

        // // TODO: 这里scaling的系数需要根据比例尺计算，目前100m比例尺固定在1544，后面需要根据比例尺计算
        // // model *= Matrix4<float>::scaling( 1222.0/512 , 611.0/512  , 1);
        // model *= Matrix4<float>::rotationZ(roll_angle_ * M_PI / 180.0);
        // model *= Matrix4<float>::rotationX(pitch_angle_ * M_PI / 180.0);
        // model *= Matrix4<float>::rotationY(yaw_angle_ * M_PI / 180.0);
        // // printf("============roll_angle_: %f===========centerMercator.x():%f, centerMercator.y():%f actualHeightMeters：%f\n", 
        // // roll_angle_, centerMercator.x(), centerMercator.y(), actualHeightMeters);
        //  printMatrix(model);
        // // 视图矩阵
        //  Vector3<float> eye(centerMercator.x(), centerMercator.y(), camera_heigth_);
        // //  Vector3<float> center(centerMercator.x(), centerMercator.y(), 0);
        // Vector3<float> center(centerMercator.x(), centerMercator.y(), 0);
        //  Vector3<float> up(0, 1, 0);
        // Matrix4<float> view = Matrix4<float>::lookAtRH(eye, center, up); 
        
        //  printf("view----\n");
        // printMatrix(view);
        // printMatrix(model*view);
        // // 投影矩阵
        // double aspect = static_cast<double>(view_width_) / view_height_;
        // double fovY =  1.0 * 0.125f *  M_PI;
        // printf("============fovY: %f===========\n", fovY);
        // Matrix4<float> projection = Matrix4<float>::perspectiveRH(fovY * M_PI / 180.0, aspect, 0.1, 1.0);
        // // Matrix4<float> projection = Matrix4<double>::orthoRH(-1.0, 1.0, -1.0, 1.0, 0.1, 100.0);
        //  printf("projection----\n");
        // printMatrix(projection);
      

        // // MVP 矩阵
        // Matrix4<float> mvp = projection*(view*model);
        //  printMatrix(mvp);
        // // 将 MVP 矩阵赋值给 projViewMatrix_, TODO:是否需要Inverse  ？？
        // // Matrix4<float> inverseMvp = mvp.getInverse();
        // proj_view_matrix_ = mvp; 
    }

    void MapRenderCamera::UpdateOrthViewMatrix(const TimePoint& now) 
    {
        // 更新正交投影矩阵逻辑
        // 更新投影矩阵逻辑 
        // 将经纬度转换为墨卡托坐标
        // PointXY<double> centerMercator = LonLat2Mercator( map_center_.longitude_, map_center_.latitude_);
        
        // // 获取当前比例尺的scale值, 这里获取逻辑level对应的实际比例尺
        // double scale = Level2Scale(map_scale_);
        // // double scale = map_scale_; // map_scale_是已经转为m的比例尺数值，如果是逻辑比例尺，需要转换为实际比例尺
        // if (scale <= 0) {
        //     scale = DEFAULT_SCALE_IN_METER; // 防止scale为0导致计算错误
        // }

        // // 计算每个像素代表的实际长度（米）
        //  double meterPerPixel = (CM_PER_INCH / dpi_) * scale;
       
        // double real_scale = data_scale_in_pixel_/meterPerPixel;
        // printf("============meterPerPixel: %f=== datameterPerPixel：%f========\n", meterPerPixel, data_scale_in_pixel_);
        // printf("============real_scale: %f===========\n", real_scale);
        // printf("============scale: %f===========\n", scale);


        // // 计算屏幕在地图上的实际宽度和高度（米）
        // double actualWidthMeters = view_width_ * meterPerPixel;
        // // double actualHeightMeters = view_height_ * meterPerPixel;
        // double actualHeightMeters = 1376.0;

        // // 构建 MVP 矩阵
        // // 模型矩阵
        // // Matrix4<float> model  = Matrix4<float>::scaling( 1222.0/512 , 611.0/512  , 1);
        // //  Matrix4<float> model = Matrix4<float>::translation(centerMercator.x(), centerMercator.y(), 0);
        // Matrix4<float> model = Matrix4<float>::translation(centerMercator.x(), centerMercator.y(), 0);
        // // model *= Matrix4<float>::translation(1.35031e+07, 1.73543e+06, 0);
        // printf("centerMercator.x() - leftbottom=%f-%f\n", centerMercator.x()- 1.35031e+07, centerMercator.y()-1.73543e+06);
        // double width_mercator = 1222;
        // double hedith_mercator = 611;

        // // TODO: 这里scaling的系数需要根据比例尺计算，目前100m比例尺固定在1544，后面需要根据比例尺计算
        // // model *= Matrix4<float>::scaling( 1222.0/512 , 611.0/512  , 1);
        // model *= Matrix4<float>::rotationZ(roll_angle_ * M_PI / 180.0);
        // model *= Matrix4<float>::rotationX(pitch_angle_ * M_PI / 180.0);
        // model *= Matrix4<float>::rotationY(yaw_angle_ * M_PI / 180.0);
        // // printf("============roll_angle_: %f===========centerMercator.x():%f, centerMercator.y():%f actualHeightMeters：%f\n", 
        // // roll_angle_, centerMercator.x(), centerMercator.y(), actualHeightMeters);
        //  printMatrix(model);
        // // 视图矩阵
        //  Vector3<float> eye(centerMercator.x(), centerMercator.y(), camera_heigth_);
        // //  Vector3<float> center(centerMercator.x(), centerMercator.y(), 0);
        // Vector3<float> center(centerMercator.x(), centerMercator.y(), 0);
        //  Vector3<float> up(0, 1, 0);
        // Matrix4<float> view = Matrix4<float>::lookAtRH(eye, center, up); 
        
        //  printf("view----\n");
        // printMatrix(view);
        // printMatrix(model*view);
        // // 投影矩阵
        //  Matrix4<float> orhth = Matrix4<double>::orthoRH(-1.0, 1.0, -1.0, 1.0, 0.1, 100.0);
        //  printf("orhth----\n");
        // printMatrix(orhth);
      

        // // MVP 矩阵
        // Matrix4<float> mvp = orhth*(view*model);
        //  printMatrix(mvp);
        // // 将 MVP 矩阵赋值给 projViewMatrix_, TODO:是否需要Inverse  ？？
        // // Matrix4<float> inverseMvp = mvp.getInverse();
        // orth_view_matrix_ = mvp; 

    }


    void MapRenderCamera::SetScreenSize(uint32_t width, uint32_t height)
    {
        CancelAnimation();
        {
            std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 独占锁，同一时间只允许一个写操作
            if (view_width_ <= 0 || view_height_ <= 0 || view_width_ != width || view_height_ != height) {
                view_width_ = width;
                view_height_ = height;
            }
            screen_width_ = std::max(width, (uint32_t)1);
            screen_height_ = std::max(height, (uint32_t)1);
            
            m_aspect_ = float(width) / float(height);
            m_orthoViewport_ = Matrix4<double>::orthoRH(0.f, (double)screen_width_, (double)screen_height_, 0.f, -1.f, 1.f);
            MarkDirtyByKey("screen");
        }
       
        
        UpdateMatrixes();
    }

     bool MapRenderCamera::MercatorWorld2Screen(double world_x, double world_y, double& x, double& y)
     {
        auto world_ll = Mercator2LonLat(world_x, world_y);
        printf("wolrdll:%f, %f\n", world_ll.x(), world_ll.y());
        return World2Screen(world_ll.x(), world_ll.y(), x, y);
     }
    

    
    bool MapRenderCamera::Screen2World(double x, double y, double& world_x, double& world_y) 
    {
        // Matrix4<double> out = Matrix4<double>::translation({-map_center_.longitude_ * kDM5, -map_center_.latitude_* kDM5, 0.0f});
        // double lonSpan = 0.0, latSpan = 0.0;
        // calcGridLonLatSpan(map_scale_ , lonSpan, latSpan);

        // out = Matrix4<double>::scaling({512 / (lonSpan * kDM5), 512 /(lonSpan* kDM5), 1.0f}) * out;
        // out = Matrix4<double>::rotationZ(roll_angle_ * M_PI / 180.0) * out;

        // out = Matrix4<double>::translation({view_width_/2.0, view_height_/2.0, 0.0f}) * out;
        // printMatrix(out);
        if (modelMatrixDirty_.load() || animationDirty_.load()) {
            #ifdef DEBUG
            printf("****************Screen2World UpdateMatrixes*************\n");
            #endif
            UpdateMatrixes();
        }
        auto out = m_view_ * m_model_;
        out = Matrix4<double>::translation({view_width_/2.0, view_height_/2.0, 0.0f}) * out;
        
        // printMatrix(out);
        Vector4<double> v{x, y, 0.0, 1.0};

        out = out.getInverse();
        v = out * v;
        world_x = v.x / kDM5;
        world_y = v.y / kDM5;
        #ifdef DEBUG
        printf("===================Screen2World world_x:%f,world_y :%f x:%f, y:%f=========================\n",
                world_x, world_y, x, y);
        #endif
        return true;
    }

    bool MapRenderCamera::World2Screen(double world_x, double world_y, double& x, double& y)
    {

        // Matrix4<double> out = Matrix4<double>::translation({-map_center_.longitude_ * kDM5, -map_center_.latitude_* kDM5, 0.0f});
        // double lonSpan = 0.0, latSpan = 0.0;
        // calcGridLonLatSpan(map_scale_ , lonSpan, latSpan);

        // out = Matrix4<double>::scaling({kDefaultTilePixelSize / (lonSpan * kDM5), kDefaultTilePixelSize /(lonSpan* kDM5), 1.0f}) * out;
        // out = Matrix4<double>::rotationZ(roll_angle_ * M_PI / 180.0) * out;

        // out = Matrix4<double>::translation({view_width_/2.0, view_height_/2.0, 0.0f}) * out;
        // printMatrix(out);
        if (modelMatrixDirty_.load() || animationDirty_.load()) {
            UpdateMatrixes();
        }
        auto out = m_view_ * m_model_;
        out = Matrix4<double>::translation({view_width_/2.0, view_height_/2.0, 0.0f}) * out;
        Vector4<double> v{world_x* kDM5, world_y* kDM5, 0.0, 1.0};

        v = out * v;
        x = v.x;
        y = v.y;
        #ifdef DEBUG
        printf("===================World2Screen world_x:%f,world_y :%f x:%f, y:%f=========================\n",
                world_x, world_y, x, y);
        
        printMatrix(out);
        #endif
        return true;
    }


    bool MapRenderCamera::Screen2WorldOld(double x, double y, double& world_x, double& world_y)
    {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 共享锁，允许多个读操作同时进行

        // 检查视口尺寸是否有效
        if (view_width_ <= 0 || view_height_ <= 0) {
            return false;
        }
        

        // 这个比例尺下一个tile的经纬度跨度，一个tile被分为512个像素
        double scale = 1.0/GetLogicDataScale(std::floor(map_scale_), data_scale_);

        // 正交投影用的是-w/2, w/2, -h/2, h/2, center->0,0
        auto lonSpan = GetLonSpan();
        double offset_x = (x - view_width_ /2) *  lonSpan / kDefaultTilePixelSize;
        double offset_y = (y- view_height_ /2) *  lonSpan / kDefaultTilePixelSize;

        // 计算屏幕坐标在世界坐标中的偏移
        Vector4<double> v = {offset_x, offset_y, 1, 1};
        // 2. 获取投影视图矩阵
        // TODO: 目前只做正交投影
        Matrix4<double> projViewMatrix;
        if (!GetOrthViewMatrix(projViewMatrix)) {
            return false;
        }
        // 正交投影，x, y的offset不变，加上旋转和缩放即可
        projViewMatrix = m_model_;
        // printMatrix(projViewMatrix);
        
       

        // 3. 求投影视图矩阵的逆矩阵
        // Matrix4<double> invProjViewMatrix = projViewMatrix.getInverse();

 
        // 6. 使用逆投影视图矩阵将 NDC 坐标转换为世界坐标
        Vector4<double> world = projViewMatrix * v;
        #ifdef DEBUG
            // printf("========world.x:%f, world.y:%f offset %f-%f===========\n", world.x, world.y, offset_x, offset_y);
        #endif
        world_x = map_center_.longitude_ + world.x;
        world_y = map_center_.latitude_ + world.y;
       #ifdef DEBUG
        // printf("========world_x:%f, world_y:%f center:%f-%f===========\n", world_x, world_y, map_center_.longitude_, map_center_.latitude_);
        #endif
        return true;
                        
        #if 0
        // 1. 将屏幕坐标转换为归一化设备坐标（NDC）
        float ndc_x = 2.0f * x / view_width_ - 1.0f;
        float ndc_y = 1.0f - 2.0f * y / view_height_;

        // 2. 获取投影视图矩阵
        // TODO: 目前只做正交投影
        Matrix4<float> projViewMatrix;
        if (!GetOrthViewMatrix(projViewMatrix)) {
            return false;
        }

        // 3. 求投影视图矩阵的逆矩阵
        Matrix4<float> invProjViewMatrix = projViewMatrix.getInverse();

 
        // 5. 将 NDC 坐标转换为齐次坐标
        Vector4<float> ndc(ndc_x, ndc_y, 0.0f, 1.0f);

        // 6. 使用逆投影视图矩阵将 NDC 坐标转换为世界坐标
        Vector4<float> world = invProjViewMatrix * ndc;

        printf("=========  x:%f, y:%f world.x:%f world.y:%f, world.z:%f world.w:%f===========\n", 
            x, y, world.x, world.y, world.z, world.w);
        // 8. 透视除法
        if (world.w != 0.0f) {
            world_x = world.x / world.w;
            world_y = world.y / world.w;
        } else {
            return false;
        }

        // 9. 假设世界坐标是墨卡托投影坐标，将其转换为经纬度
        // 这里需要实现 Mercator2LonLat 函数
        // PointXY<double> mercator(world_x, world_y);
        // Lonlat lonlat = Mercator2LonLat(mercator.x(), mercator.y());
        // world_x = lonlat.longitude_;
        // world_y = lonlat.latitude_;

        return true;
        #endif
    }

    bool MapRenderCamera::World2ScreenOld(double world_x, double world_y, double& x, double& y)
    {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 共享锁，允许多个读操作同时进行

        // 检查视口尺寸是否有效
        if (view_width_ <= 0 || view_height_ <= 0) {
            printf("MapRenderCamera::World2Screen error view_width_[%d] <= 0 || view_height_[%d] <= 0 this:%p\n", view_width_, view_height_, this);
            return false;
        }


        // 1. 假设 world_x 和 world_y 是经纬度，将其转换为墨卡托投影坐标（世界坐标）
        // 这里需要实现 LonLat2Mercator 函数
        // PointXY<double> mercator = LonLat2Mercator(world_x, world_y);
        // 若 world_x 和 world_y 已经是世界坐标，则跳过此步骤
        Matrix4<double> w_m = Matrix4<double>::identity();
        // todo: 这里要加上偏移
        double scale =  GetLogicDataScale(std::floor(map_scale_), data_scale_);
        auto lonSpan = GetLonSpan();
        double delta_x = (world_x - map_center_.longitude_) * kDefaultTilePixelSize /lonSpan;
        double delta_y = (world_y - map_center_.latitude_) * kDefaultTilePixelSize /lonSpan;
        
        Vector4<double> world(delta_x, delta_y, -camera_heigth_, 1.0f);
        world = Matrix4<double>::scaling(scale, scale, 1) * world;  // 数据是底层数据，比例尺可能是高层的
        // world = Matrix4<double>::rotationZ(roll_angle_ * M_PI / 180.0) * world;
        // 3. 获取投影视图矩阵
        // TODO: 目前只做正交投影, 正交投影下
         Matrix4<double> projViewMatrix;
         if (!GetOrthViewMatrix(projViewMatrix)) { // 触发下更新， TODO:许修改更合理
            return false;
        } 
        projViewMatrix = m_proj_orth_;
          
        Vector4<double> clip = projViewMatrix * world;

        double offset_x = clip.x  ;
        double offset_y = clip.y  ;
        // ndc坐标转为屏幕坐标
        if (clip.w!= 0.0f) {
            double ndc_x = clip.x / clip.w;
            double ndc_y = clip.y / clip.w;
            offset_x = (ndc_x ) * 0.5f * view_width_;
            offset_y = (ndc_y) * 0.5f * view_height_;
        }
        // 正交投影用的是-w/2, w/2, -h/2, h/2, center->0,0
        x =  offset_x + view_width_ /2;
        y =  offset_y + view_height_ /2;
        // printf("===================World2Screen world_x:%f,world_y :%f x:%f, y:%f=========================\n",
        //         world_x, world_y, x, y);
        #ifdef DEBUG
        // printf("=========  clip.x:%f, clip.y:%f= clipw:%f========== offset_x:%f, offset_y:%f delta_:%f-%f world:%f-%f\n", 
        //       clip.x, clip.y, clip.w, offset_x, offset_y, delta_x, delta_y, world.x, world.y);
        #endif      
        // if (clip.w != 0.0f) {
        //     float ndc_x = clip.x / clip.w;
        //     float ndc_y = clip.y / clip.w;
        //     printf("=========  ndc_x:%f, ndc_y:%f=========== delta_x:%f, deltay:%f\n",   ndc_x, ndc_y, delta_x, delta_y);

        //     // 5. 将 NDC 坐标转换为屏幕坐标
        //     x = (ndc_x + 1.0f) * 0.5f * view_width_ ;
        //     y = (1.0f - ndc_y) * 0.5f * view_height_ ;
        // } else {
        //     return false;
        // }

   
        //  x = world.x * (screen_width_ >> 1) / world.w;
        //  y = world.y * (screen_height_ >> 1) / world.w;
        
        return true;
        #if 0
        // 4. 将世界坐标转换为裁剪空间坐标
        Vector4<float> clip = projViewMatrix * world;

        // 5. 透视除法，得到归一化设备坐标（NDC）
        if (clip.w != 0.0f) {
            float ndc_x = clip.x / clip.w;
            float ndc_y = clip.y / clip.w;

            // 6. 将 NDC 坐标转换为屏幕坐标
            x = (ndc_x + 1.0f) * 0.5f * view_width_;
            y = (1.0f - ndc_y) * 0.5f * view_height_;
        } else {
            return false;
        }

        return true;
        #endif
    }

    void MapRenderCamera::OnMove(int32_t x, int32_t y, const AnimationOptions& options) 
    {
        OnMove((float)x, (float)y, options);
    }

    void MapRenderCamera::SetDpi(uint16_t dpi) 
    {
        std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 独占锁，同一时间只允许一个写操作
        dpi_ = dpi;
        MarkDirtyByKey("dpi");
    }

    void MapRenderCamera::OnMove(float deltax, float deltay, const AnimationOptions& options) 
    {
      
        // 计算移动距离
       
        // mvp矩阵重新计算
        // translate 参数，先不考虑roll和pitch，直接转为-1~1之间设置
        
        // 地图中心点移动到新的x,y
        CancelAnimation();
        double center_x = screen_width_ / 2.0f;
        double center_y = screen_height_ / 2.0f;
        center_x += -1* deltax;
        center_y += -1 * deltay;
        double new_lon = 0.0f;
        double new_lat = 0.0f;
        Screen2World(center_x, center_y, new_lon, new_lat);
        AnimationOptions adjust_options = options;
        if (adjust_options.type == AnimationType::kAnimationTypeMoveTo) {
            adjust_options.SetAnimatePos({map_center_.longitude_, map_center_.latitude_}, {new_lon, new_lat});
            if (abs(deltax) > screen_width_ * 2 || abs(deltay) > screen_height_ * 2) { // move超过2屏的距离，改为flyto
                adjust_options.type = AnimationType::kAnimationTypeFlyTo;
                adjust_options.SetAnimateScale(GetMapScale(), GetMapScale());
            }
        }

        if ((adjust_options.type &  AnimationType::kAnimationTypeFlyTo) ==  AnimationType::kAnimationTypeFlyTo) {
            FlyTo(new_lon, new_lat, GetMapScale(), adjust_options);
        }
        else {
            SetMapCenter(new_lon, new_lat, adjust_options);
        }
        
        // std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 独占锁，同一时间只允许一个写操作
        // map_center_.longitude_ = new_lon;
        // map_center_.latitude_ = new_lat;
        // MarkDirtyByKey("center");
        // // 触发动画相关
        // if (options.type == AnimationType::kAnimationTypeMoveTo) {
        //     animationDirty_.store(true);
        //     animation_options_ = adjust_options;
        // }

        // Matrix4<double> translateMatrix = Matrix4<double>::translation(norm_dx, norm_dy, 0);
        // // printMatrix(translateMatrix);
        // translateMatrix_ *= translateMatrix;
        printf("onMOve===== new center:%f %f\n", map_center_.longitude_, map_center_.latitude_);
        // printMatrix(translateMatrix_);
        // screen的一半
        // MarkDirty(true, true);                                                       


    }

    bool MapRenderCamera::UpdateTileMbr(const std::map<int64_t, AABB2<Point2d> >& tile_area_map)
    {
        std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 独占锁，同一时间只允许一个写操作
        for(auto tile_mbr : tile_area_map) 
        {
            auto tile_id = tile_mbr.first;
            auto mbr = tile_mbr.second;
            if (tile_area_map_.count(tile_id) == 0) {
                tile_area_map_[tile_id] = mbr;
                printf("========tile_id:%ld not exist, add===========\n",tile_id);
            }

        }
        return true;
    }

    bool MapRenderCamera::GetTilePosMatrix(const int64_t& tile_id, Matrix4<double>& tilePosMatrix)
    {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 共享锁，允许多个读操作同时进行
        if (tile_area_map_.count(tile_id) == 0) {
            printf("========tile_id:%ld not exist===========\n",tile_id);
            return false;
        }
        auto mbr = tile_area_map_[tile_id];
        // 获取mbr左下角的offset
        auto leftbottom_lon = mbr.minpt().x();
        auto leftbottom_lat = mbr.minpt().y();
        double leftbottom_x = 0.0f;
        double leftbottom_y = 0.0f;
        bool ret = World2Screen((double)leftbottom_lon, (double)leftbottom_lat, leftbottom_x, leftbottom_y);
        if (!ret) {
            printf("========tile_id:%ld World2Screen failed===========\n",tile_id);
            return false;
        }
        // 归一化到-1~1之间
          
        
        Vector4<double> v = {leftbottom_x, leftbottom_y, 0, 1};
        Vector4<double> clip = m_proj_orth_ * v;
        double ndc_x = 2.0f * leftbottom_x / view_width_  ;
        double ndc_y = 2.0f *leftbottom_y / view_height_ ;

        // if (clip.w != 0.0f) {
        //     ndc_x = clip.x / clip.w - 1.0;
        //     ndc_y = clip.y / clip.w - 1.0;
        // }
      
           
        // v.normalize();
        tilePosMatrix = Matrix4<double>::translation({ndc_x , ndc_y,  0.0f});  
        #ifdef DEBUG
            printf("========tile_id:%ld=leftbottom_x:%f, leftbottom_y:%f ndc_x %f, ndc_y %f clipx:%f, clipy:%f, clipw:%f,===========\n",
                tile_id, leftbottom_x, leftbottom_y, ndc_x, ndc_y, clip.x, clip.y, clip.w);
        #endif

        //  printMatrix(tilePosMatrix);
        // printf("========tilePosMatrix*m_viewProjorth_:\n");
        // auto test = tilePosMatrix * m_viewProjorth_;
        // printMatrix(test);
        return true;
    }

    void MapRenderCamera::GetViewSize(uint32_t& width, uint32_t& height)
    {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 共享锁，允许多个读操作同时进行
        width = view_width_;
        height = view_height_;
    }

    bool MapRenderCamera::IsAnimationFinished(const TimePoint& now)
    {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 共享锁，允许多个读操作同时进行
        if (animationDirty_.load()) {
            return animation_options_.IsFinished(now);
        }
        return true;
    }

    void MapRenderCamera::GetFlyToZoomOutScale(const Lonlat& start_pos, const Lonlat& dest_pos, unsigned int dest_scale, unsigned int& zoom_out_scale)
    {
        // 保证一屏里能正确显示start_pos和dest_pos是最合适的比例尺，如果不行就用最大比例尺

        double lon_span_dest = dest_pos.longitude_ - start_pos.longitude_;
        double lat_span_dest = dest_pos.latitude_ - start_pos.latitude_;
        double lon_span_dest_normalized = lon_span_dest / lonSpan_;
        double lat_span_dest_normalized = lat_span_dest / lonSpan_;
        double scale = std::max(lon_span_dest_normalized, lat_span_dest_normalized);
        auto log2_scale = std::log2(scale);
        zoom_out_scale = std::round(dest_scale - log2_scale) ;
        zoom_out_scale = std::max(zoom_out_scale,  min_zoom_level_);
    }


    void MapRenderCamera::SetMinScale(uint32_t min_scale)
    {
        std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 独占锁，同一时间只允许一个写操作
        min_zoom_level_ = min_scale;
    }

    void MapRenderCamera::SetMaxScale(uint32_t max_scale)
    {
        std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 独占锁，同一时间只允许一个写操作
        max_zoom_level_ = max_scale;
    }

    uint32_t MapRenderCamera::GetMinScale()
    {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 共享锁，允许多个读操作同时进行
        return min_zoom_level_;
    }

    uint32_t MapRenderCamera::GetMaxScale()
    {
        std::shared_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 共享锁，允许多个读操作同时进行
        return max_zoom_level_;
    }

    void MapRenderCamera::CancelAnimation(bool finishfun_call)
    {
        std::unique_lock<SpinSharedMutex> lock(map_render_camera_mutex_); // 独占锁，同一时间只允许一个写操作
        if (finishfun_call && animation_options_.transitionFinishFn) {
            // 调用finish函数
            // 为了防止finish函数里用了读写锁，导致死锁，finish调用切个线程调用
            auto finish_fun = animation_options_.transitionFinishFn;
            std::thread([this, finish_fun]() {
                finish_fun();
            }).detach();
            animation_options_.transitionFinishFn = nullptr;
        }
        animation_options_ = AnimationOptions();
        animationDirty_ = false;

    }


} //namespace 
/* EOF */
