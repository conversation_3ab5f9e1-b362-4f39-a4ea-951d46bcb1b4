/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "map_render_scene.h"
#include "map_render_camera.h"
#include "map_render_backend.h"
#include "map_render_manager.h"
#include "i_map_render_scene.h"
#include "render_tile_layer.h"
#include "render_raster_tile.h"

#include "include/core/SkPixmap.h"
#include "map_render_context.h"
#include "scale_converter.h"
#include <unistd.h>
#include "render_data_cache_lru.h"
#include "annotation/annotation_manager.h"
#include "map_render_backend_gl.h"

namespace aurora {

    moodycamel::ConcurrentQueue<TileImagePtr>  MapRenderScene::raster_tiles_;

    MapRenderScene::MapRenderScene()
    {
        
    }   

    MapRenderScene::~MapRenderScene()
    {
        render_list_.clear();
    }
    
    int32_t MapRenderScene::Init()
    {
        camera_ = std::make_shared<MapRenderCamera>();
        backend_ = MapRenderBackend::CreateBackend(BackendAPIType::API_GLES);
        backend_->Init(kTileSize, kTileSize, Color(0xFFEAF4F6));
        state_.AttachCamera(camera_);
        return 0;
    }

    void MapRenderScene::Destory()
    {

    }

    void MapRenderScene::SetCamera(CameraPtr camera)
    {
        // camera_ = camera;

        // camera_ set view port for test
    }
 
    CameraPtr MapRenderScene::GetCamera() const
    {
        return camera_;
    }

    void MapRenderScene::UpdateMapCenter(double lon, double lat)
    {

    }

    void MapRenderScene::SetCurRenderMapTiles(const std::vector<std::shared_ptr<CacheTile>>& current_render_tiles, const std::vector<TileID>& all_tile_ids) {
        current_render_tiles_ = current_render_tiles;
        currRenderTiles_ = all_tile_ids;
    }

    void MapRenderScene::RenderMap()
    {
        // TileImagePtr tileImage = nullptr;
        // if (MapRenderScene::PopRasterTileToQueue(tileImage)) 
        // {
        //     clock_t sttime = clock();
        //     SkPixmap pixmap;
        //     if (tileImage->image_->peekPixels(&pixmap)) 
        //     {
        //         std::shared_ptr<RenderTileLayer> tile = std::make_shared<RenderTileLayer>();
        //         tile->SetTileID(tileImage->tile_id_);
        //         tile->SetRasterTileData(pixmap.addr(), aurora::FORMAT_RGBA, 512, 512);
        //         backgrounds_.push_back(tile);
        //         // tiles_.insert_or_assign(tileImage->tile_id_, tile);
        //     }
        //     clock_t edtime = clock();
        //     printf("time to skia peekPixels:( %f ms)\n", double(edtime-sttime)/1000);
        //     printf("time to skia peekPixels:( %f s)\n", double(edtime-sttime)/CLOCKS_PER_SEC);
        // }
        uint32_t screen_width_ = 1;
        uint32_t screen_height_ = 1;
        state_.GetScreenSize(screen_width_, screen_height_);
        glViewport(0, 0, screen_width_, screen_height_);

        MapRenderContext* context = backend_->GetContext();
        if (context != nullptr)
        {
            context->Clear();
        }
        TimePoint now = Clock::now();
        // 判断是否所有数据都收到
        bool all_received = true;
        // for (auto& tileid : currRenderTiles_) {
        //     if (std::find_if(current_render_tiles_.begin(), current_render_tiles_.end(), [&tileid](const std::shared_ptr<CacheTile>& tile) {
        //         return tile && tile->raster_tile && tile->raster_tile->GetTileID() == tileid;
        //     }) == current_render_tiles_.end()) {
        //         all_received = false;
        //         break;
        //     }
        // }
        // 简单点判断size相等就认为全部数据都收到了，不做上面这么复杂的判断
       
        if (currRenderTiles_.size() == current_render_tiles_.size()) {
            all_received = true;
            
        }
        else {
            all_received = false;
        }
         #ifdef DEBUG
            printf(" ==========this frame %d - %d all data received [%d]========== \n", currRenderTiles_.size(), current_render_tiles_.size(), all_received);
        #endif
        for (auto& cache_tile: current_render_tiles_) {
            if (cache_tile && cache_tile->raster_tile) {
                 cache_tile->raster_tile->Render(camera_, now );
            }
        }
       
        // draw background tile
        for(auto& tile : backgrounds_)
        {
            int tiledataLevel = GetDataLevel(tile->GetTileID().z_);
            int mapStateDataLevel = GetDataLevel(state_.GetMapScale());
            if (tile && (tiledataLevel == mapStateDataLevel))
            {   
                tile->Render(camera_);
            }
        }
        
        // camera_->CalcRenderTiles(currRenderTiles_);
        // for (auto& tileid : currRenderTiles_)
        // {
        //    if (tiles_[tileid] != nullptr)
        //    {
        //         tiles_[tileid]->Render(camera_);
        //    }
        // }

        for(auto& renderlayer : layer_list)
        {
            if (renderlayer)
            {
                renderlayer->Render(camera_);
            }
        }
        if (all_received)
        {
            AnnotationManager::Instance().Clear();
            building_layer_.ClearBuildings();
            
            // 新增：复用同一个 RenderBackendGL 实例（避免重复创建上下文）
            static std::shared_ptr<RenderBackendGL> opengl_backend;
           
            for (auto tile : current_render_tiles_) {
                if (tile && tile->collision_tile) {
                    AnnotationManager::Instance().AddTileData(tile->collision_tile);
                }

                if (tile && !tile->building_list.empty()) {
                    building_layer_.AddBuildings(tile->building_list);
                    #if 0
                    if ( !opengl_backend) {
                opengl_backend = std::make_shared<RenderBackendGL>();
                // 初始化一次（使用屏幕尺寸，避免偏移）
                if (!opengl_backend->Init(screen_width_, screen_height_, Color(0))) {
                    printf("OpenGL backend init failed!\n");
                    return;
                }
            }
                    // 绘制前不清空缓冲区（避免覆盖底图）
                    // opengl_backend->BeginDraw();  // 移除该行！
                    // 直接绘制三角形到当前上下文（与底图共享同一缓冲区）
                       // 新增：获取相机的投影视图矩阵（需根据实际相机实现调整）
                Matrix4<double> proj_view_matrix;
                if (camera_) {
                    camera_->GetOrthViewMatrix(proj_view_matrix);  // 假设相机提供该接口
                }

                // 传递正确的投影视图矩阵到 DrawTriangle
                opengl_backend->DrawTriangle(tile->building_list, proj_view_matrix);
                #endif
                }
                    
            }
            building_layer_.Update(camera_);
            building_layer_.Render(camera_);
            
            over_layer_.Update(camera_);
            over_layer_.Render();
        }
    }

    void MapRenderScene::UpdateScale(unsigned int scale)
    {

    }

    void MapRenderScene::AddRenderLayer(std::list<RenderLayerIF>& renderlayers)
    {

    }

    void MapRenderScene::RemoveRenderLayer(std::list<RenderLayerIF>& renderlayers)
    {

    }

    bool MapRenderScene::Select(const Rect& rect)
    {
        return false;
    }

    void MapRenderScene::GetSeletedObjects(std::list<RenderLayerIF>& renderlayers)
    {
    }

    MapRenderState& MapRenderScene::GetRenderState()
    {
        return state_;
    }

    void MapRenderScene::AddRasterTileToQueue(TileImagePtr tileImage)
    {
        // int size = static_cast<int>(raster_tiles_.size_approx());
        // printf("tick:%d AddRasterTileToQueue raster_tiles size:%d\n", 
        //     std::chrono::duration_cast<std::chrono::milliseconds>(Clock::now().time_since_epoch()).count(), size);
        raster_tiles_.enqueue(tileImage);
    }
    
    int MapRenderScene::PopRasterTileToQueue(TileImagePtr& tileImage)
    {
        int size = static_cast<int>(raster_tiles_.size_approx());
        // printf("tick:%d PopRasterTileToQueue raster_tiles size:%d\n", 
        //     std::chrono::duration_cast<std::chrono::milliseconds>(Clock::now().time_since_epoch()).count(), size);
        raster_tiles_.try_dequeue(tileImage);
        return size;
    }

    void MapRenderScene::MoveMap(double delta_x, double delta_y, const uint32_t animation_duration_ms) {
        if (delta_x == 0.0 && delta_y == 0.0) {
            return;
        }
        if (camera_ != nullptr) {
            if (animation_duration_ms == 0) {
                camera_->OnMove(delta_x, delta_y);
                return;
            }

            AnimationOptions animation(Milliseconds(animation_duration_ms), 
            {camera_->GetMapCenter().longitude_, camera_->GetMapCenter().latitude_}, {delta_x, delta_y}, AnimationType::kAnimationTypeMoveTo);
            camera_->OnMove(delta_x, delta_y, animation);
        }
    }

    void MapRenderScene::SetMapPitch(float pitch, const uint32_t animation_duration_ms) {
        if (camera_ != nullptr) {
            if (animation_duration_ms == 0) {
                camera_->SetPitchAngle(pitch);
                return;
            }
            AnimationOptions animation(Milliseconds(animation_duration_ms), 
            {camera_->GetMapPitchAngle()}, {pitch}, AnimationType::kAnimationTypeRollTo);
            camera_->SetPitchAngle(pitch, animation);
        }
    }

    float MapRenderScene::GetMapPitch() {
        if (camera_ != nullptr) {
            return camera_->GetMapPitchAngle();
        }
        return 0;
    }

} //namespace 
/* EOF */
