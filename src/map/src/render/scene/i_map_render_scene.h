/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file map_render_scene_if.h
 * @brief Declaration file of class MapRenderSceneIF.
 * @attention used for C/C++ only.
 */

 #ifndef MAP_SRC_RENDER_SCENE_RENDERSCENE_INTERFACE_H_
 #define MAP_SRC_RENDER_SCENE_RENDERSCENE_INTERFACE_H_
 
 #include <memory> 
 #include <list>
 #include "map_render_camera_if.h"
 #include "render_geometry_type.h"
 #include "util_tile_define.h"
#include "include/core/SkImage.h"
#include "include/core/SkPixmap.h"
#include "aabb2.h"
#include "collision/collision_tile.h"

 namespace aurora {
 
 class RenderCameraIF;
 class RenderLayerIF;
 class MapRenderState;
 struct CacheTile;
 struct BuildingData;

 struct TileImage {
    sk_sp<SkImage> image_{nullptr};
    SkPixmap pixmap_{};
    TileID tile_id_{};
    AABB2<Point2d> tile_bbox_{}; 
    CollisionTilePtr collision_{nullptr};
    std::vector<BuildingData> building_list_;
};
using TileImagePtr = std::shared_ptr<TileImage>;

 /**
 * class breif description
 *
 * IMapRenderScene
 *
 */
 class IMapRenderScene
 {
 public:          
     virtual ~IMapRenderScene() = default;

     virtual int32_t Init() = 0;

     virtual void Destory() = 0;

     /// @brief 设置相机
     /// @param camera 相机
     virtual void SetCamera(CameraPtr camera) = 0;

     /// @brief  获取相机
     /// @return 相机
     virtual CameraPtr GetCamera() const = 0;

     /// @brief 更新地图中心点
     /// @param lon 经度
     /// @param lat 纬度
     virtual void UpdateMapCenter(double lon, double lat) = 0;

     /// @brief 更新地图比例尺
     /// @param scale  地图比例尺
     virtual void UpdateScale(unsigned int scale) = 0;

     /// @brief 将渲染对象添加到场景
     /// @param renderlayers 渲染对象列表
     virtual void AddRenderLayer(std::list<RenderLayerIF>& renderlayers) = 0;

     /// @brief 将渲染对象从场景中删除
     /// @param renderlayers 删除的渲染对象列表
     virtual void RemoveRenderLayer(std::list<RenderLayerIF>& renderlayers) = 0;
 
     /// @brief 选择屏幕上的渲染对象
     /// @param rect 屏幕范围(也可以是一个点)
     /// @returns 是否有渲染对象被选中
     virtual bool Select(const Rect& rect) = 0;
 
     /// @brief 获取选择的对象
     /// @param renderObjList 已选中对象列表(按距离排序, 近处的在前边)
     virtual void GetSeletedObjects(std::list<RenderLayerIF>& renderlayers) = 0;

     /// @brief 获取地图场景状态
     /// @returns 返回地图渲染状态
     virtual MapRenderState& GetRenderState() = 0;

     virtual void RenderMap() = 0;

     virtual void SetCurRenderMapTiles(const std::vector<std::shared_ptr<CacheTile>>& current_render_tiles, const std::vector<TileID>& all_tile_ids) = 0;

     // 注意：地图内部左下角为0,0点，所以如果窗口给的y是按左上角做为0，0点，delta_y应该取反传入
    virtual void MoveMap(double delta_x, double delta_y, const uint32_t animation_duration_ms = 0) = 0;
    
    virtual void SetMapPitch(float pitch, const uint32_t animation_duration_ms = 0) = 0;

    virtual float GetMapPitch() = 0;
 };
     
 } //namespace 
 
 #endif //MAP_SRC_RENDER_SCENE_RENDERSCENE_INTERFACE_H_
 /* EOF */
