#ifndef MAP_RENDER_MARK_TEXT_TEXTDEF_H
#define MAP_RENDER_MARK_TEXT_TEXTDEF_H

#include <cstdint>
#include <memory>
#include <string>
#include "util_basic_type.h"

namespace aurora {

class TextStyle;

class TextHolder {
public:
  virtual ~TextHolder() = default;
  virtual BoundingBox CreateTextBlob(const std::string& str, const std::string& font_name,
                              int32_t font_size, uint32_t length_limit) = 0;
};

using TextHolderPtr = std::shared_ptr<TextHolder>;

static const char* GetLetterUtf8(const char* name, size_t name_len, size_t& letter_len) {
  const char* p1 = name;
  size_t len = 0;

  if ((*p1 & 0xC0) == 0xC0) {
    if ((*p1 & 0xF0) == 0xF0)
      len = 4;
    else if ((*p1 & 0xE0) == 0xE0)
      len = 3;
    else if ((*p1 & 0xC0) == 0xC0)
      len = 2;
  } else {
    len = 1;
  }
  letter_len = len;
  return p1;
}

}  // namespace aurora
#endif  // MAP_RENDER_MARK_TEXT_TEXTDEF_H
