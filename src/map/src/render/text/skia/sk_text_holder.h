#ifndef MAP_RENDER_TEXT_SKIA_HOLDER_H
#define MAP_RENDER_TEXT_SKIA_HOLDER_H

#include "include/core/SkTextBlob.h"
#include "text_def.h"

namespace aurora {

class SkTextHolder : public TextHolder {
public:
  virtual ~SkTextHolder();

  virtual BoundingBox CreateTextBlob(const std::string& str, const std::string& font_name,
                                     int32_t font_size, uint32_t length_limit);

  sk_sp<SkTextBlob> GetTextBlob() { return text_blob_; }
  float GetWidth() { return width_; }
  float GetHeight() { return height_; }

private:
  void SubStrLines(const std::string& str, uint32_t length_limit, std::vector<std::string>& lines);
  sk_sp<SkTextBlob> GetTextBlob(const std::vector<std::string>& lines, const std::string& font_name,
                                int32_t font_size, float& width, float& height);

  sk_sp<SkTextBlob> text_blob_{nullptr};
  float width_{0};
  float height_{0};
};

}  // namespace aurora
#endif  // MAP_RENDER_TEXT_SKIA_HOLDER_H
