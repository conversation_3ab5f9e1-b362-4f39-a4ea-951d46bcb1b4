#include "text/skia/sk_text_holder.h"

#include "SkFontMetrics.h"
#include "SkTypeface.h"
#include "map_render_poi_style.h"
#include "map_render_skfontface.h"
#include "map_render_skfontmanager.h"
#include "text/text_def.h"

namespace aurora {

SkTextHolder::~SkTextHolder() {}

BoundingBox SkTextHolder::CreateTextBlob(const std::string& str, const std::string& font_name,
                                         int32_t font_size, uint32_t length_limit) {
  std::vector<std::string> lines;
  if (length_limit == 0) {
    lines.push_back(str);
    text_blob_ = GetTextBlob(lines, font_name, font_size, width_, height_);
  } else {
    SubStrLines(str, length_limit, lines);
    if (!lines.empty()) {
      text_blob_ = GetTextBlob(lines, font_name, font_size, width_, height_);
    }
  }
  BoundingBox box(0, 0, width_, height_);
  return box;
}

void SkTextHolder::SubStrLines(const std::string& str, uint32_t length_limit,
                               std::vector<std::string>& lines) {
  std::vector<uint32_t> indexes;
  indexes.reserve(16);

  size_t letter_count = 0;
  size_t cur_len = 0;
  size_t letter_len = 0;
  const char* w = str.c_str();
  size_t str_len = str.length();
  do {
    indexes.push_back(cur_len);
    w = GetLetterUtf8(w + letter_len, str_len - cur_len, letter_len);
    cur_len += letter_len;
    letter_count++;
  } while (cur_len < str_len && letter_len > 0);
  indexes.push_back(str_len);

  uint32_t line_count = 1;
  uint32_t letter_count_in_line = letter_count;
  if (letter_count > length_limit * 2) {
    line_count = 3;
    letter_count_in_line = (letter_count + 2) / 3;
  } else if (letter_count > length_limit) {
    line_count = 2;
    letter_count_in_line = (letter_count + 1) / 2;
  } else {
  }

  lines.resize(line_count);
  lines[0] = str.substr(0, indexes[letter_count_in_line]);
  for (uint32_t i = 1; i < line_count; ++i) {
    uint32_t start_idx = indexes[letter_count_in_line * i];
    uint32_t end_idx = letter_count_in_line * (i + 1);
    if (end_idx > letter_count) {
      end_idx = indexes[letter_count];
      ;
    } else {
      end_idx = indexes[end_idx];
    }
    lines[i] = str.substr(start_idx, end_idx - start_idx);
  }
}

sk_sp<SkTextBlob> SkTextHolder::GetTextBlob(const std::vector<std::string>& lines,
                                            const std::string& font_name, int32_t font_size,
                                            float& width, float& height) {
  SKFontFace* fontface = SKFontManager::Instance().GetFontFace(font_name);
  if (fontface == nullptr) {
    return nullptr;
  }
  SkFont& font = fontface->GetFont(font_size);

  width = 0;
  SkFontMetrics metrics;
  font.getMetrics(&metrics);
  SkScalar line_height = metrics.fDescent - metrics.fAscent;
  SkScalar line_spacing = line_height * 0.05f;  // 5%行高作为间距
  SkScalar total_line_height = line_height + line_spacing;
  SkTextBlobBuilder builder;
  SkScalar current_y = -metrics.fTop;
  height = total_line_height * lines.size();

  for (const auto& line : lines) {
    if (line.size() > 0) {
      float w = line_spacing + font.measureText(line.c_str(), line.size(), SkTextEncoding::kUTF8);
      if (w > width) {
        width = w;
      }
      const int glyph_count = font.countText(line.c_str(), line.size(), SkTextEncoding::kUTF8);
      if (glyph_count <= 0) continue;
      const auto& run_buffer = builder.allocRunPos(font, glyph_count);
      font.textToGlyphs(line.c_str(), line.size(), SkTextEncoding::kUTF8, run_buffer.glyphs,
                        glyph_count);

      std::vector<SkScalar> xpos(glyph_count);
      font.getXPos(run_buffer.glyphs, glyph_count, xpos.data());
      for (int i = 0; i < glyph_count; ++i) {
        run_buffer.points()[i] = {xpos[i], current_y};
      }
    }
    current_y += total_line_height;
  }
  return builder.make();
}

}  // namespace aurora
