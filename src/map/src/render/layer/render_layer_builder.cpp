/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "render_layer_builder.h"
#include "render_line_layer.h"
#include "render_point_layer.h"
#include "render_area_layer.h"
#include "data_manager_interface.h"
#include "display_data/display_tile_reader.h"
#include "display_data/display_data_def.h"
#include "data_provider.h"
#include "scale_converter.h"
#include "util_basic_type.h"
#include <iostream>
#include <cmath>
#include "constants.h"

#include "collision/collision_tile.h"
#include "text/skia/sk_text_holder.h"
#include "building_data.h"

namespace aurora {
    RenderLayerBuilder::RenderLayerBuilder()
    {

    }

    void RenderLayerBuilder::BuildRenderLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme,  uint32_t request_logic_level)
    {
        auto data_logic_level = reader.GetTileID().level;
        // 如： 14， 15级对应的数据返回都是15，但是14的比例尺是15的2倍，所以需要将15级数据分布*1/2
        auto scale = std::pow(2, (long)request_logic_level - (long)data_logic_level);
        BackendPtr backend = MapRenderBackend::CreateBackend(BackendAPIType::API_SKIA);
        float width = scale*kTileSize;
        float height = scale*kTileSize;
        bool ret = backend->Init(width, height, Color(0xFFEAF4F6));
        if (!ret) {
            std::cerr << "  RenderLayerBuilder::BuildRenderLayer backend init failed "  << std::endl;
            printf("  RenderLayerBuilder::BuildRenderLayer backend init failed %d %d %d reqlevel:%d \n", 
                reader.GetTileID().row, reader.GetTileID().col, reader.GetTileID().level, request_logic_level);
            return;
        }

        // 返回的数据和请求的level不一样，记录下来
        // TODO:针对这类特殊的错误，看如何处理
        if (GetDataLevel(request_logic_level) != GetDataLevel(reader.GetTileID().level))
        {
            std::cerr << "  Request Loigc level NOT get right data "  << std::endl;
        }
        // std::cout << "  tile_width : " << reader.GetTileWidth() << std::endl;
        // std::cout << "  tile_id col: " << reader.GetTileID().col << std::endl;
        // std::cout << "  tile_id row: " << reader.GetTileID().row << std::endl;
        // std::cout << "  tile_id level: " << reader.GetTileID().level << std::endl;
        // std::cout << "  request level: " << request_logic_level << std::endl;

        // draw background
        BuildAreaLayer(reader, theme, backend, request_logic_level);
        // draw poline
        BuildPolineLayer(reader, theme, backend, request_logic_level);
        // draw building
        auto buildings = BuildBuildingLayer(reader, theme, backend, request_logic_level);
        // draw road
        BuildRoadLayer(reader, theme, backend, request_logic_level);
        // draw point
        //BuildPointLayer(reader, theme, backend, request_logic_level);

        CollisionTilePtr ptr = std::make_shared<CollisionTile>(width, height);
        ptr->CreateCollisions(request_logic_level, reader, theme);

        // 存数据的时候用请求的level
        TileID id(reader.GetTileID().row, reader.GetTileID().col , request_logic_level);
//#define TILE_DEBUG
#ifdef TILE_DEBUG
        DrawTileID(id, backend, scale);
#endif
        backend->EndDraw(id, *reader.GetMbr(), ptr, buildings);
    }

    void RenderLayerBuilder::BuildRoadLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level)
    {
        parser::EdgeFeature* edges = nullptr;
        uint32_t edge_count = reader.GetEdges(edges);
        RenderLineLayer linelayer;
        linelayer.Init(backend, theme);
        auto data_logic_level = reader.GetTileID().level;
        // 如： 14， 15级对应的数据返回都是15，但是14的比例尺是15的2倍，所以需要将15级数据分布*1/2
        auto scale = std::pow(2, request_logic_level - data_logic_level);
        std::vector<aurora::Point> pointlist;

        const float kTileRtio = (float)scale * (float)kTileSize / (float)reader.GetTileWidth();

        // The road needs to be drawn twice
        // first - draw background
        for (uint32_t i = 0; i < edge_count; ++i) {
            aurora::Point pt;
            parser::EdgeFeature& edge = edges[i];

            if (!reader.IsShow(request_logic_level, reader.GetTileID(), edge.zoom_rank)) {
                    continue;
            }

            pointlist.clear();
            for (uint16_t i = 0; i < edge.point_size; ++i) {
                parser::TileXY& pos = edge.points[i];
                pt.x = pos.x() * kTileRtio;
                pt.y = pos.y() * kTileRtio;
                pointlist.push_back(pt);
            }
            linelayer.RenderBoundaries(edge.category, pointlist);
        }

        // second - draw fore
        for (uint32_t i = 0; i < edge_count; ++i) {
            aurora::Point pt;
            parser::EdgeFeature& edge = edges[i];

            if (!reader.IsShow(request_logic_level, reader.GetTileID(), edge.zoom_rank)) {
                    continue;
            }

            pointlist.clear();
            for (uint16_t i = 0; i < edge.point_size; ++i) {
                parser::TileXY& pos = edge.points[i];
                pt.x = pos.x() * kTileRtio;
                pt.y = pos.y() * kTileRtio;
                pointlist.push_back(pt);
            }
            linelayer.RenderRoad(edge.category, pointlist);
        }
    }

    void RenderLayerBuilder::BuildAreaLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level)
    {
        RenderAreaLayer polygonlayer;
        polygonlayer.Init(backend, theme);
        parser::PolygonFeature* polygons = nullptr;
        uint32_t polygon_count = reader.GetPolygons(polygons);
        std::vector<aurora::Point> pointlist;
        aurora::Point pt;
        auto data_logic_level = reader.GetTileID().level;
        // 如： 14， 15级对应的数据返回都是15，但是14的比例尺是15的2倍，所以需要将15级数据分布*1/2
        auto scale = std::pow(2, request_logic_level - data_logic_level);

        const float kTileRtio = (float)scale * (float)kTileSize / (float)reader.GetTileWidth();

        for (uint32_t i = 0; i < polygon_count; ++i) {
            parser::PolygonFeature& polygon = polygons[i];
            // std::cout << "polygon: " << i << std::endl;
            // std::cout << "  x: " << pos.x() << std::endl;
            // std::cout << "  y: " << (int)polygon.zoom_rank << std::endl;
            // std::cout << "  uuid: " << polygon.uuid << std::endl;
            if (!reader.IsShow(request_logic_level, reader.GetTileID(), polygon.zoom_rank)) {
                    continue;
            }

            pointlist.clear();
            for (uint16_t i = 0; i < polygon.point_size; ++i) {
                parser::TileXY& pos = polygon.points[i];
                pt.x = pos.x() * kTileRtio;
                pt.y = pos.y() * kTileRtio;
                pointlist.push_back(pt);
            }
            polygonlayer.ImplRender(polygon.category, pointlist);
        }
    }

    void RenderLayerBuilder::BuildPointLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level)
    {
        parser::PointFeature* points = nullptr;
        uint32_t point_count = reader.GetPoints(points);
        aurora::Point pt;
        RenderPointLayer pointLayer;
        pointLayer.Init(backend, theme);

        // 如： 14， 15级对应的数据返回都是15，但是14的比例尺是15的2倍，所以需要将15级数据分布*1/2
        auto data_logic_level = reader.GetTileID().level;
        auto scale = std::pow(2, request_logic_level - data_logic_level);
        const float kTileRtio = (float)scale * (float)kTileSize / (float)reader.GetTileWidth();

        for (uint32_t i = 0; i < point_count; ++i) {
            parser::PointFeature& p = points[i];
                //p.pos.x() << " " << p.pos.y() << ")";
            // std::cout << "|id: " << i;
            // std::cout << "|category: " << p.category;
            // std::cout << "|uuid: " << p.uuid;
            // std::cout << "|weight: " << p.weight;
            pt.x = p.pos.x() * kTileRtio;
            pt.y = p.pos.y() * kTileRtio;
            // printf("  TileXY.x : %d , TileXY.y : %d \n", p.pos.x(), p.pos.y());
            // printf("  pt.x : %f , pt.y : %f \n", pt.x, pt.y);

            if (!reader.IsShow(request_logic_level, reader.GetTileID(), p.zoom_rank)) {
                continue;
            }

            if (p.native_name_size > 0) {
                std::vector<char> name;
                name.resize(p.native_name_size + 1, 0);
                memcpy(&name[0], p.native_name, p.native_name_size);
                char* n = &name[0];
                printf("tile: %d, %d, %d, name: %s\n", (int)reader.GetTileID().level,
                       (int)reader.GetTileID().col, (int)reader.GetTileID().row, n);

                std::string str(n);
                pointLayer.RenderPOI(str, pt);
            }
        }
    }

    std::vector<BuildingData> RenderLayerBuilder::BuildBuildingLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level)
    {
        parser::BuildingFeature* buildings = nullptr;
        uint32_t building_count = reader.GetBuildings(buildings);
        RenderAreaLayer arealayer;
        arealayer.Init(backend, theme);
        aurora::Point pt;
        auto data_logic_level = reader.GetTileID().level;
        auto scale = std::pow(2, request_logic_level - data_logic_level);
        const float kTileRtio = (float)scale * (float)kTileSize / (float)reader.GetTileWidth();
    
        // 替换为 BuildingData 列表
        std::vector<BuildingData> building_list;
        double tile_min_x = reader.GetMbr()->minx();
        double tile_min_y = reader.GetMbr()->miny();
        double tile_max_x = reader.GetMbr()->maxx();
        double tile_max_y = reader.GetMbr()->maxy();
        double scale_x = (tile_max_x - tile_min_x) / reader.GetTileWidth();
        double scale_y = (tile_max_y - tile_min_y) / reader.GetTileWidth();
            
        for (uint32_t i = 0; i < building_count; ++i) {
            parser::BuildingFeature& building = buildings[i];
            if (!reader.IsShow(request_logic_level, reader.GetTileID(), building.zoom_rank)) {
                continue;
            }
    
            // 1. 转换 2D 底面多边形为 BuildingData 的 base_polygon
            std::vector<aurora::Point> base_polygon;
            for (uint16_t j = 0; j < building.point_size; ++j) {
                parser::TileXY& pos = building.points[j];
                pt.x =tile_min_x +  pos.x() * scale_x;
                pt.y =tile_min_y + pos.y() * scale_y;
                pt.x *= kDM5;
                pt.y *= kDM5;
            
                base_polygon.push_back(pt);
            }
    
            // 2. 生成 3D 顶点和索引（底面拉伸成立方体）
            const float kFloorHeight = 20.0f;  // 每层高度（示例值，可根据需求调整）
            float building_height = building.floor_count * kFloorHeight;
    
            // 顶点格式：3位置(x,y,z) + 3法线(nx,ny,nz) + 2纹理坐标(u,v)
            std::vector<float> vertices;
            std::vector<uint32_t> indices;
            Vector3<float> top_normal(0, 0, 1);
            // 顶点索引计数器
            uint32_t vertex_idx = 0;
    
            // ---------------------------
            // 1. 生成底面（Z=0）和顶面（Z=building_height）的顶点
            // ---------------------------
            std::vector<Point> bottom_pts;  // 底面 2D 点（Z=0）
            std::vector<Point> top_pts;     // 顶面 2D 点（Z=building_height）
            for (const auto& pt : base_polygon) {
                // 底面顶点（Z=0）
                bottom_pts.push_back(pt);
                // 顶面顶点（Z=building_height）
                top_pts.push_back({pt.x, pt.y});
            }
    
            // ---------------------------
            // 2. 生成侧面（连接底面和顶面的边）
            // ---------------------------
            for (size_t j = 0; j < base_polygon.size(); ++j) {
                size_t next_j = (j + 1) % base_polygon.size();  // 闭合多边形
    
                // 侧面四边形的四个顶点（底面j, 底面next_j, 顶面next_j, 顶面j）
                Point bottom_j = bottom_pts[j];
                Point bottom_next = bottom_pts[next_j];
                Point top_next = top_pts[next_j];
                Point top_j = top_pts[j];
    
                // 计算侧面法线（垂直于侧面的方向）
                Vector3<float> edge_dir(bottom_next.x - bottom_j.x, bottom_next.y - bottom_j.y, 0);  // 底边方向
                Vector3<float> up_dir(0, 0, 1);  // 向上方向
                Vector3<float> normal = up_dir.cross(edge_dir);  // 修正叉乘顺序，确保法线朝外
                normal.normalize();
    
                // 添加四个顶点（底面j, 底面next_j, 顶面next_j, 顶面j）
                // 顶点1：底面j（位置+法线+纹理）
                vertices.insert(vertices.end(), {
                    bottom_j.x, bottom_j.y, 0.0f,        // 位置
                    normal.x, normal.y, normal.z,        // 法线
                    (float)j / base_polygon.size(), 0.0f  // 纹理坐标（u按边索引，v=0）
                });
                // 顶点2：底面next_j
                vertices.insert(vertices.end(), {
                    bottom_next.x, bottom_next.y, 0.0f,
                    normal.x, normal.y, normal.z,
                    (float)(j + 1) / base_polygon.size(), 0.0f
                });
                // 顶点3：顶面next_j
                vertices.insert(vertices.end(), {
                    top_next.x, top_next.y, building_height,
                    top_normal.x, top_normal.y, top_normal.z,
                    (float)(j + 1) / base_polygon.size(), 1.0f  // v=1（顶部）
                });
                // 顶点4：顶面j
                vertices.insert(vertices.end(), {
                    top_j.x, top_j.y, building_height,
                    top_normal.x, top_normal.y, top_normal.z,
                    (float)j / base_polygon.size(), 1.0f
                });
    
                // 添加侧面的两个三角形索引（四边形拆分为两个三角形）
                indices.push_back(vertex_idx);     // 顶点1
                indices.push_back(vertex_idx + 1); // 顶点2
                indices.push_back(vertex_idx + 3); // 顶点4
                indices.push_back(vertex_idx + 1); // 顶点2
                indices.push_back(vertex_idx + 2); // 顶点3
                indices.push_back(vertex_idx + 3); // 顶点4
    
                vertex_idx += 4;  // 每侧面添加4个顶点
            }
    
            // ---------------------------
            // 3. 生成顶面（可选，根据需求决定是否绘制顶面）
            // ---------------------------
            // 顶面法线向上（0,0,1）
            
            for (size_t j = 0; j < top_pts.size(); ++j) {
                vertices.insert(vertices.end(), {
                    top_pts[j].x, top_pts[j].y, building_height,  // 位置
                    top_normal.x, top_normal.y, top_normal.z,      // 法线
                    (float)j / top_pts.size(), 1.0f                // 纹理坐标
                });
            }
            // 顶面索引（假设是三角形扇）
            for (size_t j = 2; j < top_pts.size(); ++j) {
                indices.push_back(vertex_idx);       // 第一个顶点
                indices.push_back(vertex_idx + j - 1);
                indices.push_back(vertex_idx + j);
            }
            vertex_idx += top_pts.size();
    
            // ---------------------------
            // 4. 初始化 BuildingData
            // ---------------------------
            BuildingData building_data;
            building_data.vertices = vertices;
            building_data.indices = indices;
            building_data.floor_count = building.floor_count;
            building_data.base_polygon = base_polygon;
            building_data.category = building.category;  // 假设 BuildingData 添加 category 字段
    
    
            // 5. 设置其他渲染属性（示例：从主题获取纹理和颜色）
            // building_data.texture_id = theme->GetBuildingTexture(building.category);  // 假设主题提供纹理
            // building_data.diffuse_color = theme->GetBuildingColor(building.category);  // 假设主题提供颜色
            building_data.model_matrix = Matrix4<float>::identity();  // 模型矩阵（根据需要调整）
    
            // 6. 计算深度（用于排序）
            // float center_y = 0;
            // for (const auto& p : base_polygon) center_y += p.y;
            // center_y /= base_polygon.size();
            // building_data.depth = center_y;  // 假设 BuildingData 添加 depth 字段
    
            building_list.push_back(building_data);
        }
    
        // 按深度排序（Y大的先画）
        std::sort(building_list.begin(), building_list.end(),
            [](const BuildingData& a, const BuildingData& b) {
                return a.floor_count > b.floor_count;
            });
    
        // 调用渲染接口（假设 RenderAreaLayer 支持 BuildingData 列表）
        // arealayer.RenderBuildings(building_list);
        return building_list;
    }

    void RenderLayerBuilder::BuildPolineLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level)
    {
        parser::PolylineFeature* polylines = nullptr;
        uint32_t polyline_count = reader.GetPolylines(polylines);

        RenderLineLayer linelayer;
        linelayer.Init(backend, theme);
        auto data_logic_level = reader.GetTileID().level;
        // 如： 14， 15级对应的数据返回都是15，但是14的比例尺是15的2倍，所以需要将15级数据分布*1/2
        auto scale = std::pow(2, request_logic_level - data_logic_level);
        std::vector<aurora::Point> pointlist;
        const float kTileRtio = (float)scale * (float)kTileSize / (float)reader.GetTileWidth();

        aurora::Point pt;
        for (uint32_t i = 0; i < polyline_count; ++i) {
            parser::PolylineFeature& polyline = polylines[i];

            if (!reader.IsShow(request_logic_level, reader.GetTileID(), polyline.zoom_rank)) {
                continue;
            }

            pointlist.clear();
            for (uint16_t i = 0; i < polyline.point_size; ++i) {
                parser::TileXY& pos = polyline.points[i];
                pt.x = pos.x() * kTileRtio;
                pt.y = pos.y() * kTileRtio;
                pointlist.push_back(pt);
                // std::cout << "Poline pos : " << pos.x() << " " << pos.y() << std::endl;
            }
            linelayer.ImplRender(polyline.category, pointlist);
        }
    }

    void RenderLayerBuilder::DrawTileID(const TileID& id, BackendPtr backend, float scale)
    {
         std::vector<aurora::Point> points;
         points.push_back(Point(0.,0.));
         points.push_back(Point(512* scale, 0.));
         points.push_back(Point(512* scale, 512* scale));
         points.push_back(Point(0.,512* scale));
         backend->SetFillStyle(aurora::STYLE_STROKE);
         backend->SetAntiAlias(true);
         backend->SetLineStyle(aurora::CAP_ROUNT, aurora::JOIN_ROUND);
         backend->SetColor(Color::Red);
         backend->SetLineWidth(2);
         backend->DrawPolyline(points);
         TextStyle style(26, 1, Color(Color::Red), Color(Color::White), "Arial Unicode.ttf");
         TextHolderPtr holder;
         backend->DrawText(std::to_string(id.x_) + "," + std::to_string(id.y_) + "," + std::to_string(id.z_), {10, 20}, 0, style, holder);
    }

} //namespace
