#ifndef MAP_RENDER_LAYER_OVERLAY_PATH_LAYER_H
#define MAP_RENDER_LAYER_OVERLAY_PATH_LAYER_H

#include <cstdint>
#include <mutex>

#include "overlay/layer_base.h"

namespace aurora {
class PathLayer : public LayerBase {
public:
  PathLayer();

  void SetBackend(RenderBackend2D* backend) override;

  void Update(CameraPtr camera) override;

  bool OnClicked(int32_t x, int32_t y) override;

private:
  bool ClickPath(int32_t x, int32_t y, const std::vector<Point2d>& path);
  void DrawPath(CameraPtr camera, int32_t id, bool save_click = true);
  bool GetDisplayPolyline(CameraPtr camera, const std::vector<Point2d>& polyline,
                          std::vector<std::vector<Point>>& result);
  void SavePoint(CameraPtr camera, const Point2d& point, std::vector<Point>& result);
  AABB2<Point2d> GetScreenMbr(CameraPtr camera);
  RenderBackend2D* backend_{nullptr};

  std::map<int32_t, std::vector<Point2d>> click_pathes_;
  std::mutex path_mtx_;
};
}  // namespace aurora

#endif  // MAP_RENDER_LAYER_OVERLAY_PATH_LAYER_H
