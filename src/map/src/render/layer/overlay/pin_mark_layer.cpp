#include "pin_mark_layer.h"

#include "collision_area.h"
#include "map_render_backend_2d.h"
#include "pin_mark/pin_mark_manager.h"

namespace aurora {
PinMarkLayer::PinMarkLayer() {}

void PinMarkLayer::SetBackend(RenderBackend2D* backend) { backend_ = backend; }

void PinMarkLayer::Update(CameraPtr camera) {
  if (backend_ == nullptr) {
    return;
  }
  {
    std::lock_guard<std::mutex> lock(box_mtx_);
    boxes_.clear();
  }

  TextStyle text_style(16, 4, Color::Black, Color::White, "Arial Unicode.ttf");

  auto cache = PinMarkManager::Instance().GetAllPinMarks();
  for (auto itr = cache.begin(); itr != cache.end(); ++itr) {
    auto& pin_marks = itr->second;
    for (size_t i = 0; i < pin_marks.size(); ++i) {
      auto& pin_mark = pin_marks.at(i);
      if (pin_mark) {
        pin_mark->UpdateScreen(camera);
        const auto& mark = pin_mark->GetMark();
        if (mark && mark->IsShow() == kCollisionShow) {
          backend_->DrawMark(mark->GetImageHolder(), mark->GetSrcRect(), mark->GetBox());

          ClickMarkBox box;
          box.mark_type = itr->first;
          box.mark_id = i;
          box.box = mark->GetBox();
          std::lock_guard<std::mutex> lock(box_mtx_);
          boxes_.push_back(box);
        }
        const auto& name = pin_mark->GetName();
        if (name && name->IsShow() == kCollisionShow) {
          Point p(name->GetScreenX(), name->GetScreenY());
          text_style.font_size_ = name->GetFontSize();
          text_style.outline_ = name->GetOutline();
          text_style.fore_color_.fromRGB(name->GetColor());
          text_style.back_color_.fromRGB(name->GetOutlineColor());
          text_style.type_ = name->GetAnchorType();

          backend_->DrawText(name->GetText(), p, 0, text_style, name->GetTextHolder());

          ClickMarkBox box;
          box.mark_type = itr->first;
          box.mark_id = i;
          box.box = mark->GetBox();
          std::lock_guard<std::mutex> lock(box_mtx_);
          boxes_.push_back(box);
        }
      }
    }
  }
}

bool PinMarkLayer::OnClicked(int32_t x, int32_t y) {
  Point2 p(x, y);
  std::lock_guard<std::mutex> lock(box_mtx_);
  for (const auto& box : boxes_) {
    if (box.box.Contains(p)) {
      printf("click pin mark, type: %d, id: %d", box.mark_type, box.mark_id);
      return true;
    }
  }
  return false;
}
}  // namespace aurora
