#include "poi_layer.h"

#include "annotation/annotation_manager.h"
#include "collision_area.h"
#include "map_render_backend_2d.h"
#include "text/skia/sk_text_holder.h"

namespace aurora {
POILayer::POILayer() {}

void POILayer::SetBackend(RenderBackend2D* backend) { backend_ = backend; }

void POILayer::Update(CameraPtr camera) {
  if (backend_ == nullptr) {
    return;
  }

  uint32_t w = 0;
  uint32_t h = 0;
  camera->GetViewSize(w, h);

  {
    std::lock_guard<std::mutex> lock(box_mtx_);
    click_boxes_.clear();
  }

  auto& tiles = AnnotationManager::Instance().GetTileData();
  for (auto tile : tiles) {
    if (tile) {
      auto& points = tile->GetPointCollistion();
      for (auto& point_ptr : points) {
        DrawPoint(point_ptr.get());
      }
      auto& road_names = tile->GetRoadNameCollistion();
      for (auto& road_name : road_names) {
        DrawRoadName(road_name.second.get());
      }
    }
  }
}

bool POILayer::OnClicked(int32_t x, int32_t y) {
  std::lock_guard<std::mutex> lock(box_mtx_);
  for (auto itr = click_boxes_.begin(); itr != click_boxes_.end(); ++itr) {
    for (const auto& box : itr->second) {
      if (box.Contains({x, y})) {
        uint64_t uuid = itr->first;
        printf("click POI: %lu", uuid);
        return true;
      }
    }
  }
  return false;
}

void POILayer::DrawPoint(CollisionPoint* point_ptr) {
  if (point_ptr == nullptr) {
    return;
  }
  if (point_ptr->IsShow() != kCollisionShow) {
    return;
  }

  TextStyle text_style(16, 4, Color::Black, Color::White, "Arial Unicode.ttf");

  std::vector<BoundingBox> boxes;
  boxes.reserve(2);

  CollisionMarkPtr mark = point_ptr->GetMark();
  CollisionTextPtr name = point_ptr->GetName();
  if (mark != nullptr && mark->IsShow() == kCollisionShow) {
    backend_->DrawMark(mark->GetImageHolder(), mark->GetSrcRect(), mark->GetBox());
    boxes.push_back(mark->GetBox());
  }
  if (name != nullptr && name->IsShow() == kCollisionShow) {
    Point p(name->GetScreenX(), name->GetScreenY());
    text_style.font_size_ = name->GetFontSize();
    text_style.outline_ = name->GetOutline();
    text_style.fore_color_.fromRGB(name->GetColor());
    text_style.back_color_.fromRGB(name->GetOutlineColor());
    text_style.type_ = name->GetAnchorType();

    backend_->DrawText(name->GetText(), p, 0, text_style, name->GetTextHolder());
    boxes.push_back(name->GetBox());
  }
  std::lock_guard<std::mutex> lock(box_mtx_);
  click_boxes_.emplace(point_ptr->GetUUID(), boxes);
}

void POILayer::DrawRoadName(CollisionRoadNameSet* road_name_ptr) {
  if (road_name_ptr == nullptr) {
    return;
  }

  TextStyle road_name_style(14, 4, Color(0xFF9A4A3A), Color::White, "Arial Unicode.ttf");
  const std::vector<std::string>& name = road_name_ptr->GetName();
  std::vector<TextHolderPtr>& text_holders = road_name_ptr->GetTextHolder();
  uint32_t count = name.size();
  if (text_holders.size() != count) {
    text_holders.clear();
    text_holders.resize(count, nullptr);
  }
  std::vector<CollisionRoadNamePtr>& collisions = road_name_ptr->GetCollisions();
  for (CollisionRoadNamePtr collision : collisions) {
    if (collision != nullptr && collision->IsShow() == kCollisionShow) {
      const std::vector<CollisionBox>& boxes = collision->GetNameBox();
      const std::vector<float>& dir = collision->GetNameDir();
      if (count != boxes.size() || count != dir.size()) {
        printf("Error: vector size error!!!\n");
        continue;
      }
      for (uint32_t i = 0; i < count; ++i) {
        Point p(boxes[i].x, boxes[i].y);
        uint32_t index = i;
        collision->CalcRevert();
        if (collision->NeedRevert()) {
          index = count - i - 1;
        }

        if (text_holders[index] == nullptr) {
          text_holders[index] = std::make_shared<SkTextHolder>();
          text_holders[index]->CreateTextBlob(name[index], "Arial Unicode.ttf",
                                              road_name_style.font_size_, 0);
        }
        road_name_style.type_ = collision->GetAnchorType();
        // printf("draw word: %s, dir: %f\n", name[index].c_str(), dir[i]);
        backend_->DrawText(name[index], p, dir[i], road_name_style, text_holders[index]);
      }
    }
  }
}
}  // namespace aurora
