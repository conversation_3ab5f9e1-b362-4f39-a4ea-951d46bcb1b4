#ifndef MAP_RENDER_LAYER_OVERLAY_LAYER_FACTORY_H
#define MAP_RENDER_LAYER_OVERLAY_LAYER_FACTORY_H
#include <cstdint>
#include <map>

#include "overlay/layer_base.h"

namespace aurora {
enum LayerType {
    kLayerTypeBegin = 0,
    kLayerTypeAnnotation = 0,
    kLayer<PERSON>ypePath,
    kLayerTypePinMark,

    kLayerTypeCount,
};
class LayerFactory {
public:
  LayerBase* GetLayer(LayerType type);

private:
  LayerPtr CreateLayer(LayerType type);

  std::map<LayerType, LayerPtr> layers_;
};
}  // namespace aurora
#endif  // MAP_RENDER_LAYER_OVERLAY_LAYER_FACTORY_H
