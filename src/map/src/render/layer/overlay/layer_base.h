#ifndef MAP_RENDER_LAYER_OVERLAY_LAYER_BASE_H
#define MAP_RENDER_LAYER_OVERLAY_LAYER_BASE_H
#include <cstdint>
#include <memory>

#include "map_render_camera_if.h"

namespace aurora {
class RenderBackend2D;

class LayerBase {
public:
  virtual void SetBackend(RenderBackend2D* backend) = 0;

  virtual void Update(CameraPtr camera) = 0;

  virtual bool OnClicked(int32_t x, int32_t y) = 0;
};

using LayerPtr = std::shared_ptr<LayerBase>;
}  // namespace aurora
#endif  // MAP_RENDER_LAYER_OVERLAY_LAYER_BASE_H
