#include "building_layer.h"
#include "map_render_backend_2d.h"

namespace aurora {
BuildingLayer::BuildingLayer() {}

void BuildingLayer::SetBackend(BackendPtr backend) { 
  backend_ = backend; 
}

void BuildingLayer::AddBuildings(const std::vector<BuildingData>& building_list) {
  building_list_.insert(building_list_.end(), building_list.begin(), building_list.end());
}

void BuildingLayer::ClearBuildings() { 
  building_list_.clear(); 
}

void BuildingLayer::Update(CameraPtr camera) {
  if (backend_ == nullptr) {
    return;
  }

  uint32_t w = 0;
  uint32_t h = 0;
  camera->GetViewSize(w, h);

  // 渲染建筑
  if (backend_ && camera) {
      // 新增：初始化光照参数（示例值，可根据需求调整）
      // Vector3<float> light_dir(0.5f, 0.5f, 1.0f);  // 光照方向（右上前方）
      // Vector3<float> ambient(0.3f, 0.3f, 0.3f);    // 环境光强度（灰色）
      // Matrix4<double> prject_view_matrix;
      // if (camera->GetOrthViewMatrix(prject_view_matrix)) {
      //     backend_->DrawBuildings(building_list_, light_dir, ambient, prject_view_matrix);
      // }
  }
}

bool BuildingLayer::GetBuildingRenderData(std::vector<float>& merged_vertices, 
                                        std::vector<uint32_t>& merged_indices, 
                                        std::vector<float>& merged_uvs) {
  if (building_list_.empty()) {
    return false;
  }
  uint32_t index_offset = 0;
  for (auto& building : building_list_) {
    if (building.vertices.empty() || building.indices.empty()) {
      continue;
    }
    merged_vertices.insert(merged_vertices.end(), building.vertices.begin(), building.vertices.end());
    // 合并索引数据（调整偏移量）
    
    for (auto idx : building.indices) {
        merged_indices.push_back(idx + index_offset);
    }
    index_offset += building.vertices.size() / 8;  // 每个顶点占8float

    // TODO: 纹理，uv
    // merged_uvs.insert(merged_uvs.end(), building.uvs.begin(), building.uvs.end());
  }
  if (merged_vertices.size() > 0 && merged_indices.size() > 0) {
    // printf("GetBuildingRenderData merged_vertices.size:%d merged_indices.size:%d\n", merged_vertices.size(), merged_indices.size());
    return true;
  }
  return false;
}

}  // namespace aurora
