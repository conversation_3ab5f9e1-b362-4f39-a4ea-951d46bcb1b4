#ifndef PIN_MARK_LAYER_H
#define PIN_MARK_LAYER_H

#include <cstdint>
#include <mutex>

#include "overlay/layer_base.h"

namespace aurora {
struct ClickMarkBox {
  BoundingBox box;
  uint16_t mark_type;
  uint16_t mark_id;
};
using ClickMarkBoxCache = std::vector<ClickMarkBox>;

class PinMarkLayer : public LayerBase {
public:
  PinMarkLayer();

  void SetBackend(RenderBackend2D* backend) override;

  void Update(CameraPtr camera) override;

  bool OnClicked(int32_t x, int32_t y) override;

private:
  RenderBackend2D* backend_{nullptr};
  ClickMarkBoxCache boxes_;
  std::mutex box_mtx_;
};
}  // namespace aurora
#endif  // PIN_MARK_LAYER_H
