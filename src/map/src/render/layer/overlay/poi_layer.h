#ifndef MAP_RENDER_LAYER_OVERLAY_POI_LAYER_H
#define MAP_RENDER_LAYER_OVERLAY_POI_LAYER_H

#include <cstdint>
#include <mutex>

#include "overlay/layer_base.h"

namespace aurora {
class CollisionPoint;
class CollisionRoadNameSet;

class POILayer : public LayerBase {
public:
  POILayer();

  void SetBackend(RenderBackend2D* backend) override;

  void Update(CameraPtr camera) override;

  bool OnClicked(int32_t x, int32_t y) override;

private:
  void DrawPoint(CollisionPoint* point_ptr);

  void DrawRoadName(CollisionRoadNameSet* road_name_ptr);

  RenderBackend2D* backend_{nullptr};
  std::map<uint64_t, std::vector<BoundingBox>> click_boxes_;
  std::mutex box_mtx_;
};
}  // namespace aurora
#endif  // MAP_RENDER_LAYER_OVERLAY_POI_LAYER_H
