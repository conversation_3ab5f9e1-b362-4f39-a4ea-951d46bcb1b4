#include "overlay/layer_factory.h"

#include "overlay/poi_layer.h"
#include "overlay/path_layer.h"
#include "overlay/pin_mark_layer.h"

namespace aurora {

LayerBase* LayerFactory::GetLayer(LayerType type) {
  auto itr = layers_.find(type);
  if (itr == layers_.end()) {
    auto layer_ptr = CreateLayer(type);
    if (layer_ptr) {
      layers_.emplace(type, layer_ptr);
      return layer_ptr.get();
    } else {
      return nullptr;
    }
  }
  return itr->second.get();
}

LayerPtr LayerFactory::CreateLayer(LayerType type) {
  LayerPtr ptr = nullptr;
  switch (type) {
    case kLayerTypeAnnotation: {
      ptr = std::make_shared<POILayer>();
    } break;
    case kLayerTypePath: {
      ptr = std::make_shared<PathLayer>();
    } break;
    case kLayerTypePinMark: {
      ptr = std::make_shared<PinMarkLayer>();
    } break;
    default:
      break;
  }

  return ptr;
}
}  // namespace aurora
