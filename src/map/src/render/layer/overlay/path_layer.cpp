#include "path_layer.h"

#include <limits>

#include "map_render_backend_2d.h"
#include "path/path_manager.h"

namespace aurora {
PathLayer::PathLayer() {}
void PathLayer::SetBackend(RenderBackend2D* backend) { backend_ = backend; }

void PathLayer::Update(CameraPtr camera) {
  if (backend_ == nullptr) {
    return;
  }

  {
    std::lock_guard<std::mutex> lock(path_mtx_);
    click_pathes_.clear();
  }

  std::vector<int32_t> ids = PathManager::Instance().GetAllPathID();
  for (int32_t id : ids) {
    if (id != PathManager::Instance().GetMainRouteID() ||
        id != PathManager::Instance().GetPassedRouteID()) {
      DrawPath(camera, id);
    }
  }
  DrawPath(camera, PathManager::Instance().GetMainRouteID());
  DrawPath(camera, PathManager::Instance().GetPassedRouteID(), false);
}

void PathLayer::DrawPath(CameraPtr camera, int32_t id, bool save_click) {
  std::vector<std::vector<Point>> clip_path;
  auto ptr = PathManager::Instance().GetRoute(id);
  if (ptr == nullptr) {
    return;
  }
  if (GetDisplayPolyline(camera, ptr->GetSimplifiedPoints(), clip_path)) {
    for (auto path : clip_path) {
      if (path.size() > 1) {
        backend_->SetFillStyle(aurora::STYLE_STROKE);
        backend_->SetAntiAlias(true);
        backend_->SetLineStyle(aurora::CAP_ROUNT, aurora::JOIN_ROUND);
        Color color;
        color.fromRGB(ptr->GetForecolor());
        backend_->SetColor(color);
        backend_->SetLineWidth(ptr->GetLineWidth());
        backend_->DrawPolyline(path);

        if (save_click) {
          std::lock_guard<std::mutex> lock(path_mtx_);
          std::vector<Point2d> pts;
          pts.resize(path.size());
          for (size_t i = 0; i < path.size(); ++i) {
            pts[i].Set(path[i].x, path[i].y);
          }
          click_pathes_.emplace(id, std::move(pts));
        }
      }
    }
  }
}
bool PathLayer::GetDisplayPolyline(CameraPtr camera, const std::vector<Point2d>& polyline,
                                   std::vector<std::vector<Point>>& result) {
  if (polyline.size() < 2) {
    return false;
  }
  auto mbr = GetScreenMbr(camera);
  std::vector<bool> save_flag(polyline.size(), false);
  bool prev_in_screen = false;
  if (mbr.Contains(polyline[0])) {
    save_flag[0] = true;
    prev_in_screen = true;
  }
  bool cur_in_screen = mbr.Contains(polyline[1]);
  if (!save_flag[0]) {
    if (cur_in_screen) {
      save_flag[0] = true;
    } else {
      if (mbr.Intersects(polyline[0], polyline[1])) {
        save_flag[0] = true;
      }
    }
  }

  int32_t pre_idx = 0;
  int32_t cur_idx = 1;
  for (int32_t i = 2; i < polyline.size(); ++i) {
    int32_t nex_idx = i;
    bool next_in_screen = mbr.Contains(polyline[nex_idx]);
    if (mbr.Intersects(polyline[pre_idx], polyline[cur_idx])) {
      save_flag[pre_idx] = true;
      save_flag[cur_idx] = true;
    } else if (cur_in_screen || prev_in_screen || next_in_screen) {
      save_flag[cur_idx] = true;
    } else {
    }

    prev_in_screen = cur_in_screen;
    cur_in_screen = next_in_screen;
    pre_idx = cur_idx;
    cur_idx = nex_idx;
  }
  if (mbr.Intersects(polyline[pre_idx], polyline[cur_idx])) {
    save_flag[pre_idx] = true;
    save_flag[cur_idx] = true;
  } else if (cur_in_screen || prev_in_screen) {
    save_flag[pre_idx] = true;
    save_flag[cur_idx] = true;
  } else {
  }

  std::vector<Point> path;
  path.reserve(64);
  for (int32_t i = 0; i < save_flag.size(); ++i) {
    if (save_flag[i]) {
      SavePoint(camera, polyline[i], path);
    } else {
      if (!path.empty()) {
        result.push_back(path);
        path.clear();
      }
    }
  }
  if (!path.empty()) {
    result.push_back(path);
  }
  return true;
}

void PathLayer::SavePoint(CameraPtr camera, const Point2d& point, std::vector<Point>& result) {
  double scrn_x = 0;
  double scrn_y = 0;
  camera->World2Screen(point.x(), point.y(), scrn_x, scrn_y);
  Point p(scrn_x, scrn_y);
  result.push_back(p);
}

AABB2<Point2d> PathLayer::GetScreenMbr(CameraPtr camera) {
  uint32_t w = 0, h = 0;
  int32_t delta = 10;
  camera->GetViewSize(w, h);
  double x[4] = {0.0};
  double y[4] = {0.0};
  camera->Screen2World(-delta, -delta, x[0], y[0]);
  camera->Screen2World(-delta, h + delta, x[1], y[1]);
  camera->Screen2World(w + delta, -delta, x[2], y[2]);
  camera->Screen2World(w + delta, h + delta, x[3], y[3]);
  double min_x = x[0];
  double min_y = y[0];
  double max_x = x[0];
  double max_y = y[0];
  for (uint32_t i = 1; i < 4; ++i) {
    if (min_x > x[i]) {
      min_x = x[i];
    } else if (max_x < x[i]) {
      max_x = x[i];
    } else {
    }
    if (min_y > y[i]) {
      min_y = y[i];
    } else if (max_y < y[i]) {
      max_y = y[i];
    } else {
    }
  }

  return AABB2<Point2d>(min_x, min_y, max_x, max_y);
}

bool PathLayer::OnClicked(int32_t x, int32_t y) {
  std::lock_guard<std::mutex> lock(path_mtx_);
  if (ClickPath(x, y, click_pathes_[PathManager::Instance().GetMainRouteID()])) {
    printf("OnClicked path: %d", PathManager::Instance().GetMainRouteID());
    return true;
  }
  for (auto itr = click_pathes_.begin(); itr != click_pathes_.end(); ++itr) {
    if (itr->first != PathManager::Instance().GetMainRouteID()) {
      if (ClickPath(x, y, itr->second)) {
        printf("OnClicked path: %d", itr->first);
        return true;
      }
    }
  }
  return false;
}

bool PathLayer::ClickPath(int32_t x, int32_t y, const std::vector<Point2d>& path) {
  if (path.size() < 2) {
    return false;
  }
  Point2d touch_point(x, y);
  auto proj = touch_point.ClosestPoint(path);
  return std::get<2>(proj) <= 10;
}
}  // namespace aurora
