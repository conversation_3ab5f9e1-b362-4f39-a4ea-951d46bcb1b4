/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file render_layer_builder.h
 * @brief Declaration file of class RenderLayerBuilder.
 * @attention used for C/C++ only.
 */

 #ifndef MAP_SRC_RENDER_LAYER_BUILDER_H_
 #define MAP_SRC_RENDER_LAYER_BUILDER_H_

 #include "util_basic_type.h"
 #include "map_render_backend.h"
 #include <cstdint>

 namespace aurora {

 namespace parser{
 class DisplayTileReader;
 
 }
 
 class MapRenderThemeIF;
 struct BuildingData;

 /**
 *
 * RenderLayerBuilder
 *
 */
 class RenderLayerBuilder 
 {
public:     
   RenderLayerBuilder();
   ~RenderLayerBuilder() {}   

   void BuildRenderLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme,  uint32_t request_logic_level);

  private:
   void BuildRoadLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level);
   void BuildAreaLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level);
   void BuildPointLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level);
   std::vector<BuildingData> BuildBuildingLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level);
   void BuildPolineLayer(parser::DisplayTileReader& reader, MapRenderThemeIF* theme, BackendPtr backend, uint32_t request_logic_level);
   void DrawTileID(const TileID& id, BackendPtr backend, float scale);
 };
     
 } //namespace 
 
 #endif // MAP_SRC_RENDER_LAYER_BUILDER_H_
 /* EOF */