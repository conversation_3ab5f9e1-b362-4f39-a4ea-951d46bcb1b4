/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "render_raster_tile.h"
#include "gl_texture2d.h"
#include "gpu_shader_cache.h"
#include "util_matrix4.h"
#include "display_data/display_data_def.h"

namespace aurora {

    namespace {
        // Define index data (two triangles)
        static const uint32_t kIndices[] = {
            0, 1, 2,
            2, 3, 0
        };
    }

    RasterTile::RasterTile() 
    {

    }   

    RasterTile::~RasterTile()
    {
        Destory();
    }

    bool RasterTile::Init(const void* pixeldata, TextureFormat format, uint32_t w, uint32_t h)
    {
        texture_ = std::make_shared<GLTexture2D>();
        if (!texture_->Set(pixeldata, format, w, h)) { 
            return false;
        }

        if(!vao_.GenBuffer()) {
            return false;
        }

        if(!vbo_.GenBuffer()) {
            return false;
        }

        if(!ebo_.GenBuffer()) {
            return false;
        }


        program_ = GpuShaderCache::Instance().GetProgram(glshader::PROGRAM_TILE);
        if (program_ == nullptr) {
            printf("error: tile gpu program null \n");
            program_ = std::make_shared<GPUProgram>();
            program_->CreateProgram(glshader::kVertexDefault, glshader::kFragmentDefault);
        }

        // texture_.GenerateMipmap();
        
        return true;
    }

    void RasterTile::UpdateTileBuffer(const void* pixeldata, TextureFormat format, uint32_t w, uint32_t h)
    {
        if (texture_ && texture_->IsValid()) { 
            texture_->Upload(pixeldata, format, w, h);
        }
    }

    bool RasterTile::IsValid()
    {   
        if (!texture_
            || !texture_->IsValid()
            || !program_->IsValid()
            || !vao_.IsValid()
            || !vbo_.IsValid() 
            || !ebo_.IsValid())
        {
            return false;
        }

        return true;
    }

    void RasterTile::Destory()
    {
        if (texture_) {
            texture_->Release();
        }
        vao_.Release();
        vbo_.Release();
        ebo_.Release();

    }

    void RasterTile::Render(CameraPtr camera, const TimePoint& now )
    {
        if (texture_) {
            texture_->Bind();
        }

        vao_.Bind();
        vbo_.Bind();
        ebo_.Bind();
        program_->UseProgram();
        // TODO: 后续优化，不用每次设置，目前打开会在mapviewer里出现随机不画的情况
        // if (!bInitVertData_) 
        {
            vbo_.SetData(sizeof(vertices_), vertices_, BufferUsageType::USAGE_STATIC_DRAW);
            ebo_.SetData(sizeof(kIndices), kIndices, BufferUsageType::USAGE_STATIC_DRAW);

            GLint pos_attrib = program_->GetAttributeLocation("position");
            vao_.VertexAttribute(pos_attrib, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float), (void*)0);
        
            GLint tex_attrib = program_->GetAttributeLocation("texCoord");
            vao_.VertexAttribute(tex_attrib, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float), (void*)(2 * sizeof(float)));
            
            bInitVertData_ = true;
        }
        
        Matrix4<double> mat4;
        Matrix4<double> mat4_offset = Matrix4<double>::translation(0.0f, 0.0f, 0.0f);
        if (camera != nullptr)
        {
            camera->GetOrthViewMatrix(mat4, now);
            aurora::parser::DisplayTileID dis_tile; 
            dis_tile.Set(id_.z_, id_.y_, id_.x_ );
            // id.z_是比例尺，tileID用的是实际逻辑比例尺
            // camera->GetTilePosMatrix(dis_tile.value, mat4_offset);
            
            
            // mat4 = mat4_offset * mat4;

        }
        // 拿到的double原始矩阵转一下到float
        // 因为glUniformMatrix4fv的参数是float*，所以需要转一下
         // 将 double 类型矩阵转换为 float 类型矩阵
        Matrix4<float> mat4Float;
        for (int i = 0; i < 4; ++i) {
            for (int j = 0; j < 4; ++j) {
                mat4Float.m[i][j] = static_cast<float>(mat4.m[i][j]);
            }
        }
     
        // Vector4<float> vec4(121310912.00,  31197754.00, 10.00, 1.0f);
        // Vector4<float> vec4_offset = mat4Float * vec4;
        // printf("+++++++++++++vec4_offset: %f, %f, %f, %f\n", vec4_offset.x, vec4_offset.y, vec4_offset.z, vec4_offset.w);
        GLint mvpMatrix = program_->GetUniformLocation("u_mvpMatrix");
        glUniformMatrix4fv(mvpMatrix, 1, GL_FALSE, mat4Float.getTranspose().getRawPointer());
        
        glDrawElements(GL_TRIANGLES, 6, GL_UNSIGNED_INT, 0);
        
    }

    void RasterTile::SetTileID(TileID id,  const AABB2<Point2d>& tile_mbr) { 
        id_ = id; 
        tile_mbr_ = tile_mbr;
        float vertices[] = {
        // position(x, y)    texture position (s, t)
        tile_mbr.minx() * kDM5, tile_mbr.miny()* kDM5,    0.0f, 0.0f,
        tile_mbr.maxx()* kDM5, tile_mbr.miny()* kDM5,   1.0f, 0.0f,
        tile_mbr.maxx()* kDM5,  tile_mbr.maxy()* kDM5,  1.0f, 1.0f,
        tile_mbr.minx()* kDM5,  tile_mbr.maxy()* kDM5,  0.0f, 1.0f
        };
        memcpy(vertices_, vertices, sizeof(vertices));
    }

} //namespace 
/* EOF */