/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file render_area_layer.h
 * @brief Declaration file of class RenderAreaLayer.
 * @attention used for C/C++ only.
 */

#ifndef MAPRENDERSERVICE_RENDER_AREA_LAYER_H_
#define MAPRENDERSERVICE_RENDER_AREA_LAYER_H_

#include "map_render_layer.h"

namespace aurora {

/**
* class breif description
*
* RenderAreaLayer
*
*/
class RenderAreaLayer : public RenderLayer
{
public:
    RenderAreaLayer();              
    virtual ~RenderAreaLayer();    

    virtual void ImplRender(uint16_t category, const std::vector<Point>& points);

    virtual void Render();

    void RenderRive();

    void RenderBkArea();

    void RenderGreenSapce();

    //void RenderOthers();

    void RenderBuilding(uint16_t category, const std::vector<Point>& points, float building_floor = 0.0f);

private:
    // MapRenderTheme render_theme_;
    //MapRenderStat* render_state_;
    //MapRenderDataManager map_data_;
    //bool is_active;
    //SkImage*  render_image_;  // cpu data will be submitted to the gpu later
    // MapRenderBackend renderer_;
    //std::vector<MapLayerID> layerIDs_;

};
    
} //namespace 

#endif //MAPRENDERSERVICE_RENDER_AREA_LAYER_H_
/* EOF */