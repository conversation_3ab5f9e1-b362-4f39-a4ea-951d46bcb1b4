/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "render_area_layer.h"
#include "map_render_backend.h"
#include "config_data/cfg_data_def.h"
#include "i_map_render_theme.h"

namespace aurora {

    RenderAreaLayer::RenderAreaLayer()
    {

    }

    RenderAreaLayer::~RenderAreaLayer()
    {

    }

    void RenderAreaLayer::ImplRender(uint16_t category, const std::vector<Point>& points)
    {
        if (backend_ == nullptr) {
            return ;
        }

        parser::ViewObjStyle* obj_style = render_theme_->GetViewObjStyle(category);
        if (backend_ != nullptr && obj_style != nullptr) {
            uint32_t fore_color_value = obj_style->forecolor;
            backend_->SetFillStyle(aurora::STYLE_FILE);
            aurora::Color fore_color;
            fore_color.fromRGB(fore_color_value);
            backend_->SetColor(fore_color);
            backend_->DrawPolygon(points);

            // 为了抗锯齿，画两遍
            backend_->SetAntiAlias(true);
            backend_->DrawPolygon(points);
        }
        else {
            //    printf("area no style: %u\n", category);
        }
    }

    void RenderAreaLayer::Render()
    {

    }


    void RenderAreaLayer::RenderRive()
    {

    }

    void RenderAreaLayer::RenderBkArea()
    {

    }
    
    void RenderAreaLayer::RenderGreenSapce()
    {

    }

    void RenderAreaLayer::RenderBuilding(uint16_t category, const std::vector<Point>& points, float building_floor)
    {
        parser::ViewObjStyle* obj_style = render_theme_->GetViewObjStyle(category);
        if (backend_ != nullptr && obj_style != nullptr) {
            uint32_t fore_color_value = obj_style->forecolor;
            backend_->SetFillStyle(aurora::STYLE_FILE); 
            backend_->SetAntiAlias(true);
            aurora::Color fore_color;
            fore_color.fromRGB(fore_color_value);
            backend_->SetColor(fore_color);

            // 绘制底面（原逻辑）
            backend_->DrawPolygon(points);

            // 优化：斜投影拉伸（X向右偏移，Y向上偏移）
            const float kFloorHeight = 5.0f;    // 单层高度（像素）
            const float kProjectionRatio = 0.3f; // 斜投影X轴偏移比例（0.3表示X偏移为高度的30%）
            uint8_t floor_count = std::floor(building_floor);

            if (floor_count > 0) {
                // 计算顶面点（斜投影：X += 高度*比例，Y += 高度）
                std::vector<Point> top_points;
                for (const auto& pt : points) {
                    top_points.emplace_back(
                        pt.x + kFloorHeight * floor_count * kProjectionRatio, // X向右偏移
                        pt.y + kFloorHeight * floor_count                      // Y向上偏移
                    );
                }

                // 绘制侧面（连接底面和顶面的边）
                backend_->SetColor(Color::Gray); // 侧面颜色（可从样式配置）
                for (size_t i = 0; i < points.size(); ++i) {
                    size_t next_idx = (i + 1) % points.size();
                    std::vector<Point> side = {
                        points[i],
                        points[next_idx],
                        top_points[next_idx],
                        top_points[i]
                    };
                    backend_->DrawPolygon(side); // 绘制侧面四边形
                }

                // 绘制顶面（颜色与底面相同）
                backend_->SetColor(fore_color);
                backend_->DrawPolygon(top_points);
            }
        }
        else {
            //    printf("area no style: %u\n", category);
        }
    }
    
} //namespace 
