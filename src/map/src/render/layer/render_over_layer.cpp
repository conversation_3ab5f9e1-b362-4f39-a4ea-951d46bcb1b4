/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

#include "render_over_layer.h"

#include "annotation/annotation_manager.h"
#include "collision/collision_manager.h"
#include "gl_texture2d.h"
#include "gpu_shader_cache.h"
#include "include/core/SkImage.h"
#include "include/core/SkPixmap.h"
#include "map_render_backend_2d.h"
#include "map_render_backend_gl.h"
#include "path/path_manager.h"
#include "pin_mark/pin_mark_manager.h"
#include "util_matrix4.h"

namespace aurora {
namespace {
// Define vertex data
static const float kVertices[] = {
    // position(x, y)    texture position (s, t)
    -1.f, -1.f, 0.0f, 0.0f, 1.0f,  -1.0f, 1.0f, 0.0f,
    1.0f, 1.0f, 1.0f, 1.0f, -1.0f, 1.0f,  0.0f, 1.0f};

// Define index data (two triangles)
static const uint32_t kIndices[] = {0, 1, 2, 2, 3, 0};
}  // namespace

RenderOverLayer::RenderOverLayer() {}

RenderOverLayer::~RenderOverLayer() {
  if (backend_) {
    backend_->Destory();
  }
}

bool RenderOverLayer::OnClicked(int32_t x, int32_t y) {
  for (int32_t i = kLayerTypeCount - 1; i >= kLayerTypeBegin; --i) {
    LayerBase* layer = layer_factory_.GetLayer((LayerType)i);
    if (layer && layer->OnClicked(x, y)) {
      return true;
    }
  }
  return false;
}

void RenderOverLayer::Update(CameraPtr camera) {
  Matrix4<double> mtx;
  camera->GetOrthViewMatrix(mtx);
  bool need_draw = false;
  if (::memcmp(mtx.getRawPointer(), camera_mtx_.getRawPointer(), sizeof(double) * 16)) {
    need_draw = true;
    camera_mtx_ = mtx;
  }
  uint32_t w = 0;
  uint32_t h = 0;
  camera->GetViewSize(w, h);
  if (w != width_ || h != height_) {
    Release();
    Init(w, h);
    need_draw = true;
  }
  if (backend_ == nullptr) {
    return;
  }

  bool update_path = PathManager::Instance().UpdateDisplayPath(camera);
  bool pin_mark_draw = PinMarkManager::Instance().NeedDraw();

  if (need_draw || update_path || pin_mark_draw) {
    DoCollision(camera);

    backend_->Clear(Color(0));

    for (uint32_t i = kLayerTypeBegin; i < kLayerTypeCount; ++i) {
      LayerBase* layer = layer_factory_.GetLayer((LayerType)i);
      if (layer ) {
        layer->Update(camera);
      }
    }

    sk_sp<SkImage> img = ((RenderBackend2D*)(backend_.get()))->MakeImageSnapshot();
    SkPixmap pixmap;

    if (texture_ == nullptr) {
      texture_ = std::make_shared<GLTexture2D>();
      if (img) {
        img->peekPixels(&pixmap);
        texture_->Set(pixmap.addr(), aurora::FORMAT_RGBA, pixmap.width(), pixmap.height());
      }
    } else {
      if (img) {
        img->peekPixels(&pixmap);
        texture_->Upload(pixmap.addr(), aurora::FORMAT_RGBA, pixmap.width(), pixmap.height());
      }
    }
  }
}

void RenderOverLayer::Release() {
  vao_.Release();
  vbo_.Release();
  ebo_.Release();

  if (texture_) {
    texture_->Release();
    texture_ = nullptr;
  }
  if (backend_) {
    backend_->Destory();
    backend_ = nullptr;
  }
}

void RenderOverLayer::DoCollision(CameraPtr camera) {
  CollisionManager::Instance().ClearCollisions(0);
  uint32_t w = 0;
  uint32_t h = 0;
  camera->GetViewSize(w, h);
  CollisionManager::Instance().UpdateScreen(w, h);

  auto& tiles = AnnotationManager::Instance().GetTileData();
  for (auto tile : tiles) {
    CollisionManager::Instance().AddCollisionPoint(0, tile->GetPointCollistion());
    CollisionManager::Instance().AddCollisionRoadName(0, tile->GetRoadNameCollistion());
  }
  CollisionManager::Instance().DoCollistion(camera);
}

bool RenderOverLayer::Init(uint32_t w, uint32_t h) {
  width_ = w;
  height_ = h;
  backend_ = std::make_shared<RenderBackend2D>();
  backend_->Init(w, h, Color(0));

  for (uint32_t i = kLayerTypeBegin; i < kLayerTypeCount; ++i) {
    LayerBase* layer = layer_factory_.GetLayer((LayerType)i);
    if (layer ) {
      layer->SetBackend(backend_.get());
    }
  }

  if (!vao_.GenBuffer()) {
    return false;
  }

  if (!vbo_.GenBuffer()) {
    return false;
  }

  if (!ebo_.GenBuffer()) {
    return false;
  }

  program_ = GpuShaderCache::Instance().GetProgram(glshader::PROGRAM_OVERLAYER);
  if (program_ == nullptr) {
    return false;
  }
  program_->UseProgram();

  vao_.Bind();
  vbo_.Bind();
  vbo_.SetData(sizeof(kVertices), kVertices, BufferUsageType::USAGE_STATIC_DRAW);
  ebo_.Bind();
  ebo_.SetData(sizeof(kIndices), kIndices, BufferUsageType::USAGE_STATIC_DRAW);

  vbo_.SetData(sizeof(kVertices), kVertices, BufferUsageType::USAGE_STATIC_DRAW);
  ebo_.SetData(sizeof(kIndices), kIndices, BufferUsageType::USAGE_STATIC_DRAW);

  GLint pos_attrib = program_->GetAttributeLocation("position");
  vao_.VertexAttribute(pos_attrib, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float), (void*)0);

  GLint tex_attrib = program_->GetAttributeLocation("texCoord");
  vao_.VertexAttribute(tex_attrib, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float),
                       (void*)(2 * sizeof(float)));

  return true;
}

void RenderOverLayer::Render() {
  if (program_ == nullptr) {
    return;
  }
  if (texture_) {
    texture_->Bind();
  }

  // enable alpha blend
  glEnable(GL_BLEND);
  glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

  program_->UseProgram();
  vao_.Bind();
  vbo_.Bind();
  vbo_.SetData(sizeof(kVertices), kVertices, BufferUsageType::USAGE_STATIC_DRAW);
  ebo_.Bind();
  ebo_.SetData(sizeof(kIndices), kIndices, BufferUsageType::USAGE_STATIC_DRAW);

  vbo_.SetData(sizeof(kVertices), kVertices, BufferUsageType::USAGE_STATIC_DRAW);
  ebo_.SetData(sizeof(kIndices), kIndices, BufferUsageType::USAGE_STATIC_DRAW);

  GLint pos_attrib = program_->GetAttributeLocation("position");
  vao_.VertexAttribute(pos_attrib, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float), (void*)0);

  GLint tex_attrib = program_->GetAttributeLocation("texCoord");
  vao_.VertexAttribute(tex_attrib, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float),
                       (void*)(2 * sizeof(float)));

  Matrix4<float> mat4Float;
  GLint mvpMatrix = program_->GetUniformLocation("u_mvpMatrix");
  glUniformMatrix4fv(mvpMatrix, 1, GL_FALSE, mat4Float.getTranspose().getRawPointer());
  glDrawElements(GL_TRIANGLES, 6, GL_UNSIGNED_INT, 0);

  glDisable(GL_BLEND);
}

}  // namespace aurora
