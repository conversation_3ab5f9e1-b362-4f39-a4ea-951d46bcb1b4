/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file render_building_layer.h
 * @brief Declaration file of class RenderBuildingLayer.
 * @attention used for C/C++ only.
 */

#ifndef MAP_SRC_RENDER_LAY_RENDER_BUILDING_LAYER_H_
#define MAP_SRC_RENDER_LAY_RENDER_BUILDING_LAYER_H_

#include <cstdint>

#include "gl_ebo.h"
#include "gl_texture.h"
#include "gl_vao.h"
#include "gl_vbo.h"
#include "gpu_program.h"

#include "overlay/building_layer.h"
#include "map_render_layer.h"

namespace aurora {
class MapRenderBackend;

/**
 *
 * RenderBuildingLayer
 *
 */
class RenderBuildingLayer : public RenderLayer
  {
public:
  RenderBuildingLayer();
  ~RenderBuildingLayer();


  void AddBuildings(const std::vector<BuildingData> & buildings);
  void ClearBuildings();

  void Update(CameraPtr camera);

  void Release();

  virtual void Render(CameraPtr camera);

private:
  bool Init(uint32_t w, uint32_t h);

  GLEBO ebo_{};
  GLVBO vbo_{};
  GLVAO vao_{};

  GLTexturePtr building_texture_{nullptr}; 
  GPUProgramPtr building_shader_ = nullptr;    
  BuildingLayer building_layer_;

  GLuint default_texture_id_ = 0;  // building 纯色纹理ID


  uint32_t width_{0};
  uint32_t height_{0};
  Matrix4<double> camera_mtx_;
};

}  // namespace aurora

#endif  // MAP_SRC_RENDER_LAY_RENDER_BUILDING_LAYER_H_
