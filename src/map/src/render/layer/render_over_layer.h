/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file render_over_layer.h
 * @brief Declaration file of class RenderOverLayer.
 * @attention used for C/C++ only.
 */

#ifndef MAP_SRC_RENDER_LAY_RENDER_OVER_LAYER_H_
#define MAP_SRC_RENDER_LAY_RENDER_OVER_LAYER_H_

#include <cstdint>

#include "collision/collision_detector.h"
#include "gl_ebo.h"
#include "gl_texture.h"
#include "gl_vao.h"
#include "gl_vbo.h"
#include "gpu_program.h"
#include "overlay/layer_factory.h"


namespace aurora {
class RenderBackend2D;

/**
 *
 * RenderOverLayer
 *
 */
class RenderOverLayer {
public:
  RenderOverLayer();

  ~RenderOverLayer();

  bool OnClicked(int32_t x, int32_t y);



  void Update(CameraPtr camera);

  void Release();

  void Render();

private:
  void DoCollision(CameraPtr camera);
  bool Init(uint32_t w, uint32_t h);

  GLEBO ebo_{};
  GLVBO vbo_{};
  GLVAO vao_{};

  GPUProgramPtr program_{nullptr};
  GLTexturePtr texture_{nullptr};
  std::shared_ptr<RenderBackend2D> backend_{nullptr};
  LayerFactory layer_factory_;

  uint32_t width_{0};
  uint32_t height_{0};
  Matrix4<double> camera_mtx_;
};

}  // namespace aurora

#endif  // MAP_SRC_RENDER_LAY_RENDER_OVER_LAYER_H_
