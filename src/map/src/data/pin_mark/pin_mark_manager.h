#ifndef MAP_DATA_PIN_MARK_PIN_MARK_MANAGER_H
#define MAP_DATA_PIN_MARK_PIN_MARK_MANAGER_H

#include <atomic>
#include <cstdint>
#include <map>
#include <mutex>

#include "collision/collision_pin_mark.h"
#include "mark/mark_def.h"

namespace aurora {

class PinMarkManager {
public:
  static PinMarkManager& Instance() {
    static PinMarkManager instance;
    return instance;
  }
  PinMarkManager();

  int32_t Add(uint16_t layer, UserMarkInfoPtr mark, Point2d lnglat, float degree,
              const char* str = nullptr);

  void Update(uint16_t layer, int32_t id, Point2d lnglat, float degree);

  void Update(uint16_t layer, int32_t id, UserMarkInfoPtr mark);

  CollisionPinMarkSet GetPinMarks(uint16_t layer);

  std::map<uint16_t, CollisionPinMarkSet> GetAllPinMarks();

  void Delete(uint16_t layer, int32_t id);

  void Clear(uint16_t layer);

  void ClearAll();

  bool NeedDraw();

private:
  void SetDirty();

  CollisionPinMarkPtr GetPinMark(uint16_t layer, int32_t id);

  std::atomic<bool> dirty_flag_;
  std::map<uint16_t, CollisionPinMarkSet> collisions_;
  std::mutex mark_mtx_;
};
}  // namespace aurora
#endif  // MAP_DATA_PIN_MARK_PIN_MARK_MANAGER_H
