#ifndef MAP_DATA_MARK_MARK_MANAGER_H
#define MAP_DATA_MARK_MARK_MANAGER_H

#include <cstdint>
#include <mutex>
#include <vector>

#include "mark/mark_def.h"

namespace aurora {

class MarkManager {
public:
  static MarkManager& Instance() {
    static MarkManager instance;
    return instance;
  }
  MarkManager();
  bool Init(const char* file);
  ImageHolderPtr& GetResHolder() { return res_holder_; }
  int32_t SaveDrawMarkCommand(uint16_t type, const std::string& file, Point2d& anchor,
                              Point2d& lnglat, float degree);
  void UpdateDrawMarkCommand(uint16_t type, int32_t id, const std::string& file, Point2d& anchor,
                             Point2d& lnglat, float degree);
  void ClearCommand(uint16_t type, int32_t id);
  bool MarkCmdChanged();

  DrawMarkCommandCache GetAllMarkCommand() {
    std::lock_guard<std::mutex> lock(mark_mtx_);
    return command_cache_;
  }
  bool ReadMarkFromFile(const char* file, std::vector<uint8_t>& buf, int32_t& w, int32_t& h);
  
private:
  UserMarkInfoPtr GetUserMarkFromCache(const std::string& file, Point2d& anchor);
  

  bool mark_cmd_changed_;
  ImageHolderPtr res_holder_;
  UserMarkCache mark_cache_;
  DrawMarkCommandCache command_cache_;
  std::mutex mark_mtx_;
};
}  // namespace aurora
#endif  // DATA_MARK_MARK_MANAGER_H
