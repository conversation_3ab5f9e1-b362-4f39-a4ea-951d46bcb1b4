/**
 * Copyright @ 2025
 * All Rights Reserved.
 */

/**
 * @file render_data_cache_lru.h
 * @brief Declaration file of class RenderDataCacheLRU.
 * @attention used for C/C++ only.
 */

#ifndef MAPRENDER_DATA_CACHE_LRU_H_
#define MAPRENDER_DATA_CACHE_LRU_H_

#include "render_data_cahce_if.h"
#include <list>
#include <unordered_map>
#include <mutex>
#include <vector>

namespace aurora {
class RasterTile;
class CollisionTile;
struct BuildingData;

struct CacheTile {
    std::shared_ptr<RasterTile> raster_tile;
    std::shared_ptr<CollisionTile> collision_tile;
    std::vector<BuildingData> building_list;
};
using CacheTilePtr = std::shared_ptr<CacheTile>;

class RenderDataCacheLRU : public RenderDataCacheIF {
public:
    // 修改构造函数：支持分别设置active和preload的容量
    explicit RenderDataCacheLRU(size_t active_capacity, size_t preload_capacity);

    bool GetRenderTileData() noexcept override;
    bool GetRenderTileDataById(const TileID& id, CacheTilePtr& tile_data) noexcept override;
    bool GetRenderData() noexcept override;
    bool GetRenderDataById(const TileID& id) noexcept override;
    bool IsValid() const noexcept override;
    void AddCache() noexcept override;
    // 修改：根据add_to_active参数选择目标缓存
    void AddRenderTileCache(const TileID& id, const CacheTilePtr& tile_data, bool add_to_active = true) noexcept override;
    void RemoveCache(const TileID& id) noexcept override;

private:
    // 定义LRU缓存的内部结构（增加互斥锁）
    struct LRUCache {
        size_t capacity;
        std::list<std::pair<TileID, CacheTilePtr>> list;  // 存储顺序（头部最新）
        std::unordered_map<TileID, decltype(list)::iterator, TileIDHashFunc> map;  // 快速查找
        std::mutex mutex;  // 新增：保护当前缓存的互斥锁

        explicit LRUCache(size_t cap) : capacity(cap) {}
    };

    LRUCache active_cache_;   // 当前渲染数据缓存
    LRUCache preload_cache_;  // 预加载数据缓存

    // 辅助函数：将指定缓存中的元素移动到列表头部（标记为最近使用）
    void MoveToFront(LRUCache& cache, const TileID& id);
    // 辅助函数：当缓存满时淘汰最久未使用的元素
    void EvictIfNeeded(LRUCache& cache);
};

} // namespace aurora

#endif // MAPRENDER_DATA_CACHE_LRU_H_
/* EOF */
