#include "path/display_path.h"

namespace aurora {

// 计算点到直线的距离平方
double DistanceSquaredToLine(const Point2d& p, const Point2d& a, const Point2d& b) {
  double abX = b.x() - a.x();
  double abY = b.y() - a.y();
  double apX = p.x() - a.x();
  double apY = p.y() - a.y();

  // 向量叉积绝对值除以向量长度
  double cross = abX * apY - abY * apX;
  double abLengthSquared = abX * abX + abY * abY;

  if (std::abs(abLengthSquared) < std::numeric_limits<double>::epsilon()) {
    // 起点和终点重合时，返回点到起点的距离平方
    return apX * apX + apY * apY;
  }

  return cross * cross / abLengthSquared;
}

// Douglas-Peucker算法递归实现
void DouglasPeucker(const std::vector<Point2d>& points, int start, int end, double threshold,
                    std::vector<bool>& toKeep) {
  if (end - start <= 1) return;  // 仅保留端点

  double maxDistance = 0.0;
  int index = start;

  // 寻找距离直线AB最大的点
  for (int i = start + 1; i < end; ++i) {
    double d = DistanceSquaredToLine(points[i], points[start], points[end]);
    if (d > maxDistance) {
      maxDistance = d;
      index = i;
    }
  }

  // 如果最大距离大于阈值，则保留该点并递归处理
  if (maxDistance > threshold) {
    toKeep[index] = true;
    DouglasPeucker(points, start, index, threshold, toKeep);
    DouglasPeucker(points, index, end, threshold, toKeep);
  }
}

DisplayPath::DisplayPath(PathType type, const std::vector<Point2d>& points) : type_(type) {
  path_points_.resize(points.size());
  ::memcpy(path_points_.data(), points.data(), points.size() * sizeof(Point2d));
}

void DisplayPath::SimplifyPath(double threshold) {
  if (path_points_.size() <= 2) {
    simplified_points_ = path_points_;
    return;
  }

  std::vector<bool> toKeep(path_points_.size(), false);
  toKeep[0] = true;
  toKeep.back() = true;
  DouglasPeucker(path_points_, 0, static_cast<int>(path_points_.size()) - 1, threshold, toKeep);

  std::vector<Point2d> simplified_points;
  simplified_points.reserve(toKeep.size());
  for (size_t i = 0; i < toKeep.size(); ++i) {
    if (toKeep[i]) {
      simplified_points.push_back(path_points_[i]);
    }
  }

  simplified_points_.resize(simplified_points.size());
  ::memcpy(simplified_points_.data(), simplified_points.data(),
           simplified_points.size() * sizeof(Point2d));
}

const std::vector<Point2d>& DisplayPath::GetSimplifiedPoints() { return simplified_points_; }

PathType DisplayPath::GetPathType() { return type_; }

DisplayPolyline::DisplayPolyline(const std::vector<Point2d>& points)
    : DisplayPath(kPathTypePolyline, points) {}

void DisplayPolyline::SetLineWidth(int32_t width) { line_width_ = width; }

void DisplayPolyline::SetColor(uint32_t color) { forecolor_ = color; }

int32_t DisplayPolyline::GetLineWidth() { return line_width_; }

uint32_t DisplayPolyline::GetForecolor() { return forecolor_; }

DisplayPolygon::DisplayPolygon(const std::vector<Point2d>& points)
    : DisplayPath(kPathTypePolygon, points) {}

void DisplayPolygon::SetColor(uint32_t color) { color_ = color; }

uint32_t DisplayPolygon::Getcolor() { return color_; }
}  // namespace aurora
