#ifndef MAP_DATA_PATH_PATH_MANAGER_H
#define MAP_DATA_PATH_PATH_MANAGER_H

#include <cstdint>
#include <map>
#include <mutex>

#include "map_render_camera_if.h"
#include "path/display_path.h"
#include "point2.h"
#include "render_geometry_type.h"

namespace aurora {
class PathManager {
public:
  static PathManager& Instance() {
    static PathManager instance;
    return instance;
  }

  int32_t GetMainRouteID();
  void SetMainRouteID(int32_t id);
  int32_t GetPassedRouteID();
  void SetPassedRouteID(int32_t id);
  void SetRoute(int32_t id, const std::vector<Point2d>& points);
  void ClearRoute(int32_t id);
  void ClearAllRoute();
  DisplayPolylinePtr GetRoute(int32_t id);
  std::vector<int32_t> GetAllPathID();
  bool UpdateDisplayPath(CameraPtr camera);

  int32_t SetUserPolyline(uint16_t layer, const std::vector<Point2d>& points);
  std::vector<DisplayPolylinePtr> GetUserPolyline(uint16_t layer);
  void DeletePolyline(uint16_t layer, int32_t id);
  void ClearPolyline(uint16_t layer);
  int32_t SetUserPolygon(uint16_t layer, const std::vector<Point2d>& points);
  std::vector<DisplayPolygonPtr> GetUserPolygon(uint16_t layer);
  void DeletePolygon(uint16_t layer, int32_t id);
  void ClearPolygon(uint16_t layer);

private:
  PathManager() {}
  void SimplifyPath(double threshold);
  double CalcThreshold(CameraPtr camera);
  void MarkDraw() { need_draw_ = true; }
  void SetMainRouteDrawStyle(DisplayPolylinePtr ptr);
  void SetPassedRouteDrawStyle(DisplayPolylinePtr ptr);
  void SetRouteDrawStyle(DisplayPolylinePtr ptr);

  std::map<int32_t, DisplayPolylinePtr> route_set_;
  std::map<uint16_t, std::vector<DisplayPolylinePtr>> user_polyline_;
  std::map<uint16_t, std::vector<DisplayPolygonPtr>> user_polygon_;
  uint32_t logic_level_{0};
  int32_t main_route_id_{0};
  int32_t passed_route_id_{-1};
  bool need_draw_{false};
  std::mutex route_mtx_;
  std::mutex polyline_mtx_;
  std::mutex polygon_mtx_;
};
}  // namespace aurora
#endif  // MAP_DATA_PATH_PATH_MANAGER_H
