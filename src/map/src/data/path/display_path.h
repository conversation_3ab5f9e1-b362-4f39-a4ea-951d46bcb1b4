#ifndef MAP_DATA_PATH_DISPLAY_PATH_H
#define MAP_DATA_PATH_DISPLAY_PATH_H

#include <cstdint>
#include <vector>
#include <memory>

#include "point2.h"

namespace aurora {
enum PathType {
  kPathTypePolyline = 0,
  kPathTypePolygon,
};

class DisplayPath {
public:
  DisplayPath(PathType type, const std::vector<Point2d>& points);
  void SimplifyPath(double threshold);
  const std::vector<Point2d>& GetSimplifiedPoints();
  PathType GetPathType();

private:
  std::vector<Point2d> path_points_;
  std::vector<Point2d> simplified_points_;
  PathType type_;
};

class DisplayPolyline : public DisplayPath {
public:
  DisplayPolyline(const std::vector<Point2d>& points);
  void SetLineWidth(int32_t width);
  void SetColor(uint32_t color);
  int32_t GetLineWidth();
  uint32_t GetForecolor();
private:
  int32_t line_width_;
  uint32_t forecolor_;
};

using DisplayPolylinePtr = std::shared_ptr<DisplayPolyline>;

class DisplayPolygon : public DisplayPath {
public:
  DisplayPolygon(const std::vector<Point2d>& points);
  void SetColor(uint32_t color);
  uint32_t Getcolor();
private:
  uint32_t color_;
};

using DisplayPolygonPtr = std::shared_ptr<DisplayPolygon>;

}  // namespace aurora
#endif  // MAP_DATA_PATH_DISPLAY_PATH_H
