#include "path/path_manager.h"

#include <algorithm>
#include <cmath>
#include <limits>

namespace aurora {

int32_t PathManager::GetMainRouteID() { return main_route_id_; }

void PathManager::SetMainRouteID(int32_t id) {
  SetRouteDrawStyle(GetRoute(main_route_id_));
  SetMainRouteDrawStyle(GetRoute(id));
  main_route_id_ = id;
  MarkDraw();
}
int32_t PathManager::GetPassedRouteID() { return passed_route_id_; }

void PathManager::SetPassedRouteID(int32_t id) {
  SetRouteDrawStyle(GetRoute(passed_route_id_));
  SetPassedRouteDrawStyle(GetRoute(id));
  passed_route_id_ = id;
  MarkDraw();
}

void PathManager::SetRoute(int32_t id, const std::vector<Point2d>& points) {
  DisplayPolylinePtr route = std::make_shared<DisplayPolyline>(points);

  if (id == passed_route_id_) {
    SetPassedRouteDrawStyle(route);
  } else if (id == main_route_id_) {
    SetMainRouteDrawStyle(route);
  } else {
    SetRouteDrawStyle(route);
  }

  std::lock_guard<std::mutex> lock(route_mtx_);
  auto result = route_set_.emplace(id, route);
  if (!result.second) {
    result.first->second = route;
  }
  logic_level_ = 0;
  MarkDraw();
}

void PathManager::ClearRoute(int32_t id) {
  std::lock_guard<std::mutex> lock(route_mtx_);
  auto itr = route_set_.find(id);
  if (itr != route_set_.end()) {
    route_set_.erase(itr);
  }
  MarkDraw();
}

void PathManager::ClearAllRoute() {
  std::lock_guard<std::mutex> lock(route_mtx_);
  route_set_.clear();
  main_route_id_ = 0;
  passed_route_id_ = -1;
  MarkDraw();
}

DisplayPolylinePtr PathManager::GetRoute(int32_t id) {
  std::lock_guard<std::mutex> lock(route_mtx_);
  auto itr = route_set_.find(id);
  if (itr == route_set_.end()) {
    return nullptr;
  }
  return itr->second;
}

std::vector<int32_t> PathManager::GetAllPathID() {
  std::vector<int32_t> result;
  result.reserve(route_set_.size());
  std::lock_guard<std::mutex> lock(route_mtx_);
  for (auto itr = route_set_.begin(); itr != route_set_.end(); ++itr) {
    result.push_back(itr->first);
  }
  return result;
}

bool PathManager::UpdateDisplayPath(CameraPtr camera) {
  if (camera == nullptr) {
    return false;
  }
  uint32_t logic_level = camera->GetMapScale();
  bool need_simplify = false;
  {
    std::lock_guard<std::mutex> lock(route_mtx_);
    if (route_set_.empty()) {
      return false;
    }
    if (logic_level != logic_level_) {
      logic_level_ = logic_level;
      need_simplify = true;
    }
  }
  if (need_simplify) {
    logic_level_ = logic_level;
    double threshold = CalcThreshold(camera);
    SimplifyPath(threshold);
    need_draw_ = false;
    return true;
  }
  if (need_draw_) {
    need_draw_ = false;
    return true;
  }
  return false;
}

int32_t PathManager::SetUserPolyline(uint16_t layer, const std::vector<Point2d>& points) {
  DisplayPolylinePtr ptr = std::make_shared<DisplayPolyline>(points);

  std::lock_guard<std::mutex> lock(polyline_mtx_);
  auto itr = user_polyline_.emplace(layer, std::vector<DisplayPolylinePtr>());
  int32_t id = itr.first->second.size();
  itr.first->second.push_back(ptr);
  MarkDraw();
  return id;
}

std::vector<DisplayPolylinePtr> PathManager::GetUserPolyline(uint16_t layer) {
  std::lock_guard<std::mutex> lock(polyline_mtx_);
  auto itr = user_polyline_.find(layer);
  if (itr != user_polyline_.end()) {
    return itr->second;
  }
  return std::vector<DisplayPolylinePtr>();
}

void PathManager::DeletePolyline(uint16_t layer, int32_t id) {
  std::lock_guard<std::mutex> lock(polyline_mtx_);
  auto itr = user_polyline_.find(layer);
  if (itr == user_polyline_.end()) {
    return;
  }
  auto& set = itr->second;
  if (id < 0) {
    set.clear();
  } else if (id < set.size()) {
    set[id] = nullptr;
  } else {
  }
  MarkDraw();
}

void PathManager::ClearPolyline(uint16_t layer) {
  std::lock_guard<std::mutex> lock(polyline_mtx_);
  auto itr = user_polyline_.find(layer);
  if (itr == user_polyline_.end()) {
    return;
  }
  user_polyline_.erase(itr);
  MarkDraw();
}

int32_t PathManager::SetUserPolygon(uint16_t layer, const std::vector<Point2d>& points) {
  DisplayPolygonPtr ptr = std::make_shared<DisplayPolygon>(points);

  std::lock_guard<std::mutex> lock(polygon_mtx_);
  auto itr = user_polygon_.emplace(layer, std::vector<DisplayPolygonPtr>());
  int32_t id = itr.first->second.size();
  itr.first->second.push_back(ptr);
  MarkDraw();
  return id;
}

std::vector<DisplayPolygonPtr> PathManager::GetUserPolygon(uint16_t layer) {
  std::lock_guard<std::mutex> lock(polygon_mtx_);
  auto itr = user_polygon_.find(layer);
  if (itr != user_polygon_.end()) {
    return itr->second;
  }
  return std::vector<DisplayPolygonPtr>();
}

void PathManager::DeletePolygon(uint16_t layer, int32_t id) {
  std::lock_guard<std::mutex> lock(polygon_mtx_);
  auto itr = user_polygon_.find(layer);
  if (itr == user_polygon_.end()) {
    return;
  }
  auto& set = itr->second;
  if (id < 0) {
    set.clear();
  } else if (id < set.size()) {
    set[id] = nullptr;
  } else {
  }
  MarkDraw();
}

void PathManager::ClearPolygon(uint16_t layer) {
  std::lock_guard<std::mutex> lock(polygon_mtx_);
  auto itr = user_polygon_.find(layer);
  if (itr == user_polygon_.end()) {
    return;
  }
  user_polygon_.erase(itr);
  MarkDraw();
}

double PathManager::CalcThreshold(CameraPtr camera) {
  uint32_t w = 0;
  uint32_t h = 0;
  camera->GetViewSize(w, h);
  double scrn_x1 = w / 2;
  double scrn_y1 = h / 2;
  double scrn_x2 = scrn_x1 + 1;
  double scrn_y2 = h / 2 + 1;
  double lng1 = 0;
  double lat1 = 0;
  double lng2 = 0;
  double lat2 = 0;
  camera->Screen2World(scrn_x1, scrn_y1, lng1, lat1);
  camera->Screen2World(scrn_x2, scrn_y2, lng2, lat2);
  double delta_lng = lng2 - lng1;
  double delta_lat = lat2 - lat1;
  return delta_lng * delta_lng + delta_lat * delta_lat;
}

void PathManager::SetMainRouteDrawStyle(DisplayPolylinePtr ptr) {
  if (ptr) {
    ptr->SetLineWidth(10);
    ptr->SetColor(0x2385FF);
  }
}

void PathManager::SetPassedRouteDrawStyle(DisplayPolylinePtr ptr) {
  if (ptr) {
    ptr->SetLineWidth(10);
    ptr->SetColor(0x9DC9FF);
  }
}

void PathManager::SetRouteDrawStyle(DisplayPolylinePtr ptr) {
  if (ptr) {
    ptr->SetLineWidth(10);
    ptr->SetColor(0x9DC9FF);
  }
}

void PathManager::SimplifyPath(double threshold) {
  std::map<int32_t, DisplayPolylinePtr> route_set;
  {
    std::lock_guard<std::mutex> lock(route_mtx_);
    route_set = route_set_;
  }

  for (auto itr = route_set.begin(); itr != route_set.end(); ++itr) {
    itr->second->SimplifyPath(threshold);
  }

  std::map<uint16_t, std::vector<DisplayPolylinePtr>> user_polyline;
  {
    std::lock_guard<std::mutex> lock(polyline_mtx_);
    user_polyline = user_polyline_;
  }
  for (auto itr = user_polyline.begin(); itr != user_polyline.end(); ++itr) {
    for (auto it : itr->second) {
      if (it) {
        it->SimplifyPath(threshold);
      }
    }
  }

  std::map<uint16_t, std::vector<DisplayPolygonPtr>> user_polygon;
  {
    std::lock_guard<std::mutex> lock(polygon_mtx_);
    user_polygon = user_polygon_;
  }
  for (auto itr = user_polygon.begin(); itr != user_polygon.end(); ++itr) {
    for (auto it : itr->second) {
      if (it) {
        it->SimplifyPath(threshold);
      }
    }
  }
}

}  // namespace aurora
