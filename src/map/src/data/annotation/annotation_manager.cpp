#include "annotation/annotation_manager.h"

#include <cstdio>
#include <iostream>
#include <string>

namespace aurora {
AnnotationManager::AnnotationManager() : tiles_() { tiles_.reserve(512); }

void AnnotationManager::AddTileData(CollisionTilePtr tile) { tiles_.push_back(tile); }

void AnnotationManager::Clear() { tiles_.clear(); }

std::vector<CollisionTilePtr>& AnnotationManager::GetTileData() { return tiles_; }

}  // namespace aurora
