#ifndef MAP_DATA_ANNOTATION_ANNOTATION_MANAGER_H
#define MAP_DATA_ANNOTATION_ANNOTATION_MANAGER_H

#include <cstdint>
#include <vector>

#include "collision/collision_tile.h"

namespace aurora {

class AnnotationManager {
public:
  static AnnotationManager& Instance() {
    static AnnotationManager instance;
    return instance;
  }
  AnnotationManager();

  void AddTileData(CollisionTilePtr tile);

  void Clear();

  std::vector<CollisionTilePtr>& GetTileData();

private:
  std::vector<CollisionTilePtr> tiles_;
};
}  // namespace aurora
#endif  // MAP_DATA_ANNOTATION_ANNOTATION_MANAGER_H
