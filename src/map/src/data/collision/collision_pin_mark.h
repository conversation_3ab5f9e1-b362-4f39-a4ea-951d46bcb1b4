#ifndef MAP_DATA_COLLISION_COLLISION_PIN_MARK_H
#define MAP_DATA_COLLISION_COLLISION_PIN_MARK_H
#include <memory>

#include "collision/collision_feature.h"
#include "collision/collision_text.h"
#include "collision/collision_mark.h"

namespace aurora {

class CollisionPinMark : public CollisionFeature {
public:
  CollisionPinMark(CollisionMarkPtr mark, CollisionTextPtr name);
  CollisionMarkPtr GetMark();
  CollisionTextPtr GetName();
  void SetCollisionLayer(int32_t layer);
  int32_t GetCollisionLayer();
  void SetCollisionFlag(bool flag);
  bool NeedCollistion();
  virtual void UpdateScreen(CameraPtr camera);
  void SetNameOffset(float offset_x, float offset_y);

private:
  CollisionMarkPtr mark_;
  CollisionTextPtr name_;
  float name_offset_x_;
  float name_offset_y_;
  bool collision_flag_;
  int32_t collision_layer_;
};
using CollisionPinMarkPtr = std::shared_ptr<CollisionPinMark>;
using CollisionPinMarkSet = std::vector<CollisionPinMarkPtr>;
}  // namespace aurora
#endif  // MAP_DATA_COLLISION_COLLISION_PIN_MARK_H
