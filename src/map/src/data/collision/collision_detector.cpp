#include "collision/collision_detector.h"

#include <assert.h>

#include <algorithm>
#include <cmath>

namespace aurora {
const uint32_t FEATURE_CACHE_COUNT = 128;
const AnchorType allow_type[] = {kAnchorMiddleTop, kAnchorLeftMiddle, kAnchorRightMiddle};

bool Compare(const CollisionBasePtr& ptr1, const CollisionBasePtr& ptr2) {
  assert(ptr1 != nullptr && ptr2 != nullptr);
  if (ptr1->GetPriority() == ptr2->GetPriority()) {
    if (ptr1->IsShow() == kCollisionShow) {
      if (ptr2->IsShow() == kCollisionShow) {
        return ptr1.get() < ptr2.get();
      }
      return true;
    } else if (ptr2->IsShow() == kCollisionShow) {
      return false;
    }
    return ptr1.get() < ptr2.get();
  }
  return ptr1->GetPriority() < ptr2->GetPriority();
}

CollisionDetector::CollisionDetector() { all_features_.reserve(FEATURE_CACHE_COUNT); }

void CollisionDetector::ResetGrid(uint32_t w, uint32_t h) { grid_.Reset(w, h); }

void CollisionDetector::AddCollision(CollisionBasePtr ptr) {
  assert(ptr != nullptr);
  if (ptr) {
    all_features_.push_back(ptr);
  }
}

void CollisionDetector::AddCollisions(std::vector<CollisionPointPtr>& collisions) {
  for (auto ptr : collisions) {
    AddCollision(ptr);
  }
}

void CollisionDetector::AddRoadName(CollistionRoadNameCache& road_names) {
  for (auto itr = road_names.begin(); itr != road_names.end(); ++itr) {
    auto it = road_names_.find(itr->first);
    if (it != road_names_.end()) {
      std::vector<CollisionRoadNamePtr>& collisions = itr->second->GetCollisions();
      for (auto ptr : collisions) {
        it->second->AddCollision(ptr);
      }
    } else {
      if (itr->second) {
        CollisionRoadNameSetPtr set = std::make_shared<CollisionRoadNameSet>(*(itr->second.get()));
        road_names_.emplace(itr->first, set);
      }
    }
  }
}

void CollisionDetector::Clear() {
  all_features_.clear();
  road_names_.clear();
  grid_.Clear();
}

void CollisionDetector::DetectCollision(CameraPtr camera) {
  for (auto itr = road_names_.begin(); itr != road_names_.end(); ++itr) {
    AddCollision(itr->second);
  }
  std::sort(all_features_.begin(), all_features_.end(), Compare);
  for (auto ptr : all_features_) {
    if (ptr != nullptr) {
      if (ptr->GetType() == kPointSet) {
        CollisionPoint* point_ptr = (CollisionPoint*)ptr.get();
        DetectPointCollision(camera, *point_ptr);
      } else if (ptr->GetType() == kRoadNameSet) {
        CollisionRoadNameSet* road_name_ptr = (CollisionRoadNameSet*)ptr.get();
        DetectRoadNameCollision(camera, *road_name_ptr);
      } else {
      }
    }
  }
}

std::vector<CollisionBasePtr>& CollisionDetector::GetCollisions() { return all_features_; }

CollisionResult CollisionDetector::CalcFeatureCollision(CollisionFeature* feature) {
  if (feature == nullptr) {
    return kCollisionOutOfScreen;
  }
  CollisionResult need_show = kCollisionOutOfScreen;
  const BoundingBox& box = feature->GetBox();
  int32_t min_x = box.minx();
  int32_t min_y = box.miny();
  int32_t max_x = std::ceil(box.maxx());
  int32_t max_y = std::ceil(box.maxy());
#if 0
  if (feature->IsShow() == kCollisionOutOfScreen || feature->IsShow() == kCollisionShow) {
    need_show = grid_.Place(min_x, min_y, max_x, max_y);
  } else {
    if (!grid_.IsInScreen(min_x, min_y, max_x, max_y)) {
      feature->SetShow(kCollisionOutOfScreen);
    } else {
      need_show = kCollisionHide;
    }
  }
#else
  if (!grid_.IsInScreen(min_x, min_y, max_x, max_y)) {
    feature->SetShow(kCollisionOutOfScreen);
  } else {
    need_show = grid_.Place(min_x, min_y, max_x, max_y);
  }
#endif
  return need_show;
}

bool CollisionDetector::GetNameBoxAnchorPoint(float anchor_x, float anchor_y, float offset_x,
                                              float offset_y, AnchorType type, float& x, float& y) {
  switch (type) {
    case kAnchorMiddleTop: {
      x = anchor_x;
      y = anchor_y - offset_y;
    } break;
    case kAnchorLeftMiddle: {
      x = anchor_x + offset_x;
      y = anchor_y;
    } break;
    case kAnchorRightMiddle: {
      x = anchor_x - offset_x;
      y = anchor_y;
    } break;
    default:
      return false;
  }
  return true;
}

void CollisionDetector::DetectPointCollision(CameraPtr camera, CollisionPoint& point_collision) {
  point_collision.UpdateScreen(camera);
  CollisionMarkPtr mark = point_collision.GetMark();
  CollisionTextPtr name = point_collision.GetName();
  {
    const BoundingBox& box = point_collision.GetBox();
    int32_t min_x = box.minx();
    int32_t min_y = box.miny();
    int32_t max_x = std::ceil(box.maxx());
    int32_t max_y = std::ceil(box.maxy());
    if (!grid_.IsInScreen(min_x, min_y, max_x, max_y)) {
      if (mark != nullptr) {
        mark->SetShow(kCollisionOutOfScreen);
      }
      if (name != nullptr) {
        name->SetShow(kCollisionOutOfScreen);
      }
      point_collision.SetShow(kCollisionOutOfScreen);
      return;
    }
  }
  CollisionResult mark_show = kCollisionOutOfScreen;
  CollisionResult name_show = kCollisionOutOfScreen;
  if (mark != nullptr) {
    mark->UpdateBox(point_collision.GetScreenX(), point_collision.GetScreenY());
    mark_show = CalcFeatureCollision(mark.get());
    mark->SetShow(mark_show);
  }
  if (name != nullptr) {
    if (mark_show != kCollisionHide) {
      uint32_t offset_x = mark->GetBoxWidth() / 2 + 4;
      uint32_t offset_y = mark->GetBoxHeight() / 2 + 4;
      float x = 0;
      float y = 0;
      int32_t last_name_show = name->IsShow();
      AnchorType last_type = name->GetAnchorType();
      bool need_layout = true;
      if (last_name_show != kCollisionHide) {
        GetNameBoxAnchorPoint(point_collision.GetScreenX(), point_collision.GetScreenY(), offset_x,
                              offset_y, last_type, x, y);
        name->UpdateBox(x, y);
        name_show = CalcFeatureCollision(name.get());
        need_layout = false;
      }
      if (need_layout) {
        for (auto type : allow_type) {
          if (name_show == kCollisionShow) {
            break;
          }
          GetNameBoxAnchorPoint(point_collision.GetScreenX(), point_collision.GetScreenY(),
                                offset_x, offset_y, type, x, y);
          name->SetAnchorType(type);
          name->UpdateBox(x, y);
          name_show = CalcFeatureCollision(name.get());
        }
      }

#if 0
      name->SetAnchorType(kAnchorMiddleTop);
      name->UpdateBox(point_collision.GetScreenX(), point_collision.GetScreenY() - offset_y);
      name_show = CalcFeatureCollision(name.get());
      if (name_show != kCollisionShow) {
        name->SetAnchorType(kAnchorLeftMiddle);
        name->UpdateBox(point_collision.GetScreenX() + offset_x, point_collision.GetScreenY());
        name_show = CalcFeatureCollision(name.get());
      }
      if (name_show != kCollisionShow) {
        name->SetAnchorType(kAnchorRightMiddle);
        name->UpdateBox(point_collision.GetScreenX() - offset_x, point_collision.GetScreenY());
        name_show = CalcFeatureCollision(name.get());
      }
#endif
      name->SetShow(name_show);
#if 0
      printf("name: %s, box(%f, %f, %f, %f) ori(%lf, %lf) show: %d\n", name->GetName().c_str(),
             name->GetBox().minx(), name->GetBox().miny(), std::ceil(name->GetBox().maxx()),
             std::ceil(name->GetBox().maxy()), name->GetLng(), name->GetLat(), (int)name_show);
#endif
    } else {
      name->SetShow(false);
    }
  }
  if (mark_show == kCollisionShow || name_show == kCollisionShow) {
    point_collision.SetShow(kCollisionShow);
  } else if (mark_show == kCollisionShow && name == nullptr) {
    point_collision.SetShow(kCollisionShow);
  } else if (mark_show == kCollisionHide) {
    point_collision.SetShow(kCollisionHide);
  } else {
    point_collision.SetShow(kCollisionOutOfScreen);
  }
}
void CollisionDetector::DetectRoadNameCollision(CameraPtr camera,
                                                CollisionRoadNameSet& road_name_collision) {
  std::vector<CollisionRoadNamePtr>& collisions = road_name_collision.GetCollisions();
  if (collisions.empty()) {
    return;
  }
  std::sort(collisions.begin(), collisions.end(), Compare);
  int32_t is_show = kCollisionOutOfScreen;
  for (CollisionRoadNamePtr collision : collisions) {
    if (collision != nullptr) {
      if (is_show == kCollisionShow) {
        collision->SetShow(kCollisionOutOfScreen);
        continue;
      }
      collision->UpdateScreen(camera);

      if (collision->IsShow() == kCollisionOutOfScreen || collision->IsShow() == kCollisionShow) {
        const BoundingBox& box = collision->GetBox();
        int32_t min_x = box.minx();
        int32_t min_y = box.miny();
        int32_t max_x = std::ceil(box.maxx());
        int32_t max_y = std::ceil(box.maxy());
        if (!grid_.IsInScreen(min_x, min_y, max_x, max_y)) {
          collision->SetShow(kCollisionOutOfScreen);
          continue;
        }
        is_show = grid_.CanPlace(min_x, min_y, max_x, max_y);
        if (is_show == kCollisionHide) {
          const std::vector<CollisionBox>& boxes = collision->GetNameBox();
          CollisionResult result = kCollisionOutOfScreen;
          for (const CollisionBox& b : boxes) {
            int32_t min_x = b.box.minx();
            int32_t min_y = b.box.miny();
            int32_t max_x = std::ceil(b.box.maxx());
            int32_t max_y = std::ceil(b.box.maxy());
            result = grid_.CanPlace(min_x, min_y, max_x, max_y);
            if (result == kCollisionHide) {
              is_show = kCollisionHide;
              break;
            }
          }
          if (is_show == kCollisionOutOfScreen) {
            is_show = result;
          }
        }
      } else {
        is_show = kCollisionHide;
      }
      if (is_show == kCollisionShow) {
        const std::vector<CollisionBox>& boxes = collision->GetNameBox();
        for (const CollisionBox& b : boxes) {
          grid_.ForcePlace(b.box.minx(), b.box.miny(), std::ceil(b.box.maxx()),
                           std::ceil(b.box.maxy()));
        }
      }
#if 0
      const std::vector<CollisionBox>& boxes = collision->GetNameBox();
      const std::vector<std::string>& name = road_name_collision.GetName();
      for (uint32_t i = 0; i < boxes.size(); ++i) {
        const CollisionBox& box = boxes[i];
        const std::string n = name[i];
        printf("name: %s, box(%f, %f, %f, %f) ori(%lf, %lf) show: %d\n", n.c_str(), box.box.minx(),
               box.box.miny(), std::ceil(box.box.maxx()), std::ceil(box.box.maxy()), box.lng,
               box.lat, (int)is_show);
      }
#endif
      collision->SetShow(is_show);
    }
  }
  road_name_collision.SetShow(is_show);
}
}  // namespace aurora
