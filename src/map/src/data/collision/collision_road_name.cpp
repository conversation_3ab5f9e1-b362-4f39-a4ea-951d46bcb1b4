#include "collision/collision_road_name.h"

namespace aurora {

CollisionRoadName::CollisionRoadName()
    : CollisionFeature(kRoadNameData), is_revert_(kNeedCalculate) {}

const std::vector<CollisionBox>& CollisionRoadName::GetNameBox() { return name_box_; }

const std::vector<float>& CollisionRoadName::GetNameDir() { return word_dir_; }

void CollisionRoadName::SetNameBox(std::vector<CollisionBox>&& boxes) {
  name_box_ = std::move(boxes);
}

void CollisionRoadName::SetNameDir(std::vector<float>&& dir) { word_dir_ = std::move(dir); }

void CollisionRoadName::CalcRevert() {
  if (name_box_.size() < 2) {
    is_revert_ = kForwardDisplay;
    return;
  }

  float delta_x = name_box_.back().x - name_box_.front().x;
  float delta_y = name_box_.back().y - name_box_.front().y;
  if (delta_x >= 0 && delta_y < 0) {
    is_revert_ = kForwardDisplay;
  } else if (delta_x >= 0 && delta_y > 0) {
    if (delta_x > delta_y) {
      is_revert_ = kForwardDisplay;
    } else {
      is_revert_ = kRevertDisplay;
    }
  } else if (delta_x < 0 && delta_y <= 0) {
    if (delta_x > delta_y) {
      is_revert_ = kForwardDisplay;
    } else {
      is_revert_ = kRevertDisplay;
    }
  } else {
    is_revert_ = kRevertDisplay;
  }
}

bool CollisionRoadName::NeedRevert() { return is_revert_ == kRevertDisplay; }

void CollisionRoadName::UpdateScreen(CameraPtr camera) {
  uint32_t w = 0;
  uint32_t h = 0;
  camera->GetViewSize(w, h);
  double minx = w;
  double miny = h;
  double maxx = 0.;
  double maxy = 0.;
  for (CollisionBox& box : name_box_) {
    box.UpdateScreen(camera);
    if (box.box.minx() < minx) {
      minx = box.box.minx();
    }
    if (box.box.miny() < miny) {
      miny = box.box.miny();
    }
    if (maxx < box.box.maxx()) {
      maxx = box.box.maxx();
    }
    if (maxy < box.box.maxy()) {
      maxy = box.box.maxy();
    }
  }
  SetBox(minx, miny, maxx, maxy);
}

CollisionRoadNameSet::CollisionRoadNameSet()
    : CollisionBase(kRoadNameSet),
      collisions_(),
      road_name_(),
      forecolor_(0),
      backcolor_(0),
      font_size_(0),
      font_outline_(0),
      outline_style_(0),
      font_name_() {}

CollisionRoadNameSet::CollisionRoadNameSet(const CollisionRoadNameSet& set)
    : CollisionBase(kRoadNameSet),
      collisions_(set.collisions_),
      road_name_(set.road_name_),
      forecolor_(set.forecolor_),
      backcolor_(set.backcolor_),
      font_size_(set.font_size_),
      font_outline_(set.font_outline_),
      outline_style_(set.outline_style_),
      font_name_(set.font_name_) {
  SetPriority(set.GetPriority());
  SetShow(set.IsShow());
}

void CollisionRoadNameSet::AddCollision(CollisionRoadNamePtr collision_ptr) {
  if (collision_ptr) {
    collisions_.push_back(collision_ptr);
  }
}

std::vector<CollisionRoadNamePtr>& CollisionRoadNameSet::GetCollisions() { return collisions_; }

const std::vector<std::string>& CollisionRoadNameSet::GetName() { return road_name_; }

std::vector<TextHolderPtr>& CollisionRoadNameSet::GetTextHolder() { return text_holder_; }

uint32_t CollisionRoadNameSet::GetColor() { return forecolor_; }

uint32_t CollisionRoadNameSet::GetOutlineColor() { return backcolor_; }

int32_t CollisionRoadNameSet::GetFontSize() { return font_size_; }

int32_t CollisionRoadNameSet::GetOutline() { return font_outline_; }

int32_t CollisionRoadNameSet::GetOutlineStyle() { return outline_style_; }

int32_t CollisionRoadNameSet::GetFont() { return font_name_; }

void CollisionRoadNameSet::SetRoadName(std::vector<std::string>&& name) {
  road_name_ = std::move(name);
}

void CollisionRoadNameSet::SetFont(uint32_t color, uint32_t backcolor, int32_t font_size,
                                   int32_t font_outline, int32_t outline_style, int32_t font_name) {
  forecolor_ = color;
  backcolor_ = backcolor;
  font_size_ = font_size;
  font_outline_ = font_outline;
  outline_style_ = outline_style;
  font_name_ = font_name;
}
}  // namespace aurora
