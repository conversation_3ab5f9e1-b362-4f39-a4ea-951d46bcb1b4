#ifndef MAP_DATA_COLLISION_COLLISION_TEXT_H
#define MAP_DATA_COLLISION_COLLISION_TEXT_H
#include <memory>

#include "collision/collision_feature.h"
#include "text_def.h"

namespace aurora {
class CollisionText : public CollisionFeature {
public:
  CollisionText();
  void SetText(const char* text, uint32_t len);
  void SetText(const char* text);
  const std::string& GetText();
  void SetTextHolder(TextHolderPtr& ptr);
  TextHolderPtr& GetTextHolder();
  uint32_t GetColor();
  uint32_t GetOutlineColor();
  int32_t GetFontSize();
  int32_t GetOutline();
  int32_t GetOutlineStyle();
  int32_t GetFont();
  void SetFont(uint32_t color, uint32_t backcolor, int32_t font_size, int32_t font_outline,
               int32_t outline_style, int32_t font_name);

private:
  std::string text_;
  TextHolderPtr text_holder_;
  uint32_t forecolor_;
  uint32_t backcolor_;
  int32_t font_size_;
  int32_t font_outline_;
  int32_t outline_style_;
  int32_t font_name_;
};
using CollisionTextPtr = std::shared_ptr<CollisionText>;

}  // namespace aurora
#endif  // MAP_DATA_COLLISION_COLLISION_TEXT_H
