#ifndef MAP_DATA_COLLISION_COLLISION_MARK_H
#define MAP_DATA_COLLISION_COLLISION_MARK_H
#include <memory>

#include "collision/collision_feature.h"
#include "collision/collision_text.h"
#include "mark/mark_def.h"

namespace aurora {
class CollisionMark : public CollisionFeature {
public:
  CollisionMark();
  void SetImageHolder(ImageHolderPtr image_holder);
  ImageHolderPtr GetImageHolder();
  void SetSrcRect(MarkRect rect);
  const MarkRect& GetSrcRect();
  void SetDegree(float degree);
  float GetDegree();

private:
  ImageHolderPtr image_holder_;
  MarkRect rect_;
  float degree_;
};
using CollisionMarkPtr = std::shared_ptr<CollisionMark>;
}  // namespace aurora
#endif  // MAP_DATA_COLLISION_COLLISION_MARK_H
