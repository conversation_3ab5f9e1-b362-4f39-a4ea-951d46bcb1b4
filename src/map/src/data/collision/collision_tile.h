#ifndef MAP_DATA_COLLISION_COLLISION_TILE_H
#define MAP_DATA_COLLISION_COLLISION_TILE_H
#include <memory>
#include <unordered_map>

#include "collision/collision_point.h"
#include "collision/collision_road_name.h"
#include "display_data/display_data_def.h"
#include "display_data/display_tile_reader.h"
#include "i_map_render_theme.h"

namespace aurora {
class CollisionTile {
public:
  CollisionTile(float w, float h) : width_(w), height_(h) {}
  void CreateCollisions(uint8_t logic_level, parser::DisplayTileReader& reader,
                        MapRenderThemeIF* theme);
  void UpdateScreen(CameraPtr camera);
  std::vector<CollisionPointPtr>& GetPointCollistion() { return poi_set_; }
  CollistionRoadNameCache& GetRoadNameCollistion() { return road_name_set_; }

private:
  void CreatePointCollistions(uint8_t logic_level, parser::DisplayTileReader& reader,
                              MapRenderThemeIF* theme);
  void CreateRoadNameCollistions(float scale_w, float scale_h, uint8_t logic_level,
                                 parser::DisplayTileReader& reader, MapRenderThemeIF* theme);
  uint32_t GetMarkID(uint32_t category);

  std::vector<CollisionPointPtr> poi_set_;
  CollistionRoadNameCache road_name_set_;
  float width_;
  float height_;
};
using CollisionTilePtr = std::shared_ptr<CollisionTile>;
}  // namespace aurora
#endif  // MAP_DATA_COLLISION_COLLISION_TILE_H
