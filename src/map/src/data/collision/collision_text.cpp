#include "collision/collision_text.h"

namespace aurora {
CollisionText::CollisionText()
    : CollisionFeature(kTextData),
      text_(),
      forecolor_(0),
      backcolor_(0),
      font_size_(0),
      font_outline_(0),
      outline_style_(0),
      font_name_(0) {}

void CollisionText::SetText(const char* name, uint32_t len) {
  std::string n(name, len);
  text_ = std::move(n);
}

void CollisionText::SetText(const char* text) { text_ = text; }

const std::string& CollisionText::GetText() { return text_; }

void CollisionText::SetTextHolder(TextHolderPtr& ptr) { text_holder_ = ptr; }

TextHolderPtr& CollisionText::GetTextHolder() { return text_holder_; }

uint32_t CollisionText::GetColor() { return forecolor_; }

uint32_t CollisionText::GetOutlineColor() { return backcolor_; }

int32_t CollisionText::GetFontSize() { return font_size_; }

int32_t CollisionText::GetOutline() { return font_outline_; }

int32_t CollisionText::GetOutlineStyle() { return outline_style_; }

int32_t CollisionText::GetFont() { return font_name_; }

void CollisionText::SetFont(uint32_t color, uint32_t backcolor, int32_t font_size,
                            int32_t font_outline, int32_t outline_style, int32_t font_name) {
  forecolor_ = color;
  backcolor_ = backcolor;
  font_size_ = font_size;
  font_outline_ = font_outline;
  outline_style_ = outline_style;
  font_name_ = font_name;
}
}  // namespace aurora
