#ifndef MAP_DATA_COLLISION_COLLISION_DETECTOR_H
#define MAP_DATA_COLLISION_COLLISION_DETECTOR_H
#include <vector>

#include "collision/collision_tile.h"
#include "collision_area.h"

namespace aurora {
class CollisionDetector {
public:
  CollisionDetector();
  void ResetGrid(uint32_t w, uint32_t h);
  void Clear();
  void AddCollisions(std::vector<CollisionPointPtr>& collisions);
  void AddRoadName(CollistionRoadNameCache& road_names);
  void DetectCollision(CameraPtr camera);
  std::vector<CollisionBasePtr>& GetCollisions();

private:
  void AddCollision(CollisionBasePtr ptr);
  void DetectPointCollision(CameraPtr camera, CollisionPoint& point_collision);
  CollisionResult CalcFeatureCollision(CollisionFeature* feature);
  void DetectRoadNameCollision(CameraPtr camera, CollisionRoadNameSet& road_name_collision);
  bool GetNameBoxAnchorPoint(float anchor_x, float anchor_y, float offset_x, float offset_y,
                             AnchorType type, float& x, float& y);

private:
  std::vector<CollisionBasePtr> all_features_;
  CollistionRoadNameCache road_names_;
  CollisionArea grid_;
};
}  // namespace aurora
#endif  // MAP_DATA_COLLISION_COLLISION_DETECTOR_H
