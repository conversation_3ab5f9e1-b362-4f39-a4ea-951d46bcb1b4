#include "collision/collision_point.h"

namespace aurora {
CollisionPoint::CollisionPoint(CollisionMarkPtr mark, CollisionTextPtr name)
    : CollisionFeature(kPointSet), mark_(mark), name_(name), poi_uuid_(0) {}

CollisionMarkPtr CollisionPoint::GetMark() { return mark_; }

CollisionTextPtr CollisionPoint::GetName() { return name_; }

void CollisionPoint::SetUUID(uint64_t uuid) { poi_uuid_ = uuid; }

uint64_t CollisionPoint::GetUUID() { return poi_uuid_; }

}  // namespace aurora
