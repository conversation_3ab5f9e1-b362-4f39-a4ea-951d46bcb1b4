#ifndef MAP_DATA_COLLISION_COLLISION_ROAD_NAME_H
#define MAP_DATA_COLLISION_COLLISION_ROAD_NAME_H
#include <memory>

#include "collision/collision_feature.h"
#include "text_def.h"

namespace aurora {

class CollisionRoadName : public CollisionFeature {
private:
  enum {
    kNeedCalculate = 0,
    kForwardDisplay,
    kRevertDisplay,
  };

public:
  CollisionRoadName();
  const std::vector<CollisionBox>& GetNameBox();
  const std::vector<float>& GetNameDir();
  void SetNameBox(std::vector<CollisionBox>&& boxes);
  void SetNameDir(std::vector<float>&& dir);
  void CalcRevert();
  bool NeedRevert();
  void UpdateScreen(CameraPtr camera) override;

private:
  std::vector<CollisionBox> name_box_;
  std::vector<float> word_dir_;
  int32_t is_revert_;
};
using CollisionRoadNamePtr = std::shared_ptr<CollisionRoadName>;

class CollisionRoadNameSet : public CollisionBase {
public:
  CollisionRoadNameSet();
  CollisionRoadNameSet(const CollisionRoadNameSet& set);
  void AddCollision(CollisionRoadNamePtr collision_ptr);
  std::vector<CollisionRoadNamePtr>& GetCollisions();
  const std::vector<std::string>& GetName();
  std::vector<TextHolderPtr>& GetTextHolder();
  uint32_t GetColor();
  uint32_t GetOutlineColor();
  int32_t GetFontSize();
  int32_t GetOutline();
  int32_t GetOutlineStyle();
  int32_t GetFont();
  void SetRoadName(std::vector<std::string>&& name);
  void SetFont(uint32_t color, uint32_t backcolor, int32_t font_size, int32_t font_outline,
               int32_t outline_style, int32_t font_name);

private:
  std::vector<CollisionRoadNamePtr> collisions_;
  std::vector<std::string> road_name_;
  std::vector<TextHolderPtr> text_holder_;
  uint32_t forecolor_;
  uint32_t backcolor_;
  int32_t font_size_;
  int32_t font_outline_;
  int32_t outline_style_;
  int32_t font_name_;
};
using CollisionRoadNameSetPtr = std::shared_ptr<CollisionRoadNameSet>;
using CollistionRoadNameCache = std::unordered_map<std::string, CollisionRoadNameSetPtr>;
}  // namespace aurora
#endif  // MAP_DATA_COLLISION_COLLISION_ROAD_NAME_H
