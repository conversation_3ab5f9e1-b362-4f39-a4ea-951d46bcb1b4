#include "collision/collision_pin_mark.h"

namespace aurora {
CollisionPinMark::CollisionPinMark(CollisionMarkPtr mark, CollisionTextPtr name)
    : CollisionFeature(kPinMarkSet),
      mark_(mark),
      name_(name),
      collision_flag_(false),
      collision_layer_(0) {}

CollisionMarkPtr CollisionPinMark::GetMark() { return mark_; }

CollisionTextPtr CollisionPinMark::GetName() { return name_; }

void CollisionPinMark::SetCollisionLayer(int32_t layer) { collision_layer_ = layer; }

int32_t CollisionPinMark::GetCollisionLayer() { return collision_layer_; }

void CollisionPinMark::SetCollisionFlag(bool flag) { collision_flag_ = flag; }

bool CollisionPinMark::NeedCollistion() { return collision_flag_; }

void CollisionPinMark::SetNameOffset(float offset_x, float offset_y) {
    name_offset_x_ = offset_x;
    name_offset_y_ = offset_y;
}

void CollisionPinMark::UpdateScreen(CameraPtr camera) {
  CollisionFeature::UpdateScreen(camera);
  if (mark_) {
    mark_->UpdateBox(GetScreenX(), GetScreenY());
  }
  if (name_) {
    switch (name_->GetAnchorType()) {
      case kAnchorMiddleTop: {
        name_->UpdateBox(GetScreenX(), GetScreenY() - name_offset_y_);
      } break;
      case kAnchorLeftMiddle: {
        name_->UpdateBox(GetScreenX() + name_offset_x_, GetScreenY());
      } break;
      case kAnchorRightMiddle: {
        name_->UpdateBox(GetScreenX() - name_offset_x_, GetScreenY());
      } break;
      default:
        break;
    }
  }
}

}  // namespace aurora
