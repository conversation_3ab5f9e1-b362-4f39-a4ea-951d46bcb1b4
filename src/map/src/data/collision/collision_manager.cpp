#include "collision/collision_manager.h"

namespace aurora {

CollisionManager::CollisionManager() : detectors_() {}

void CollisionManager::AddCollisionPoint(int32_t layer, std::vector<CollisionPointPtr>& pois) {
  CollisionDetector& detector = GetDetector(layer);
  detector.AddCollisions(pois);
}

void CollisionManager::AddCollisionRoadName(int32_t layer, CollistionRoadNameCache& road_names) {
  CollisionDetector& detector = GetDetector(layer);
  detector.AddRoadName(road_names);
}

void CollisionManager::ClearCollisions(int32_t layer) {
  CollisionDetector& detector = GetDetector(layer);
  detector.Clear();
}

void CollisionManager::UpdateScreen(uint32_t w, uint32_t h) {
  for (auto itr = detectors_.begin(); itr != detectors_.end(); ++itr) {
    itr->second.ResetGrid(w, h);
  }
}

void CollisionManager::DoCollistion(CameraPtr camera) {
  for (auto itr = detectors_.begin(); itr != detectors_.end(); ++itr) {
    itr->second.DetectCollision(camera);
  }
}

CollisionDetector& CollisionManager::GetDetector(int32_t layer) {
  auto itr = detectors_.find(layer);
  if (itr == detectors_.end()) {
    auto result = detectors_.emplace(layer, CollisionDetector());
    return result.first->second;
  }
  return itr->second;
}
}  // namespace aurora
