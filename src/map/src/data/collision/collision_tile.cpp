#include "collision/collision_tile.h"

#include <assert.h>

#include "config_data/cfg_data_def.h"
#include "map_render_skfontface.h"
#include "map_render_skfontmanager.h"
#include "mark/mark_def.h"
#include "mark/mark_manager.h"
#include "spline.h"
#include "text/skia/sk_text_holder.h"
#include "text/text_def.h"

namespace aurora {
void CollisionTile::CreateCollisions(uint8_t logic_level, parser::DisplayTileReader& reader,
                                     MapRenderThemeIF* theme) {
  if (reader.GetMbr() == nullptr || theme == nullptr) {
    return;
  }
  float scale_w = width_ / reader.GetTileWidth();
  float scale_h = height_ / reader.GetTileWidth();
  CreatePointCollistions(logic_level, reader, theme);
  CreateRoadNameCollistions(scale_w, scale_h, logic_level, reader, theme);
}

void CollisionTile::UpdateScreen(CameraPtr camera) {
  for (auto& ptr : poi_set_) {
    if (ptr) {
      if (ptr->GetMark()) {
        ptr->GetMark()->UpdateScreen(camera);
      }
      if (ptr->GetName()) {
        ptr->GetName()->UpdateScreen(camera);
      }
    }
  }
  for (auto itr = road_name_set_.begin(); itr != road_name_set_.end(); ++itr) {
    std::vector<CollisionRoadNamePtr>& collision_set = itr->second->GetCollisions();
    for (auto it = collision_set.begin(); it != collision_set.end(); ++it) {
      (*it)->UpdateScreen(camera);
    }
  }
}
void CollisionTile::CreatePointCollistions(uint8_t logic_level, parser::DisplayTileReader& reader,
                                           MapRenderThemeIF* theme) {
  if (theme == nullptr) {
    return;
  }
  SKFontFace* font_face = SKFontManager::Instance().GetFontFace("Arial Unicode.ttf");
  if (font_face == nullptr) {
    return;
  }
  double tile_min_x = reader.GetMbr()->minx();
  double tile_min_y = reader.GetMbr()->miny();
  double tile_max_x = reader.GetMbr()->maxx();
  double tile_max_y = reader.GetMbr()->maxy();
  double scale_x = (tile_max_x - tile_min_x) / reader.GetTileWidth();
  double scale_y = (tile_max_y - tile_min_y) / reader.GetTileWidth();
  parser::PointFeature* points = nullptr;
  uint32_t point_count = reader.GetPoints(points);
  for (uint32_t i = 0; i < point_count; ++i) {
    parser::PointFeature& p = points[i];
    if (!reader.IsShow(logic_level, reader.GetTileID(), p.zoom_rank)) {
      continue;
    }
    parser::ViewObjStyle* obj_style = theme->GetViewObjStyle(p.category);
    if (obj_style == nullptr) {
      obj_style = theme->GetViewObjStyle(p.category & 0xFFF0);
      if (obj_style == nullptr) {
        continue;
      }
    }
    uint32_t font_size = obj_style->font_size;
    uint32_t icon_width = obj_style->icon_width;
    uint32_t icon_height = obj_style->icon_height;
    uint32_t mark_id = GetMarkID(p.category);
    CollisionMarkPtr mark_ptr = std::make_shared<CollisionMark>();
    double lng = tile_min_x + p.pos.x() * scale_x;
    double lat = tile_min_y + p.pos.y() * scale_y;
    mark_ptr->SetImageHolder(MarkManager::Instance().GetResHolder());
    mark_ptr->SetSrcRect(mark_info[mark_id]);
    mark_ptr->SetBoxWidth(icon_width);
    mark_ptr->SetBoxHeight(icon_height);
    mark_ptr->SetAnchorType(kAnchorCenter);
    CollisionTextPtr name_ptr = nullptr;
    if (p.native_name_size > 0 && font_size > 0) {
      name_ptr = std::make_shared<CollisionText>();
      name_ptr->SetText(p.native_name, p.native_name_size);
#if 0
      printf("collision tile: %d, %d, %d, name: %s\n", (int)reader.GetTileID().level,
             (int)reader.GetTileID().col, (int)reader.GetTileID().row, name_ptr->GetName().c_str());
#endif
      //      name_ptr->SetAnchorType(kAnchorLeftMiddle);
      name_ptr->SetAnchorType(kAnchorMiddleTop);
      name_ptr->SetFont(obj_style->forecolor, obj_style->background_color, font_size,
                        obj_style->font_outline, obj_style->font_outline_style,
                        obj_style->font_name);
      TextHolderPtr text_holder = std::make_shared<SkTextHolder>();
      BoundingBox box =
          text_holder->CreateTextBlob(name_ptr->GetText(), "Arial Unicode.ttf", font_size, 8);
      name_ptr->SetTextHolder(text_holder);
      name_ptr->SetBoxWidth(box.Width());
      name_ptr->SetBoxHeight(box.Height());
    }
    uint32_t box_w = mark_ptr->GetBoxWidth();
    uint32_t box_h = mark_ptr->GetBoxHeight();
    if (name_ptr != nullptr) {
      box_w += 2 * name_ptr->GetBoxWidth();
      box_h += 2 * name_ptr->GetBoxHeight();
    }

    CollisionPointPtr collision = std::make_shared<CollisionPoint>(mark_ptr, name_ptr);
    // temp code
    collision->SetPriority(100);

    collision->SetLonLat(lng, lat);
    collision->SetBoxWidth(box_w);
    collision->SetBoxHeight(box_h);
    collision->SetAnchorType(kAnchorCenter);
    collision->SetUUID(p.uuid);

    poi_set_.push_back(collision);
  }
}
void CollisionTile::CreateRoadNameCollistions(float scale_w, float scale_h, uint8_t logic_level,
                                              parser::DisplayTileReader& reader,
                                              MapRenderThemeIF* theme) {
  SKFontFace* font_face = SKFontManager::Instance().GetFontFace("Arial Unicode.ttf");
  if (font_face == nullptr) {
    return;
  }

  double tile_min_x = reader.GetMbr()->minx();
  double tile_min_y = reader.GetMbr()->miny();
  double tile_max_x = reader.GetMbr()->maxx();
  double tile_max_y = reader.GetMbr()->maxy();
  double scale_x = (tile_max_x - tile_min_x) / reader.GetTileWidth();
  double scale_y = (tile_max_y - tile_min_y) / reader.GetTileWidth();
  std::map<const char*, std::pair<int, CollisionRoadNameSetPtr>> road_name_boxes;
  parser::EdgeFeature* edges = nullptr;
  uint32_t edge_count = reader.GetEdges(edges);
  for (uint32_t i = 0; i < edge_count; ++i) {
    parser::EdgeFeature& edge = edges[i];
    if (!reader.IsShow(logic_level, reader.GetTileID(), edge.zoom_rank)) {
      continue;
    }

    if (edge.native_name_size > 0) {
      float priority = (edge.category & 0x0FFF) * 0.1;
      parser::ViewObjStyle* obj_style = theme->GetViewObjStyle(edge.category);
      if (obj_style == nullptr) {
        continue;
      }
      uint32_t font_size = obj_style->font_size;
      // temp code
      if (font_size == 0) {
        font_size = 16;
      }
      const char* w = edge.native_name;
      std::vector<std::string> letters;
      letters.reserve(10);
      size_t letter_len = 0;
      size_t cur_len = 0;
      do {
        w = GetLetterUtf8(w + letter_len, edge.native_name_size - cur_len, letter_len);
        cur_len += letter_len;
        std::string s(w, letter_len);
        letters.push_back(s);

      } while (cur_len < edge.native_name_size && letter_len > 0);

      Vector2d pt;
      if (!letters.empty()) {
        Spline spline;
        Vector2d pt;
        for (uint16_t i = 0; i < edge.point_size; ++i) {
          parser::TileXY& pos = edge.points[i];
          pt.Set(pos.x() * scale_w, pos.y() * scale_h);
          spline.AddPoint(pt);
        }
        double length = spline.GetLength();
        size_t letter_num = letters.size();

        // temp code
        double letter_width = font_size;
        double gap = 4;
        double start_offset = 100;
        double letter_offset = letter_width + gap;
        double letter_len = letter_width + (letter_num - 1) * letter_offset;

        if (length - 2 * start_offset > letter_len) {
          double cur_pos = start_offset;
          std::vector<CollisionBox> boxes;
          std::vector<float> word_dir;
          boxes.reserve(letter_num);
          word_dir.reserve(letter_num);

          for (std::string& letter : letters) {
            auto itr = spline.GetPoint(cur_pos);
            cur_pos += letter_offset;
            const Vector2d& v = itr.pos_;
            double dir = atan2(itr.dir_.y(), itr.dir_.x()) * 180 / kPiD;
            if (dir > 90) {
              dir -= 180;
            } else if (dir < -90) {
              dir += 180;
            } else {
            }
            word_dir.push_back(dir);
            Rect bounds;
            font_face->MeasureText(font_size, letter, bounds);
            CollisionBox box;
            box.anchor_type = kAnchorCenter;
            box.w = bounds.size.width * 1.3;
            box.h = bounds.size.height * 1.3;
            box.lng = tile_min_x + v.x() * scale_x / scale_w;
            box.lat = tile_min_y + v.y() * scale_y / scale_h;
            boxes.push_back(box);
          }
#if 0
          std::string road_name(edge.native_name, edge.native_name_size);
          printf("%d, %s, tile(%d, %d, %d)\n", i, road_name.c_str(),
                 reader.GetTileID().level, reader.GetTileID().col, reader.GetTileID().row);
          for (auto box : boxes) {
              printf("(%lf, %lf) ", box.lng, box.lat);
          }
          printf("\n");
          for (int j = 0; j < edge.point_size; ++j) {
              printf("(%d, %d) ", edge.points[j].x(), edge.points[j].y());
          }
          printf("\n");
          const auto& l = spline.GetPath();
          for (int k = 0; k < l.size(); ++k) {
              printf("(%lf, %lf)", l[k].x(), l[k].y());
          }

          printf("\n\n");
#endif
          auto road_name_ptr = std::make_shared<CollisionRoadName>();
          road_name_ptr->SetNameBox(std::move(boxes));
          road_name_ptr->SetNameDir(std::move(word_dir));

          CollisionRoadNameSetPtr ptr = nullptr;
          auto itr = road_name_boxes.find(w);
          if (itr == road_name_boxes.end()) {
            ptr = std::make_shared<CollisionRoadNameSet>();
            ptr->SetPriority(2000 + priority);
            ptr->SetFont(obj_style->forecolor, obj_style->background_color, font_size,
                         obj_style->font_outline, obj_style->font_outline_style,
                         obj_style->font_name);
            ptr->SetRoadName(std::move(letters));

            road_name_boxes.emplace(edge.native_name, std::make_pair(edge.native_name_size, ptr));
          } else {
            ptr = itr->second.second;
          }
          ptr->AddCollision(road_name_ptr);
        }
      }
    }
  }

  if (!road_name_boxes.empty()) {
    for (auto itr = road_name_boxes.begin(); itr != road_name_boxes.end(); ++itr) {
      assert(itr->second.second != nullptr);
      std::string name(itr->first, itr->second.first);
      road_name_set_.emplace(name, itr->second.second);
    }
  }
}

uint32_t CollisionTile::GetMarkID(uint32_t category) {
  // temp code
  if ((category & 0xFFF0) == 0x4120) {  //餐饮服务
    return kMarkCatering;
  } else if ((category & 0xFFFF) == 0x4542) {  //飞机场
    return kMarkAirport;
  } else if ((category & 0xFFF0) == 0x4400) {
    return kMarkAttraction;
  } else if ((category & 0xFFF0) == 0x43E0) {  // 公园广场
    return kMarkParking;
  } else {
    return kMarkOthers;
  }
}
}  // namespace aurora
