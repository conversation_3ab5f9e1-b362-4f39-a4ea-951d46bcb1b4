#ifndef MAP_DATA_COLLISION_COLLISION_FEATURE_H
#define MAP_DATA_COLLISION_COLLISION_FEATURE_H
#include <memory>

#include "map_render_camera_if.h"
#include "util_basic_type.h"

namespace aurora {

struct CollisionBox {
  AnchorType anchor_type;
  double lng;
  double lat;
  double x;
  double y;
  int32_t w;
  int32_t h;
  float anchor_x;
  float anchor_y;

  BoundingBox box;

  CollisionBox();
  void UpdateScreen(CameraPtr camera);
  void UpdateBox(double scrnx, double scrny);
};

enum CollisionFeatureType {
  kMarkData = 0,
  kTextData,
  kRoadNameData,
  kPointSet,
  kRoadNameSet,
  kPinMarkSet,
};

class CollisionBase {
public:
  CollisionBase(CollisionFeatureType type);
  void SetPriority(int32_t priority);
  int32_t GetPriority() const;
  CollisionFeatureType GetType() const;
  void SetShow(int32_t show);
  int32_t IsShow() const;

private:
  int32_t priority_;
  int32_t show_status_;
  CollisionFeatureType type_;
};
using CollisionBasePtr = std::shared_ptr<CollisionBase>;

class CollisionFeature : public CollisionBase {
public:
  CollisionFeature(CollisionFeatureType type);
  void SetBox(double min_x, double min_y, double max_x, double max_y);
  void SetBoxWidth(int32_t w);
  void SetBoxHeight(int32_t h);
  void SetLonLat(double lng, double lat);
  int32_t GetBoxWidth();
  int32_t GetBoxHeight();
  const BoundingBox& GetBox();
  void SetAnchorType(AnchorType type);
  AnchorType GetAnchorType();
  void SetAnchor(float anchor_x, float anchor_y);
  float GetAnchorX();
  float GetAnchorY();
  double GetScreenX();
  double GetScreenY();
  double GetLng();
  double GetLat();

  virtual void UpdateScreen(CameraPtr camera);
  virtual void UpdateBox(double x, double y);

private:
  void UpdateAnchor();
  CollisionBox box_;
};

}  // namespace aurora
#endif  // MAP_DATA_COLLISION_COLLISION_FEATURE_H
