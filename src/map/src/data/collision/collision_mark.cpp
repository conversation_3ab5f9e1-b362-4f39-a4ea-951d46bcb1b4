#include "collision/collision_mark.h"

namespace aurora {

CollisionMark::CollisionMark()
    : CollisionFeature(kMarkData), image_holder_(nullptr), rect_(), degree_(0.f) {}

void CollisionMark::SetImageHolder(ImageHolderPtr image_holder) { image_holder_ = image_holder; }

ImageHolderPtr CollisionMark::GetImageHolder() { return image_holder_; }

void CollisionMark::SetSrcRect(MarkRect rect) { rect_ = rect; }

const MarkRect& CollisionMark::GetSrcRect() { return rect_; }

void CollisionMark::SetDegree(float degree) { degree_ = degree; }

float CollisionMark::GetDegree() { return degree_; }
}  // namespace aurora
