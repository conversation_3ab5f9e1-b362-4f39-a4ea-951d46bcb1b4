#ifndef MAP_DATA_COLLISION_COLLISION_MANAGER_H
#define MAP_DATA_COLLISION_COLLISION_MANAGER_H

#include <cstdint>
#include <map>

#include "collision/collision_detector.h"

namespace aurora {

class CollisionManager {
public:
  static CollisionManager& Instance() {
    static CollisionManager instance;
    return instance;
  }
  CollisionManager();
  void AddCollisionPoint(int32_t layer, std::vector<CollisionPointPtr>& pois);
  void AddCollisionRoadName(int32_t layer, CollistionRoadNameCache& road_names);
  void ClearCollisions(int32_t layer);
  void UpdateScreen(uint32_t w, uint32_t h);
  void DoCollistion(CameraPtr camera);
  std::vector<CollisionBasePtr>& GetCollistionFeatures(int32_t layer);

private:
  CollisionDetector& GetDetector(int32_t layer);

  std::map<int32_t, CollisionDetector> detectors_;
};
}  // namespace aurora
#endif  // MAP_DATA_COLLISION_COLLISION_MANAGER_H
