#ifndef MAP_DATA_COLLISION_COLLISION_POINT_H
#define MAP_DATA_COLLISION_COLLISION_POINT_H
#include <memory>

#include "collision/collision_feature.h"
#include "collision/collision_text.h"
#include "collision/collision_mark.h"

namespace aurora {

class CollisionPoint : public CollisionFeature {
public:
  CollisionPoint(CollisionMarkPtr mark, CollisionTextPtr name);
  CollisionMarkPtr GetMark();
  CollisionTextPtr GetName();
  void SetUUID(uint64_t uuid);
  uint64_t GetUUID();

private:
  CollisionMarkPtr mark_;
  CollisionTextPtr name_;
  uint64_t poi_uuid_;
};
using CollisionPointPtr = std::shared_ptr<CollisionPoint>;
}  // namespace aurora
#endif  // MAP_DATA_COLLISION_COLLISION_POINT_H
