#include "collision/collision_feature.h"

namespace aurora {

CollisionBox::CollisionBox()
    : anchor_type(kAnchorCenter), lng(0.), lat(0.), x(0.), y(0.), w(0), h(0), box() {}

void CollisionBox::UpdateBox(double scrnx, double scrny) {
  x = scrnx;
  y = scrny;

  switch (anchor_type) {
    case kAnchorCenter: {
      float min_x = x - w / 2;
      float min_y = y - h / 2;
      box.Create({{min_x, min_y}, {min_x + w, min_y + h}});
    } break;
    case kAnchorLeftTop: {
      box.Create({{x, y - h}, {x + w, y}});
    } break;
    case kAnchorLeftMiddle: {
      float min_y = y - h / 2;
      box.Create({{x, min_y}, {x + w, min_y + h}});
    } break;
    case kAnchorLeftBottom: {
      box.Create({{x, y}, {x + w, y + h}});
    } break;
    case kAnchorMiddleBottom: {
      float min_x = x - w / 2;
      box.Create({{min_x, y}, {min_x + w, y + h}});
    } break;
    case kAnchorRightBottom: {
      box.Create({{x - w, y}, {x, y + h}});
    } break;
    case kAnchorRightMiddle: {
      float min_y = y - h / 2;
      box.Create({{x - w, min_y}, {x, min_y + h}});
    } break;
    case kAnchorRightTop: {
      box.Create({{x - w, y - h}, {x, y}});
    } break;
    case kAnchorMiddleTop: {
      float min_x = x - w / 2;
      box.Create({{min_x, y - h}, {min_x + w, y}});
    } break;
    case kAnchorFree: {
      float min_x = x - anchor_x;
      float min_y = y - h + anchor_y;
      box.Create({{min_x, min_y}, {min_x + w, min_y + h}});
    } break;
    default:
      break;
  }
}

void CollisionBox::UpdateScreen(CameraPtr camera) {
  if (camera->World2Screen(lng, lat, x, y)) {
    UpdateBox(x, y);
  }
}

CollisionBase::CollisionBase(CollisionFeatureType type)
    : type_(type), show_status_(0), priority_(0.f) {}

void CollisionBase::SetPriority(int32_t priority) { priority_ = priority; }

int32_t CollisionBase::GetPriority() const { return priority_; }

CollisionFeatureType CollisionBase::GetType() const { return type_; }

void CollisionBase::SetShow(int32_t show) { show_status_ = show; }

int32_t CollisionBase::IsShow() const { return show_status_; }

CollisionFeature::CollisionFeature(CollisionFeatureType type) : CollisionBase(type), box_() {}

void CollisionFeature::SetBox(double min_x, double min_y, double max_x, double max_y) {
  box_.box.Create({{min_x, min_y}, {max_x, max_y}});
}

void CollisionFeature::SetBoxWidth(int32_t w) {
  box_.w = w;
  UpdateAnchor();
}

void CollisionFeature::SetBoxHeight(int32_t h) {
  box_.h = h;
  UpdateAnchor();
}

void CollisionFeature::SetLonLat(double lng, double lat) {
  box_.lng = lng;
  box_.lat = lat;
}

int32_t CollisionFeature::GetBoxWidth() { return box_.w; }

int32_t CollisionFeature::GetBoxHeight() { return box_.h; }

const BoundingBox& CollisionFeature::GetBox() { return box_.box; }

void CollisionFeature::SetAnchorType(AnchorType type) {
  box_.anchor_type = type;
  UpdateAnchor();
}

AnchorType CollisionFeature::GetAnchorType() { return box_.anchor_type; }

void CollisionFeature::SetAnchor(float anchor_x, float anchor_y) {
  box_.anchor_x = anchor_x;
  box_.anchor_y = anchor_y;
}

float CollisionFeature::GetAnchorX() { return box_.anchor_x; }

float CollisionFeature::GetAnchorY() { return box_.anchor_y; }

double CollisionFeature::GetScreenX() { return box_.x; }

double CollisionFeature::GetScreenY() { return box_.y; }

double CollisionFeature::GetLng() { return box_.lng; }

double CollisionFeature::GetLat() { return box_.lat; }

void CollisionFeature::UpdateScreen(CameraPtr camera) { box_.UpdateScreen(camera); }

void CollisionFeature::UpdateBox(double x, double y) { box_.UpdateBox(x, y); }

void CollisionFeature::UpdateAnchor() {
  switch (box_.anchor_type) {
    case kAnchorCenter: {
      box_.anchor_x = box_.w / 2;
      box_.anchor_y = box_.h / 2;
    } break;
    case kAnchorLeftTop: {
      box_.anchor_x = 0;
      box_.anchor_y = 0;
    } break;
    case kAnchorLeftMiddle: {
      box_.anchor_x = 0;
      box_.anchor_y = box_.h / 2;
    } break;
    case kAnchorLeftBottom: {
      box_.anchor_x = 0;
      box_.anchor_y = box_.h;
    } break;
    case kAnchorMiddleBottom: {
      box_.anchor_x = box_.w / 2;
      box_.anchor_y = box_.h;
    } break;
    case kAnchorRightBottom: {
      box_.anchor_x = box_.w;
      box_.anchor_y = box_.h;
    } break;
    case kAnchorRightMiddle: {
      box_.anchor_x = box_.w;
      box_.anchor_y = box_.h / 2;
    } break;
    case kAnchorRightTop: {
      box_.anchor_x = box_.w;
      box_.anchor_y = 0;
    } break;
    case kAnchorMiddleTop: {
      box_.anchor_x = box_.w / 2;
      box_.anchor_y = 0;
    } break;
    case kAnchorFree:
      // not break;
    default:
      break;
  }
}

}  // namespace aurora
