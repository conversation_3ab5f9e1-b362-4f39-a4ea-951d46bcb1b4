/**
 * @file route_node.h
 * @brief Route node data structure definition
 *
 * @details This file defines the @c RouteNode class and some related structure, representing a
 * navigational node in a route network. It contains geometric position data, administrative
 * boundaries, and connectivity information for stairs/elevators.
 *
 * <AUTHOR>
 * @date 2025-5-13
 */

#ifndef MAP_SRC_DATA_PROVIDER_INCLUDE_ROUTE_NODE_H
#define MAP_SRC_DATA_PROVIDER_INCLUDE_ROUTE_NODE_H

#include "export_type_def.h"
#include "route_data/route_data_def.h"

namespace aurora {
namespace parser {
#pragma pack(push, 1)

/**
 * @struct NodeBase
 * @brief Base structure for road network nodes
 *
 * Stores geographic coordinates, boundary type flags, and connectivity counters.
 * Uses bit fields for memory optimization.
 */
struct NodeBase {
  uint16_t lng;  ///< Longitude coordinate (unit: meters)
  uint16_t lat;  ///< Latitude coordinate (unit: meters)

  uint32_t is_top_boundary : 1;      ///< Whether the node is a top boundary node
  uint32_t is_bottom_boundary : 1;   ///< Whether the node is a bottom boundary node
  uint32_t is_left_boundary : 1;     ///< Whether the node is a left boundary node
  uint32_t is_right_boundary : 1;    ///< Whether the node is a right boundary node
  uint32_t is_admin_boundary : 1;    ///< Whether the node is an administrative boundary node
  uint32_t is_trans_down : 1;        ///< Whether the node is a downward node
  uint32_t is_trans_up : 1;          ///< Whether the node is an upward node
  uint32_t boundary_node_count : 2;  ///< Number of boundary connections
  uint32_t expansion_address : 23;   ///< Memory address for extension data

  uint8_t connect_edge_count : 4;  ///< Number of connected edges, (0-15) means (1-16)
  uint8_t not_used : 4;            ///< Reserved field

  /**
   * @brief Default constructor initializing all fields to 0
   */
  NodeBase() { ::memset(this, 0, sizeof(*this)); }
};

/**
 * @struct BoundaryNode
 * @brief Boundary node information structure
 *
 * Stores adjacency information for cross-tile boundary nodes used in pathfinding.
 */
struct BoundaryNode {
  uint16_t adj_mesh_col;  ///< Column index of adjacent mesh grid
  uint16_t adj_mesh_row;  ///< Row index of adjacent mesh grid

  uint32_t adj_tile_id : 8;   ///< ID of adjacent tile
  uint32_t adj_node_id : 24;  ///< ID of adjacent node

  /**
   * @brief Default constructor initializing all fields to .
   */
  BoundaryNode() { ::memset(this, 0, sizeof(*this)); }
};

/**
 * @struct AdminBoundaryNode
 * @brief Administrative boundary node structure
 *
 * Extends BoundaryNode with administrative boundary information for regional management.
 */
struct AdminBoundaryNode {
  uint16_t adj_mesh_col;  ///< Column index of adjacent mesh grid
  uint16_t adj_mesh_row;  ///< Row index of adjacent mesh grid

  uint32_t adj_tile_id : 8;   ///< ID of adjacent tile
  uint32_t adj_node_id : 24;  ///< ID of adjacent node

  uint32_t adcode;  ///< Administrative region code

  /**
   * @brief Default constructor initializing all fields to 0
   */
  AdminBoundaryNode() { ::memset(this, 0, sizeof(*this)); }
};

/**
 * @struct WarmholeNodeInfo
 * @brief Warmhole node connection information
 *
 * Stores cross-hierarchy node connections for seamless multi-layer network navigation.
 */

struct TransNodeInfo {
  uint32_t opp_tile_id : 8;   ///< Target tile ID
  uint32_t opp_node_id : 24;  ///< Target node ID
  /**
   * @brief Default constructor initializing all fields to 0
   */
  TransNodeInfo() { ::memset(this, 0, sizeof(*this)); }
};

/**
 * @struct ConnectedEdge
 * @brief Connected edge information structure
 *
 * Stores direction angle and edge ID for path direction calculations.
 */
struct ConnectedEdge {
  uint32_t angle : 8;     ///< Edge direction angle
  uint32_t edge_id : 24;  ///< Unique edge identifier

  /**
   * @brief Default constructor initializing all fields to 0
   */
  ConnectedEdge() { ::memset(this, 0, sizeof(*this)); }
  /**
   * @brief Get edge's angle with north direction
   * @return angle with north direction(0~358)
   */
  uint32_t GetAngle() { return this->angle * 2; }
};

/**
 * @struct TransEdgeInfo
 * @brief Warmhole edge connection information
 *
 * Stores cross-hierarchy edge connections for multi-layer navigation.
 */
struct TransEdgeInfo {
  uint32_t opp_tile_id : 8;   ///< Target tile ID
  uint32_t opp_edge_id : 24;  ///< Target edge ID
  /**
   * @brief Implicit constructor using memset initialization
   *
   * All fields are initialized to 0 through memory initialization.
   */
  TransEdgeInfo() { ::memset(this, 0, sizeof(*this)); }
};

#pragma pack(pop)

/**
 * @brief Navigational node in a route network containing spatial and connectivity data.
 *
 * The @c RouteNode class stores information about a node in a route network, including:
 * - Unique identifier and geographic coordinates
 * - Administrative boundary associations
 * - Connectivity to other nodes via edges
 * - Warmhole (staircase/elevator) connections between levels
 */
class AURORA_EXPORT RouteNode {
public:
  /**
   * @brief Default constructor initializing empty data containers.
   */
  RouteNode();

  /**
   * @brief Get the unique identifier of this route node.
   * @return 32-bit unsigned integer node ID.
   */
  uint32_t GetID() { return id_; }

  /**
   * @brief Get base configuration parameters of the node.
   * @return Pointer to the NodeBase structure containing type/category information.
   */
  const NodeBase* GetBaseInfo() { return base_; }

  /**
   * @brief Get edge's angle with north direction
   * @return angle with north direction(0~358), if edge_id is not found, return 0xFFFF
   */
  uint16_t GetEdgeAngle(uint32_t edge_id);

  /**
   * @brief Get geographic coordinates of this node.
   * @return LngLat structure representing (x,y) coordinates.
   */
  LngLat GetPosition() { return lnglat_; }

  /**
   * @brief Get administrative boundary information associated with this node.
   * @return Pointer to the AdminBoundaryNode structure, or @c nullptr if unset.
   */
  const AdminBoundaryNode* GetAdBoundaryNode() { return admin_bound_nodes_; }

  /**
   * @brief Get warmhole node information for downward stairs/elevators.
   * @return Pointer to the TransNodeInfo structure, or @c nullptr if none.
   */
  const TransNodeInfo* GetTransDownNodeInfo() { return trans_down_node_infos_; }

  /** TransNode
   * @brief Get warmhole node information for upward stairs/elevators.
   * @return Pointer to the TransNodeInfo structure, or @c nullptr if none.
   */
  const TransNodeInfo* GetTransUpNodeInfo() { return trans_up_node_infos_; }

  /**
   * @brief Get list of boundary nodes connected to this node.
   * @return Const reference to the vector of BoundaryNode pointers.
   */
  const std::vector<BoundaryNode*>& GetBoundaryNode() { return boundary_nodes_; }

  /**
   * @brief Get list of edges connected to this node.
   * @return Const reference to the vector of ConnectedEdge pointers.
   */
  const std::vector<ConnectedEdge*>& GetConnectedEdge() { return connected_edges_; }

  /**
   * @brief Get warmhole edge information for downward connections.
   * @return Const reference to the vector of TransEdgeInfo pointers.
   */
  const std::vector<TransEdgeInfo*>& GetTransDownEdgeInfo() { return trans_down_edge_infos_; }

  /**
   * @brief Get warmhole edge information for upward connections.
   * @return Const reference to the vector of TransEdgeInfo pointers.
   */
  const std::vector<TransEdgeInfo*>& GetTransUpEdgeInfo() { return trans_up_edge_infos_; }

private:
  // Member variables
  /**
   * @brief Unique identifier of this route node.
   */
  uint32_t id_;

  /**
   * @brief Base configuration parameters (node type, category, etc.).
   */
  NodeBase* base_;

  /**
   * @brief Geographic coordinates (x,y) of the node.
   */
  LngLat lnglat_;

  /**
   * @brief Administrative boundary information associated with the node.
   */
  AdminBoundaryNode* admin_bound_nodes_;

  /**
   * @brief Warmhole node information for downward connections.
   */
  TransNodeInfo* trans_down_node_infos_;

  /**
   * @brief Warmhole node information for upward connections.
   */
  TransNodeInfo* trans_up_node_infos_;

  /**
   * @brief List of boundary nodes connected to this node.
   */
  std::vector<BoundaryNode*> boundary_nodes_;

  /**
   * @brief List of edges connected to this node.
   */
  std::vector<ConnectedEdge*> connected_edges_;

  /**
   * @brief Warmhole edge information for downward connections between nodes.
   */
  std::vector<TransEdgeInfo*> trans_down_edge_infos_;

  /**
   * @brief Warmhole edge information for upward connections between nodes.
   */
  std::vector<TransEdgeInfo*> trans_up_edge_infos_;

  // Friend declaration for data access
  friend class RouteTileParser;
};
typedef std::vector<RouteNode> RouteNodeSet;
}  // namespace parser
}  // namespace aurora
#endif  // MAP_SRC_DATA_PROVIDER_INCLUDE_ROUTE_NODE_H
