/**
 * @file topol_edge.h
 * @brief Topological edge data structure definition
 *
 * @details This file defines the @c TopolEdge class and some related structure, representing basic
 * configuration parameters for a topological edge in a route network. It contains essential
 * information such as the edge's unique identifier and foundational attributes like direction or
 * type.
 *
 * <AUTHOR>
 * @date 2025-5-13
 */

#ifndef MAP_SRC_DATA_PROVIDER_INCLUDE_TOPOL_EDGE_H
#define MAP_SRC_DATA_PROVIDER_INCLUDE_TOPOL_EDGE_H

#include "export_type_def.h"
#include "route_data/route_data_def.h"

namespace aurora {
namespace parser {
#pragma pack(push, 1)
/**
 * @struct TopolEdgeBase
 * @brief Topological edge base structure containing road properties and connectivity information
 *
 * Stores essential attributes of a road segment including node connections, speed limits,
 * traffic rules, and physical characteristics. Uses bit fields for memory optimization.
 */
struct TopolEdgeBase {
  uint8_t function_class : 3;  ///< Road function class
  uint8_t edge_type : 2;       ///< Edge type (0-3)
  uint8_t direction : 2;       ///< Direction type (0-3)
  uint8_t need_toll : 1;       ///< Flag indicating if the edge requires toll payment (0/1)

  uint32_t start_node_id : 20;  ///< Start node ID
  uint32_t not_used0 : 4;
  uint32_t positive_speed_limit : 5;  ///< Positive direction speed limit
  uint32_t is_overhead : 1;           ///< Flag indicating if edge is overhead structure (0/1)
  uint32_t is_inner_edge : 1;         ///< Flag indicating if edge is inside the intersection  (0/1)
  uint32_t is_separate : 1;  ///< Flag indicating if edge is separated from other lanes (0/1)

  uint32_t end_node_id : 20;  ///< End node ID
  uint32_t not_used1 : 4;
  uint32_t negtive_speed_limit : 5;  ///< Reverse direction speed limit
  uint8_t is_area_edge : 1;          ///< Flag indicating if edge is part of area boundary (0/1)
  uint8_t is_city_edge : 1;          ///< Flag indicating if edge is within city limits (0/1)
  uint8_t is_ramp : 1;               ///< Flag indicating if edge is a ramp (0/1)

  uint8_t edge_form : 5;    ///< Edge geometry type
  uint8_t speed_grade : 3;  ///< Speed grade
  uint16_t length;          ///< Edge length in meters

  uint8_t forward_lane_count : 2;   ///< Forward direction lanes
  uint8_t backward_lane_count : 2;  ///< Backward direction lanes
  uint8_t lane_count : 4;           ///< Total lanes

  uint8_t road_class : 4;         ///< Road hierarchy class
  uint8_t is_left : 1;            ///< Flag indicating left-hand traffic (0/1)
  uint8_t is_limit_in_edge : 1;   ///< Flag indicating some limit pass in edge (0/1)
  uint8_t is_limit_out_edge : 1;  ///< Flag indicating some limit pass out edge (0/1)
  uint8_t not_used2 : 1;          ///< Reserved field (unused bits)

  uint8_t is_building : 1;              ///< Flag indicating edge is adjacent to buildings (0/1)
  uint8_t is_paved : 1;                 ///< Flag indicating surface is paved (0/1)
  uint8_t is_gate : 1;                  ///< Flag indicating presence of controlled gate (0/1)
  uint8_t no_crossing : 1;              ///< Flag indicating no pedestrian crossing (0/1)
  uint8_t is_private : 1;               ///< Flag indicating private access only (0/1)
  uint8_t has_traffic_light_start : 1;  ///< Flag indicating any traffic light in edge start (0/1)
  uint8_t has_traffic_light_end : 1;    ///< Flag indicating any traffic light in edge end (0/1)
  uint8_t not_used3 : 1;                ///< Reserved field (unused bits)
};

#pragma pack(pop)

/**
 * @brief Basic topological edge container storing essential configuration parameters.
 *
 * The @c TopolEdge class holds core data for a topological edge, including:
 * - Unique identifier
 * - Directional and type information (stored in @c TopolEdgeBase)
 */
class AURORA_EXPORT TopolEdge {
public:
  /**
   * @brief Default constructor initializing empty data containers.
   */
  TopolEdge();

  /**
   * @brief Get the unique identifier of this topological edge.
   * @return 32-bit unsigned integer edge ID.
   */
  uint32_t GetID() { return id_; }

  /**
   * @brief Get foundational configuration parameters of the edge.
   * @return Const pointer to the TopolEdgeBase structure, or @c nullptr if unset.
   */
  const TopolEdgeBase* GetBaseInfo() { return base_; }

private:
  /**
   * @brief Unique identifier of this topological edge.
   */
  uint32_t id_;

  /**
   * @brief Base configuration parameters (direction, type, etc.).
   */
  TopolEdgeBase* base_;

  // Friend declaration for data access
  friend class RouteTileParser;
};
typedef std::vector<TopolEdge> TopolEdgeSet;
}  // namespace parser
}  // namespace aurora
#endif  // MAP_SRC_DATA_PROVIDER_INCLUDE_TOPOL_EDGE_H
