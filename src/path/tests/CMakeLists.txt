# Set Google Test paths
set(GTEST_ROOT ${CMAKE_SOURCE_DIR}/third_party/googletest-1.16.x)
set(GTEST_INCLUDE_DIR ${GTEST_ROOT}/googletest/include)
# set(GTEST_LIBRARY ${GTEST_ROOT}/build/lib/libgtest.a)
# set(GTEST_MAIN_LIBRARY ${GTEST_ROOT}/build/lib/libgtest_main.a)

set(GTEST_LIBRARY ${CMAKE_SOURCE_DIR}/build/lib/libgtest.a)
set(GTEST_MAIN_LIBRARY ${CMAKE_SOURCE_DIR}/build/lib/libgtest_main.a)

# Add test executable
add_executable(double_bucket_queue_test
    double_bucket_queue_test.cpp
)

# Include directories
target_include_directories(double_bucket_queue_test
    PRIVATE
    ${GTEST_INCLUDE_DIR}
)

# Link against GTest and the path library
target_link_libraries(double_bucket_queue_test
    PRIVATE
    ${GTEST_LIBRARY}
    ${GTEST_MAIN_LIBRARY}
    aurora_path
)

# Add test to CTest
add_test(NAME double_bucket_queue_test COMMAND double_bucket_queue_test)

# Add astar_heuristic_test executable
add_executable(astar_heuristic_test astar_heuristic_test.cpp)

# Include directories for astar_heuristic_test
target_include_directories(astar_heuristic_test
    PRIVATE
    ${GTEST_INCLUDE_DIR}
)

# Link against GTest and the path library
target_link_libraries(astar_heuristic_test
    PRIVATE
    ${GTEST_LIBRARY}
    ${GTEST_MAIN_LIBRARY}
    aurora_path
)

# Add test to CTest
add_test(NAME astar_heuristic_test COMMAND astar_heuristic_test)

# Add path_module_test executable
add_executable(path_module_test path_module_test.cpp bidirectional_astar_test.cpp)

# Include directories for path_module_test
target_include_directories(path_module_test
    PRIVATE
    ${GTEST_INCLUDE_DIR}
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/base/include
    ${CMAKE_SOURCE_DIR}/third_party/spdlog/include
)

# Link against GTest and the path library
target_link_libraries(path_module_test
    PRIVATE
    ${GTEST_LIBRARY}
    ${GTEST_MAIN_LIBRARY}
    aurora_path
    aurora_base
    pthread
)

# Add test to CTest
add_test(NAME path_module_test COMMAND path_module_test)

# Add interrupt_test executable
add_executable(interrupt_test interrupt_test.cpp)

# Include directories for interrupt_test
target_include_directories(interrupt_test
    PRIVATE
    ${GTEST_INCLUDE_DIR}
    ${CMAKE_SOURCE_DIR}/src
    ${CMAKE_SOURCE_DIR}/base/include
    ${CMAKE_SOURCE_DIR}/third_party/spdlog/include
)

# Link against GTest and the path library
target_link_libraries(interrupt_test
    PRIVATE
    ${GTEST_LIBRARY}
    ${GTEST_MAIN_LIBRARY}
    aurora_path
    aurora_base
    pthread
)

# Add test to CTest
add_test(NAME interrupt_test COMMAND interrupt_test)