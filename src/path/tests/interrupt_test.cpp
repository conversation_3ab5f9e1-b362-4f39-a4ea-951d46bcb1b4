// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Test for path calculation interruption functionality
//

#include <gtest/gtest.h>
#include <thread>
#include <chrono>
#include <memory>

#include "path_module.h"
#include "path_def.h"
#include "base/include/pointll.h"
#include "base/include/errorcode.h"

using namespace aurora::path;
using namespace aurora;

class InterruptTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 创建路径模块
        path_module_ = std::make_shared<PathModule>();

        // 准备模块配置（使用绝对路径）
        std::string config_path = "/home/<USER>/dlc/map_engine/src/path/config/path.yaml";
        int prepare_result = path_module_->Prepare(config_path);
        if (prepare_result != 0) {
            GTEST_SKIP() << "Failed to prepare path module with config: " << config_path;
        }

        // 创建接口查找器（返回空指针，表示没有其他模块）
        InterfaceFinder finder = [](const ModuleId id) -> std::shared_ptr<IInterface> {
            return nullptr;  // 测试中不需要其他模块
        };

        // 初始化模块
        int init_result = path_module_->Init(finder);
        if (init_result != 0) {
            GTEST_SKIP() << "Failed to initialize path module";
        }

        // 启动模块
        int start_result = path_module_->Start();
        if (start_result != 0) {
            GTEST_SKIP() << "Failed to start path module";
        }

        // 获取路径接口
        path_interface_ = std::dynamic_pointer_cast<PathInterface>(path_module_->GetInterface());
        if (!path_interface_) {
            GTEST_SKIP() << "Failed to get path interface";
        }

        // 添加测试监听器
        test_listener_ = std::make_shared<TestPathListener>();
        bool listener_added = path_interface_->AddPathListener(test_listener_);
        if (!listener_added) {
            GTEST_SKIP() << "Failed to add path listener";
        }
    }
    
    void TearDown() override {
        if (path_interface_ && test_listener_) {
            path_interface_->RemovePathListener(test_listener_);
        }
        if (path_module_) {
            path_module_->Stop();
            path_module_->UnInit();
        }
    }
    
    class TestPathListener : public PathListener {
    public:
        void OnPathResult(const PathQueryPtr& query, const PathResultPtr& result) override {
            std::lock_guard<std::mutex> lock(mutex_);
            results_.push_back(result);
            result_received_ = true;
            cv_.notify_one();
        }
        
        bool WaitForResult(int timeout_ms = 5000) {
            std::unique_lock<std::mutex> lock(mutex_);
            return cv_.wait_for(lock, std::chrono::milliseconds(timeout_ms), 
                               [this] { return result_received_; });
        }
        
        PathResultPtr GetLastResult() {
            std::lock_guard<std::mutex> lock(mutex_);
            return results_.empty() ? nullptr : results_.back();
        }
        
        void Reset() {
            std::lock_guard<std::mutex> lock(mutex_);
            results_.clear();
            result_received_ = false;
        }
        
    private:
        std::mutex mutex_;
        std::condition_variable cv_;
        std::vector<PathResultPtr> results_;
        bool result_received_ = false;
    };
    
    std::shared_ptr<PathModule> path_module_;
    std::shared_ptr<PathInterface> path_interface_;
    std::shared_ptr<TestPathListener> test_listener_;
};

// 测试算路请求的取消功能
TEST_F(InterruptTest, TestCancelRequest) {
    // 创建一个长距离的路径查询（可能需要较长时间计算）
    auto query = std::make_shared<PathQuery>();
    
    // 设置起点（北京）
    auto start_point = std::make_shared<PathLandmark>();
    start_point->valid = true;
    start_point->waypoint_type = WayPointType::kStartPoint;
    start_point->landmark_type = LandmarkType::kClick;
    start_point->pt = PointLL(116.3974, 39.9093);  // 北京天安门

    // 设置终点（上海）
    auto end_point = std::make_shared<PathLandmark>();
    end_point->valid = true;
    end_point->waypoint_type = WayPointType::kEndPoint;
    end_point->landmark_type = LandmarkType::kClick;
    end_point->pt = PointLL(121.4737, 31.2304);  // 上海人民广场

    query->path_points = {start_point, end_point};
    query->strategy = PathStrategy::kTimeFirst;
    query->mode = PathMode::kOffline;
    
    // 发起算路请求
    std::string uuid = path_interface_->RequestPath(query);
    ASSERT_FALSE(uuid.empty());
    
    // 等待一小段时间让算路开始
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 取消请求
    int32_t cancel_result = path_interface_->CancelRequest(uuid);
    EXPECT_EQ(cancel_result, static_cast<int32_t>(ErrorCode::kErrorCodeOk));
    
    // 等待结果
    bool got_result = test_listener_->WaitForResult(3000);
    
    if (got_result) {
        auto result = test_listener_->GetLastResult();
        ASSERT_NE(result, nullptr);
        EXPECT_EQ(result->uuid, uuid);
        
        // 检查是否是取消状态
        if (result->code == static_cast<uint32_t>(ErrorCode::kErrorCodePathUserCalcel)) {
            EXPECT_EQ(result->status, "failed");
            std::cout << "✅ Request was successfully cancelled during execution" << std::endl;
        } else {
            std::cout << "ℹ️ Request completed before cancellation (code: " 
                      << result->code << ", status: " << result->status << ")" << std::endl;
        }
    } else {
        std::cout << "⚠️ No result received within timeout" << std::endl;
    }
}

// 测试取消不存在的请求
TEST_F(InterruptTest, TestCancelNonExistentRequest) {
    std::string fake_uuid = "non-existent-uuid-12345";
    
    int32_t result = path_interface_->CancelRequest(fake_uuid);
    EXPECT_EQ(result, static_cast<int32_t>(ErrorCode::kErrorCodeFailed));
}

// 测试正常算路流程（不取消）
TEST_F(InterruptTest, TestNormalRouting) {
    auto query = std::make_shared<PathQuery>();
    
    // 设置较近的起终点
    auto start_point = std::make_shared<PathLandmark>();
    start_point->valid = true;
    start_point->waypoint_type = WayPointType::kStartPoint;
    start_point->landmark_type = LandmarkType::kClick;
    start_point->pt = PointLL(116.3974, 39.9093);  // 北京天安门

    auto end_point = std::make_shared<PathLandmark>();
    end_point->valid = true;
    end_point->waypoint_type = WayPointType::kEndPoint;
    end_point->landmark_type = LandmarkType::kClick;
    end_point->pt = PointLL(116.4074, 39.9193);  // 附近1km

    query->path_points = {start_point, end_point};
    query->strategy = PathStrategy::kTimeFirst;
    query->mode = PathMode::kOffline;
    
    std::string uuid = path_interface_->RequestPath(query);
    ASSERT_FALSE(uuid.empty());
    
    // 等待结果
    bool got_result = test_listener_->WaitForResult(10000);
    EXPECT_TRUE(got_result);
    
    if (got_result) {
        auto result = test_listener_->GetLastResult();
        ASSERT_NE(result, nullptr);
        EXPECT_EQ(result->uuid, uuid);
        std::cout << "✅ Normal routing completed with status: " << result->status 
                  << ", code: " << result->code << std::endl;
    }
}
