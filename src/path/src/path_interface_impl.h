// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-04-30
//

#ifndef AURORA_PATH_SRC_PATH_INTERFACE_IMPL_H_
#define AURORA_PATH_SRC_PATH_INTERFACE_IMPL_H_

#include <memory>
#include <string>
#include <vector>
#include <mutex>

#include "path/include/path_module.h"
#include "path/src/route_algorithm/route_algorithm.h"
#include "path/src/route_algorithm/bidirectional_astar.h"
#include "path/src/route_algorithm/dijkstra.h"
#include "path/src/route_algorithm/path_engine.h"
#include "path/src/map_matching/map_matching_manager.h"
#include "path/src/debug/debug_manager.h"
#include "path/src/request/path_request_manager.h"
#include "path/src/config/path_config.h"
#include "base/include/loopthread.h"
#include "location_module.h"
#include "graph_reader/graph_reader.h"

namespace aurora {
namespace path {

class PathInterfaceImpl :
                      public PathInterface,
                      public std::enable_shared_from_this<PathInterfaceImpl> {
 public:
    PathInterfaceImpl();
    ~PathInterfaceImpl() override = default;

    // Implement PathModule methods
    int Prepare(const std::string& config);

    int32_t SetParams(const ParameterMap &parameters);
    std::shared_ptr<IInterface> GetInterface();
    int Init(InterfaceFinder finder);
    int Start();
    int Stop();
    int UnInit();

    ModuleId GetModuleId() const override;

    // Implement PathInterface methods
    std::string RequestPath(const PathQueryPtr& query) override;
    int32_t CancelRequest(const std::string& uuid) override;
    bool AddPathListener(const PathListenerPtr& listener) override;
    bool RemovePathListener(const PathListenerPtr& listener) override;

    int32_t DoLandMarkMatching(PathLandmarkPtr& landmark);

    void OnMapMatchingCallback(const loc::MatchResult& result);

    std::string ReRoute(const PathQueryPtr& query);

 private:
    std::vector<PathListenerPtr> listeners_;

    // 管理器组件
    std::unique_ptr<PathEngine> path_engine_;
    std::unique_ptr<MapMatchingManager> map_matching_manager_;
    std::unique_ptr<DebugManager> debug_manager_;
    std::unique_ptr<PathRequestManager> request_manager_;

    // 核心组件
    std::shared_ptr<GraphReader> graph_reader_ptr_;
    std::shared_ptr<loc::ILocation> loc_;
    std::unique_ptr<LoopThread> path_thread_;  // Thread for path calculation

};

}  // namespace path
}  // namespace aurora

#endif  // AURORA_PATH_SRC_PATH_INTERFACE_IMPL_H_
