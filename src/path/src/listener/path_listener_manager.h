// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by Augment Agent on 2025-07-07
//

#ifndef AURORA_PATH_SRC_LISTENER_PATH_LISTENER_MANAGER_H_
#define AURORA_PATH_SRC_LISTENER_PATH_LISTENER_MANAGER_H_

#include <memory>
#include <vector>
#include <mutex>
#include <algorithm>

#include "path_module.h"
#include "logger.h"

namespace aurora {
namespace path {

/**
 * PathListenerManager - 路径监听器管理器
 * 
 * 职责：
 * - 管理路径结果监听器的注册和注销
 * - 分发路径结果到所有注册的监听器
 * - 提供线程安全的监听器管理
 * - 处理监听器的生命周期
 */
class PathListenerManager {
public:
    /**
     * 构造函数
     */
    PathListenerManager() = default;
    
    /**
     * 析构函数
     */
    ~PathListenerManager() = default;

    // 禁用拷贝构造和赋值
    PathListenerManager(const PathListenerManager&) = delete;
    PathListenerManager& operator=(const PathListenerManager&) = delete;

    /**
     * 添加路径结果监听器
     * @param listener 监听器指针
     * @return true 如果成功添加
     */
    bool AddListener(PathListenerPtr listener);

    /**
     * 移除路径结果监听器
     * @param listener 监听器指针
     * @return true 如果成功移除
     */
    bool RemoveListener(PathListenerPtr listener);
    
    /**
     * 移除所有监听器
     */
    void RemoveAllListeners();
    
    /**
     * 分发路径结果到所有监听器
     * @param query 路径查询
     * @param result 路径结果
     */
    void NotifyPathResult(const PathQueryPtr& query, const PathResultPtr& result);
    
    /**
     * 获取监听器数量
     * @return 监听器数量
     */
    size_t GetListenerCount() const;
    
    /**
     * 检查是否有监听器
     * @return true 如果有监听器
     */
    bool HasListeners() const;
    
    /**
     * 检查指定监听器是否已注册
     * @param listener 监听器指针
     * @return true 如果已注册
     */
    bool HasListener(PathListenerPtr listener) const;

private:
    /**
     * 清理无效的监听器（弱引用已失效）
     * @return 清理的监听器数量
     */
    size_t CleanupInvalidListeners();
    
    /**
     * 安全地调用监听器方法
     * @param listener 监听器指针
     * @param func 要调用的函数
     */
    template<typename Func>
    void SafeCallListener(PathListenerPtr listener, Func&& func);

private:
    // 监听器列表
    std::vector<std::weak_ptr<PathListener>> listeners_;
    
    // 保护监听器列表的互斥锁
    mutable std::mutex listeners_mutex_;
};

// 类型别名
using PathListenerManagerPtr = std::unique_ptr<PathListenerManager>;

}  // namespace path
}  // namespace aurora

#endif  // AURORA_PATH_SRC_LISTENER_PATH_LISTENER_MANAGER_H_
