// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by Augment Agent on 2025-07-07
//

#include "path_listener_manager.h"

namespace aurora {
namespace path {

bool PathListenerManager::AddListener(PathListenerPtr listener) {
    if (!listener) {
        LOG_WARN("PathListenerManager: Cannot add null listener");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(listeners_mutex_);
    
    // 检查是否已经存在
    for (const auto& weak_listener : listeners_) {
        if (auto existing_listener = weak_listener.lock()) {
            if (existing_listener == listener) {
                LOG_WARN("PathListenerManager: Listener already exists");
                return false;
            }
        }
    }
    
    // 添加新监听器
    listeners_.emplace_back(listener);
    
    LOG_DEBUG("PathListenerManager: Added listener, total count: {}", listeners_.size());
    return true;
}

bool PathListenerManager::RemoveListener(PathListenerPtr listener) {
    if (!listener) {
        LOG_WARN("PathListenerManager: Cannot remove null listener");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(listeners_mutex_);
    
    auto it = std::remove_if(listeners_.begin(), listeners_.end(),
        [listener](const std::weak_ptr<PathListener>& weak_listener) {
            if (auto existing_listener = weak_listener.lock()) {
                return existing_listener == listener;
            }
            return true; // 同时清理已失效的弱引用
        });
    
    bool removed = (it != listeners_.end());
    listeners_.erase(it, listeners_.end());
    
    if (removed) {
        LOG_DEBUG("PathListenerManager: Removed listener, remaining count: {}", listeners_.size());
    } else {
        LOG_WARN("PathListenerManager: Listener not found for removal");
    }
    
    return removed;
}

void PathListenerManager::RemoveAllListeners() {
    std::lock_guard<std::mutex> lock(listeners_mutex_);
    
    size_t count = listeners_.size();
    listeners_.clear();
    
    LOG_INFO("PathListenerManager: Removed all {} listeners", count);
}

void PathListenerManager::NotifyPathResult(const PathQueryPtr& query, const PathResultPtr& result) {
    std::vector<PathListenerPtr> active_listeners;
    
    // 获取所有有效的监听器
    {
        std::lock_guard<std::mutex> lock(listeners_mutex_);
        
        for (const auto& weak_listener : listeners_) {
            if (auto listener = weak_listener.lock()) {
                active_listeners.push_back(listener);
            }
        }
        
        // 清理无效的监听器
        CleanupInvalidListeners();
    }
    
    // 通知所有监听器
    for (auto& listener : active_listeners) {
        SafeCallListener(listener, [&query, &result](PathListenerPtr l) {
            l->OnPathResult(query, result);
        });
    }

    LOG_DEBUG("PathListenerManager: Notified {} listeners of path result",
             active_listeners.size());
}



size_t PathListenerManager::GetListenerCount() const {
    std::lock_guard<std::mutex> lock(listeners_mutex_);
    
    size_t active_count = 0;
    for (const auto& weak_listener : listeners_) {
        if (weak_listener.lock()) {
            active_count++;
        }
    }
    
    return active_count;
}

bool PathListenerManager::HasListeners() const {
    return GetListenerCount() > 0;
}

bool PathListenerManager::HasListener(PathListenerPtr listener) const {
    if (!listener) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(listeners_mutex_);
    
    for (const auto& weak_listener : listeners_) {
        if (auto existing_listener = weak_listener.lock()) {
            if (existing_listener == listener) {
                return true;
            }
        }
    }
    
    return false;
}

size_t PathListenerManager::CleanupInvalidListeners() {
    // 注意：此方法假设调用者已经持有锁
    
    auto it = std::remove_if(listeners_.begin(), listeners_.end(),
        [](const std::weak_ptr<PathListener>& weak_listener) {
            return weak_listener.expired();
        });
    
    size_t removed_count = std::distance(it, listeners_.end());
    listeners_.erase(it, listeners_.end());
    
    if (removed_count > 0) {
        LOG_DEBUG("PathListenerManager: Cleaned up {} invalid listeners", removed_count);
    }
    
    return removed_count;
}

template<typename Func>
void PathListenerManager::SafeCallListener(PathListenerPtr listener, Func&& func) {
    try {
        if (listener) {
            func(listener);
        }
    } catch (const std::exception& e) {
        LOG_ERROR("PathListenerManager: Exception in listener callback: {}", e.what());
    } catch (...) {
        LOG_ERROR("PathListenerManager: Unknown exception in listener callback");
    }
}

}  // namespace path
}  // namespace aurora
