// Copyright (c) 2025 BYD Corporation. All rights reserved.
// Author: <EMAIL>
//
// Created: 2025-05-12

#ifndef AURORA_PATH_SRC_GRAPH_READER_TILECACHE_H_
#define AURORA_PATH_SRC_GRAPH_READER_TILECACHE_H_

#include <unordered_map>
#include <unordered_set>
#include <list>
#include <cstdint>
#include <memory>
#include "../base/data_interface.h"
#include "logger.h"

namespace aurora {
namespace path {

class TileCache {
public:
    TileCache();
    virtual ~TileCache();

    // Reserve space for the specified number of tiles
    virtual void Reserve(size_t tile_size);

    // Check if a tile exists in the cache
    virtual bool Contains(const uint64_t& tile_id);

    // Add a tile to the cache with LRU tracking
    virtual void Put(const uint64_t tile_id, graphTilePtr tile_ptr);

    // Get a tile from the cache, returns nullptr if not found
    virtual const graphTilePtr& Get(const uint64_t& tile_id);

    // Check if a tile exists in the cache by tile ID
    virtual bool IsTileExist(const uint64_t tile_id) const;

    // Release all tiles from the cache
    virtual void Release();

    // Get the maximum cache size
    virtual size_t GetMaxCacheSize() const { return max_cache_size_; }

    virtual size_t GetCacheSize() const { return cache_.size(); }

    virtual void PrintMemInfo() const {
        size_t num_link_node = 0;
        for (auto& tile : cache_) {
            // LOG_INFO("Tile ID: {}", tile.first);
            // LOG_INFO("{}", tile.second->memory_pool_->get_statistics());
            num_link_node += tile.second->header()->edge_count + tile.second->header()->node_count;
        }
        LOG_INFO("Total link and node count: {}", num_link_node);
    }

protected:
    // Set of tile IDs currently in the cache
    std::unordered_set<uint64_t> tile_ids_;

    // Main cache storage mapping tile keys to tile pointers
    std::unordered_map<uint64_t, graphTilePtr> cache_;

    // List to maintain LRU order (most recently used at front)
    std::list<uint64_t> lru_list_;

    // Map to track positions in the LRU list for O(1) updates
    std::unordered_map<uint64_t, std::list<uint64_t>::iterator> lru_tracker_;

    // Maximum number of tiles to cache
    size_t max_cache_size_;

    // Remove the least recently used tile from cache
    void RemoveLRU();

    // Update the access order for LRU tracking
    void TouchTile(uint64_t key);
};

class SimpleTileCache : public TileCache {
public:
    SimpleTileCache() = default;
    ~SimpleTileCache() override = default;

    void Reserve(size_t tile_size) override;
    bool Contains(const uint64_t& tile_id) override;
    void Put(const uint64_t tile_id, graphTilePtr tile_ptr) override;
    const graphTilePtr& Get(const uint64_t& tile_id) override;
    bool IsTileExist(const uint64_t tile_id) const override;
    void Release() override;
    size_t GetMaxCacheSize() const override { return max_cache_size_; }

private:
    // 只用自己的cache_
    // size_t max_cache_size_ = 0; // 复用基类的
};

using TileCachePtr = std::shared_ptr<TileCache>;

} // namespace path
} // namespace aurora

#endif  // AURORA_PATH_SRC_GRAPH_READER_TILECACHE_H_
