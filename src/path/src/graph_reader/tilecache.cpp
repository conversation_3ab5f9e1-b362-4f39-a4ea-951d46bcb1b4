// Copyright (c) 2025 BYD Corporation. All rights reserved.
// Author: <EMAIL>
//
// Created: 2025-05-12

#include "tilecache.h"

namespace aurora {
namespace path {

TileCache::TileCache() : max_cache_size_(5120) {}

TileCache::~TileCache() {
    Release();
}

void TileCache::Reserve(size_t tile_size) {
    max_cache_size_ = tile_size;
    cache_.reserve(tile_size);
    tile_ids_.reserve(tile_size);
}

bool TileCache::Contains(const uint64_t& tile_id) {
    return cache_.find(tile_id) != cache_.end();
}

void TileCache::Put(const uint64_t tile_id, graphTilePtr tile_ptr) {
    if (!tile_ptr) {
        return;
    }

    uint64_t key = tile_ptr->header()->tile_id.value;

    // If cache is full, remove least recently used tile
    if (cache_.size() >= max_cache_size_) {
        RemoveLRU();
    }

    // Add new tile to cache
    cache_[key] = tile_ptr;
    tile_ids_.insert(tile_id);

    // Update LRU tracking
    // TouchTile(key);
}

const graphTilePtr& TileCache::Get(const uint64_t& tile_id) {
    auto it = cache_.find(tile_id);
    if (it == cache_.end()) {
        static const graphTilePtr kEmptyPtr = nullptr;  // 静态空对象
        return kEmptyPtr;
    }

    // Update LRU tracking
    // TouchTile(tile_id);
    return it->second;
}

bool TileCache::IsTileExist(const uint64_t tile_id) const {
    return tile_ids_.find(tile_id) != tile_ids_.end();
}

void TileCache::Release() {
    cache_.clear();
    tile_ids_.clear();
    lru_list_.clear();
    lru_tracker_.clear();
}

void TileCache::RemoveLRU() {
    if (lru_list_.empty()) {
        return;
    }

    // Get the least recently used key
    uint64_t lru_key = lru_list_.back();

    // Remove from all tracking structures
    cache_.erase(lru_key);
    lru_tracker_.erase(lru_key);
    lru_list_.pop_back();

    // Remove from tile_ids_ (convert key back to tile_id if necessary)
    tile_ids_.erase(static_cast<uint64_t>(lru_key));
}

void TileCache::TouchTile(uint64_t key) {
    auto tracker_it = lru_tracker_.find(key);
    
    if (tracker_it != lru_tracker_.end()) {
        // Remove from current position
        lru_list_.erase(tracker_it->second);
    }

    // Add to front of LRU list
    lru_list_.push_front(key);
    lru_tracker_[key] = lru_list_.begin();
}

void SimpleTileCache::Reserve(size_t tile_size) {
    max_cache_size_ = tile_size;
    cache_.reserve(tile_size);
}

bool SimpleTileCache::Contains(const uint64_t& tile_id) {
    return cache_.find(tile_id) != cache_.end();
}

void SimpleTileCache::Put(const uint64_t tile_id, graphTilePtr tile_ptr) {
    if (cache_.size() >= max_cache_size_) {
        // If cache is full, remove the first element
        cache_.erase(cache_.begin());
    }
    cache_[tile_id] = tile_ptr;
}

const graphTilePtr& SimpleTileCache::Get(const uint64_t& tile_id) {
    auto it = cache_.find(tile_id);
    static const graphTilePtr kEmptyPtr = nullptr;  // 静态空对象
    return it != cache_.end() ? it->second : kEmptyPtr;
}

bool SimpleTileCache::IsTileExist(const uint64_t tile_id) const {
    return cache_.find(tile_id) != cache_.end();
}

void SimpleTileCache::Release() {
    cache_.clear();
}

} // namespace path
} // namespace aurora
