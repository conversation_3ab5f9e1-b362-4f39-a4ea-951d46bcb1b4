// Copyright (c) 2025 BYD Corporation. All rights reserved.
// Author: <EMAIL>
//
// Created: 2025-05-12

#ifndef AURORA_PATH_SRC_GRAPH_READER_GRAPH_READER_H_
#define AURORA_PATH_SRC_GRAPH_READER_GRAPH_READER_H_

#include <string>
#include <memory>
#include <atomic>
#include <mutex>
#include <unordered_map>
#include "../base/data_interface.h"
#include "../debug/geojson_reader.h"
#include "tilecache.h"
#include "data_provider.h"
#include "base/include/logger.h"
#include "../debug/geojson_writter.h"
#include "base/index_tile.h"
#include "path_def.h"
#include "../config/path_config.h"

namespace aurora {
namespace path {

using EdgeId = GraphId;
using NodeId = GraphId;
class GraphReader {
public:
    GraphReader();

    GraphReader(const std::string& data_dir);
    ~GraphReader();

    bool InitDataProvider();

    bool Init();

    void Clear();

    void SwitchRouteTileCacheMode(bool cache_flag);

    // Load map data from GeoJSON files
    bool LoadGeoJsonMap(const std::string& edge_file, const std::string& node_file);

    bool LoadTileData(const PointLL& pointll, uint8_t level, double radius);

    bool GetLinksByRange(const PointLL& pt, double radius, std::vector<EdgeInfoPtr>& edges);

    /**
     * Calculate the projection distance and point from a point to an edge
     * @param pt The point to project
     * @param edge_geos The edge geometry points
     * @param proj_dis The projection distance (output)
     * @param proj_pt The projection point (output)
     * @param proj_index The projection index (output)
     */
    void proj2d(const PointLL& pt, const std::vector<PointLL>& edge_geos, double& proj_dis, PointLL& proj_pt, size_t& proj_index);

    // Get edge information by edge ID
    const DirectEdgeInfoRawPtr& GetDirectEdgeInfo(const EdgeId& edge_id, bool forward);

    // get forward & reverse edge, load tile if not exist
    DirectEdgeInfoRawPtr GetDirectEdgeInfo(const EdgeId& edge_id);

    parser::TopolEdge* GetTopoEdgeById(const parser::FeatureID& feature_id);

    // get augment edge information by edge ID
    AugmentEdgeInfoPtr GetAugmentEdgeInfo(const EdgeId& edge_id, bool cache = true);

    // Get node information by node ID
    const NodeInfoRawPtr& GetNodeInfo(const NodeId& node_id, bool need_load = false);

    graphTilePtr GetTile(const GraphId& graph_id);

    void LoadTile(const uint64_t& tile_id);

    std::vector<RestrictionPtr> GetEnterRestrictions(const DirectEdgeInfoRawPtr& edge);

    std::vector<RestrictionPtr> GetExitRestrictions(const DirectEdgeInfoRawPtr& edge);

    bool GetAllEnterEdges(const NodeInfoRawPtr& node_ptr, std::vector<DirectEdgeInfoRawPtr>& edges);

    bool GetAllExitEdges(const NodeInfoRawPtr& node_ptr, std::vector<DirectEdgeInfoRawPtr>& edges);


    // Write route data to file
    void WriteRouteData(const std::string& file_name, const PointLL& pt, double radius);

    // Write route tile package to file
    void WriteRouteTilePackage(const std::string& file_name, const aurora::parser::RouteTilePackagePtr& tile_package);


    bool StaticMatching(PathLandmarkPtr landmark, double radius);

    bool FilterCloestEdge(PathLandmarkPtr landmark, const std::vector<EdgeInfoPtr>& edges);

    bool MatchMultiEdges(PathLandmarkPtr landmark, const std::vector<EdgeInfoPtr>& edges, double search_radius);

    bool EgoMatch(const PointLL& pt, GraphId id, uint8_t direction, double offset, const PathLandmarkPtr& landmark);

    void PrintStatistics();


    // todo:clear before each calc route
    void UpdateSearchCache(bool forward, const graphTilePtr& tile_ptr);

private:
    // Store the graph tile
    graphTilePtr tile_;

    // GeoJSON reader for loading map data
    GeoJsonReader json_reader_;

    TileCachePtr tile_cache_;

    std::unordered_map<uint64_t, IndexTilePtr> index_tile_map_;

    std::shared_ptr<aurora::parser::DataProvider> data_provider_ptr_;
    static std::once_flag init_flag_;

    std::string data_dir_ = "../route"; // Default value

    std::shared_ptr<parser::RouteTileReader> tile_reader_;
    std::unique_ptr<PathMemoryPool> memory_pool_;

    // last used tile of each direction
    std::vector<graphTilePtr> search_tiles_;
};
using GraphReaderPtr = std::shared_ptr<GraphReader>;

} // namespace path
} // namespace aurora

#endif  // AURORA_PATH_SRC_GRAPH_READER_GRAPH_READER_H_
