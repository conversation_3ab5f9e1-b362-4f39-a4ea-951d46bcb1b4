/**
 * @file path_memory_pool.h
 * @brief Modern C++17 Memory Pool Implementation (Single-threaded optimized)
 * <AUTHOR>
 * @date 2025-06-26
 *
 * A high-performance memory pool designed for path planning and routing applications.
 * Features:
 * - Self-contained with no external dependencies
 * - Single-threaded optimized (no locking overhead)
 * - RAII memory management
 * - Modern C++ idioms and best practices
 */

#pragma once

#include <cstdint>
#include <cstring>
#include <memory>
#include <vector>
#include <cassert>
#include <stdexcept>
#include <iostream>
#include <iomanip>

namespace aurora::path {

/**
 * @brief Configuration constants for the memory pool
 */
struct MemoryPoolConfig {
    static constexpr std::size_t kDefaultBlockSize = 2 * 1024 * 1024;  // 2MB per block
    static constexpr std::size_t kMaxBlockCount = 500;                 // Maximum number of blocks
    static constexpr std::size_t kAlignment = 8;                       // Memory alignment in bytes (must be power of 2)
    static constexpr std::size_t kMinBlockSize = 512 * 1024;            // Minimum block size (512KB)
    static constexpr std::size_t kMaxBlockSize = 64 * 1024 * 1024;     // Maximum block size (64MB)

    // Compile-time validation
    static_assert((kAlignment & (kAlignment - 1)) == 0, "Alignment must be a power of 2");
    static_assert(kDefaultBlockSize >= kMinBlockSize, "Default block size too small");
    static_assert(kDefaultBlockSize <= kMaxBlockSize, "Default block size too large");
};

/**
 * @brief Exception thrown when memory pool operations fail
 */
class MemoryPoolException : public std::runtime_error {
public:
    explicit MemoryPoolException(const std::string& message) 
        : std::runtime_error("MemoryPool Error: " + message) {}
};

/**
 * @brief A single memory buffer within the pool
 */
class MemoryBuffer {
public:
    /**
     * @brief Construct a new Memory Buffer object
     * @param size Size of the buffer to allocate
     */
    explicit MemoryBuffer(std::size_t size = MemoryPoolConfig::kDefaultBlockSize);
    
    /**
     * @brief Destroy the Memory Buffer object
     */
    ~MemoryBuffer() = default;
    
    // Non-copyable but movable
    MemoryBuffer(const MemoryBuffer&) = delete;
    MemoryBuffer& operator=(const MemoryBuffer&) = delete;
    MemoryBuffer(MemoryBuffer&&) = default;
    MemoryBuffer& operator=(MemoryBuffer&&) = default;
    
    /**
     * @brief Allocate memory from this buffer
     * @param size Size of memory to allocate
     * @return Pointer to allocated memory, or nullptr if insufficient space
     */
    void* allocate(std::size_t size);
    
    /**
     * @brief Get the total size of this buffer
     * @return Total buffer size in bytes
     */
    [[nodiscard]] std::size_t size() const noexcept { return buffer_.size(); }
    
    /**
     * @brief Get the used size of this buffer
     * @return Used buffer size in bytes
     */
    [[nodiscard]] std::size_t used_size() const noexcept { return used_size_; }
    
    /**
     * @brief Get the available size in this buffer
     * @return Available buffer size in bytes
     */
    [[nodiscard]] std::size_t available_size() const noexcept { 
        return buffer_.size() - used_size_; 
    }
    
    /**
     * @brief Check if buffer is empty
     * @return true if no memory has been allocated from this buffer
     */
    [[nodiscard]] bool empty() const noexcept { return used_size_ == 0; }
    
    /**
     * @brief Reset the buffer (mark all memory as available)
     */
    void reset() noexcept;
    
    /**
     * @brief Zero out all used memory in the buffer
     */
    void zero_memory() noexcept;

    /**
     * @brief Free the buffer memory back to OS
     * @note This will invalidate all pointers allocated from this buffer
     */
    void free_memory() noexcept;

private:
    std::vector<std::uint8_t> buffer_;  ///< The actual memory buffer
    std::size_t used_size_ = 0;         ///< Currently used size
    
    /**
     * @brief Align size to the configured alignment boundary
     * @param size Size to align
     * @return Aligned size
     */
    static std::size_t align_size(std::size_t size) noexcept;
};

/**
 * @brief High-performance memory pool for path planning applications
 * 
 * This memory pool provides fast allocation of temporary memory blocks
 * commonly needed during path planning and routing calculations.
 * 
 * Features:
 * - Fast O(1) allocation for most cases
 * - Automatic buffer management
 * - Single-threaded optimized (no locking overhead)
 * - Memory usage statistics
 * - Bulk reset and cleanup operations
 */
class PathMemoryPool {
public:
    /**
     * @brief Construct a new Path Memory Pool object
     * @param initial_block_size Size of each memory block (default: 2MB)
     * @param max_blocks Maximum number of blocks to allocate (default: 30)
     */
    explicit PathMemoryPool(
        std::size_t initial_block_size = MemoryPoolConfig::kDefaultBlockSize,
        std::size_t max_blocks = MemoryPoolConfig::kMaxBlockCount
    );
    
    /**
     * @brief Destroy the Path Memory Pool object
     */
    ~PathMemoryPool();
    
    // Non-copyable but movable
    PathMemoryPool(const PathMemoryPool&) = delete;
    PathMemoryPool& operator=(const PathMemoryPool&) = delete;
    PathMemoryPool(PathMemoryPool&&) = default;
    PathMemoryPool& operator=(PathMemoryPool&&) = default;
    
    /**
     * @brief Allocate memory from the pool
     * @param size Size of memory to allocate in bytes
     * @return Pointer to allocated memory
     * @throws MemoryPoolException if allocation fails
     */
    void* allocate(std::size_t size);
    
    /**
     * @brief Allocate and construct an object of type T
     * @tparam T Type of object to construct
     * @tparam Args Constructor argument types
     * @param args Constructor arguments
     * @return Pointer to constructed object
     */
    template<typename T, typename... Args>
    T* construct(Args&&... args) {
        void* ptr = allocate(sizeof(T));
        return new(ptr) T(std::forward<Args>(args)...);
    }
    
    /**
     * @brief Allocate an array of objects of type T
     * @tparam T Type of objects in the array
     * @param count Number of objects to allocate
     * @return Pointer to the first object in the array
     */
    template<typename T>
    T* allocate_array(std::size_t count) {
        return static_cast<T*>(allocate(sizeof(T) * count));
    }
    
    /**
     * @brief Reset all buffers (mark all memory as available)
     */
    void reset() noexcept;

    /**
     * @brief Zero out all used memory in all buffers
     */
    void zero_memory() noexcept;

    /**
     * @brief Free all buffers and release memory back to OS
     * @note This will invalidate all previously allocated pointers
     */
    void free_buffers() noexcept;

    /**
     * @brief Free unused buffers (empty buffers only)
     * @note This is safer as it only frees completely unused buffers
     */
    void free_unused_buffers() noexcept;

    /**
     * @brief Get total allocated memory size
     * @return Total memory size in bytes
     */
    [[nodiscard]] std::size_t total_size() const noexcept;
    
    /**
     * @brief Get total used memory size
     * @return Used memory size in bytes
     */
    [[nodiscard]] std::size_t used_size() const noexcept;
    
    /**
     * @brief Get number of active buffers
     * @return Number of buffers currently in use
     */
    [[nodiscard]] std::size_t buffer_count() const noexcept;
    
    /**
     * @brief Get memory usage statistics
     * @return String containing detailed memory usage information
     */
    [[nodiscard]] std::string get_statistics() const;
    
    /**
     * @brief Print memory usage statistics to output stream
     * @param os Output stream to print to
     */
    void print_statistics(std::ostream& os = std::cout) const;

private:
    std::vector<std::unique_ptr<MemoryBuffer>> buffers_;  ///< Memory buffers
    std::size_t current_buffer_index_ = 0;                ///< Current active buffer
    std::size_t block_size_;                              ///< Size of each block
    std::size_t max_blocks_;                              ///< Maximum number of blocks
    
    /**
     * @brief Add a new buffer to the pool
     * @return Reference to the newly added buffer
     * @throws MemoryPoolException if maximum buffer count is reached
     */
    MemoryBuffer& add_buffer();
};

} // namespace aurora::path
