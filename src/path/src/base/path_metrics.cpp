// Copyright (c) 2025 BYD Corporation. All rights reserved.
// Author: <EMAIL>
//
// Created: 2025-06-26

#include "path_metrics.h"
#include <sstream>
#include <iomanip>
#include <logger.h>

namespace aurora {
namespace path {

std::string OperationTypeToString(OperationType type) {
    switch (type) {
        case OperationType::TILE_CREATION: return "TileCreation";
        case OperationType::GET_DIRECT_EDGE_INFO: return "GetDirectEdgeInfo";
        case OperationType::GET_NODE_INFO: return "GetNodeInfo";
        case OperationType::GET_AUGMENT_EDGE_INFO: return "GetAugmentEdgeInfo";
        case OperationType::GET_TILE: return "GetTile";
        case OperationType::LOAD_TILE: return "LoadTile";
        case OperationType::LOAD_TILE_DATA: return "LoadTileData";
        case OperationType::GET_LINKS_BY_RANGE: return "GetLinksByRange";
        case OperationType::STATIC_MATCHING: return "StaticMatching";
        case OperationType::GET_ENTER_EDGES: return "GetEnterEdges";
        case OperationType::GET_EXIT_EDGES: return "GetExitEdges";
        case OperationType::CUSTOM: return "Custom";
        case OperationType::DATA_PROVIDER_GetInEdgeID: return "DATA_PROVIDER_GetInEdgeID";
        case OperationType::CalcRoute: return "CalcRoute";
        case OperationType::CONSTRUCT_NODE: return "ConstructNode";
        case OperationType::CONSTRUCT_EDGE: return "ConstructEdge";
        default: return "Unknown";
    }
}

PathMetrics::PathMetrics() {
    InitializeOperationMetrics();
}

PathMetrics& PathMetrics::GetInstance() {
    static PathMetrics instance;
    return instance;
}

void PathMetrics::InitializeOperationMetrics() {
    // Initialize all operation types
    operation_metrics_[OperationType::TILE_CREATION] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::GET_DIRECT_EDGE_INFO] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::GET_NODE_INFO] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::GET_AUGMENT_EDGE_INFO] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::GET_TILE] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::LOAD_TILE] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::LOAD_TILE_DATA] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::GET_LINKS_BY_RANGE] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::STATIC_MATCHING] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::GET_ENTER_EDGES] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::GET_EXIT_EDGES] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::CUSTOM] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::DATA_PROVIDER_GetInEdgeID] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::CalcRoute] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::CONSTRUCT_NODE] = std::make_unique<OperationMetrics>();
    operation_metrics_[OperationType::CONSTRUCT_EDGE] = std::make_unique<OperationMetrics>();
}

void PathMetrics::RecordOperationTime(OperationType type, int64_t duration_microseconds) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    auto it = operation_metrics_.find(type);
    if (it != operation_metrics_.end()) {
        it->second->total_time_us.fetch_add(duration_microseconds, std::memory_order_relaxed);
        it->second->call_count.fetch_add(1, std::memory_order_relaxed);
    }
}

void PathMetrics::RecordOperationTime(OperationType type,
                                     const std::chrono::steady_clock::time_point& start_time,
                                     const std::chrono::steady_clock::time_point& end_time) {
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    RecordOperationTime(type, duration.count());
}

void PathMetrics::Reset() {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    for (auto& pair : operation_metrics_) {
        pair.second->Reset();
    }
}

void PathMetrics::ResetOperation(OperationType type) {
    std::lock_guard<std::mutex> lock(metrics_mutex_);
    auto it = operation_metrics_.find(type);
    if (it != operation_metrics_.end()) {
        it->second->Reset();
    }
}

void PathMetrics::PrintMetrics() const {
    std::string metrics_str = GetMetricsString();
    LOG_INFO("PathMetrics: {}", metrics_str.c_str());
}

void PathMetrics::PrintOperationMetrics(OperationType type) const {
    std::string metrics_str = GetOperationMetricsString(type);
    LOG_INFO("PathMetrics[{}]: {}", OperationTypeToString(type).c_str(), metrics_str.c_str());
}

std::string PathMetrics::GetMetricsString() const {
    std::lock_guard<std::mutex> lock(metrics_mutex_);

    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2);

    bool first = true;
    for (const auto& pair : operation_metrics_) {
        int64_t count = pair.second->call_count.load(std::memory_order_relaxed);
        if (count > 0) {  // Only show operations that have been called
            if (!first) oss << ", ";
            first = false;

            int64_t total_time_us = pair.second->total_time_us.load(std::memory_order_relaxed);
            double avg_time_us = pair.second->GetAverageTime();

            oss << OperationTypeToString(pair.first) << "[Count=" << count
                << ", TotalTime=" << (total_time_us / 1000.0) << "ms"
                << ", AvgTime=" << (avg_time_us) << "us] \n";
        }
    }

    return oss.str();
}

std::string PathMetrics::GetOperationMetricsString(OperationType type) const {
    std::lock_guard<std::mutex> lock(metrics_mutex_);

    auto it = operation_metrics_.find(type);
    if (it == operation_metrics_.end()) {
        return "No data";
    }

    int64_t total_time_us = it->second->total_time_us.load(std::memory_order_relaxed);
    int64_t count = it->second->call_count.load(std::memory_order_relaxed);
    double avg_time_us = it->second->GetAverageTime();

    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2);
    oss << "Count=" << count
        << ", TotalTime=" << (total_time_us / 1000.0) << "ms"
        << ", AvgTime=" << (avg_time_us) << "us";

    return oss.str();
}

// OperationTimer implementation
OperationTimer::OperationTimer(OperationType type)
    : operation_type_(type), start_time_(std::chrono::steady_clock::now()), stopped_(false) {
}

OperationTimer::OperationTimer(const std::string& operation_name)
    : operation_type_(OperationType::CUSTOM), custom_name_(operation_name),
      start_time_(std::chrono::steady_clock::now()), stopped_(false) {
}

OperationTimer::~OperationTimer() {
    Stop();
}

void OperationTimer::Stop() {
// #if 0
#ifdef PATH_DEBUG
    if (!stopped_) {
        auto end_time = std::chrono::steady_clock::now();
        PathMetrics::GetInstance().RecordOperationTime(operation_type_, start_time_, end_time);
        stopped_ = true;
    }
#endif
}

} // namespace path
} // namespace aurora
