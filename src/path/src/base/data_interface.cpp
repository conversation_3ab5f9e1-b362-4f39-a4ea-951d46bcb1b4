// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-04-30
//

#include "data_interface.h"

namespace aurora {
namespace path {

// Restriction implementation
Restriction::Restriction(const parser::LimitPassBase* base) {
    in_tile_id = base->in_tile_id;
    in_edge_dir = base->in_edge_dir;
    in_edge_id = base->in_edge_id;
    out_tile_id = base->out_tile_id;
    out_edge_dir = base->out_edge_dir;
    out_edge_id = base->out_edge_id;
    access_ctrl_type = base->access_ctrl_type;
    access_ctrl_relation = base->access_ctrl_relation;
    has_time_domain = base->time_domain_address == 65535 ? 0 : 1;
}

std::string Restriction::ToString() {
    return std::to_string(in_tile_id) + "_" + std::to_string(in_edge_id) + "_" + std::to_string(in_edge_dir) + "_" +
           std::to_string(out_tile_id) + "_" + std::to_string(out_edge_id) + "_" + std::to_string(out_edge_dir) + "_" +
           std::to_string(static_cast<int>(access_ctrl_type)) + "_" + std::to_string(static_cast<int>(access_ctrl_relation))
           + "-" + std::to_string(static_cast<int>(has_time_domain)) + ";";
}

uint32_t Restriction::InDirectId() {
    // in_edge_dir : 0 : forward, 1: reverse
    return in_edge_id << 1 | (in_edge_dir ? 0 : 1);
}

uint32_t Restriction::OutDirectId() {
    return out_edge_id << 1 | (out_edge_dir ? 0: 1);
}

// DirectEdgeInfo implementation
DirectEdgeInfo::DirectEdgeInfo(grapthTileRawPtr tile_ptr, parser::TopolEdge& edge, const parser::RouteTileID& route_tile_id,
    std::shared_ptr<parser::RouteTileReader>& tile_reader, const GraphId& direct_id)
: tile_ptr(tile_ptr), topo_edge(edge), tile_id(route_tile_id), edge_status{} {
  this->id = direct_id;
  this->level_id = tile_id.level;
  auto* baseinfo = topo_edge.GetBaseInfo();
  start_heading = tile_reader->GetNodeByID(baseinfo->start_node_id)->GetEdgeAngle(edge.GetID());
  end_heading = tile_reader->GetNodeByID(baseinfo->end_node_id)->GetEdgeAngle(edge.GetID());
}

bool DirectEdgeInfo::is_forward() {
    return id.is_forward();
}

void DirectEdgeInfo::UpdateCost(DirectEdgeInfo* pred_edge, uint32_t pred_idx, Cost cost, float sortcost, Cost transition_cost, uint32_t turn_degree) {
    predecessor_edge = pred_edge;
    predecessor_idx_ = pred_idx;
    cost_ = cost;
    sortcost_ = sortcost;
    transition_cost_ = transition_cost;
    turn_degree_ = turn_degree;
}

uint16_t DirectEdgeInfo::GetStartHeading() {
    return start_heading;
}

uint16_t DirectEdgeInfo::GetEndHeading() {
    return end_heading;
}

// Getter functions for member variables (now all via topo_edge.GetBaseInfo())
uint16_t DirectEdgeInfo::length() { 
    return topo_edge.GetBaseInfo()->length; 
}

uint32_t DirectEdgeInfo::startnode() { 
    return topo_edge.GetBaseInfo()->start_node_id; 
}

uint32_t DirectEdgeInfo::endnode() { 
    return topo_edge.GetBaseInfo()->end_node_id; 
}

uint8_t DirectEdgeInfo::function_class() { 
    return topo_edge.GetBaseInfo()->function_class; 
}

uint8_t DirectEdgeInfo::edge_type() { 
    return topo_edge.GetBaseInfo()->edge_type; 
}

uint8_t DirectEdgeInfo::direction() { 
    return static_cast<uint8_t>(topo_edge.GetBaseInfo()->direction); 
}

uint8_t DirectEdgeInfo::need_toll() { 
    return topo_edge.GetBaseInfo()->need_toll; 
}

uint32_t DirectEdgeInfo::positive_speed_limit() { 
    return topo_edge.GetBaseInfo()->positive_speed_limit; 
}

uint32_t DirectEdgeInfo::is_overhead() { 
    return topo_edge.GetBaseInfo()->is_overhead; 
}

uint32_t DirectEdgeInfo::is_inner_edge() { 
    return topo_edge.GetBaseInfo()->is_inner_edge; 
}

uint32_t DirectEdgeInfo::is_separate() { 
    return topo_edge.GetBaseInfo()->is_separate; 
}

uint32_t DirectEdgeInfo::negtive_speed_limit() { 
    return topo_edge.GetBaseInfo()->negtive_speed_limit; 
}

uint8_t DirectEdgeInfo::is_area_link() { 
    return topo_edge.GetBaseInfo()->is_area_edge; 
}

uint8_t DirectEdgeInfo::is_city_edge() { 
    return topo_edge.GetBaseInfo()->is_city_edge; 
}

uint8_t DirectEdgeInfo::is_ramp() { 
    return topo_edge.GetBaseInfo()->is_ramp; 
}

uint8_t DirectEdgeInfo::edge_form() { 
    return topo_edge.GetBaseInfo()->edge_form; 
}

uint8_t DirectEdgeInfo::speed_grade() { 
    return topo_edge.GetBaseInfo()->speed_grade; 
}

uint8_t DirectEdgeInfo::forward_lane_count() { 
    return topo_edge.GetBaseInfo()->forward_lane_count; 
}

uint8_t DirectEdgeInfo::backward_lane_count() { 
    return topo_edge.GetBaseInfo()->backward_lane_count; 
}

uint8_t DirectEdgeInfo::lane_count() { 
    return topo_edge.GetBaseInfo()->lane_count; 
}

uint8_t DirectEdgeInfo::road_class() { 
    return topo_edge.GetBaseInfo()->road_class; 
}

uint8_t DirectEdgeInfo::is_left() { 
    return topo_edge.GetBaseInfo()->is_left; 
}

uint8_t DirectEdgeInfo::is_limit_in_edge() { 
    return topo_edge.GetBaseInfo()->is_limit_in_edge; 
}

uint8_t DirectEdgeInfo::is_limit_out_edge() { 
    return topo_edge.GetBaseInfo()->is_limit_out_edge; 
}

uint8_t DirectEdgeInfo::is_building() { 
    return topo_edge.GetBaseInfo()->is_building; 
}

uint8_t DirectEdgeInfo::is_paved() { 
    return topo_edge.GetBaseInfo()->is_paved; 
}

uint8_t DirectEdgeInfo::is_gate() { 
    return topo_edge.GetBaseInfo()->is_gate; 
}

uint8_t DirectEdgeInfo::no_crossing() { 
    return topo_edge.GetBaseInfo()->no_crossing; 
}

uint8_t DirectEdgeInfo::is_private() {
    return topo_edge.GetBaseInfo()->is_private;
}

uint8_t DirectEdgeInfo::has_traffic_light() {
    return id.is_forward() ? topo_edge.GetBaseInfo()->has_traffic_light_end : topo_edge.GetBaseInfo()->has_traffic_light_start;
}

// NodeInfo implementation
NodeInfo::NodeInfo(grapthTileRawPtr tile_ptr, parser::RouteNode& node, const parser::RouteTileID& route_tile_id)
: tile_ptr(tile_ptr), route_node(node), tile_id(route_tile_id) {
  this->id = GraphId(tile_id.value, node.GetID());
  this->local_idx = node.GetID();
}

PointLL NodeInfo::GetPoint() {
  PointLL node_pt = route_node.GetPosition();
  return node_pt;
}

bool NodeInfo::IsTransUp() {
    return route_node.GetTransUpNodeInfo() != nullptr;
}

uint32_t NodeInfo::level() {
    return tile_id.level;
}

GraphId NodeInfo::GetTranUpId() {
    auto transup_node = route_node.GetTransUpNodeInfo();
    assert(transup_node != nullptr);
    parser::RouteTileID up_tile_Id = tile_id;
    up_tile_Id.level += 1;
    up_tile_Id.tile_id = transup_node->opp_tile_id;
    return GraphId(up_tile_Id.value, transup_node->opp_node_id);;
}

// graphTile implementation
graphTile::graphTile(parser::RouteTilePackagePtr tile_package, std::unique_ptr<PathMemoryPool>& memory_pool, TileLoadStatus load_status)
  : tile_header(std::make_shared<GraphHeader>()),
    reader(std::make_shared<parser::RouteTileReader>()),
    edges_{},
    aug_edges_{},
    nodes_{},
    memory_pool_(memory_pool),
    fwd_edges_(nullptr),
    bwd_edges_(nullptr) {

    reader->SetTarget(tile_package);
    tile_header->tile_id = reader->GetTileID();
    tile_header->load_flag = static_cast<uint16_t>(load_status);

    std::vector<parser::RouteNode>& nodes = reader->GetNodes();
    tile_header->node_count = nodes.size();
    nodes_.resize(tile_header->node_count);

    tile_header->edge_count = reader->GetTopolEdges().size();
    edges_.resize(tile_header->edge_count);

    auto&limit_pass = reader->GetLimitPass();

    enter_restrictions_.reserve(limit_pass.size());
    for (size_t idx = 0; idx < limit_pass.size(); ++idx) {
        auto& limit = limit_pass.at(idx);
        const auto* base = limit.GetBaseInfo();
        enter_restrictions_.emplace_back(std::make_shared<Restriction>(base));
    }

    exit_restrictions_ = enter_restrictions_;
    std::sort(exit_restrictions_.begin(), exit_restrictions_.end(), [](const auto& a, const auto& b) {
        return a->out_edge_id < b->out_edge_id || (a->out_edge_id == b->out_edge_id && a->out_edge_dir < b->out_edge_dir);
    });
}

GraphHeaderPtr& graphTile::header() {
    return tile_header;
}

const std::vector<AugmentEdgeInfoPtr>& graphTile::aug_edges() {
    if (aug_edges_.empty()) {
        auto& topo_augment_links = reader->GetAugmentEdges();
        aug_edges_.reserve(topo_augment_links.size());
        for (size_t idx = 0; idx < topo_augment_links.size(); ++idx) {
            auto& current_link = topo_augment_links.at(idx);
            auto aug_edge = std::make_shared<AugmentEdgeInfo>();
            aug_edge->id = GraphId(tile_header->tile_id.value, current_link.GetID());
            const auto& points = current_link.GetGeoPoints();
            aug_edge->geos.insert(aug_edge->geos.end(), points.begin(), points.end());
            const auto& sub_edges = current_link.GetSubedges();
            for (const auto& sublink : sub_edges) {
                aug_edge->sub_edges.push_back(*sublink);
            }

            aug_edges_.push_back(aug_edge);
        }
    }
    return aug_edges_;
}

AugmentEdgeInfoPtr graphTile::GetAugmentEdgeInfo(const GraphId& id) {
    const auto& edges = aug_edges();
    assert(id.local_id_no_dir() < edges.size());
    return edges.at(id.local_id_no_dir());
}

const DirectEdgeInfoRawPtr& graphTile::GetDirectEdgeInfo(const GraphId& id, bool forward) {
    // assert(id.local_id() < edges_.size());
    assert(id.tileid() == tile_header->tile_id.value);
    if (forward && fwd_edges_ == nullptr) {
        fwd_edges_ = memory_pool_->allocate_array<DirectEdgeInfoRawPtr>(tile_header->edge_count * 2);
        tile_header->load_flag |= static_cast<uint16_t>(TileLoadStatus::TILE_FWD_LOAD);
    } else if (!forward && bwd_edges_ == nullptr) {
        bwd_edges_ = memory_pool_->allocate_array<DirectEdgeInfoRawPtr>(tile_header->edge_count * 2);
        tile_header->load_flag |= static_cast<uint16_t>(TileLoadStatus::TILE_REV_LOAD);
    }

    DirectEdgeInfoRawPtr* edges = forward ? fwd_edges_ : bwd_edges_;

    if (edges[id.direct_id()] == nullptr) {
        OperationTimer timer(OperationType::CONSTRUCT_EDGE);
        auto& current_link = reader->GetTopolEdges().at(id.local_id_no_dir()); // origin id
        edges[id.direct_id()] = memory_pool_->construct<DirectEdgeInfo>(this, current_link, tile_header->tile_id, reader, id);
    }
    return edges[id.direct_id()];
}

const parser::TopolEdge* graphTile::GetTopoEdgeById(const uint32_t& local_id) {
    assert(local_id < reader->GetTopolEdges().size());
    return reader->GetTopolEdgeByID(local_id);
}

const NodeInfoRawPtr& graphTile::GetNodeInfo(const uint32_t& local_id) {
    assert(local_id < nodes_.size());

    if (!nodes_[local_id]) {
        OperationTimer timer(OperationType::CONSTRUCT_NODE);
        auto& current_node = reader->GetNodes().at(local_id);
        nodes_[local_id] = memory_pool_->construct<NodeInfo>(this, current_node, tile_header->tile_id);
    }
    return nodes_.at(local_id);
}

DirectEdgeInfoRawPtr graphTile::FindDirectEdgeInfo(uint32_t direct_id, bool forward) {
    if (forward) {
        if (!fwd_edges_) {
            return nullptr;
        }
        return fwd_edges_[direct_id];
    } else {
        if (!bwd_edges_) {
            return nullptr;
        }
        return bwd_edges_[direct_id];
    }
}

}  // namespace path
}  // namespace aurora
