// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by Augment Agent on 2025-07-07
//

#include "map_matching_manager.h"
#include "config/path_config.h"

namespace aurora {
namespace path {

// MapMatchingListener 实现类
class MapMatchingListenerImpl : public loc::MapMatchingListener {
public:
    using MatchResultCallback = std::function<void(const loc::MatchResult&)>;
    
    explicit MapMatchingListenerImpl(MatchResultCallback callback) 
        : callback_(callback) {}

    void OnMapMatchingResult(const loc::MatchResult& result) override {
        if (callback_) {
            callback_(result);
        }
    }

private:
    MatchResultCallback callback_;
};

MapMatchingManager::MapMatchingManager(const GraphReaderPtr& graph_reader)
    : graph_reader_(graph_reader)
    , location_interface_(nullptr)
    , map_matching_listener_(nullptr)
    , match_result_timestamp_(0)
    , reroute_timestamp_(0)
    , initialized_(false) {
    
    if (!graph_reader_) {
        LOG_ERROR("MapMatchingManager: GraphReader is null");
        return;
    }
    
    // 初始化匹配结果
    match_result_.timestamp = 0U;
    
    LOG_INFO("MapMatchingManager initialized");
}

bool MapMatchingManager::Initialize(std::shared_ptr<loc::ILocation> location_interface) {
    if (!location_interface) {
        LOG_WARN("MapMatchingManager: Location interface is null, map matching will not be available");
        return false;
    }
    
    location_interface_ = location_interface;
    
    // 创建地图匹配监听器
    map_matching_listener_ = std::make_shared<MapMatchingListenerImpl>(
        std::bind(&MapMatchingManager::OnMapMatchingCallback, this, std::placeholders::_1));
    
    // 注册监听器
    location_interface_->AddMapMatchingListener(map_matching_listener_);
    
    initialized_ = true;
    LOG_INFO("MapMatchingManager: Location interface initialized successfully");
    
    return true;
}

int32_t MapMatchingManager::DoLandMarkMatching(PathLandmarkPtr& landmark) {
    if (!landmark || !landmark->valid) {
        LOG_ERROR("MapMatchingManager: Landmark is invalid");
        return -1;
    }

    if (!landmark->candidates.empty()) {
        LOG_WARN("MapMatchingManager: Landmark already has candidates");
        return -1;
    }

    // 处理起点的特殊情况
    if (landmark->waypoint_type == WayPointType::kStartPoint) {
        uint64_t cur_time = Time::Now().ToMillisecond();
        loc::MatchResult match_result;
        
        {
            std::lock_guard<std::mutex> lock(match_result_mutex_);
            match_result = match_result_;
        }
        
        if (match_result.timestamp == 0U) {
            LOG_ERROR("MapMatchingManager: No valid match result available");
            return -1;
        }
        
        if (abs_diff(match_result.timestamp, cur_time) > 5000) { // 5秒超时
            LOG_ERROR("MapMatchingManager: Match result is too old, timestamp: {}, cur_time: {}", 
                     match_result.timestamp, cur_time);
            return -1;
        }
        
        return DoEgoMatching(landmark, match_result) ? 0 : -1;
    } else {
        // 对于其他类型的地标，可以扩展静态匹配逻辑
        // TODO: 实现静态匹配
        LOG_INFO("MapMatchingManager: Static matching not implemented yet");
        return -1;
    }
}

void MapMatchingManager::ProcessMatchResult(const loc::MatchResult& result) {
    uint64_t cur_time = Time::Now().ToMillisecond();

    auto to_string = [](const loc::MatchResult& result) {
        return "MatchResult{origin_pos{" + std::to_string(result.origin_pos.lnglat.x()) + ", " +
               std::to_string(result.origin_pos.lnglat.y()) + "}, " + "road_match_info{" +
               std::to_string(result.road_match_info.tile_id) + ", " + std::to_string(result.road_match_info.link_id) + ", " +
               std::to_string(result.road_match_info.dir) + ", " + std::to_string(result.road_match_info.offset) + "}, " +
               "car_pos_info{" + std::to_string(result.car_pos_info.speed) + ", " + std::to_string(result.car_pos_info.heading) +
               "}, " + "timestamp{" + std::to_string(result.timestamp) + "}, " + "on_road{" + std::to_string(result.on_road) + "}, " +
               "reroute{" + std::to_string(result.reroute) + "}" + "}";
    };
    LOG_INFO_EVERY_N(10, "ProcessMatchResult timestamp: {}, cur_time:{}", to_string(result), cur_time);

    std::lock_guard<std::mutex> lock(match_result_mutex_);
    match_result_ = result;
    match_result_timestamp_ = cur_time;

    // 更新重新路由状态
    if (result.reroute && cur_time - reroute_timestamp_ > 5000) {
        reroute_timestamp_ = cur_time;
    }
}

loc::MatchResult MapMatchingManager::GetLatestMatchResult() const {
    std::lock_guard<std::mutex> lock(match_result_mutex_);
    return match_result_;
}

bool MapMatchingManager::IsMatchResultValid(uint64_t max_age_ms) const {
    std::lock_guard<std::mutex> lock(match_result_mutex_);
    
    if (match_result_.timestamp == 0U) {
        return false;
    }
    
    uint64_t cur_time = Time::Now().ToMillisecond();
    return abs_diff(match_result_.timestamp, cur_time) <= max_age_ms;
}

bool MapMatchingManager::ShouldReroute() const {
    std::lock_guard<std::mutex> lock(match_result_mutex_);
    
    if (!match_result_.reroute) {
        return false;
    }
    
    uint64_t cur_time = Time::Now().ToMillisecond();
    return (cur_time - reroute_timestamp_) > 5000; // 5秒间隔
}

void MapMatchingManager::UpdateRerouteTimestamp() {
    reroute_timestamp_ = Time::Now().ToMillisecond();
}

bool MapMatchingManager::FillEgoStartPoint(PathQueryPtr& query) {
    if (!query || query->path_points.empty()) {
        return false;
    }
    
    // 检查第一个点是否已经是起点
    if (query->path_points[0]->waypoint_type == WayPointType::kStartPoint) {
        return true; // 已经有起点了
    }
    
    // 检查匹配结果是否有效
    if (!IsMatchResultValid()) {
        LOG_WARN("MapMatchingManager: No valid match result for ego start point");
        return false;
    }
    
    // 创建ego起点
    PathLandmarkPtr ego_landmark = std::make_shared<PathLandmark>();
    ego_landmark->valid = true;
    ego_landmark->waypoint_type = WayPointType::kStartPoint;
    ego_landmark->landmark_type = LandmarkType::kEgo;
    
    {
        std::lock_guard<std::mutex> lock(match_result_mutex_);
        ego_landmark->pt = match_result_.origin_pos.lnglat;
    }
    
    // 插入到路径点列表的开头
    query->path_points.insert(query->path_points.begin(), ego_landmark);
    
    LOG_INFO("MapMatchingManager: Inserted ego start point: ({}, {})", 
             ego_landmark->pt.x(), ego_landmark->pt.y());
    
    return true;
}

bool MapMatchingManager::IsInitialized() const {
    return initialized_;
}

uint64_t MapMatchingManager::GetMatchResultTimestamp() const {
    std::lock_guard<std::mutex> lock(match_result_mutex_);
    return match_result_timestamp_;
}

void MapMatchingManager::OnMapMatchingCallback(const loc::MatchResult& result) {
    uint64_t cur_time = Time::Now().ToMillisecond();
    
    // 构建日志字符串
    auto to_string = [](const loc::MatchResult& result) {
        return "MatchResult{origin_pos{" + std::to_string(result.origin_pos.lnglat.x()) + ", " +
               std::to_string(result.origin_pos.lnglat.y()) + "}, " + "road_match_info{" +
               std::to_string(result.road_match_info.tile_id) + ", " + 
               std::to_string(result.road_match_info.link_id) + ", " +
               std::to_string(result.road_match_info.dir) + ", " + 
               std::to_string(result.road_match_info.offset) + "}, " +
               "car_pos_info{" + std::to_string(result.car_pos_info.speed) + ", " + 
               std::to_string(result.car_pos_info.heading) + "}, " + 
               "timestamp{" + std::to_string(result.timestamp) + "}, " + 
               "on_road{" + std::to_string(result.on_road) + "}, " +
               "reroute{" + std::to_string(result.reroute) + "}" + "}";
    };
    
    LOG_INFO_EVERY_N(10, "MapMatchingManager: OnMapMatchingCallback {}, cur_time: {}", 
                     to_string(result), cur_time);
    
    // 更新匹配结果
    {
        std::lock_guard<std::mutex> lock(match_result_mutex_);
        match_result_ = result;
        match_result_timestamp_ = cur_time;
    }
}

bool MapMatchingManager::DoEgoMatching(PathLandmarkPtr& landmark, const loc::MatchResult& match_result) {
    if (!graph_reader_) {
        LOG_ERROR("MapMatchingManager: GraphReader is null");
        return false;
    }
    
    // 构建GraphId
    GraphId road_id{
        match_result.road_match_info.tile_id, 
        static_cast<uint32_t>(match_result.road_match_info.link_id), 
        match_result.road_match_info.dir == 0 ? true : false
    };
    
    // 调用GraphReader的EgoMatch方法
    bool success = graph_reader_->EgoMatch(
        match_result.origin_pos.lnglat, 
        road_id, 
        match_result.road_match_info.dir, 
        match_result.road_match_info.offset, 
        landmark
    );
    
    if (success) {
        LOG_INFO("MapMatchingManager: EgoMatch successful");
    } else {
        LOG_ERROR("MapMatchingManager: EgoMatch failed");
    }
    
    return success;
}

}  // namespace path
}  // namespace aurora
