// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by Augment Agent on 2025-07-07
//

#ifndef AURORA_PATH_SRC_MAP_MATCHING_MAP_MATCHING_MANAGER_H_
#define AURORA_PATH_SRC_MAP_MATCHING_MAP_MATCHING_MANAGER_H_

#include <memory>
#include <mutex>
#include <functional>

#include "path_module.h"
#include "location_module.h"
#include "graph_reader/graph_reader.h"
#include "Time.h"
#include "logger.h"

namespace aurora {
namespace path {

/**
 * MapMatchingManager - 地图匹配管理器
 * 
 * 职责：
 * - 管理地图匹配相关的逻辑
 * - 处理 ego 位置匹配
 * - 管理重路由逻辑
 * - 维护匹配结果缓存
 */
class MapMatchingManager {
public:
    /**
     * 构造函数
     * @param graph_reader 图数据读取器
     */
    explicit MapMatchingManager(const GraphReaderPtr& graph_reader);
    
    /**
     * 析构函数
     */
    ~MapMatchingManager() = default;

    // 禁用拷贝构造和赋值
    MapMatchingManager(const MapMatchingManager&) = delete;
    MapMatchingManager& operator=(const MapMatchingManager&) = delete;

    /**
     * 初始化地图匹配接口
     * @param location_interface 位置接口
     * @return true 如果初始化成功
     */
    bool Initialize(std::shared_ptr<loc::ILocation> location_interface);
    
    /**
     * 地标匹配
     * @param landmark 待匹配的地标
     * @return 匹配结果，0表示成功，负数表示失败
     */
    int32_t DoLandMarkMatching(PathLandmarkPtr& landmark);
    
    /**
     * 处理地图匹配结果
     * @param result 匹配结果
     */
    void ProcessMatchResult(const loc::MatchResult& result);

    /**
     * 获取最新的匹配结果
     * @return 最新的匹配结果
     */
    loc::MatchResult GetLatestMatchResult() const;
    
    /**
     * 检查匹配结果是否有效
     * @param max_age_ms 最大有效时间（毫秒）
     * @return true 如果匹配结果有效
     */
    bool IsMatchResultValid(uint64_t max_age_ms = 5000) const;
    
    /**
     * 判断是否应该重路由
     * @return true 如果应该重路由
     */
    bool ShouldReroute() const;
    
    /**
     * 更新重路由时间戳
     */
    void UpdateRerouteTimestamp();
    
    /**
     * 自动填充起点（ego位置）
     * @param query 路径查询请求
     * @return true 如果成功填充起点
     */
    bool FillEgoStartPoint(PathQueryPtr& query);

    /**
     * 检查是否已初始化
     * @return true 如果已初始化
     */
    bool IsInitialized() const;

    /**
     * 获取匹配结果的时间戳
     * @return 匹配结果时间戳
     */
    uint64_t GetMatchResultTimestamp() const;

private:
    /**
     * 地图匹配回调函数
     * @param result 匹配结果
     */
    void OnMapMatchingCallback(const loc::MatchResult& result);
    
    /**
     * 执行ego匹配
     * @param landmark 地标对象
     * @param match_result 匹配结果
     * @return true 如果匹配成功
     */
    bool DoEgoMatching(PathLandmarkPtr& landmark, const loc::MatchResult& match_result);
    
    /**
     * 计算时间差的绝对值
     * @param a 时间戳a
     * @param b 时间戳b
     * @return 时间差的绝对值
     */
    uint64_t abs_diff(uint64_t a, uint64_t b) const {
        return a > b ? a - b : b - a;
    }

private:
    // 图数据读取器
    GraphReaderPtr graph_reader_;
    
    // 位置接口
    std::shared_ptr<loc::ILocation> location_interface_;
    
    // 地图匹配监听器
    loc::MapMatchingListenerPtr map_matching_listener_;
    
    // 最新匹配结果
    loc::MatchResult match_result_;
    
    // 匹配结果时间戳
    uint64_t match_result_timestamp_;
    
    // 重路由时间戳
    uint64_t reroute_timestamp_;
    
    // 互斥锁保护匹配结果
    mutable std::mutex match_result_mutex_;
    
    // 初始化状态
    bool initialized_;
};

// 类型别名
using MapMatchingManagerPtr = std::unique_ptr<MapMatchingManager>;

}  // namespace path
}  // namespace aurora

#endif  // AURORA_PATH_SRC_MAP_MATCHING_MAP_MATCHING_MANAGER_H_
