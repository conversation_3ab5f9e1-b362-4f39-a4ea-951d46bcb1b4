// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-05-15
//
#include <vector>
#include <limits>
#include "path_module.h"
#include "route_algorithm.h"
#include "dijkstra.h"
#include "logger.h"

namespace aurora {
namespace path {

// Invalid label value
// constexpr uint32_t kInvalidLabel = std::numeric_limits<uint32_t>::max();

// Bucket count for the adjacency list
constexpr uint32_t kBucketCount = 20000;
const std::string kMaxReservedLableCntKey = "algorithms.dijkstra.max_reserved_labels_count";

Dijkstra::Dijkstra(const GraphReaderPtr& reader) : RouteAlgorithm(), graph_reader_ptr_(reader) {

    max_reserved_labels_count_ = 1000000;
    // Get configuration values from PathConfig
    const auto& config = PathConfigManager::Instance();

    // Set max reserved labels count from configuration
    max_reserved_labels_count_ = config.Get(kMaxReservedLableCntKey, 1000000);

    // Initialize other members
    cost_threshold_ = std::numeric_limits<float>::max();
    max_iterations_threshold_ = config.Get("algorithms.dijkstra.max_iterations_threshold", 2000000);
    current_strategy_ = static_cast<PathStrategy>(config.Get("strategy", 0));
    error_code_ = ErrorCode::kErrorCodeOk;
    tag_ = "dijkstra";
    link_costing_ = std::make_shared<LinkCost>();

    LOG_INFO("Dijkstra initialized with max_reserved_labels_count: {}, strategy: {}, max_iterations_threshold: {}",
        max_reserved_labels_count_, static_cast<int>(current_strategy_), max_iterations_threshold_);
}

Dijkstra::~Dijkstra() = default;

void Dijkstra::SetRoutingStrategy(PathStrategy strategy) {
    current_strategy_ = strategy;
}

PathResultPtr Dijkstra::CalcRoute(const PathQuery& query) {
    LOG_INFO("Dijkstra::CalcRoute");
    ScopeTime scope_time("CalcRoute");
    query_ = query;
    auto start_landmark = query.path_points.front();
    auto end_landmark = query.path_points.back();
    // Initialize the algorithm
    Init(start_landmark->pt, end_landmark->pt);
    
    // Set origin and destination
    if (!SetOrigin(start_landmark)) {
        LOG_INFO("Origin has no candidates");
        error_code_ = ErrorCode::kErrorCodePathStartNoRoad;
        return GeneratePathResult({});
    }

    if (!SetDestination(end_landmark)) {
        LOG_INFO("destination has no candidates");
        error_code_ = ErrorCode::kErrorCodePathEndNoRoad;
        return GeneratePathResult({});
    }
    
    // Main Dijkstra loop
    uint32_t pred_idx = 0;
    DirectEdgeInfoRawPtr pred;
    
    while (true) {
        // Get the next edge from the adjacency list
        pred_idx = adjacencylist_.pop();
        
        // Check if the queue is empty
        if (pred_idx == kInvalidLabel) {
            LOG_INFO("Dijkstra: No path found - adjacency list is empty, label size: {}", edgelabels_.size());
            break;
        }

        // Check iteration threshold
        if (edgelabels_.size() > iterations_threshold_) {
            LOG_INFO("Exceeded iteration threshold: {}", iterations_threshold_);
            break;
        }

        // Get the edge label
        pred = edgelabels_[pred_idx];

        // if ((edgelabels_.size() % 5000) == 1) {
        //     WriteVisitedEdges(std::to_string(pred_idx), edgelabels_);
        // }

        if (edgelabels_.size() % 10000 == 0) {
            LOG_INFO("Dijkstra: Visited {} edges", edgelabels_.size());
            // WriteVisitedEdges("final", edgelabels_);
        }

        if (pred->cost().cost > cost_threshold_) {
            LOG_INFO("Dijkstra: Cost threshold exceeded");
            break;
        }

        // Set edge status to permanent
        pred->Update(EdgeSet::kPermanent);
        
        // Check if we've reached the destination
        bool destination_found = false;
        for (const auto& candidate : destination_->candidates) {
            if (pred->id == GraphId(candidate.link->tile_id, candidate.link->id, candidate.link->direction & 0x01 ? true : false) ||
                pred->id == GraphId(candidate.link->tile_id, candidate.link->id, candidate.link->direction & 0x02 ? false : true)) { // todo: delete this?
                destination_found = true;

                GraphId edge_id = {candidate.link->tile_id, candidate.link->id, pred->is_forward()};
                auto edgePtr = graph_reader_ptr_->GetDirectEdgeInfo(edge_id, true);
        
                Cost edge_cost = costing_->EdgeCost(edgePtr);
                if (pred->is_forward()) {
                    edge_cost = edge_cost * (candidate.offset / candidate.link->length);
                } else {
                    edge_cost = edge_cost * (1.0f - (candidate.offset / candidate.link->length));
                }
                // best_connection_.push_back({pred_idx, pred.cost().cost, pred.forward()});
                float c =0.0f;
                if (pred->predecessor() != kInvalidLabel) {
                    c = edgelabels_[pred->predecessor()]->cost().cost + edge_cost.cost;
                    if (cost_threshold_ == std::numeric_limits<float>::max() || c < best_connection_.front().cost) {
                        cost_threshold_ = 1.2 * c + 100;
                        iterations_threshold_ = edgelabels_.size() + 100000;
                        LOG_INFO_EVERY_N(100, "Found forward connection with cost: {}, fwd_idx: {}", c, pred_idx);
                    }

                    best_connection_.push_back({pred_idx, c, pred->is_forward(), pred});
                    if (c < best_connection_.front().cost) {
                        std::swap(best_connection_.front(), best_connection_.back());
                    }
                } else {
                    // todo: single link situation
                }

                break;
            }
        }

        // Check if we've exceeded the iteration threshold
        if (edgelabels_.size() > iterations_threshold_) {
            LOG_INFO("Dijkstra: Exceeded iteration threshold");
            break;
        }

        // Expand from the current edge
        Expand(pred->id, pred, pred_idx);
    }

    auto path_infos = FormPath();

    return GeneratePathResult(path_infos);
}

void Dijkstra::Clear() {
    edgelabels_.clear();
    adjacencylist_.clear();
}

void Dijkstra::Init(const PointLL& orig, const PointLL& dest) {

    SetRoutingStrategy(static_cast<PathStrategy>(query_.strategy));
    // Create costing object
    Costing costing_options;
    costing_options.options.strategy = current_strategy_;
    costing_ = std::make_shared<AutoCost>(costing_options, graph_reader_ptr_);
    costing_->SetStrategy(current_strategy_);

    heuristic_cost_ = std::make_shared<AStarHeuristic>();
    heuristic_cost_->Init(dest, costing_->AStarCostFactor());
    
    // Reserve space for edge labels
    edgelabels_.reserve(max_reserved_labels_count_);
    
    // Initialize the adjacency list
    const float mincost = heuristic_cost_->Get(orig);
    const float range = 1000000.0f;
    const uint32_t bucketsize = 1;
    adjacencylist_.reuse(mincost, range, bucketsize, &edgelabels_);

    // Initialize connection tracking
    best_connection_.clear();
    cost_threshold_ = std::numeric_limits<float>::max();
    iterations_threshold_ = max_iterations_threshold_;
}

bool Dijkstra::SetOrigin(const PathLandmarkPtr& origin) {
    ScopeTime scope_time("SetOrigin");
    if (!origin || !origin->valid) {
        LOG_INFO("Origin is invalid");
        return false;
    }

    graph_reader_ptr_->StaticMatching(origin, PathConfigManager::Instance().GetMatchRadius());
    origin_ = origin;

    if (origin->candidates.empty()) {
        // throw std::runtime_error("Origin is invalid");
        return false;
    }

    // Add each origin candidate to the adjacency list
    for (const auto& candidate : origin->candidates) {
        // Calculate the cost from the origin to the edge

        Cost node_cost = costing_->CandidateCost(candidate);

        // if (edge_ptr can forward)
        if (candidate.link->direction & 0x01) {
            GraphId edge_id = {candidate.link->tile_id, candidate.link->id, true};
            auto edgePtr = graph_reader_ptr_->GetDirectEdgeInfo(edge_id, true);
            Cost edge_cost = costing_->EdgeCost(edgePtr);
            Cost cost = edge_cost *  (1.0f - (candidate.offset / candidate.link->length));
            cost += node_cost;

            // In A*, the sort cost is  the cost + heuristic (with heuristic)
            float sortcost = cost.cost;

            auto node_ptr = graph_reader_ptr_->GetNodeInfo(GraphId(edgePtr->id.tileid(), edgePtr->endnode()));
            sortcost += heuristic_cost_->Get(node_ptr->GetPoint());

            // Add to the edge labels
            uint32_t idx = edgelabels_.size();
            edgePtr->Set(EdgeSet::kTemporary, idx);
            edgePtr->UpdateCost(nullptr, kInvalidLabel, cost, sortcost, node_cost, 0U);
            edgelabels_.push_back(edgePtr);

            // Add to the adjacency list
            adjacencylist_.add(idx);
        }

        if (candidate.link->direction & 0x02) {
            GraphId edge_id = {candidate.link->tile_id, candidate.link->id, false};
            auto edgePtr = graph_reader_ptr_->GetDirectEdgeInfo(edge_id, true); // true is forward search
            Cost edge_cost = costing_->EdgeCost(edgePtr);
            Cost cost = edge_cost *  (candidate.offset / candidate.link->length);
            cost += node_cost;

            // In A*, the sort cost is just the cost the cost + heuristic (with heuristic)
            float sortcost = cost.cost;
            auto node_ptr = graph_reader_ptr_->GetNodeInfo(GraphId(edgePtr->id.tileid(), edgePtr->startnode()));
            sortcost += heuristic_cost_->Get(node_ptr->GetPoint());

            // Add to the edge labels
            uint32_t idx = edgelabels_.size();
            edgePtr->Set(EdgeSet::kTemporary, idx);
            edgePtr->UpdateCost(nullptr, kInvalidLabel, cost, sortcost, node_cost, 0U);
            edgelabels_.push_back(edgePtr);

            // Add to the adjacency list
            adjacencylist_.add(idx);
        }
    }
    return true;
}

bool Dijkstra::SetDestination(const PathLandmarkPtr& destination) {
    // In Dijkstra, we don't need to do anything special for the destination
    // We'll check for the destination during the main loop
    ScopeTime scope_time("SetDestination");
    if (!graph_reader_ptr_->StaticMatching(destination, PathConfigManager::Instance().GetMatchRadius())) {
        LOG_INFO("Destination is invalid");
        // throw std::runtime_error("Destination is invalid");
        return false;
    }
    destination_ = destination;
    return true;
}

void Dijkstra::Expand(GraphId id, const DirectEdgeInfoRawPtr& pred, uint32_t pred_idx) {
    // Get the directed edge
    auto edge = graph_reader_ptr_->GetDirectEdgeInfo(id, true);
    if (!edge) {
        LOG_INFO("Edge not found: {}", id.ToString());
        return;
    }
    
    // Get the end node of this edge
    // keneng 跨tile

    auto expand_node_id = pred->is_forward() ? edge->endnode() : edge->startnode();
    auto unique_node_id = GraphId(id.tileid(), expand_node_id);

    auto expandnode_ptr = graph_reader_ptr_->GetNodeInfo(unique_node_id);
    if (!expandnode_ptr) {
        LOG_INFO("End node not found: {}", static_cast<uint64_t>(edge->endnode()));
        return;
    }

    // Get outgoing edges
    std::vector<DirectEdgeInfoRawPtr> out_edges;
    graph_reader_ptr_->GetAllExitEdges(expandnode_ptr, out_edges);

    // Expand to all outgoing edges from this node, get outgong_edges
    for (const auto& outedge : out_edges) {
        if (outedge->id == id) {
            // todo: not uturn
            continue;
        }

        if (outedge->edge_status.set() == EdgeSet::kPermanent) {
            continue;
        }

        // Check if we should prune this edge based on costing
        //   if (!costing_->Allowed(pred, expandnode_ptr, outedge)) {
        //     continue;
        //   }

        // Get cost for traversing this edge
        Cost edgecost = costing_->EdgeCost(outedge);
        Cost nodecost = costing_->NodeCost(pred, expandnode_ptr, outedge, true);

        // Get the cost from the previous edge
        Cost newcost = pred->cost() + edgecost;

        // Check if this is a new edge or a better path to an existing edge
        EdgeStatusInfo status = outedge->edge_status;
        if (status.set() == EdgeSet::kTemporary) {
            uint32_t idx = status.index();
            if (newcost.cost < edgelabels_[idx]->cost().cost) {
                outedge->predecessor_edge = pred;
                float newsortcost = pred->sortcost() - (pred->cost().cost - newcost.cost);
                // Update the adjacency list
                adjacencylist_.decrease(idx, newsortcost);
                // Update the edge label with the new cost
                edgelabels_[idx]->UpdateCost(pred, pred_idx, newcost, newsortcost, nodecost, 0U);
            }
        } else {
            outedge->predecessor_edge = pred;
            // Add a new edge label
            auto node_ptr = graph_reader_ptr_->GetNodeInfo(
                GraphId(outedge->id.tileid(), outedge->endnode()));  // todo: judge dir to get start or end node
            float sortcost = newcost.cost + heuristic_cost_->Get(node_ptr->GetPoint());
            uint32_t idx = edgelabels_.size();
            outedge->UpdateCost(pred, pred_idx, newcost, sortcost, nodecost, 0U);
            edgelabels_.push_back(outedge);

            // Add to the adjacency list and edge status
            adjacencylist_.add(idx);
            outedge->Set(EdgeSet::kTemporary, idx);
        }
    }
}

std::vector<PathInfo> Dijkstra::FormPath() {
    // Find the destination edge
    if (best_connection_.empty()) {
        LOG_INFO("Dijkstra: Could not find destination edge in edge labels");
        return {};
    }

    WriteVisitedEdges("final", edgelabels_);
    auto dest_edge_ptr = best_connection_.back().edge_ptr;
    bool found = true;
    // Reverse trace the path from destination to origin
    std::vector<EdgeInfoPtr> path_edges;
    std::vector<DirectEdgeInfoRawPtr> direct_edges;
    std::vector<PointLL> path_points;
    double path_length = 0.0;
    double path_time = 0.0;
    double start_offset = 0.0f;
    double end_offset = 0.0f;
    
    // Start with the destination edge
    DirectEdgeInfoRawPtr direct_edge = dest_edge_ptr;
    while (direct_edge != nullptr) {
        // const EdgeLabelPtr& edge_label = edgelabels_[idx];
        
        // Get the edge info
        auto aug_edge_ptr = graph_reader_ptr_->GetAugmentEdgeInfo(direct_edge->id, false);
        direct_edges.push_back(direct_edge);
        auto edge = std::make_shared<EdgeInfo>();
        if (edge && aug_edge_ptr) {
            edge->tile_id = direct_edge->id.tileid();
            edge->id = direct_edge->id.local_id_no_dir();
            edge->forward = direct_edge->id.is_forward();
            edge->is_inner_link = direct_edge->is_inner_edge();
            edge->length = direct_edge->length();
            edge->geos = aug_edge_ptr->geos;

            // start landmark process
            bool is_start_landmark = false;
            if (direct_edge->predecessor_edge == nullptr) {
                for (const auto& candidate : origin_->candidates) {
                    if (direct_edge->id.tileid() == candidate.link->tile_id &&
                        direct_edge->id.local_id_no_dir() == candidate.link->id) {
                        std::vector<PointLL> used_points;
                        if (direct_edge->id.is_forward()) {
                            used_points.push_back(candidate.proj_pt);
                            for (size_t i = candidate.proj_index + 1; i < aug_edge_ptr->geos.size(); i++) {
                                used_points.push_back(aug_edge_ptr->geos[i]);
                            }
                            std::reverse(used_points.begin(), used_points.end());
                            path_length += std::fmax(0.0f, edge->length - candidate.offset);  // use offset
                            start_offset = candidate.offset;
                        } else {
                            for (size_t i = 0; i <= candidate.proj_index; i++) {
                                used_points.push_back(aug_edge_ptr->geos[i]);
                            }
                            used_points.push_back(candidate.proj_pt);
                            path_length += std::fmin(0.0f, candidate.offset);  // use offset
                        }
                        is_start_landmark = true;
                        path_points.insert(path_points.end(), used_points.begin(), used_points.end());
                        start_offset = std::fmax(0.0f, edge->length - candidate.offset);
                        break;
                    }
                }
            }

            // end lanemark process
            bool is_end_landmark = false;
            if (direct_edge == dest_edge_ptr) { // first reverse link
                for (const auto& candidate : destination_->candidates) {
                    if (direct_edge->id.tileid() == candidate.link->tile_id &&
                        direct_edge->id.local_id_no_dir() == candidate.link->id) {
                        std::vector<PointLL> used_points;
                        if (direct_edge->id.is_forward()) {
                            for (size_t i = 0; i <= candidate.proj_index; i++) {
                                used_points.push_back(aug_edge_ptr->geos[i]);
                            }
                            used_points.push_back(candidate.proj_pt);
                            std::reverse(used_points.begin(), used_points.end());
                            path_length += candidate.offset;  // use offset
                            end_offset = std::fmax(0.0f, edge->length - candidate.offset);
                        } else {
                            used_points.push_back(candidate.proj_pt);
                            for (size_t i = candidate.proj_index + 1; i < aug_edge_ptr->geos.size(); i++) {
                                used_points.push_back(aug_edge_ptr->geos[i]);
                            }
                            path_length += std::fmax(0.0f, edge->length - candidate.offset);  // use offset
                            end_offset = candidate.offset;
                        }
                        is_end_landmark = true;
                        path_points.insert(path_points.end(), used_points.begin(), used_points.end());
                        break;
                    }
                }
            }

            if (!is_start_landmark && !is_end_landmark) {
                std::vector<PointLL> geo_points = aug_edge_ptr->geos;
                if (direct_edge->id.is_forward()) {
                    std::reverse(geo_points.begin(), geo_points.end());
                }
                path_points.insert(path_points.end(), geo_points.begin(), geo_points.end());
                // Add to the path length and time
                path_length += edge->length;  // use offset
            }

            // Add the edge to the path
            path_edges.push_back(edge);
            
            // Add the edge points to the path
            // path_time += edge_label->cost().secs; // todo:
            path_time += link_costing_->LinkTravelTime(direct_edge);
        } else {
            assert(false);
        }
        // Move to the predecessor
        direct_edge = direct_edge->predecessor_edge;
    }

    // Reverse the path since we traced from destination to origin
    std::reverse(path_edges.begin(), path_edges.end());
    std::reverse(path_points.begin(), path_points.end());

    // debug 
    // auto path_edge_ptr = std::make_shared<DirectEdgeInfo>();
    // path_edge_ptr->id = GraphId(1, 1);
    // path_edge_ptr->geos = path_points;

    // if (PathConfigManager::Instance().IsDebugEnabled()) {
    //     std::string path_file = PathConfigManager::Instance().GetDebugOutPath() + "/dijkstra_path.geojson";
    //     geojson_writter_.WriteDirectEdgeInfo(path_edge_ptr, path_file);
    // }

    // Create the path info
    PathInfo path_info;
    path_info.path_id = 1;  // Assign a unique ID
    path_info.length = path_length;
    // path_info.travel_time = path_time;
    path_info.travel_time = path_info.length * 3.6f / 60.0f;
    path_info.links = path_edges;
    path_info.points = path_points;

    {
        Section section;
        section.index = 0;
        section.num = path_edges.size();
        section.length = path_info.length;
        section.time = path_info.travel_time;
        // TODO: offset
        section.start_offset = origin_->candidates.front().offset;
        section.end_offset = destination_->candidates.front().offset;
        path_info.sections.push_back(section);
    }

    // Calculate the bounding box
    if (!path_points.empty()) {
        path_info.bbox = AABB2<PointLL>(path_points);
    }

    if (PathConfigManager::Instance().IsDebugEnabled()) {
        std::string route_file = PathConfigManager::Instance().GetDebugOutPath() + "/route.geojson";
        geojson_writter_.WriteDirectEdgeInfos(direct_edges, route_file);
    }
    LOG_INFO("Dijkstra: Path found with length {}, links {}", path_length, path_edges.size());

    return {path_info};
}

void Dijkstra::WriteVisitedEdges(const std::string& filename, const std::vector<DirectEdgeInfoRawPtr>& edge_labels) {
    // Get debug output path from configuration
    const auto& config = PathConfigManager::Instance();
    std::string debug_out_path = config.GetDebugOutPath();
    
    // Only write debug output if debug is enabled
    if (!config.IsDebugEnabled()) {
        return;
    }

    // Construct output file path
    std::string out_file = debug_out_path + "/dijsktra_edgelabel_" + filename + ".geojson";

    for (const auto& edge : edge_labels) {
        auto aug_edge = graph_reader_ptr_->GetAugmentEdgeInfo(edge->id, false);
        if (edge && aug_edge) {
            edge->geos = aug_edge->geos;
        } else {
            assert(false);
        }
    }

    // Write edge labels to file
    geojson_writter_.WriteEdgeLabels(edgelabels_, out_file);
}

}  // namespace path
}  // namespace aurora
