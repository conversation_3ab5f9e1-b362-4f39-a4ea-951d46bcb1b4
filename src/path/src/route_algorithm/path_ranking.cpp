// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-06-10
//

#include "path_ranking.h"
#include <algorithm>
#include <unordered_map>
#include "logger.h"
#include "scope_time.h"
#include "base/constants.h"

namespace aurora {
namespace path {

PathRanking::PathRanking(std::shared_ptr<GraphReader> graph_reader)
    : graph_reader_ptr_(graph_reader) {
}

std::vector<PathInfo> PathRanking::FindAlternativePaths(const std::vector<CandidateConnection>& connections,
                                                        const PathLandmarkPtr& origin,
                                                        const PathLandmarkPtr& destination) {
    ScopeTime scope_time("FindAlternativePaths");
    LOG_INFO("FindAlternativePaths conn size:{}", connections.size());

    // Check if we have any connections
    if (connections.empty()) {
        LOG_INFO("No connections found for alternative paths");
        return {};
    }

    // Use connections directly as candidates
    std::vector<CandidateConnection> candidates = connections;

    // Sort candidates by cost
    std::sort(candidates.begin(), candidates.end());
    // erase duplicate candidates
    auto last = std::unique(candidates.begin(), candidates.end());
    candidates.erase(last, candidates.end());
    LOG_INFO("FindAlternativePaths conn size:{}", candidates.size());

    // Collect edge IDs for each candidate
    // for (auto& candidate : candidates) {
    //     candidate.UpdatePathEdges();
    // }

    // Select the best paths
    std::vector<CandidateConnection> best_paths;

    // Always include the best path
    if (!candidates.empty()) {
        best_paths.push_back(candidates[0]);

        // Try to add more paths if available
        for (size_t i = 1; i < candidates.size() && best_paths.size() < PathRankingConstants::kMaxAlternativePaths; ++i) {
            if (IsValidAlternative(candidates[i], best_paths)) {
                LOG_INFO("Adding alternative path with cost: {}, idx: {}", candidates[i].cost, i);
                best_paths.push_back(candidates[i]);
            }
        }
    }

    LOG_INFO("Selected {} alternative paths", best_paths.size());

    // Form the final paths
    std::vector<PathInfo> result;
    for (const auto& path : best_paths) {
        result.push_back(FormPath(path, origin, destination));
    }

    return result;
}

PathInfo PathRanking::FormPath(const CandidateConnection& candidate, const PathLandmarkPtr& origin,
                               const PathLandmarkPtr& destination) {
    // Get the forward and reverse path edges
    std::vector<EdgeInfoPtr> path_edges;
    std::vector<DirectEdgeInfoRawPtr> direct_edges;
    std::vector<PointLL> path_points;
    double path_length = 0.0;
    double path_time = 0.0;
    double start_offset = 0.0f;
    double end_offset = 0.0f;

    uint32_t traffic_light_num = 0U;

    // deal with single line

    if (candidate.edge_ids.size() == 1) {
        Candidate start_candi;
        Candidate end_candi;
        bool is_start_find = false;
        bool is_end_find = false;
        const auto& edge_ptr  = candidate.forward_edge;
        auto aug_edge_ptr = graph_reader_ptr_->GetAugmentEdgeInfo(edge_ptr->id, false);
        bool dir = edge_ptr->is_forward();

        for (const auto& cand : origin->candidates) {
            if (edge_ptr->id.tileid() == cand.link->tile_id && edge_ptr->id.local_id_no_dir() == cand.link->id) {
                start_candi = cand;
                is_start_find = true;
                break;
            }
        }


        for (const auto& cand : destination->candidates) {
            if (edge_ptr->id.tileid() == cand.link->tile_id && edge_ptr->id.local_id_no_dir() == cand.link->id) {
                end_candi = cand;
                is_end_find = true;
                break;
            }
        }

        if (is_start_find && is_end_find) {
            if (dir && end_candi.offset >= start_candi.offset) {
                // ok.
                std::vector<PointLL> path_points;
                path_points.push_back(start_candi.proj_pt);

                for (size_t i = start_candi.proj_index + 1; i < end_candi.proj_index + 1; i++) {
                    path_points.push_back(aug_edge_ptr->geos[i]);
                }
                path_points.push_back(end_candi.proj_pt);

                auto edge = std::make_shared<EdgeInfo>();
                edge->tile_id = edge_ptr->id.tileid();
                edge->id = edge_ptr->id.local_id_no_dir();
                edge->forward = true;
                edge->is_inner_link = edge_ptr->is_inner_edge();
                edge->length = edge_ptr->length();
                edge->geos = aug_edge_ptr->geos;

                // Create the path info
                PathInfo path_info;
                path_info.path_id = static_cast<uint64_t>(std::hash<double>{}(candidate.cost));  // Generate a unique ID
                path_info.length = end_candi.offset - start_candi.offset;
                path_info.travel_time =
                    path_info.length * 3.6f / 60.0f;  // Use cost as travel time
                path_info.links = {edge};
                path_info.points = path_points;

                // Create sections
                Section section;
                section.index = 0;
                section.num = 1;
                section.length = path_length;
                section.time = candidate.cost / kSecMultiplier;
                section.start_offset = start_candi.offset;
                section.end_offset = end_candi.offset;
                path_info.sections.push_back(section);

                // Calculate bounding box
                if (!path_points.empty()) {
                  path_info.bbox = AABB2<PointLL>(path_points);
                }
                return path_info;
            }
            if (!dir && end_candi.offset <= start_candi.offset) {
                // ok construct result.
                std::vector<PointLL> path_points;
                path_points.push_back(end_candi.proj_pt);

                for (size_t i = end_candi.proj_index + 1; i < start_candi.proj_index + 1; i++) {
                    path_points.push_back(aug_edge_ptr->geos[i]);
                }
                path_points.push_back(start_candi.proj_pt);
                std::reverse(path_points.begin(), path_points.end());

                auto edge = std::make_shared<EdgeInfo>();
                edge->tile_id = edge_ptr->id.tileid();
                edge->id = edge_ptr->id.local_id_no_dir();
                edge->forward = false;
                edge->is_inner_link = edge_ptr->is_inner_edge();
                edge->length = edge_ptr->length();
                edge->geos = aug_edge_ptr->geos;

                // Create the path info
                PathInfo path_info;
                path_info.path_id = static_cast<uint64_t>(std::hash<double>{}(candidate.cost));  // Generate a unique ID
                path_info.length = start_candi.offset - end_candi.offset;
                path_info.travel_time =
                    path_info.length * 3.6f / 60.0f;  // Use cost as travel time
                path_info.links = {edge};
                path_info.points = path_points;

                // Create sections
                Section section;
                section.index = 0;
                section.num = 1;
                section.length = path_length;
                section.time = candidate.cost / kSecMultiplier;
                section.start_offset = std::fmax(0.0f, edge->length -  start_candi.offset);//  start_offset;
                section.end_offset = std::fmax(0.0f, edge->length - end_candi.offset); //end_offset;
                path_info.sections.push_back(section);

                // Calculate bounding box
                if (!path_points.empty()) {
                  path_info.bbox = AABB2<PointLL>(path_points);
                }
                return path_info;
            }
        }
        // throw std::runtime_error("FormPath error"); 
        LOG_INFO("search error....");
        PathInfo info;
        return info;
    }

    // Start with the forward path
    auto cur_edge = candidate.forward_edge;
    if (cur_edge == nullptr) {
      throw std::runtime_error("forward_index is invalid");
    }
    bool reverse_skip_meet_edge = true;
    // 相遇的link属于终点匹配的link, 在反向中处理offset计算。
    if (candidate.reverse_edge->predecessor() == kInvalidLabel) {
        cur_edge = cur_edge->predecessor_edge;
        reverse_skip_meet_edge = false;
    }
    assert(cur_edge != nullptr);
    while (cur_edge != nullptr) {
        // Get the edge info
        const auto& direct_edge = cur_edge;
        auto aug_direct_edge = graph_reader_ptr_->GetAugmentEdgeInfo(direct_edge->id, false);
        assert(aug_direct_edge);
        if (aug_direct_edge) {
            direct_edges.push_back(direct_edge);
            // LOG_INFO("Forward tile_Id: {}, edge: {}, idx:{}, prev: {}", edge->tile_id, edge->id, idx, edge_label->predecessor());

            // Check if this is the origin edge
            bool is_origin_edge = false;
            Candidate start_candi;
            if (cur_edge->predecessor() == kInvalidLabel) {
            for (const auto& cand : origin->candidates) {
                bool dir_same = cur_edge->is_forward() ? (cand.link->direction & 0x01) : (cand.link->direction & 0x02);
                if (direct_edge->id.tileid() == cand.link->tile_id && direct_edge->id.local_id_no_dir() == cand.link->id && dir_same) {
                    start_candi = cand;
                    // start_candi.link->direction = edge_label->forward() ? 1 : 2;
                    std::vector<PointLL> used_points;
                    if (direct_edge->is_forward()) {
                        used_points.push_back(cand.proj_pt);
                        for (size_t i = cand.proj_index + 1; i < aug_direct_edge->geos.size(); i++) {
                            used_points.push_back(aug_direct_edge->geos[i]);
                        }
                        std::reverse(used_points.begin(), used_points.end());
                        path_length += std::fmax(0.0f, direct_edge->length() - cand.offset);
                        start_offset = cand.offset;
                    } else {
                        for (size_t i = 0; i <= cand.proj_index; i++) {
                            used_points.push_back(aug_direct_edge->geos[i]);
                        }
                        used_points.push_back(cand.proj_pt);
                        path_length += cand.offset;
                        start_offset = std::fmax(0.0f, direct_edge->length() - cand.offset);
                    }
                    is_origin_edge = true;
                    path_points.insert(path_points.end(), used_points.begin(), used_points.end());
                    break;
                }
            }
            }

            // If not the origin edge, add the full edge
            if (!is_origin_edge) {
                std::vector<PointLL> used_points = aug_direct_edge->geos;
                if (direct_edge->is_forward()) {
                    std::reverse(used_points.begin(), used_points.end());
                }
                path_points.insert(path_points.end(), used_points.begin(), used_points.end());
                path_length += direct_edge->length();
            }

            traffic_light_num += direct_edge->has_traffic_light();

            auto edges = UnPackEdge(direct_edge, direct_edge->is_forward());
            path_edges.insert(path_edges.end(), edges.begin(), edges.end());
        }

        // Move to the next edge
        cur_edge = cur_edge->predecessor_edge;
    }

    // Reverse the path edges since we built it from destination to origin
    std::reverse(path_edges.begin(), path_edges.end());
    std::reverse(path_points.begin(), path_points.end());

    // Now add the reverse path
    cur_edge = candidate.reverse_edge;
    if (cur_edge == nullptr) {
        throw std::runtime_error("reverse_index is invalid"); 
    }
    if (reverse_skip_meet_edge) {
      cur_edge = cur_edge->predecessor_edge;
    }
    assert(cur_edge);
    while (cur_edge != nullptr) {
        // Get the edge info
        const auto& direct_edge = cur_edge;
        auto aug_direct_edge = graph_reader_ptr_->GetAugmentEdgeInfo(direct_edge->id, false);
        if (direct_edge) {
            // LOG_INFO("Reverse tile_Id: {}, edge: {}, idx:{}, prev: {}", edge->tile_id, edge->id, idx, edge_label->predecessor());
            direct_edges.push_back(direct_edge);
            // Check if this is the destination edge
            bool is_dest_edge = false;
            Candidate des_candi;
            if (direct_edge->predecessor() == kInvalidLabel) {
            for (const auto& cand : destination->candidates) {
                bool dir_same = direct_edge->is_forward() ? (cand.link->direction & 0x01) : (cand.link->direction & 0x02);
                if (direct_edge->id.tileid() == cand.link->tile_id && direct_edge->id.local_id_no_dir() == cand.link->id && dir_same) {
                    des_candi = cand;
                    std::vector<PointLL> used_points;
                    if (direct_edge->is_forward()) {
                        for (size_t i = 0; i <= cand.proj_index; i++) {
                            used_points.push_back(aug_direct_edge->geos[i]);
                        }
                        used_points.push_back(cand.proj_pt);
                        path_length += cand.offset;
                        end_offset = cand.offset;
                    } else {
                        used_points.push_back(cand.proj_pt);
                        for (size_t i = cand.proj_index + 1; i < aug_direct_edge->geos.size(); i++) {
                            used_points.push_back(aug_direct_edge->geos[i]);
                        }
                        std::reverse(used_points.begin(), used_points.end());
                        path_length += std::fmax(0.0f, direct_edge->length() - cand.offset);
                        end_offset = std::fmax(0.0f, direct_edge->length() - cand.offset);
                    }
                    is_dest_edge = true;
                    path_points.insert(path_points.end(), used_points.begin(), used_points.end());
                    break;
                }
            }
            }


            // If not the destination edge, add the full edge
            if (!is_dest_edge) {
                std::vector<PointLL> used_points = aug_direct_edge->geos;
                if (!direct_edge->is_forward()) {
                    std::reverse(used_points.begin(), used_points.end());
                }
                path_points.insert(path_points.end(), used_points.begin(), used_points.end());
                path_length += direct_edge->length();
                traffic_light_num += direct_edge->has_traffic_light();
            }

            auto edges = UnPackEdge(direct_edge, direct_edge->is_forward());
            path_edges.insert(path_edges.end(), edges.begin(), edges.end());
        }

        // Move to the next edge
        cur_edge = cur_edge->predecessor_edge; // edge_label->predecessor();
    }

    // Create the path info
    PathInfo path_info;
    path_info.path_id = static_cast<uint64_t>(std::hash<double>{}(candidate.cost)); // Generate a unique ID
    path_info.length = path_length;
    path_info.travel_time = candidate.cost / kSecMultiplier;; // Use cost as travel time
    path_info.traffic_light_num = traffic_light_num;
    path_info.links = path_edges;
    path_info.points = path_points;

    // Create sections
    Section section;
    section.index = 0;
    section.num = path_edges.size();
    section.length = path_length;
    section.time = candidate.cost / kSecMultiplier;
    section.start_offset = start_offset;
    section.end_offset = end_offset;
    path_info.sections.push_back(section);

    // Calculate bounding box
    if (!path_points.empty()) {
        path_info.bbox = AABB2<PointLL>(path_points);
    }

    return path_info;
}

bool PathRanking::IsValidAlternative(
    const CandidateConnection& candidate,
    const std::vector<CandidateConnection>& best_paths) {

    // Check if the cost is within the allowed ratio
    if (candidate.cost > best_paths[0].cost * PathRankingConstants::kAtMostLonger) {
        LOG_INFO("Path rejected: cost ratio exceeded ({} > {})",
                 candidate.cost / best_paths[0].cost, PathRankingConstants::kAtMostLonger);
        return false;
    }

    // Check shared ratio with all existing best paths
    for (const auto& best_path : best_paths) {
        float shared_ratio = CalculateSharedRatio(candidate, best_path);
        if (shared_ratio > PathRankingConstants::kAtMostShared) {
            // LOG_INFO("Path rejected: shared ratio exceeded ({} > {})",
            //          shared_ratio, PathRankingConstants::kAtMostShared);
            return false;
        } else {
            // LOG_INFO("Path accepted: shared ratio within limit ({} <= {})",
            //          shared_ratio, PathRankingConstants::kAtMostShared);
        }
    }

    return true;
}

float PathRanking::CalculateSharedRatio(
    const CandidateConnection& path1,
    const CandidateConnection& path2) {

    // Count shared edges
    size_t shared_count = 0;
    std::vector<GraphId> common_ids;
    auto it1 = path1.edge_ids.begin();
    auto it2 = path2.edge_ids.begin();
    while (it1 != path1.edge_ids.end() && it2 != path2.edge_ids.end()) {
        if (*it1 == *it2) {
            shared_count++;
            common_ids.push_back(*it1);
            ++it1;
            ++it2;
        } else if (*it1 < *it2) {
            ++it1;
        } else {
            ++it2;
        }
    }

    float common_length = 0.0f;
    for (const auto& edge_id : common_ids) {
        auto edge = graph_reader_ptr_->GetDirectEdgeInfo(edge_id, true);// todo
        common_length += edge->length();
    }

    float total_length = 0.01f;
    for (const auto& edge_id : path2.edge_ids) {
        auto edge = graph_reader_ptr_->GetDirectEdgeInfo(edge_id, true); // todo:
        total_length += edge->length();
    }
    // LOG_INFO("common_length: {}, total_length: {}, shared_count: {}, total_cnt: {}", common_length, total_length, shared_count, path2.edge_ids.size());
    return common_length / total_length;

    // Calculate ratio
    // size_t min_size = std::min(path1.edge_ids.size(), path2.edge_ids.size());
    // return min_size > 0 ? static_cast<float>(shared_count) / min_size : 0.0f;
}

void PathRanking::CollectPathEdgeIds(CandidateConnection& candidate) {
    // Use the UpdatePathEdges method from CandidateConnection
    candidate.UpdatePathEdges();
}

std::vector<EdgeInfoPtr> PathRanking::UnPackEdge(const DirectEdgeInfoRawPtr& direct_edge, bool forward) {
    std::vector<EdgeInfoPtr> edges;
    auto make_edge = [&edges](const DirectEdgeInfoRawPtr& direct_edge, bool forward) {
        auto edge = std::make_shared<EdgeInfo>();
        edge->tile_id = direct_edge->id.tileid();
        edge->id = direct_edge->id.local_id_no_dir();
        edge->forward = forward;
        edge->is_inner_link = direct_edge->is_inner_edge();
        edge->length = direct_edge->length();
        // edge->geos = aug_direct_edge->geos;
        edges.push_back(edge);
    };

    auto get_sub_edgeid = [](const DirectEdgeInfoRawPtr& direct_edge, SubEdge subedge) {
        parser::RouteTileID sub_tile_id(direct_edge->id.tileid());
        assert(direct_edge->level_id > 0);
        sub_tile_id.level = 0; // subedge is level 0
        sub_tile_id.tile_id = subedge.tile_id;
        return GraphId(sub_tile_id.value, subedge.edge_id, subedge.edge_dir == 0 ? true : false);
    };

    if (direct_edge->level_id == 0) {
        make_edge(direct_edge, forward);
    } else {
        auto aug_direct_edge = graph_reader_ptr_->GetAugmentEdgeInfo(direct_edge->id, false);
        if (aug_direct_edge->sub_edges.size() == 1) {
            auto& sub_edge = aug_direct_edge->sub_edges.front();
            auto sub_edge_id = get_sub_edgeid(direct_edge, sub_edge);

            auto edge = std::make_shared<EdgeInfo>();
            edge->tile_id = sub_edge_id.tileid();
            edge->id = sub_edge_id.local_id_no_dir();
            edge->forward = sub_edge.edge_dir == 0 ? forward : !forward;
            edge->is_inner_link = direct_edge->is_inner_edge();
            edge->length = direct_edge->length();
            edges.push_back(edge);
        } else {
          // iter subedges, if subedge's level_id > 0, then recursively call
          // UnPackEdge, and combine the result
          for (const auto& sub_edge : aug_direct_edge->sub_edges) {
            auto sub_edge_id = get_sub_edgeid(direct_edge, sub_edge);
            auto sub_edge_ptr = graph_reader_ptr_->GetDirectEdgeInfo(sub_edge_id, true);// todo:
            // assert(sub_edge_ptr);
            if (sub_edge_ptr) {
              // edge_dir = 0 mean the same dir as high level edge
              auto sub_edges = UnPackEdge(sub_edge_ptr, sub_edge.edge_dir == 0 ? forward : !forward);
              edges.insert(edges.end(), sub_edges.begin(), sub_edges.end());
            }
          }
        }
    }
    return edges;
}

}  // namespace path
}  // namespace aurora
