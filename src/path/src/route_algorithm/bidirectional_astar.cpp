// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-04-30
//
#include <vector>
#include "path_module.h"
#include "route_algorithm.h"
#include "bidirectional_astar.h"
#include "path_metrics.h"
#include "iostream"

namespace aurora {
namespace path {

constexpr uint32_t kBucketCount = 20000;
// Threshold (seconds) to extend search once the first connection has been found.
// TODO - this is currently set based on some exceptional cases (e.g. routes taking
// the PA Turnpike which have very long edges). Using a metric based on maximum edge
// cost creates large performance drops - so perhaps some other metric can be found?
constexpr float kThresholdDelta = 100; //420.0f;

// Relative cost extension to find alternative routes. It's a multiplier that we apply
// to the optimal route cost in order to get a new cost threshold. This threshold indicates
// an upper bound value cost for alternative routes we're looking for. Due to the fact that
// we can't estimate route cost that goes through some particular edge very precisely, we
// can find alternatives with costs greater than the threshold.
constexpr float kAlternativeCostExtend = 1.2f;

// Maximum number of additional iterations allowed once the first connection has been found.
// For alternative routes we use bigger cost extension than in the case with one route. This
// may lead to a significant increase in the number of iterations (~time). So, we should limit
// iterations in order no to drop performance too much.
constexpr uint32_t kAlternativeIterationsDelta = 100000;

// 100km
constexpr double kAlternativePathMaxDistance = 500000;

BidirectionalAStar::BidirectionalAStar(const GraphReaderPtr& reader) : RouteAlgorithm(), graph_reader_ptr_(reader) {
    // Get configuration values from PathConfig
    const auto& config = PathConfigManager::Instance();

    // Set max reserved labels count from configuration
    max_reserved_labels_count_ = config.Get("algorithms.bidirectional_astar.max_reserved_labels_count", 1000000);

    // Initialize other members
    cost_threshold_ = std::numeric_limits<float>::max();
    max_iterations_threshold_ = config.Get("algorithms.bidirectional_astar.max_iterations_threshold", 2000000);
    desired_paths_count_ = 1;
    ignore_hierarchy_limits_ = false;
    cost_diff_ = 0.0f;

    // Set default routing strategy
    current_strategy_ = static_cast<PathStrategy>(config.Get("strategy", 0));

    error_code_ = ErrorCode::kErrorCodeOk;
    tag_ = "bidirectional_astar";

    max_expand_level_ = 2;

    LOG_INFO("BidirectionalAStar initialized with max_reserved_labels_count: {}, strategy: {}, max_iterations_threshold: {}",
        max_reserved_labels_count_, static_cast<int>(current_strategy_), max_iterations_threshold_);
}

BidirectionalAStar::~BidirectionalAStar() = default;

void BidirectionalAStar::SetRoutingStrategy(PathStrategy strategy) {
    current_strategy_ = strategy; // 算路请求中的strategy
}

PathResultPtr BidirectionalAStar::CalcRoute(const PathQuery& query) {
    LOG_INFO("BidirectionalAStar::CalcRoute");
    ScopeTime scope_time("CalcRoute");
    OperationTimer timer(OperationType::CalcRoute);

    // Store the query for later use
    query_ = query;

    // Check if we have waypoints (more than 2 path points)
    if (query.path_points.size() > 2) {
        LOG_INFO("Waypoint routing with {} points", query.path_points.size());
        return CalcWaypointRoute(query);
    }

    // Standard two-point routing
    auto start_landmark = query.path_points.front();
    auto end_landmark = query.path_points.back();
    // Initialize the algorithm
    Init(start_landmark->pt, end_landmark->pt);

    // Set origin and destination, Check if origin or destination are invalid
    if (!SetOrign(start_landmark)) {
        LOG_INFO("Origin has no candidates");
        error_code_ = ErrorCode::kErrorCodePathStartNoRoad;
        return GeneratePathResult({});
    }

    if (!SetDestination(end_landmark)) {
        LOG_INFO("destination has no candidates");
        error_code_ = ErrorCode::kErrorCodePathEndNoRoad;
        return GeneratePathResult({});
    }

    // Main bidirectional A* loop
    int n = 0;
    uint32_t forward_pred_idx{0}, reverse_pred_idx{0};
    DirectEdgeInfoRawPtr fwd_pred;
    DirectEdgeInfoRawPtr rev_pred;
    bool expand_forward = true;
    bool expand_reverse = true;

    while (true) {
        // Check iteration threshold
        if ((edgelabels_reverse_.size() + edgelabels_forward_.size()) > iterations_threshold_) {
            LOG_INFO("Exceeded iteration threshold: {}", iterations_threshold_);
            break;
        }

        if (desired_paths_count_ == 1 && !best_connection_.empty()) {
            break;
        }

        // Forward search expansion
        if (expand_forward) {
            forward_pred_idx = adjacencylist_forward_.pop();

            // Check if forward queue is empty
            if (forward_pred_idx == kInvalidLabel) {
                LOG_INFO("Forward search exhausted, edgelabels_forward size: {}", edgelabels_forward_.size());
                break;
            } else {
                // Get the edge label and update status
                fwd_pred = edgelabels_forward_[forward_pred_idx];
                fwd_pred->Update(EdgeSet::kPermanent);

                // Check if cost exceeds threshold
                if (fwd_pred->cost().cost > cost_threshold_) {
                    LOG_INFO("Forward cost exceeds threshold: {} > {}",
                             fwd_pred->cost().cost, cost_threshold_);
                    break;
                }

                // Check for connection to reverse search
                SetForwardConnection(fwd_pred);
            }
        }

        // Reverse search expansion
        if (expand_reverse) {
            reverse_pred_idx = adjacencylist_reverse_.pop();

            // Check if reverse queue is empty
            if (reverse_pred_idx == kInvalidLabel) {
                LOG_INFO("Reverse search exhausted, edgelabels_reverse size:{}", edgelabels_reverse_.size());
                break;
            } else {
                // Get the edge label and update status
                rev_pred = edgelabels_reverse_[reverse_pred_idx];
                rev_pred->Update(EdgeSet::kPermanent);

                // Check if cost exceeds threshold
                if (rev_pred->cost().cost > cost_threshold_) {
                    LOG_INFO("Reverse cost exceeds threshold: {} > {}",
                             rev_pred->cost().cost, cost_threshold_);
                    break;
                }

                // Check for connection to forward search
                SetReverseConnection(rev_pred);
            }
        }

        // Determine which direction to expand next based on sort costs
        if (fwd_pred->sortcost() < rev_pred->sortcost()) {
            expand_forward = true;
            expand_reverse = false;

            // Expand from the forward edge
            Expand<true>(fwd_pred->graphid(), fwd_pred, forward_pred_idx);
        } else {
            expand_forward = false;
            expand_reverse = true;

            // Expand from the reverse edge
            Expand<false>(rev_pred->graphid(), rev_pred, reverse_pred_idx);
        }

        // Periodically log progress
        if (++n % 10000 == 0) {
            LOG_INFO("Processed {} iterations, forward: {}, reverse: {}",
                     n, edgelabels_forward_.size(), edgelabels_reverse_.size());
        }

        // Check if we've found a connection
        if (!best_connection_.empty() &&
            (fwd_pred->cost().cost + rev_pred->cost().cost) > (cost_threshold_ * 1.1)) {
            LOG_INFO("Found connection and costs exceed threshold by 10%");
            break;
        }
    }

    auto path_infos = FormPath();
    return GeneratePathResult(path_infos);
}

PathResultPtr BidirectionalAStar::CalcWaypointRoute(const PathQuery& query) {
    LOG_INFO("BidirectionalAStar::CalcWaypointRoute with {} waypoints", query.path_points.size());
    ScopeTime scope_time("CalcWaypointRoute");

    std::vector<PathInfo> segments;

    // Route between each consecutive pair of points
    for (size_t i = 0; i < query.path_points.size() - 1; ++i) {
        graph_reader_ptr_->Init();
        LOG_INFO("Calculating segment {} to {}", i, i + 1);

        // Create a two-point query for this segment
        PathQuery segment_query = query;
        segment_query.path_points.clear();
        segment_query.path_points.push_back(query.path_points[i]);
        SetWaypointCandidate(segment_query.path_points.front(), segments); // 根据上一段路线结果筛选出当前路段的起始点的匹配link
        segment_query.path_points.push_back(query.path_points[i + 1]);

        // Clear previous state
        Clear();

        // Calculate route for this segment
        auto start_landmark = segment_query.path_points.front();
        auto end_landmark = segment_query.path_points.back();

        // Initialize the algorithm
        Init(start_landmark->pt, end_landmark->pt);

        // Set origin and destination
        if (!SetOrign(start_landmark)) {
            LOG_ERROR("Failed to set origin for segment {} to {}", i, i + 1);
            return GeneratePathResult({});
        }

        if (!SetDestination(end_landmark)) {
            LOG_ERROR("Failed to set destination for segment {} to {}", i, i + 1);
            return GeneratePathResult({});
        }

        // Main bidirectional A* loop for this segment
        int n = 0;
        uint32_t forward_pred_idx{0}, reverse_pred_idx{0};
        DirectEdgeInfoRawPtr fwd_pred;
        DirectEdgeInfoRawPtr rev_pred;
        bool expand_forward = true;
        bool expand_reverse = true;

        while (true) {
            // Check iteration threshold
            if ((edgelabels_reverse_.size() + edgelabels_forward_.size()) > iterations_threshold_) {
                LOG_INFO("Exceeded iteration threshold: {}", iterations_threshold_);
                break;
            }

            if (desired_paths_count_ == 1 && !best_connection_.empty()) {
                break;
            }

            // Forward search expansion
            if (expand_forward) {
                forward_pred_idx = adjacencylist_forward_.pop();

                // Check if forward queue is empty
                if (forward_pred_idx == kInvalidLabel) {
                    LOG_INFO("Forward search exhausted, edgelabels_forward size: {}", edgelabels_forward_.size());
                    break;
                } else {
                    // Get the edge label and update status
                    fwd_pred = edgelabels_forward_[forward_pred_idx];
                    fwd_pred->Update(EdgeSet::kPermanent);

                    // Check if cost exceeds threshold
                    if (fwd_pred->cost().cost > cost_threshold_) {
                        LOG_INFO("Forward cost exceeds threshold: {} > {}", fwd_pred->cost().cost, cost_threshold_);
                        break;
                    }

                    // Check for connection to reverse search
                    SetForwardConnection(fwd_pred);
                }
            }

            // Reverse search expansion
            if (expand_reverse) {
                reverse_pred_idx = adjacencylist_reverse_.pop();

                // Check if reverse queue is empty
                if (reverse_pred_idx == kInvalidLabel) {
                    LOG_INFO("Reverse search exhausted, edgelabels_reverse size:{}", edgelabels_reverse_.size());
                    break;
                } else {
                    // Get the edge label and update status
                    rev_pred = edgelabels_reverse_[reverse_pred_idx];
                    rev_pred->Update(EdgeSet::kPermanent);

                    // Check if cost exceeds threshold
                    if (rev_pred->cost().cost > cost_threshold_) {
                        LOG_INFO("Reverse cost exceeds threshold: {} > {}", rev_pred->cost().cost, cost_threshold_);
                        break;
                    }

                    // Check for connection to forward search
                    SetReverseConnection(rev_pred);
                }
            }

            // Determine which direction to expand next based on sort costs
            if (fwd_pred->sortcost() < rev_pred->sortcost()) {
                expand_forward = true;
                expand_reverse = false;

                // Expand from the forward edge
                Expand<true>(fwd_pred->graphid(), fwd_pred, forward_pred_idx);
            } else {
                expand_forward = false;
                expand_reverse = true;

                // Expand from the reverse edge
                Expand<false>(rev_pred->graphid(), rev_pred, reverse_pred_idx);
            }

            // Periodically log progress
            if (++n % 10000 == 0) {
                LOG_INFO("Processed {} iterations, forward: {}, reverse: {}", n, edgelabels_forward_.size(),
                         edgelabels_reverse_.size());
            }

            // Check if we've found a connection
            if (!best_connection_.empty() && (fwd_pred->cost().cost + rev_pred->cost().cost) > (cost_threshold_ * 1.1)) {
                LOG_INFO("Found connection and costs exceed threshold by 10%");
                break;
            }
        }

        // Form path for this segment
        auto segment_paths = FormPath();
        if (segment_paths.empty()) {
            LOG_ERROR("No path found for segment {} to {}", i, i + 1);
            return GeneratePathResult({});
        }

        // Take the first (best) path for this segment
        segments.push_back(segment_paths[0]);
        LOG_INFO("Segment {} to {} completed: length={:.2f}m, time={:.2f}s, edges={}",
                 i, i + 1, segment_paths[0].length, segment_paths[0].travel_time, segment_paths[0].links.size());
        graph_reader_ptr_->Clear();
    }

    // Concatenate all segments into a single path
    if (segments.empty()) {
        LOG_ERROR("No segments found for waypoint routing");
        return GeneratePathResult({});
    }

    PathInfo concatenated_path = ConcatenatePaths(segments);
    LOG_INFO("Waypoint routing completed: total length={:.2f}m, total time={:.2f}s, total edges={}",
             concatenated_path.length, concatenated_path.travel_time, concatenated_path.links.size());

    return GeneratePathResult({concatenated_path});
}

bool BidirectionalAStar::SetWaypointCandidate(const PathLandmarkPtr& land_mark_ptr, const std::vector<PathInfo>& segments) {
    if (segments.empty() || segments.back().links.empty() || !land_mark_ptr || land_mark_ptr->candidates.empty()) {
        return false;
    }

    auto last_edge = segments.back().links.back(); 
    assert(last_edge != nullptr);

    Candidate match_candidate;
    bool founded = false;
    for (auto& candidate : land_mark_ptr->candidates) {
        if (candidate.link->tile_id == last_edge->tile_id && candidate.link->id == last_edge->id) {
            match_candidate = candidate;
            match_candidate.link->direction = last_edge->forward ? 1 : 2; // 要么正向，要么反向
            founded = true;
            break;
        }
    }

    if (founded) {
        land_mark_ptr->candidates = {match_candidate};
        return true;
    }
    assert(false);
    return true;
}

void BidirectionalAStar::Clear() {
    edgelabels_forward_.clear();
    edgelabels_reverse_.clear();

    adjacencylist_forward_.clear();
    adjacencylist_reverse_.clear();
}

void BidirectionalAStar::SetMaxExpandLevel(double distance) {
    if (distance < 5000.0f) { // 5km
        max_expand_level_ = 0;
    } else if (distance < 10000.0f) { // 10km
        max_expand_level_ = 1;
    } else {
        max_expand_level_ = 2;
    }
}

void BidirectionalAStar::Init(const PointLL& orig, const PointLL& dest) {
    ScopeTime scope_time("Init");
    // if distance between orig to dest is > 500km , set desired_paths_count_ = 1, else = 3
    double distance = orig.Distance(dest);
    if (distance > kAlternativePathMaxDistance) {
        desired_paths_count_ = 1;
    } else {
        desired_paths_count_ = 3;
    }

    SetMaxExpandLevel(distance);

    error_code_ = ErrorCode::kErrorCodeOk;
    // todo: current use default strategy configured in path.yaml
    SetRoutingStrategy(static_cast<PathStrategy>(query_.strategy));
    // Create costing object
    Costing costing_options;
    costing_options.options.strategy = current_strategy_;
    costing_ = std::make_shared<AutoCost>(costing_options, graph_reader_ptr_);
    costing_->SetStrategy(current_strategy_);

    // Initialize A* heuristics
    heuristic_forward_.Init(dest, costing_->AStarCostFactor());
    heuristic_backward_.Init(orig, costing_->AStarCostFactor());

    // Reserve space for edge labels
    edgelabels_forward_.reserve(max_reserved_labels_count_);
    edgelabels_reverse_.reserve(max_reserved_labels_count_);

    // Initialize adjacency lists
    const float mincostf = heuristic_forward_.Get(orig);
    const float range = 1000000.0f;
    const uint32_t bucketsize = 1;
    adjacencylist_forward_.reuse(mincostf, range, bucketsize, &edgelabels_forward_);

    const float mincostr = heuristic_backward_.Get(dest);
    adjacencylist_reverse_.reuse(mincostr, range, bucketsize, &edgelabels_reverse_);

    // Calculate cost difference for bidirectional search balancing
    cost_diff_ = mincostf - mincostr;
    LOG_INFO("Cost difference: {:03.2f}", cost_diff_);

    // Initialize connection tracking
    best_connection_.clear();
    cost_threshold_ = std::numeric_limits<float>::max();
    iterations_threshold_ = max_iterations_threshold_;

    // Initialize hierarchy limits for multi-level routing
    hierarchy_limits_forward_.clear();
    hierarchy_limits_reverse_.clear();

    // Set up hierarchy limits for each level (0, 1, 2)
    for (uint32_t level = 0; level < 3; level++) {
        HierarchyLimits forward_limits;
        HierarchyLimits reverse_limits;

        // Level 0 (local roads) - no limits on transitions
        if (level == 0) {
            forward_limits.set_max_up_transitions(200);
            forward_limits.set_expand_within_dist(5000.0f);
            reverse_limits.set_max_up_transitions(200);
            reverse_limits.set_expand_within_dist(5000.0f);
        }
        // Level 1 (arterial roads) - moderate limits
        else if (level == 1) {
            forward_limits.set_max_up_transitions(100);
            forward_limits.set_expand_within_dist(10000.0f);
            reverse_limits.set_max_up_transitions(100);
            reverse_limits.set_expand_within_dist(10000.0f);
        }
        // Level 2 (highways) - stricter limits
        else if (level == 2) {
            // No limits on transitions
            forward_limits.set_max_up_transitions(std::numeric_limits<uint32_t>::max());
            forward_limits.set_expand_within_dist(std::numeric_limits<float>::max());
            reverse_limits.set_max_up_transitions(std::numeric_limits<uint32_t>::max());
            reverse_limits.set_expand_within_dist(std::numeric_limits<float>::max());
        }

        hierarchy_limits_forward_.push_back(forward_limits);
        hierarchy_limits_reverse_.push_back(reverse_limits);
    }

    // Set flag to ignore hierarchy limits if needed
    ignore_hierarchy_limits_ = false;
    graph_reader_ptr_->SwitchRouteTileCacheMode(true);

    LOG_INFO("Bidirectional A* initialized with {} hierarchy levels", hierarchy_limits_forward_.size());
}

bool BidirectionalAStar::SetOrign(const PathLandmarkPtr& origin) {
    ScopeTime scope_time("SetOrigin");
    if (!origin || !origin->valid) {
        LOG_INFO("Origin is invalid");
        return false;
    }

    if (origin->candidates.empty()) {
        graph_reader_ptr_->StaticMatching(origin, PathConfigManager::Instance().GetMatchRadius());
    }

    if (origin->candidates.empty()) {
        LOG_INFO("Origin has no candidates");
        return false;
    }
    origin_ = origin;

    // Add each origin candidate to the adjacency list
    for (const auto& candidate : origin->candidates) {
        // Calculate the cost from the origin to the edge
        Cost node_cost = costing_->CandidateCost(candidate);

        // Forward direction
        if ((candidate.link->direction & 0x01)) {
            GraphId edge_id = {candidate.link->tile_id, candidate.link->id, true};
            const auto& edgePtr = graph_reader_ptr_->GetDirectEdgeInfo(edge_id, true);
            assert(edgePtr);
            Cost edge_cost = costing_->EdgeCost(edgePtr);

            Cost cost = edge_cost * (1.0f - (candidate.offset / candidate.link->length));
            cost += node_cost;

            // In A*, the sort cost is the cost + heuristic
            float sortcost = cost.cost;
            const auto& node_ptr = edgePtr->tile_ptr->GetNodeInfo(edgePtr->endnode());
            if (node_ptr) {
                sortcost += heuristic_forward_.Get(node_ptr->GetPoint());
            }

            // Add to the edge labels
            uint32_t idx = edgelabels_forward_.size();
            edgePtr->Set(EdgeSet::kTemporary, idx);
            edgePtr->UpdateCost(nullptr, kInvalidLabel, cost, sortcost, node_cost, 0U);
            edgelabels_forward_.push_back(edgePtr);

            // Add to the adjacency list
            adjacencylist_forward_.add(idx);
        }

        // Reverse direction
        if ((candidate.link->direction & 0x02)) {
            GraphId edge_id = {candidate.link->tile_id, candidate.link->id, false};
            const auto& edgePtr = graph_reader_ptr_->GetDirectEdgeInfo(edge_id, true);
            assert(edgePtr);
            Cost edge_cost = costing_->EdgeCost(edgePtr);

            Cost cost = edge_cost * (candidate.offset / candidate.link->length);
            cost += node_cost;

            // In A*, the sort cost is the cost + heuristic
            float sortcost = cost.cost;
            const auto& node_ptr = edgePtr->tile_ptr->GetNodeInfo(edgePtr->startnode());
            if (node_ptr) {
                sortcost += heuristic_forward_.Get(node_ptr->GetPoint());
            }

            // Add to the edge labels
            uint32_t idx = edgelabels_forward_.size();
            edgePtr->Set(EdgeSet::kTemporary, idx);
            edgePtr->UpdateCost(nullptr, kInvalidLabel, cost, sortcost, node_cost, 0U);
            edgelabels_forward_.push_back(edgePtr);

            // Add to the adjacency list
            adjacencylist_forward_.add(idx);
        }
    }
    return true;
}

bool BidirectionalAStar::SetDestination(const PathLandmarkPtr& destination) {
    ScopeTime scope_time("SetDestination");
    if (!destination || !destination->valid) {
        LOG_INFO("Destination is invalid");
        return false;
    }

    if (destination->candidates.empty()) {
        graph_reader_ptr_->StaticMatching(destination, PathConfigManager::Instance().GetMatchRadius());
    }

    if (destination->candidates.empty()) {
        LOG_INFO("Destination has no candidates");
        return false;
    }
    destination_ = destination;

    // Add each destination candidate to the adjacency list
    for (const auto& candidate : destination->candidates) {
        // Calculate the cost from the destination to the edge
        Cost node_cost = costing_->CandidateCost(candidate);

        // Forward direction (for reverse search)
        if (candidate.link->direction & 0x01) {
            GraphId edge_id = {candidate.link->tile_id, candidate.link->id, true};
            const auto& edgePtr = graph_reader_ptr_->GetDirectEdgeInfo(edge_id, false);
            assert(edgePtr);
            Cost edge_cost = costing_->EdgeCost(edgePtr);
            Cost cost = edge_cost * (candidate.offset / candidate.link->length);
            cost += node_cost;

            // In A*, the sort cost is the cost + heuristic
            float sortcost = cost.cost;
            const auto& node_ptr = edgePtr->tile_ptr->GetNodeInfo(edgePtr->startnode());
            if (node_ptr) {
                sortcost += heuristic_backward_.Get(node_ptr->GetPoint());
            }

            // Add to the edge labels
            uint32_t idx = edgelabels_reverse_.size();
            edgePtr->Set(EdgeSet::kTemporary, idx);
            edgePtr->UpdateCost(nullptr, kInvalidLabel, cost, sortcost, node_cost, 0U);
            edgelabels_reverse_.push_back(edgePtr);

            // Add to the adjacency list
            adjacencylist_reverse_.add(idx);
        }

        // Reverse direction (for reverse search)
        if (candidate.link->direction & 0x02) {
            GraphId edge_id = {candidate.link->tile_id, candidate.link->id, false};
            const auto& edgePtr = graph_reader_ptr_->GetDirectEdgeInfo(edge_id, false);
            assert(edgePtr);
            Cost edge_cost = costing_->EdgeCost(edgePtr);
            Cost cost = edge_cost * (1.0f - (candidate.offset / candidate.link->length));
            cost += node_cost;

            // In A*, the sort cost is the cost + heuristic
            float sortcost = cost.cost;
            const auto& node_ptr = edgePtr->tile_ptr->GetNodeInfo(edgePtr->endnode());
            if (node_ptr) {
                sortcost += heuristic_backward_.Get(node_ptr->GetPoint());
            }

            // Add to the edge labels
            uint32_t idx = edgelabels_reverse_.size();
            edgePtr->Set(EdgeSet::kTemporary, idx);
            edgePtr->UpdateCost(nullptr, kInvalidLabel, cost, sortcost, node_cost, 0U);
            edgelabels_reverse_.push_back(edgePtr);

            // Add to the adjacency list
            adjacencylist_reverse_.add(idx);
        }
    }
    return true;
}

template <bool forward>
void BidirectionalAStar::Expand(GraphId id, const DirectEdgeInfoRawPtr& pred, uint32_t pred_idx) {
    // Get the node info
    auto expand_node_id = forward ? (pred->is_forward() ? pred->endnode() : pred->startnode())
                                  : (pred->is_forward() ? pred->startnode() : pred->endnode());
    auto unique_node_id = GraphId(id.tileid(), expand_node_id);
    const auto& node_ptr = pred->tile_ptr->GetNodeInfo(expand_node_id);

    if (!node_ptr) {
        LOG_INFO("Node not found: {}", unique_node_id.ToString());
        return;
    }

    // Check if costing allows access to this node
    if (!costing_->Allowed(node_ptr)) {
        return;
    }

    // Get hierarchy limits
    auto& hierarchy_limits = forward ? hierarchy_limits_forward_ : hierarchy_limits_reverse_;

    // Check for upward transitions
    if (node_ptr->level() < max_expand_level_ && node_ptr->IsTransUp()) {
      // Increment the transition count
      hierarchy_limits[node_ptr->level()].set_up_transition_count(
          hierarchy_limits[node_ptr->level()].up_transition_count() + 1);

      // Get the node in the higher level
      auto upnode_id = node_ptr->GetTranUpId();
      const auto& up_node_ptr = graph_reader_ptr_->GetNodeInfo(upnode_id, true);
      if (up_node_ptr) {
        // LOG_INFO("Expanding upward to level {}", up_node_ptr->level());
        // Expand at the higher level
        ExpandInner<forward>(up_node_ptr, pred, pred_idx);
      }
    }

    // Handle stopping at the current level transition if allowed by hierarchy limits
    const auto& pt = node_ptr->GetPoint();
    // double search_distance = 0.0f;
    // if (forward) {
    //     search_distance = heuristic_backward_.GetDistance(node_ptr->GetPoint());
    // } else {
    //     search_distance = heuristic_forward_.GetDistance(node_ptr->GetPoint());
    // }
    if (!ignore_hierarchy_limits_ &&
        StopExpanding(hierarchy_limits[node_ptr->level()], pred->cost().length)) {
    //   LOG_INFO("Skip current level expand due to hierarchy limits");
    } else {
      // Expand at the current level
      ExpandInner<forward>(node_ptr, pred, pred_idx);
    }
}

template <bool forward>
void BidirectionalAStar::ExpandInner(const NodeInfoRawPtr& node_ptr, const DirectEdgeInfoRawPtr& pred, uint32_t pred_idx) {
    // Get outgoing edges
    std::vector<DirectEdgeInfoRawPtr> out_edges;
    if (forward) {
        graph_reader_ptr_->GetAllExitEdges(node_ptr, out_edges);
    } else {
        graph_reader_ptr_->GetAllEnterEdges(node_ptr, out_edges);
    }

    // Expand to all outgoing edges
    for (const auto& outedge : out_edges) {
        // Skip the edge we came from (avoid U-turns)
        if (outedge->graphid().tileid() == pred->graphid().tileid() &&
            outedge->graphid().local_id_no_dir() == pred->graphid().local_id_no_dir() &&
            out_edges.size() > 1) { // todo:opt
            continue;
        }

        // Check if costing allows this edge
        if (!costing_->Allowed(pred, node_ptr, outedge)) {
            continue;
        }

        if (true) {
            if (outedge->edge_status.set() == EdgeSet::kPermanent) {
                continue;
            }

            if (costing_->Restricted(pred, node_ptr, outedge, forward)) {
                // LOG_INFO("expand dir: {}, edge_dir: Restricted, id {}", forward, true, outedge->id.ToString());
                continue;
            }

            // Calculate cost
            Cost edgecost = costing_->EdgeCost(outedge);
            Cost nodecost = costing_->NodeCost(pred, node_ptr, outedge, forward);
            uint32_t turn_degree = CalculateTurnDegree(pred, node_ptr, outedge, forward);
            // todo： add node cost
            Cost newcost = pred->cost() + edgecost + nodecost;

            // Calculate sort cost with heuristic
            float sortcost = newcost.cost;
            const auto& end_node_ptr = outedge->tile_ptr->GetNodeInfo(forward ? outedge->endnode() : outedge->startnode());
            if (end_node_ptr) {
                sortcost += forward ?
                    heuristic_forward_.Get(end_node_ptr->GetPoint()) :
                    heuristic_backward_.Get(end_node_ptr->GetPoint());
            }

            // Check if this is a new edge or a better path to an existing edge
            EdgeStatusInfo status = outedge->edge_status;
            if (status.set() == EdgeSet::kTemporary) {
                uint32_t idx = status.index();
                auto& edgelabels = forward ? edgelabels_forward_ : edgelabels_reverse_;
                auto& adjacencylist = forward ? adjacencylist_forward_ : adjacencylist_reverse_;

                if (newcost.cost < edgelabels[idx]->cost().cost) {
                    outedge->predecessor_edge = pred;
                    // Update with the new cost
                    float newsortcost = edgelabels[idx]->sortcost() - (edgelabels[idx]->cost().cost - newcost.cost);
                    // float newsortcost = sortcost;
                    adjacencylist.decrease(idx, newsortcost);
                    edgelabels[idx]->UpdateCost(pred, pred_idx, newcost, newsortcost, nodecost, turn_degree);
                }
            } else {
                outedge->predecessor_edge = pred;
                // Add a new edge label
                uint32_t idx = forward ? edgelabels_forward_.size() : edgelabels_reverse_.size();
                auto& edgelabels = forward ? edgelabels_forward_ : edgelabels_reverse_;
                auto& adjacencylist = forward ? adjacencylist_forward_ : adjacencylist_reverse_;

                outedge->UpdateCost(pred, pred_idx, newcost, sortcost, nodecost, turn_degree);
                edgelabels.push_back(outedge);
                adjacencylist.add(idx);
                outedge->Set(EdgeSet::kTemporary, idx);
            }
        }
    }
}


bool BidirectionalAStar::SetForwardConnection(const DirectEdgeInfoRawPtr& pred) {
    // 先判断正向/反向是否都加载了当前的tile.然后再进行是否edge已经探索的判断。

    if (!pred->tile_ptr->header()->TileLoadBoth()) {
        return false;
    }

    uint32_t direct_id = pred->id.direct_id();

    auto* back_edge_ptr = pred->tile_ptr->FindDirectEdgeInfo(direct_id, false);
    if (!back_edge_ptr) {
        return false;
    }

    // Check if this edge is already in the reverse edge labels
    auto back_edge_status = back_edge_ptr->edge_status;
    if (back_edge_status.set() == EdgeSet::kUnreachedOrReset) {
        return false;
    }
    auto rev_pred = edgelabels_reverse_[back_edge_status.index()];
    // only connect kPermanent or start/end edge.
    if (back_edge_status.set() == EdgeSet::kTemporary && rev_pred->predecessor() != kInvalidLabel) {
        return false;
    }

    if (IsOrignDestLoop(pred, rev_pred)) {
        LOG_INFO("origin loop to dest");
        return false;
    }

    // Check for turn restrictions at the bridging edge
    if (costing_->IsBridgingEdgeRestricted(pred, rev_pred)) {
        // LOG_INFO("Bridging edge restricted between forward searches");
        return false;
    }
    // todo: fix cost in one edge
    double c;
    if (pred->predecessor() != kInvalidLabel) {
        c = edgelabels_forward_[pred->predecessor()]->cost().cost + rev_pred->cost().cost + pred->transition_cost().cost;
    } else {
        c = pred->cost().cost;
        uint32_t predidx = rev_pred->predecessor();
        if (predidx != kInvalidLabel) {
            c += edgelabels_reverse_[predidx]->cost().cost + rev_pred->transition_cost().cost;
        } else {
            c += rev_pred->transition_cost().cost;
        }
    }

    // Found a connection, create a candidate connection
    CandidateConnection connection{pred, rev_pred, c};

    // Add to best connections if it's better than the current threshold
    if (cost_threshold_ == std::numeric_limits<float>::max() || connection.cost < best_connection_.front().cost) {
        if (desired_paths_count_ == 1) {
            cost_threshold_ = connection.cost + kThresholdDelta;
        } else {
          cost_threshold_ =
              kAlternativeCostExtend * connection.cost + kThresholdDelta;
          iterations_threshold_ = edgelabels_forward_.size() +
                                  edgelabels_reverse_.size() +
                                  kAlternativeIterationsDelta;
        }
        LOG_INFO_EVERY_N(100, "Found forward connection with cost: {}, fwd_idx: {}, rvs_idx:{}", connection.cost, pred->edge_status.index(), rev_pred->edge_status.index());
    }

    best_connection_.push_back(connection);
    if (connection.cost < best_connection_.front().cost) {
        std::swap(best_connection_.front(), best_connection_.back());
    }

    return true;
}

bool BidirectionalAStar::SetReverseConnection(const DirectEdgeInfoRawPtr& pred) {
    // 先判断正向/反向是否都加载了当前的tile.然后再进行是否edge已经探索的判断。
    if (!pred->tile_ptr->header()->TileLoadBoth()) {
        return false;
    }

    uint32_t direct_id = pred->id.direct_id();
    auto* fwd_edge_ptr = pred->tile_ptr->FindDirectEdgeInfo(direct_id, true);
    if (!fwd_edge_ptr) {
        return false;
    }

    auto forward_edge_status = fwd_edge_ptr->edge_status;
    // Check if this edge is already in the forward edge labels
    if (forward_edge_status.set() == EdgeSet::kUnreachedOrReset) {
        return false;
    }
    auto fwd_pred = edgelabels_forward_[forward_edge_status.index()];
    // only connect kPermanent or start/end edge.
    if (forward_edge_status.set() == EdgeSet::kTemporary && fwd_pred->predecessor() != kInvalidLabel) {
        return false;
    }

    if (IsOrignDestLoop(fwd_pred, pred)) {
        LOG_INFO("origin loop to dest");
        return false;
    }

    // Check for turn restrictions at the bridging edge
    if (costing_->IsBridgingEdgeRestricted(fwd_pred, pred)) {
        // LOG_INFO("Bridging edge restricted between reverse searches");
        return false;
    }


    double c;
    if (pred->predecessor() != kInvalidLabel) {
        c = edgelabels_reverse_[pred->predecessor()]->cost().cost + fwd_pred->cost().cost + pred->transition_cost().cost;
    } else {
        c = pred->cost().cost;
        uint32_t predidx = fwd_pred->predecessor();
        if (predidx != kInvalidLabel) {
            c += edgelabels_forward_[predidx]->cost().cost + fwd_pred->transition_cost().cost;
        } else {
            c += fwd_pred->transition_cost().cost;
        }
    }

    // Found a connection, create a candidate connection
    CandidateConnection connection{fwd_pred, pred, c};

    // Add to best connections if it's better than the current threshold
    if (cost_threshold_ == std::numeric_limits<float>::max() || connection.cost < best_connection_.front().cost) {
        if (desired_paths_count_ == 1) {
            cost_threshold_ = connection.cost + kThresholdDelta;
        } else {
            cost_threshold_ = kAlternativeCostExtend * connection.cost + kThresholdDelta;
            iterations_threshold_ = edgelabels_forward_.size() + edgelabels_reverse_.size() + kAlternativeIterationsDelta;
        }
        LOG_INFO_EVERY_N(100, "Found forward connection with cost: {}, fwd_idx: {}, rvs_idx:{}", connection.cost, fwd_pred->edge_status.index(), pred->edge_status.index());
    }

    best_connection_.push_back(connection);
    if (connection.cost < best_connection_.front().cost) {
        std::swap(best_connection_.front(), best_connection_.back());
    }

    return true;
}

bool BidirectionalAStar::IsOrignDestLoop(const DirectEdgeInfoRawPtr& fwd_pred, const DirectEdgeInfoRawPtr& rev_pred) {
  if (fwd_pred->predecessor() == kInvalidLabel &&
      rev_pred->predecessor() == kInvalidLabel) {
    // Find the corresponding candidates from origin and destination

    // Get candidate by graphid
    auto find_candidate = [this](const GraphId& graphid,
                                 bool forward) -> const Candidate* {
      auto land_ptr = forward ? origin_ : destination_;
      for (const auto& candidate : land_ptr->candidates) {
        if (candidate.link && candidate.link->tile_id == graphid.tileid() &&
            candidate.link->id == graphid.local_id_no_dir()) {
          return &candidate;
        }
      }
      return nullptr;
    };
    auto fwd_candidate = find_candidate(fwd_pred->id, true);
    auto rev_candidate = find_candidate(rev_pred->id, false);

    // Check if both candidates exist
    if (!fwd_candidate || !rev_candidate) {
      return false;
    }

    // Check the offset conditions based on forward direction
    if (fwd_pred->is_forward()) {
      if (fwd_candidate->offset > rev_candidate->offset) {
        return true;
      }
    } else {
      if (fwd_candidate->offset < rev_candidate->offset) {
        return true;
      }
    }
  }
  return false;
}

std::vector<PathInfo> BidirectionalAStar::FormPath() {
    // For debugging
    WriteVisitedEdges("bidirectional_forward", edgelabels_forward_);
    WriteVisitedEdges("bidirectional_reverse", edgelabels_reverse_);

    // Check if we have any connections
    if (best_connection_.empty()) {
        LOG_INFO("BidirectionalAStar: No path found - no connections");
        return {};
    }
    std::sort(best_connection_.begin(), best_connection_.end());

    // Use the path ranking system to find alternative paths
    PathRanking path_ranking(graph_reader_ptr_);
    auto paths = path_ranking.FindAlternativePaths(best_connection_, origin_, destination_);

    // Debug output for alternative paths
    if (PathConfigManager::Instance().IsDebugEnabled() && !paths.empty()) {
        for (size_t i = 0; i < paths.size(); ++i) {
            std::string route_file = PathConfigManager::Instance().GetDebugOutPath() +
                                    "/route_alternative_" + std::to_string(i) + ".geojson";
            std::vector<DirectEdgeInfoRawPtr> direct_edges;
            for (const auto& link : paths[i].links) {
                auto edge_ptr = graph_reader_ptr_->GetDirectEdgeInfo(GraphId(link->tile_id, link->id, link->forward));
                assert(edge_ptr);
                direct_edges.push_back(edge_ptr);
            }
            geojson_writter_.WriteDirectEdgeInfos(direct_edges, route_file);
            LOG_INFO("Alternative path {}: length={:.2f}m, time={:.2f}s, edges={}",
                    i, paths[i].length, paths[i].travel_time, paths[i].links.size());
        }
    }

    LOG_INFO("edgelabels_forward_: {}, edgelabels_reverse_: {}", edgelabels_forward_.size(), edgelabels_reverse_.size());

    return paths;
}

void BidirectionalAStar::WriteVisitedEdges(const std::string& filename, const std::vector<DirectEdgeInfoRawPtr>& edge_labels) {
    if (PathConfigManager::Instance().IsDebugEnabled()) {
        std::string out_file = PathConfigManager::Instance().GetDebugOutPath() + "/astar_debug_" + filename +  "_labels.geojson";

        for (const auto& edge : edge_labels) {
            auto aug_edge = graph_reader_ptr_->GetAugmentEdgeInfo(edge->id, false);
            if (edge && aug_edge) {
                assert(edge->geos.empty());
                edge->geos = aug_edge->geos;
            } else {
                assert(false);
            }
        }
        // Write edge labels to file
        geojson_writter_.WriteEdgeLabels(edge_labels, out_file);
        LOG_INFO("Wrote {} edgelabels to {}", edge_labels.size(), out_file);
    }
}

PathInfo BidirectionalAStar::ConcatenatePaths(const std::vector<PathInfo>& segments) {
    if (segments.empty()) {
        LOG_ERROR("Cannot concatenate empty segments");
        return {};
    }

    if (segments.size() == 1) {
        return segments[0];
    }

    PathInfo result = segments[0];

    // Concatenate all segments
    for (size_t i = 1; i < segments.size(); ++i) {
        const auto& segment = segments[i];

        // Update total length and travel time
        result.length += segment.length;
        result.travel_time += segment.travel_time;

        size_t links_start_idx = 0;
        size_t points_start_idx = 0;

        // Concatenate links (skip the first link of subsequent segments to avoid duplication)
        if (!segment.links.empty()) {
            // Skip the first link if it's the same as the last link of the previous segment
            if (!result.links.empty() && !segment.links.empty()) {
                // Check if the first link of this segment is the same as the last link of the result
                const auto& last_result_link = result.links.back();
                const auto& first_segment_link = segment.links.front();

                if (last_result_link->tile_id == first_segment_link->tile_id &&
                    last_result_link->id == first_segment_link->id) {
                    links_start_idx = 1; // Skip the duplicate link
                }
            }

            // Add the remaining links
            for (size_t j = links_start_idx; j < segment.links.size(); ++j) {
                result.links.push_back(segment.links[j]);
            }
        }

        // Concatenate points (skip the first point of subsequent segments to avoid duplication)
        if (!segment.points.empty()) {
            if (!result.points.empty() && !segment.points.empty()) {
                // Check if the first point of this segment is very close to the last point of the result
                const auto& last_result_point = result.points.back();
                const auto& first_segment_point = segment.points.front();

                double distance = last_result_point.Distance(first_segment_point);
                if (distance < 1.0) { // Within 1 meter, consider it the same point
                    points_start_idx = 1; // Skip the duplicate point
                }
            }

            // Add the remaining points
            for (size_t j = points_start_idx; j < segment.points.size(); ++j) {
                result.points.push_back(segment.points[j]);
            }
        }

        // Update sections
        if (!segment.sections.empty()) {
            for (const auto& section : segment.sections) {
                Section new_section = section;
                // Adjust the index to account for the concatenated links
                new_section.index += result.links.size() - segment.links.size() + links_start_idx;
                result.sections.push_back(new_section);
            }
        }

        // Update bounding box
        result.bbox.Expand(segment.bbox);
    }

    LOG_INFO("Concatenated {} segments into single path: length={:.2f}m, time={:.2f}s, edges={}",
             segments.size(), result.length, result.travel_time, result.links.size());

    return result;
}

}  // namespace path
}  // namespace aurora
