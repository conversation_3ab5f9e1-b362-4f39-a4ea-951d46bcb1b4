#include "dynamiccost.h"
#include "logger.h"

namespace aurora {
namespace path {

constexpr uint8_t MAX_TRACE_BACK = 10;

DynamicCost::DynamicCost(const Costing& options, std::shared_ptr<GraphReader> graph_reader_ptr)
    : costing_(options), graph_reader_ptr_(graph_reader_ptr) {
}

DynamicCost::~DynamicCost() = default;


bool DynamicCost::Restricted(const DirectEdgeInfoRawPtr& pred, const NodeInfoRawPtr& node, const DirectEdgeInfoRawPtr& edge,
     bool expand_forward) {

    auto is_restricted = [](const RestrictionPtr& cr) {
      if (cr->access_ctrl_relation == parser::AccessCtrlRelation::kAccessCtrlRelation0 &&
          cr->access_ctrl_type == parser::AccessCtrlType::kAccessCtrlType0) {
        return true;
      }

    // todo
    //   if (cr->access_ctrl_relation == parser::AccessCtrlRelation::kAccessCtrlRelation1 &&
    //       cr->access_ctrl_type != parser::AccessCtrlType::kAccessCtrlType0) {
    //     return true;
    //   }

      return false;
    };

    // reset_edge_stauts
    if (expand_forward) {
        if (edge->is_limit_out_edge()) {
            // todo: check turn restriction
            auto restrictions = graph_reader_ptr_->GetExitRestrictions(edge); // current edge
            if (restrictions.size() == 0) {
                return false;
            }

            for (const auto& cr: restrictions) {
                // 普通禁转
                if (is_restricted(cr)) {

                    int32_t max_trace_back_num = MAX_TRACE_BACK;
                    DirectEdgeInfoRawPtr current_label = pred;
                    while (max_trace_back_num > 0) {
                        if (current_label->graphid().local_id() == cr->InDirectId()) {
                            // LOG_INFO("forward Restricted, from {} to {}", current_label->graphid().ToString(), edge->id.ToString());
                            return true;
                        }
                        if (current_label->predecessor() == kInvalidLabel) {
                            break;
                        }
                        current_label = current_label->predecessor_edge;
                        max_trace_back_num--;
                    }
                }
                // todo: 门禁
            }
        }
    } else {
        // todo
        if (edge->is_limit_in_edge()) {
            auto restrictions = graph_reader_ptr_->GetEnterRestrictions(edge); // current edge
            if (restrictions.size() == 0) {
                return false;
            }

            for (const auto& cr: restrictions) {
                // 普通禁转
                if (is_restricted(cr)) {
                    int32_t max_trace_back_num = MAX_TRACE_BACK;
                    DirectEdgeInfoRawPtr current_label = pred;
                    while (max_trace_back_num > 0) {
                        if (current_label->graphid().direct_id() == cr->OutDirectId()) {
                            // LOG_INFO("reverse Restricted, from {} to {}", edge->id.ToString(), current_label->graphid().ToString());
                            return true;
                        } else {
                          if (!current_label->is_inner_edge()) {
                            break;
                          }
                        }
                        if (current_label->predecessor() == kInvalidLabel) {
                            break;
                        }
                        current_label = current_label->predecessor_edge;

                        max_trace_back_num--;
                    }
                }
                // todo: 门禁
            }
        }
    }

    return false;
}

bool DynamicCost::IsBridgingEdgeRestricted(const DirectEdgeInfoRawPtr& fwd_pred, const DirectEdgeInfoRawPtr& rev_pred) {

    // 检查双向A*搜索相遇时是否存在禁转限制
    // 当前实现主要检查简单的from-to禁转限制

    // 构建从前向搜索到相遇点的路径片段
    std::vector<DirectEdgeInfoRawPtr> forward_path;
    DirectEdgeInfoRawPtr current_fwd = fwd_pred;
    int32_t trace_count = 0;

    std::vector<RestrictionPtr> fwd_restrictions;
    // 收集前向路径的边ID
    while (trace_count < MAX_TRACE_BACK && current_fwd->predecessor() != kInvalidLabel) {
        forward_path.push_back(current_fwd);
        auto restrictions = graph_reader_ptr_->GetEnterRestrictions(current_fwd);
        fwd_restrictions.insert(fwd_restrictions.end(), restrictions.begin(), restrictions.end());
        current_fwd = current_fwd->predecessor_edge; //  next_predecessor_fwd(current_fwd);
        trace_count++;
    }
    // 添加最后一条边
    if (trace_count < MAX_TRACE_BACK) {
        forward_path.push_back(current_fwd);
        auto restrictions = graph_reader_ptr_->GetEnterRestrictions(current_fwd);
        fwd_restrictions.insert(fwd_restrictions.end(), restrictions.begin(), restrictions.end());
    }

    // 构建从相遇点到反向搜索的路径片段
    std::vector<DirectEdgeInfoRawPtr> reverse_path;
    DirectEdgeInfoRawPtr current_rev = rev_pred; // ignore duplicate edge of last fwd edge
    trace_count = 0;

    // 收集反向路径的边ID（注意：反向搜索的路径需要反转方向理解）
    while (trace_count < MAX_TRACE_BACK && current_rev->predecessor() != kInvalidLabel) {
        reverse_path.push_back(current_rev);
        current_rev = current_rev->predecessor_edge;
        trace_count++;
    }
    // 添加最后一条边
    if (trace_count < MAX_TRACE_BACK) {
        reverse_path.push_back(current_rev);
    }

    // 构建完整的路径：前向路径 + 反向路径（反转）
    std::vector<DirectEdgeInfoRawPtr> complete_path;

    // 添加前向路径（反转，因为我们是从相遇点往回追溯的）
    for (auto it = forward_path.rbegin(); it != forward_path.rend(); ++it) {
        complete_path.push_back(*it);
    }

    // 添加反向路径
    for (const auto& edge_id : reverse_path) {
        complete_path.push_back(edge_id);
    }

    // 检查路径中的每条边是否有禁转限制
    for (size_t i = 0; i < complete_path.size(); ++i) {
        const auto& current_edge_id = complete_path[i];

        for (const auto& restriction : fwd_restrictions) {
            // 只检查普通禁转限制
            if (restriction->access_ctrl_relation == parser::AccessCtrlRelation::kAccessCtrlRelation0 &&
                restriction->access_ctrl_type == parser::AccessCtrlType::kAccessCtrlType0) {

                // 检查是否存在匹配的from-to限制
                // 在完整路径中查找是否存在 in_edge -> out_edge 的禁转模式
                for (size_t j = 0; j < complete_path.size(); ++j) {
                    const auto& path_edge_id = complete_path[j];

                    // 检查是否匹配限制的起始边, 都是同一个tile， 不用再判断。
                    if (path_edge_id->id.local_id_no_dir() == restriction->in_edge_id &&
                        ((path_edge_id->is_forward() && !restriction->in_edge_dir) ||
                            (!path_edge_id->is_forward() && restriction->in_edge_dir))) {
                        // 查找对应的目标边
                        for (size_t k = j + 1; k < complete_path.size(); ++k) {
                            const auto& target_edge_id = complete_path[k];

                            if (target_edge_id->id.local_id_no_dir() == restriction->out_edge_id &&
                                ((target_edge_id->is_forward() && !restriction->out_edge_dir) ||
                                    (!target_edge_id->is_forward() && restriction->out_edge_dir))) {
                                // LOG_INFO("Bridging edge restricted: from {} to {}, fwd_idx:{}, rev_idx:{}",
                                //         path_edge_id.graphid().ToString(), target_edge_id.graphid().ToString(),
                                //         fwd_pred.predecessor(), rev_pred.predecessor());
                                return true;
                            }
                            // else {
                            //   if (!target_edge_id.edge_ptr()->is_inner_edge()) {
                            //     break;
                            //   }
                            // }
                        }
                    }
                }
            }
        }
    }

    // 没有发现禁转限制
    return false;
}


} // namespace path
} // namespace aurora