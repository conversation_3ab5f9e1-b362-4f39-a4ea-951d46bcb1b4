// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-05-22
//

#ifndef AURORA_PATH_TURN_COST_H_
#define AURORA_PATH_TURN_COST_H_
#include <cstdint>
#include <memory>
#include <unordered_map>
#include <vector>
#include <array>
#include <functional>

#include "graphid.h"
#include "../base/data_interface.h"
#include "costconstants.h"
#include "base/path_util.h"
#include "base/turn.h"
#include "logger.h"
#include "base/graphconstants.h"


namespace aurora {
namespace path {

using EdgeForm = parser::EdgeForm;
using RoadClass = parser::RoadClass;
using EdgeType = parser::EdgeType;
using RoadClass = parser::RoadClass;


static uint32_t LIGHT_COST[static_cast<uint8_t>(Turn::Type::kTurnCount)] = {
  5, 5, 5, 5, 5, 5, 5, 5, 0
};

static uint32_t TURN_COST[2][static_cast<uint8_t>(Turn::Type::kTurnCount)] = {
        {0, 0, 20, 40, 40, 40, 20, 0, 0},  //low level
        {0, 0, 0, 0, 0, 0, 0, 0, 0}   //high level
};

// LinkTurnTime function implementation
inline uint32_t LinkTurnTime(const uint32_t& light_flag, const uint32_t& link_rank, Turn::Type turn_type) {
    int32_t road_rank_level = 0;
    if (link_rank == RoadClass::kRoadClassHighway || link_rank == RoadClass::kRoadClassCityFastWay) {
        road_rank_level = 1;  // Road_Rank_High = 1
    }

    uint32_t node_cost = TURN_COST[road_rank_level][static_cast<uint32_t>(turn_type)];

    if (light_flag) {
        node_cost += LIGHT_COST[static_cast<uint32_t>(turn_type)] * light_flag;
    }
    return node_cost;
}

constexpr double kTurnPenalty = 7.5;
constexpr double kUTurnPenalty = 20;
constexpr double kTurnBias = 1.075;
constexpr double kTurnBiasInv = 1.0 / kTurnBias;
constexpr double kTrafficLightPenalty = 5;

// Turn cost table
const uint32_t LINK_TRAVEL_TURN_COST[2][static_cast<uint8_t>(Turn::Type::kTurnCount)] = {
    {0, 5, 10, 25, 46, 30, 25, 5, 0},  // Right driving , 左舵靠右行驶
    {0, 5, 25, 30, 46, 25, 10, 5, 0}   // Left driving
};

inline uint32_t CalculateTurnDegree(const DirectEdgeInfoRawPtr& pred, const NodeInfoRawPtr& node, const DirectEdgeInfoRawPtr& edge, bool forward) {
//   auto in_heading = node->heading(idx_pred_opp);
//   in_heading = ((in_heading + 180) % 360);
//   auto out_heading = node->heading(edge->localedgeidx());
//   uint32_t turn_degree =
//       valhalla::midgard::GetTurnDegree(in_heading, out_heading);

    int32_t enter_angle = 0;
    int32_t exit_angle = 0;
    // inedge may the same as outedge(U-turn)
    // for (size_t i = 0U; i < node->edge_ids.size(); ++i) {
    //     if (node->edge_ids[i] == pred->graphid().local_id()) {
    //         enter_angle = node->angles[i];
    //     }
    //     if (node->edge_ids[i] == edge->id.local_id()) {
    //         exit_angle = node->angles[i];
    //     }
    // }
    if (forward) {
      enter_angle = pred->is_forward() ? pred->GetEndHeading() : pred->GetStartHeading();
      exit_angle = edge->is_forward() ? edge->GetStartHeading() : edge->GetEndHeading();
    } else {
      exit_angle = pred->is_forward() ? pred->GetStartHeading() : pred->GetEndHeading();
      enter_angle = edge->is_forward() ? edge->GetEndHeading() : edge->GetStartHeading();
    }

    enter_angle = (enter_angle + 180) % 360;
    // int turn_degree = forward ? exit_angle - enter_angle : enter_angle - exit_angle;
    int turn_degree = (exit_angle - enter_angle) % 360;

    turn_degree =  (turn_degree < 0  ? turn_degree + 360 : turn_degree);
    return turn_degree;
}

// we create a lookup tables since the range is well known and the computation
// is relatively expensive
static std::array<double, 360> lookup_table(bool right) {
  std::array<double, 360> turn_durations;
  for (int angle = 0; angle < 360; ++angle) {
    // make the angle symmetric about 0
    int symmetric = angle > 180 ? static_cast<int32_t>(angle) - 360 : angle;
    // calculate a left turn, note the turnary cares what side of the road you
    // drive on
    if (symmetric >= 0)
      turn_durations[angle] =
          kTurnPenalty /
          (1 + std::exp(-((13 * (right ? kTurnBiasInv : kTurnBias)) *
                              symmetric / 180 -
                          6.5 * (right ? kTurnBias : kTurnBiasInv))));
    // calculate a right turn, note the turnary cares what side of the road you
    // drive on
    else
      turn_durations[angle] =
          kTurnPenalty /
          (1 + std::exp(-((13 * (right ? kTurnBias : kTurnBiasInv)) *
                              -symmetric / 180 -
                          6.5 * (right ? kTurnBiasInv : kTurnBias))));
  }

  return turn_durations;
}

// Constants for penalties (as floats)
constexpr float K_PREFERRED_STRAIGHT_PENALTY = 300.0f;
constexpr float K_AVOID_TURNBACK_PENALTY = 600.0f; // Note: flag_avoid_turn_back logic omitted for now
constexpr float K_DOOR_PROHIBIT_PENALTY = 1800.0f;
constexpr float K_TIME_LIMIT_PENALTY = 1800.0f;
constexpr float K_U_TURN_ON_NARROW_DUAL_WAY_PENALTY = 300.0f;
constexpr float K_ENTERING_PATHWAY_PENALTY = 120.0f;
constexpr float K_MAIN_AUXILIARY_SWITCH_STRAIGHT_PENALTY = 30.0f;
constexpr float K_AUXILIARY_TO_NARROWER_AUXILIARY_PENALTY = 60.0f;
constexpr float K_ENTERING_FERRY_PENALTY = 1800.0f;
constexpr float K_ENTERING_SAPA_PENALTY = 30.0f;
constexpr float K_ENTERING_AREA_LINK_PENALTY = 1800.0f;
constexpr float K_ROAD_CONDITION_PROHIBIT_PENALTY = 3600.0f; // e.g. private road, other prohibitions
constexpr float K_ENTERING_NARROW_PATH_PENALTY = 600.0f;
constexpr float K_NORMAL_TO_ONE_LANE_PENALTY = 30.0f;

constexpr float K_PREFERRED_STRAIGHT_DISTANCE_METERS = 0.0f; // 500.0f
// constexpr bool K_AVOID_TURNBACK_RULE_ACTIVE = true; // Placeholder if this rule is globally enabled

inline bool IsDualCarriageway(const DirectEdgeInfoRawPtr& edge_info) {
    if (!edge_info) return false;
    // A simple check: if it has defined lanes for both directions or is marked as separated.
    return (edge_info->forward_lane_count() > 0 && edge_info->backward_lane_count() > 0) || edge_info->is_separate();
}

inline bool IsNormalRoadForm(const DirectEdgeInfoRawPtr& edge_info) {
    if (!edge_info) return true; // Default to normal if no info
    return edge_info->edge_form() == parser::kEdgeFormNormal;
}

// Simplified turn classification based on CalculateTurnDegree output
inline Turn::Type ClassifyTurnBasedOnDegree(uint32_t turn_degree) {
    // valhalla::baldr::Turn::Type uses specific ranges.
    // OSRM-style (which CalculateTurnDegree seems to be inspired by) often has U-turn near 0/360.
    // Given turn_degree = (exit_angle - enter_angle_mod_180) * 2, (0 to 358)
    // A U-turn would be when exit_angle is ~180 deg from enter_angle_mod_180.
    // If enter_angle_mod_180 is relative to road, then exit relative to road.
    // Example: enter 0 (straight), exit 180 (u-turn) -> (180-0)*2 = 360 -> ~0. exit 0 -> 0.
    // This needs to align with how `CalculateTurnDegree` actually behaves.
    // For now, a simple classification:
    // if (turn_degree <= 20 || turn_degree >= 340) { // Straight-ish or U-turn like
    //     // Differentiate U-turn from straight based on context if possible, or rely on specific U-turn flags.
    //     // The reference code's GetTurnType is more sophisticated.
    //     // If CalculateTurnDegree makes U-turns close to 0 or 360, this is ambiguous.
    //     // Let's assume very sharp turns are U-turns for this placeholder:
    //     if (turn_degree <= 10 || turn_degree >= 350) return Turn::Type::kReverse; // Very tight turns
    //     return Turn::Type::kStraight;
    // }
    // if (turn_degree > 20 && turn_degree < 180) return Turn::Type::kRight; // Assuming right-hand driving convention for angles
    // if (turn_degree >= 180 && turn_degree < 340) return Turn::Type::kLeft;
    // return Turn::Type::kStraight; // Default

    auto turn_type = Turn::GetType(turn_degree);
    auto turn_str = Turn::GetTypeString(turn_type);
    // LOG_INFO("turn_degree: {}, turn_type: {}", turn_degree, turn_str);
    return turn_type;
}

class NodeCost {
 public:

  static double CalcTurnTime(const DirectEdgeInfoRawPtr edge, const Turn::Type& turn_type) {
    double timeTotal = 0.0f;
    // Add turn cost
    timeTotal += LINK_TRAVEL_TURN_COST[edge->is_left()][static_cast<uint8_t>(turn_type)];

    return timeTotal;
  }

  // This is a port of:
  // https://github.com/Project-OSRM/osrm-backend/blob/f5ebe8bc3b51831b7b19e73d4879ebbad0161a19/profiles/car.lua#L455
  static double TurnDuration(const DirectEdgeInfoRawPtr& pred, const NodeInfoRawPtr& node,
                            const DirectEdgeInfoRawPtr& edge, bool forward) {
    if (edge->is_inner_edge()) {
      return 0.0f;
    }
    // look up tables for turn penalties based on angle
    static const auto left_hand_lookup(lookup_table(false));
    static const auto right_hand_lookup(lookup_table(true));

    // start with the traffic light
    double turn_duration = 0.0f;  //= node->traffic_signal() ? kTrafficLightPenalty : 0;

    // const auto is_uturn = guidance::getTurnDirection(turn_angle) ==
    // guidance::DirectionModifier::UTurn; BUT for OSRM uturn is 0 angle only,
    // not a range
    uint32_t turn_degree = 0U;
    DirectEdgeInfoRawPtr current_edge = pred;
    while(current_edge->predecessor() != kInvalidLabel) {
      if (current_edge->is_inner_edge() == 0) {
        break;
      }
      current_edge = current_edge->predecessor_edge;
    }
    // const auto& prev_edge = current_label->edge_ptr();
    if (!edge->is_inner_edge() && !current_edge->is_inner_edge()) {
      turn_degree = CalculateTurnDegree(current_edge, node, edge, forward);
    }
    // uint32_t turn_degree = CalculateTurnDegree(pred, node, edge, forward);
    const bool is_u_turn = false;  // valhalla::baldr::Turn::GetType(turn_degree)
                                   // ==valhalla::baldr::Turn::Type::kReverse;

    // if its not a "false node" or its a uturn
    const uint32_t number_of_roads = 3;  //= node->local_edge_count();
    if (number_of_roads > 2 || is_u_turn) {
      // get the duration due to the turn angle and whether it has the right of
      // way
      // node->drive_on_right()-> 左舵靠右行驶。
      assert(turn_degree>=0);
      assert(turn_degree<360);
      turn_duration +=
          true ? right_hand_lookup[turn_degree]
               : left_hand_lookup[turn_degree];
      Turn::Type turn_type = ClassifyTurnBasedOnDegree(turn_degree);
      // turn_duration += CalcTurnTime(edge, turn_type);

      // add a penalty for uturns
      turn_duration += is_u_turn ? kUTurnPenalty : 0;

      bool at_traffic_light = forward ? pred->has_traffic_light() : edge->has_traffic_light();
      if (at_traffic_light && !edge->is_inner_edge() && IsTrafficLightTurn(turn_type)) {
        turn_duration += kTrafficLightPenalty;
      }
    }

    return turn_duration;
  }

  static double CalcNodeCost(const DirectEdgeInfoRawPtr& pred, const NodeInfoRawPtr& node,
                     const DirectEdgeInfoRawPtr& edge, bool forward) {
    double node_cost = 0.0f;
    bool need_calc_extra_cost = true;

    if (!pred || !node || !edge) {
      // Missing critical information, return no extra cost or a high penalty
      return 0.0f;
    }

    DirectEdgeInfoRawPtr in_edge = pred;
    DirectEdgeInfoRawPtr out_edge = edge;
    bool search_forward = pred->is_forward();

    auto out_edge_form =
        search_forward ? out_edge->edge_form() : in_edge->edge_form();
    bool at_traffic_light = forward ? pred->has_traffic_light() : edge->has_traffic_light();

    // 1. Calculate Turn Type (Simplified inner link logic)
    Turn::Type turn_type = Turn::Type::kStraight;
    uint32_t raw_turn_degree = CalculateTurnDegree(pred, node, edge, forward);

    if (in_edge->is_inner_edge() == 0 && out_edge->is_inner_edge() == 0) {
      if (IsBeforeHandTurnForm(in_edge) || IsBeforeHandTurnForm(out_edge) ||
          in_edge->edge_form() == parser::kEdgeFormIC ||
          out_edge->edge_form() == parser::kEdgeFormIC ||
          out_edge_form == parser::kEdgeFormRoundabout) {
        turn_type = Turn::Type::kStraight;
      } else {
        turn_type = ClassifyTurnBasedOnDegree(raw_turn_degree);
      }
    }
    // inner_edge to normal_edge
    else if (in_edge->is_inner_edge() != 0 && out_edge->is_inner_edge() == 0) {
      // todo: 回溯到正常的link
      // auto next_predecessor = [&edge_labels](const EdgeLabelPtr& label) {
      //   return label->predecessor() == kInvalidLabel
      //              ? label
      //              : edge_labels[label->predecessor()];
      // };

      DirectEdgeInfoRawPtr current_edge = pred;
      while(current_edge->predecessor() != kInvalidLabel) {
        if (current_edge->is_inner_edge() == 0) {
          break;
        }
        // current_label = next_predecessor(current_label);
        current_edge = current_edge->predecessor_edge;
      }
      in_edge = current_edge;

      need_calc_extra_cost = false;
      turn_type = ClassifyTurnBasedOnDegree(raw_turn_degree);
    } else {  // Inner-to-inner or Normal-to-inner
      need_calc_extra_cost = false;
      turn_type = Turn::Type::kStraight;
    }

    node_cost += CalcTurnTime(edge, turn_type);

    // 2. Apply penalties based on search direction and initial conditions
    DirectEdgeInfoRawPtr path_dir_in_link = search_forward ? in_edge : out_edge;
    DirectEdgeInfoRawPtr path_dir_out_link = search_forward ? out_edge : in_edge;

    if (search_forward) {
      // U-turn/sharp turn penalty near start of route segment, if no traffic
      // light to control
      if (pred->cost().length < K_PREFERRED_STRAIGHT_DISTANCE_METERS &&
          turn_type != Turn::Type::kStraight && turn_type != Turn::Type::kTurnDest &&
          !at_traffic_light) {
        node_cost += K_PREFERRED_STRAIGHT_PENALTY;
      }
      // 起点或者终点不让掉头？打上标记
      // Avoid turnback flag logic omitted due to missing DirectEdgeInfo flag
      // if (K_AVOID_TURNBACK_RULE_ACTIVE &&
      // !path_dir_in_link->flag_avoid_turn_back &&
      // path_dir_out_link->flag_avoid_turn_back) {
      //   node_cost += K_AVOID_TURNBACK_PENALTY;
      // }

      // Door prohibit (e.g. private gate)
      if (path_dir_out_link->is_gate() ||
          path_dir_out_link
              ->is_private()) {  // Approximation for door_prohibit_type > 4
        node_cost += K_DOOR_PROHIBIT_PENALTY;
      }
      // Time limit
      if (path_dir_out_link->is_limit_in_edge() || path_dir_out_link->is_limit_out_edge()) {
        node_cost += K_TIME_LIMIT_PENALTY;
      }
    } else {  // Backward search
      // Door prohibit
      if (path_dir_in_link->is_gate() ||
          path_dir_in_link->is_private()) {  // path_dir_in_link is the one being
                                           // entered in actual path direction
        node_cost += K_DOOR_PROHIBIT_PENALTY;
      }
      // Time limit
      // if (path_dir_in_link->is_time_limit()) {
      //     node_cost += K_TIME_LIMIT_PENALTY;
      // }
    }

    // 3. Apply 'need_calc_extra_cost' penalties
    if (need_calc_extra_cost) {
      assert(path_dir_in_link->is_inner_edge() == 0 &&
             path_dir_out_link->is_inner_edge() == 0);

      // U-turn on narrow dual-carriageway
      if (path_dir_in_link->lane_count() <= 1 &&
          IsDualCarriageway(path_dir_in_link) &&
          path_dir_in_link->id.local_id_no_dir() ==
              path_dir_out_link->id.local_id_no_dir()) {  // U-turn on same link ID
        node_cost += K_U_TURN_ON_NARROW_DUAL_WAY_PENALTY;
      }

      // Entering pathway (e.g. narrow pedestrian/cycle path)
      if (!IsPathWay(path_dir_in_link) && IsPathWay(path_dir_out_link)) {
        node_cost += K_ENTERING_PATHWAY_PENALTY;
      }

      // Main/auxiliary road switching
      if (!IsAuxiliaryLink(path_dir_in_link) &&
          IsAuxiliaryLink(path_dir_out_link) &&
          turn_type == Turn::Type::kStraight) {
        node_cost += K_MAIN_AUXILIARY_SWITCH_STRAIGHT_PENALTY;
      } else {
        if (turn_type == Turn::Type::kStraight &&
            IsAuxiliaryLink(path_dir_in_link) &&
            IsAuxiliaryLink(path_dir_out_link) &&
            IsDualCarriageway(path_dir_in_link) &&
            !IsDualCarriageway(path_dir_out_link)) {
          node_cost += K_AUXILIARY_TO_NARROWER_AUXILIARY_PENALTY;
        }
      }

      // Entering Ferry
      if (path_dir_in_link->edge_type() != parser::kEdgeTypeFerry &&
          path_dir_out_link->edge_type() == parser::kEdgeTypeFerry) {
        node_cost += K_ENTERING_FERRY_PENALTY;
      }

      // Entering SAPA
      if (path_dir_in_link->edge_form() != parser::kEdgeFormSAPA &&
          path_dir_out_link->edge_form() == parser::kEdgeFormSAPA) {
        node_cost += K_ENTERING_SAPA_PENALTY;
      }

      // Entering area link (e.g. parking lot internal road)
      if (path_dir_in_link->is_area_link() == 0 &&
          path_dir_out_link->is_area_link() != 0) {
        node_cost += K_ENTERING_AREA_LINK_PENALTY;
      }

      // road constructing
      if (path_dir_in_link->is_building() == 0 &&
          path_dir_out_link->is_building() != 0) {
        node_cost += K_ENTERING_NARROW_PATH_PENALTY;
      }

      // Prohibition (e.g. road_condition in ref, approximated by private
      // access)
      if (!path_dir_in_link->is_private() &&
          path_dir_out_link->is_private()) {  // Entering a private road
        node_cost += K_ROAD_CONDITION_PROHIBIT_PENALTY;
      }

      // Entering narrow path
      if (path_dir_in_link->road_class() != parser::kRoadClassPath &&
          path_dir_out_link->road_class() == parser::kRoadClassPath) {
        node_cost += K_ENTERING_NARROW_PATH_PENALTY;
      }

      // Normal road to 1-lane road (approximation for lane_cnt_by_width == 0)
      // Check node_cost == 0 to ensure this is a primary penalty if applicable
      if (node_cost == 0.0f &&
          path_dir_in_link->lane_count() >
              1 &&  // Coming from multi-lane or standard road
          path_dir_out_link->lane_count() == 1 &&  // Going to a single lane road
          path_dir_out_link->direction() !=
              3 &&  // Assuming 0 means non-directional or pedestrian, check
                    // spec for 'kLinkDirectionFree'
          !path_dir_out_link->is_ramp() &&
          (IsNormalRoadForm(path_dir_out_link) ||
           IsAuxiliaryLink(path_dir_out_link))) {
        node_cost += K_NORMAL_TO_ONE_LANE_PENALTY;
      }
    }

    if (at_traffic_light && !edge->is_inner_edge()) {
      // todo:
      node_cost += kTrafficLightPenalty;
    }

    // use edge or pred?
    uint32_t turn_time = LinkTurnTime(at_traffic_light ? 1 : 0, edge->road_class(), turn_type);

    return node_cost + turn_time;
  }
};

} // namespace path
} // namespace aurora

#endif // AURORA_PATH_TURN_COST_H_