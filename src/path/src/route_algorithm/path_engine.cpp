// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-07-04
//

#include "path_engine.h"
#include "Time.h"
#include "config/path_config.h"

namespace aurora {
namespace path {

PathEngine::PathEngine(const GraphReaderPtr& graph_reader)
    : graph_reader_(graph_reader),
      current_algorithm_(nullptr),
      algorithm_type_("dijkstra") // 默认使用 dijkstra
      ,
      interrupt_callback_(nullptr),
      initialized_(false) {
    if (!graph_reader_) {
        LOG_ERROR("PathEngine: GraphReader is null");
        return;
    }

    // 从配置中获取默认算法类型
    algorithm_type_ = PathConfigManager::Instance().GetRouteAlgorithm();

    // 初始化算法实例
    InitializeAlgorithm();

    LOG_INFO("PathEngine initialized with algorithm: {}", algorithm_type_);
}

PathResultPtr PathEngine::CalculateRoute(const PathQuery& query) {
    if (!initialized_ || !current_algorithm_) {
        LOG_ERROR("PathEngine not properly initialized");
        auto result = std::make_shared<PathResult>();
        result->status = "error";
        result->code = static_cast<int>(ErrorCode::kErrorCodeFailed);
        return result;
    }

    // 记录开始时间
    auto start_time = std::chrono::high_resolution_clock::now();

    LOG_INFO("PathEngine: Starting route calculation with {}", algorithm_type_);

    // 设置中断回调
    current_algorithm_->set_interrupt(interrupt_callback_);

    // 清理之前的计算状态
    current_algorithm_->Clear();

    // 算路
    PathResultPtr result = current_algorithm_->CalcRoute(query);

    // 计算耗时
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    // 更新性能指标
    result->metadata.query_time_ms = duration.count();
    UpdateMetrics(duration.count(), result);

    LOG_INFO("PathEngine: Route calculation completed in {}ms, status: {}", duration.count(),
             result ? result->status : "null");

    return result;
}

void PathEngine::SetAlgorithmType(const std::string& algorithm_type) {
    if (algorithm_type == algorithm_type_) {
        return; // 算法类型没有变化
    }

    LOG_INFO("PathEngine: Changing algorithm from {} to {}", algorithm_type_, algorithm_type);

    algorithm_type_ = algorithm_type;
    InitializeAlgorithm();
}

std::string PathEngine::GetCurrentAlgorithmType() const {
    return algorithm_type_;
}

void PathEngine::SetInterruptCallback(const std::function<void()>* interrupt_callback) {
    interrupt_callback_ = interrupt_callback;

    if (current_algorithm_) {
        current_algorithm_->set_interrupt(interrupt_callback_);
    }
}

void PathEngine::CancelCurrentCalculation() {
    if (current_algorithm_) {
        // 创建一个中断回调函数，当被调用时会抛出异常来中断计算
        static std::function<void()> interrupt_func = []() {
            throw std::runtime_error("Calculation cancelled by user request");
        };

        // 设置中断回调
        current_algorithm_->set_interrupt(&interrupt_func);

        LOG_INFO("PathEngine: Cancelling current calculation");
    } else {
        LOG_WARN("PathEngine: No current calculation to cancel");
    }
}

void PathEngine::Clear() {
    if (current_algorithm_) {
        current_algorithm_->Clear();
    }

    // metrics_.Reset();

    LOG_DEBUG("PathEngine: Cleared all temporary data");
}

const PathEngineMetrics& PathEngine::GetMetrics() const {
    return metrics_;
}

bool PathEngine::IsInitialized() const {
    return initialized_ && current_algorithm_ != nullptr;
}

std::shared_ptr<RouteAlgorithm> PathEngine::CreateAlgorithm(const std::string& algorithm_type) {
    if (algorithm_type == "dijkstra") {
        return std::make_shared<Dijkstra>(graph_reader_);
    } else if (algorithm_type == "bidirectional_astar") {
        return std::make_shared<BidirectionalAStar>(graph_reader_);
    } else {
        LOG_WARN("PathEngine: Unknown algorithm type: {}, using Dijkstra as default", algorithm_type);
        return std::make_shared<Dijkstra>(graph_reader_);
    }
}

void PathEngine::InitializeAlgorithm() {
    try {
        current_algorithm_ = CreateAlgorithm(algorithm_type_);

        if (current_algorithm_) {
            // 设置中断回调（如果已设置）
            if (interrupt_callback_) {
                current_algorithm_->set_interrupt(interrupt_callback_);
            }

            initialized_ = true;
            LOG_INFO("PathEngine: Algorithm {} initialized successfully", algorithm_type_);
        } else {
            initialized_ = false;
            LOG_ERROR("PathEngine: Failed to create algorithm instance for {}", algorithm_type_);
        }
    } catch (const std::exception& e) {
        initialized_ = false;
        LOG_ERROR("PathEngine: Exception during algorithm initialization: {}", e.what());
    }
}

void PathEngine::UpdateMetrics(int64_t calculation_time_ms, const PathResultPtr& result) {
    // 更新计算次数
    metrics_.total_calculations++;

    // 更新总计算时间
    metrics_.total_calculation_time_ms += calculation_time_ms;

    // 更新平均计算时间
    metrics_.average_calculation_time_ms =
        static_cast<double>(metrics_.total_calculation_time_ms) / metrics_.total_calculations;

    // 更新最大和最小计算时间
    if (calculation_time_ms > metrics_.max_calculation_time_ms) {
        metrics_.max_calculation_time_ms = calculation_time_ms;
    }

    if (metrics_.min_calculation_time_ms == 0 || calculation_time_ms < metrics_.min_calculation_time_ms) {
        metrics_.min_calculation_time_ms = calculation_time_ms;
    }

    // 更新成功/失败统计
    if (result && result->status == "ok") {
        metrics_.successful_calculations++;
    } else {
        metrics_.failed_calculations++;
    }

    // 更新最后计算时间
    metrics_.last_calculation_timestamp = Time::Now().ToMillisecond();

    LOG_DEBUG("PathEngine: Updated metrics - Total: {}, Avg: {:.2f}ms, Success: {}, Failed: {}",
              metrics_.total_calculations, metrics_.average_calculation_time_ms, metrics_.successful_calculations,
              metrics_.failed_calculations);
}

} // namespace path
} // namespace aurora
