// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-04-30
//

#ifndef AURORA_PATH_SRC_ROUTE_ALGORITHM_ROUTE_ALGORITHM_H_
#define AURORA_PATH_SRC_ROUTE_ALGORITHM_ROUTE_ALGORITHM_H_

#include <memory>
#include <functional>
#include "path_module.h"
#include "logger.h"
#include "config/path_config.h"
#include "base/path_util.h"


namespace aurora {
namespace path {

class RouteAlgorithm {
public:
    virtual ~RouteAlgorithm() = default;

    RouteAlgorithm(const RouteAlgorithm&) = delete;
    RouteAlgorithm& operator=(const RouteAlgorithm&) = delete;

    RouteAlgorithm() = default;

    virtual PathResultPtr CalcRoute(const PathQuery& query) = 0;

    virtual void Clear() = 0;

    /**
     * Set a callback that will throw when the path computation should be aborted
     * @param interrupt_callback  the function to periodically call to see if
     *                            we should abort
     */
    virtual void set_interrupt(const std::function<void()>* interrupt_callback) {
        interrupt = interrupt_callback;
    }

protected:
 PathResultPtr GeneratePathResult(const std::vector<PathInfo>& path_infos) {
     // 1. 根据path_infos生成path_result
     auto result = std::make_shared<PathResult>();
     result->code = static_cast<uint32_t>(error_code_);
     result->tag = tag_ + " " +GetStrategyString(current_strategy_);
     result->paths = path_infos;

     // erase if links.empty()
     result->paths.erase(std::remove_if(result->paths.begin(), result->paths.end(),
                                        [](const PathInfo& path) { return path.links.empty(); }),
                         result->paths.end());
     result->status = result->paths.empty() ? "error" : "ok";
     if (path_infos.empty() && result->code == 0) {
        result->code = static_cast<int>(ErrorCode::kErrorCodePathOfflineError);
     }

     if (!path_infos.empty() && result->paths.empty()) {
         result->code = static_cast<int>(ErrorCode::KErrorCodePathFormPath);
     }

     return result;
 }

protected:
    ErrorCode error_code_;
    // Current routing strategy
    PathStrategy current_strategy_;
    std::string tag_;

protected:
    const std::function<void()>* interrupt = nullptr;
};

}  // namespace path
}  // namespace aurora

#endif  // AURORA_PATH_SRC_ROUTE_ALGORITHM_ROUTE_ALGORITHM_H_
