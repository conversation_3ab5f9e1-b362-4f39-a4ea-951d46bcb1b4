// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-07-04
//

#ifndef AURORA_PATH_SRC_ROUTE_ALGORITHM_PATH_ENGINE_H_
#define AURORA_PATH_SRC_ROUTE_ALGORITHM_PATH_ENGINE_H_

#include <chrono>
#include <functional>
#include <memory>
#include <string>

#include "bidirectional_astar.h"
#include "dijkstra.h"
#include "graph_reader/graph_reader.h"
#include "path_module.h"
#include "route_algorithm.h"

#include "logger.h"

namespace aurora {
namespace path {

/**
 * PathEngine 性能指标结构
 */
struct PathEngineMetrics {
    uint64_t total_calculations = 0;
    uint64_t successful_calculations = 0;
    uint64_t failed_calculations = 0;
    int64_t total_calculation_time_ms = 0;
    int64_t min_calculation_time_ms = 0;
    int64_t max_calculation_time_ms = 0;
    double average_calculation_time_ms = 0.0;
    uint64_t last_calculation_timestamp = 0;

    void Reset() {
        total_calculations = 0;
        successful_calculations = 0;
        failed_calculations = 0;
        total_calculation_time_ms = 0;
        min_calculation_time_ms = 0;
        max_calculation_time_ms = 0;
        average_calculation_time_ms = 0.0;
        last_calculation_timestamp = 0;
    }
};

/**
 * PathEngine - 路由引擎管理器
 *
 * - 统一管理 Dijkstra 和 BidirectionalAStar 算法
 * - 根据配置选择合适的路由算法
 * - 提供统一的路由计算接口
 * - 集成性能监控和指标收集
 */
class PathEngine {
public:
    /**
     * 构造函数
     * @param graph_reader 图数据读取器
     */
    explicit PathEngine(const GraphReaderPtr& graph_reader);

    /**
     * 析构函数
     */
    ~PathEngine() = default;

    // 禁用拷贝构造和赋值
    PathEngine(const PathEngine&) = delete;
    PathEngine& operator=(const PathEngine&) = delete;

    /**
     * 核心路由计算接口
     * @param query 路径查询请求
     * @return 路径计算结果
     */
    PathResultPtr CalculateRoute(const PathQuery& query);

    /**
     * 设置路由算法类型
     * @param algorithm_type 算法类型 ("dijkstra" 或 "bidirectional_astar")
     */
    void SetAlgorithmType(const std::string& algorithm_type);

    /**
     * 获取当前算法类型
     * @return 当前使用的算法类型
     */
    std::string GetCurrentAlgorithmType() const;

    /**
     * 设置中断回调函数
     * @param interrupt_callback 中断回调函数指针
     */
    void SetInterruptCallback(const std::function<void()>* interrupt_callback);

    /**
     * 取消当前计算
     */
    void CancelCurrentCalculation();

    /**
     * 清理所有临时数据和缓存
     */
    void Clear();

    /**
     * 获取性能统计信息
     * @return 性能指标对象的常量引用
     */
    const PathEngineMetrics& GetMetrics() const;

    /**
     * 检查引擎是否已初始化
     * @return true 如果引擎已正确初始化
     */
    bool IsInitialized() const;

private:
    /**
     * 根据算法类型创建算法实例
     * @param algorithm_type 算法类型
     * @return 算法实例指针
     */
    std::shared_ptr<RouteAlgorithm> CreateAlgorithm(const std::string& algorithm_type);

    /**
     * 初始化当前算法实例
     */
    void InitializeAlgorithm();

    /**
     * 更新性能指标
     * @param calculation_time_ms 计算耗时（毫秒）
     * @param result 计算结果
     */
    void UpdateMetrics(int64_t calculation_time_ms, const PathResultPtr& result);

private:
    // 图数据读取器
    GraphReaderPtr graph_reader_;

    // 当前算法实例
    std::shared_ptr<RouteAlgorithm> current_algorithm_;

    // 算法类型
    std::string algorithm_type_;

    // 性能指标
    mutable PathEngineMetrics metrics_;

    // 中断回调函数
    const std::function<void()>* interrupt_callback_;

    // 初始化状态
    bool initialized_;
};

// 类型别名
using PathEnginePtr = std::unique_ptr<PathEngine>;

} // namespace path
} // namespace aurora

#endif // AURORA_PATH_SRC_ROUTE_ALGORITHM_PATH_ENGINE_H_
