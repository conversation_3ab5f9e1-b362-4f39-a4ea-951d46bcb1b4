// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by Augment Agent on 2025-07-07
//

#include "path_request_manager.h"
#include <random>
#include <sstream>
#include <iomanip>

namespace aurora {
namespace path {

PathRequestManager::PathRequestManager()
    : request_counter_(0)
    , total_requests_(0)
    , completed_requests_(0)
    , failed_requests_(0)
    , cancelled_requests_(0) {
    LOG_INFO("PathRequestManager initialized");
}

std::string PathRequestManager::CreateRequest(const PathQueryPtr& query) {
    if (!query) {
        LOG_ERROR("PathRequestManager: Cannot create request with null query");
        return "";
    }
    
    std::string uuid = GenerateUUID();
    auto request_info = std::make_shared<PathRequestInfo>();
    request_info->uuid = uuid;
    request_info->query = query;
    request_info->state = PathRequestState::kPending;
    request_info->create_timestamp = Time::Now().ToMillisecond();
    
    {
        std::lock_guard<std::mutex> lock(requests_mutex_);
        requests_[uuid] = request_info;
    }
    
    total_requests_++;
    
    LOG_DEBUG("PathRequestManager: Created request {}", uuid);
    return uuid;
}

bool PathRequestManager::StartProcessing(const std::string& uuid) {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    
    auto it = requests_.find(uuid);
    if (it == requests_.end()) {
        LOG_WARN("PathRequestManager: Request {} not found for start processing", uuid);
        return false;
    }
    
    if (it->second->state != PathRequestState::kPending) {
        LOG_WARN("PathRequestManager: Request {} is not in pending state, current state: {}", 
                uuid, static_cast<int>(it->second->state));
        return false;
    }
    
    it->second->state = PathRequestState::kProcessing;
    it->second->start_timestamp = Time::Now().ToMillisecond();
    
    LOG_DEBUG("PathRequestManager: Started processing request {}", uuid);
    return true;
}

bool PathRequestManager::CompleteRequest(const std::string& uuid, const PathResult& result) {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    
    auto it = requests_.find(uuid);
    if (it == requests_.end()) {
        LOG_WARN("PathRequestManager: Request {} not found for completion", uuid);
        return false;
    }
    
    if (it->second->state != PathRequestState::kProcessing) {
        LOG_WARN("PathRequestManager: Request {} is not in processing state, current state: {}", 
                uuid, static_cast<int>(it->second->state));
        return false;
    }
    
    it->second->state = PathRequestState::kCompleted;
    it->second->complete_timestamp = Time::Now().ToMillisecond();
    it->second->result = result;
    
    completed_requests_++;
    
    LOG_DEBUG("PathRequestManager: Completed request {}", uuid);
    return true;
}

bool PathRequestManager::FailRequest(const std::string& uuid, const std::string& error_message) {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    
    auto it = requests_.find(uuid);
    if (it == requests_.end()) {
        LOG_WARN("PathRequestManager: Request {} not found for failure", uuid);
        return false;
    }
    
    it->second->state = PathRequestState::kFailed;
    it->second->complete_timestamp = Time::Now().ToMillisecond();
    it->second->error_message = error_message;
    
    failed_requests_++;
    
    LOG_DEBUG("PathRequestManager: Failed request {}: {}", uuid, error_message);
    return true;
}

bool PathRequestManager::CancelRequest(const std::string& uuid) {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    
    auto it = requests_.find(uuid);
    if (it == requests_.end()) {
        LOG_WARN("PathRequestManager: Request {} not found for cancellation", uuid);
        return false;
    }
    
    if (it->second->state == PathRequestState::kCompleted || 
        it->second->state == PathRequestState::kFailed) {
        LOG_WARN("PathRequestManager: Cannot cancel completed/failed request {}", uuid);
        return false;
    }
    
    it->second->state = PathRequestState::kCancelled;
    it->second->complete_timestamp = Time::Now().ToMillisecond();
    
    cancelled_requests_++;
    
    LOG_DEBUG("PathRequestManager: Cancelled request {}", uuid);
    return true;
}

std::shared_ptr<PathRequestInfo> PathRequestManager::GetRequestInfo(const std::string& uuid) const {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    
    auto it = requests_.find(uuid);
    if (it != requests_.end()) {
        return it->second;
    }
    
    return nullptr;
}

bool PathRequestManager::HasRequest(const std::string& uuid) const {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    return requests_.find(uuid) != requests_.end();
}

PathRequestState PathRequestManager::GetRequestState(const std::string& uuid) const {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    
    auto it = requests_.find(uuid);
    if (it != requests_.end()) {
        return it->second->state;
    }
    
    return PathRequestState::kFailed; // 不存在的请求视为失败
}

PathQueryPtr PathRequestManager::GetLatestQuery() const {
    std::lock_guard<std::mutex> lock(requests_mutex_);

    if (requests_.empty()) {
        return nullptr;
    }

    // 找到最新的请求（按创建时间排序）
    auto latest_it = std::max_element(requests_.begin(), requests_.end(),
        [](const auto& a, const auto& b) {
            return a.second->create_timestamp < b.second->create_timestamp;
        });

    return latest_it->second->query;
}

size_t PathRequestManager::CleanupExpiredRequests(uint64_t max_age_ms) {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    
    uint64_t current_time = Time::Now().ToMillisecond();
    size_t cleaned_count = 0;
    
    auto it = requests_.begin();
    while (it != requests_.end()) {
        uint64_t request_age = current_time - it->second->create_timestamp;
        
        // 清理过期的已完成、失败或取消的请求
        if (request_age > max_age_ms && 
            (it->second->state == PathRequestState::kCompleted ||
             it->second->state == PathRequestState::kFailed ||
             it->second->state == PathRequestState::kCancelled)) {
            
            LOG_DEBUG("PathRequestManager: Cleaning up expired request {}", it->first);
            it = requests_.erase(it);
            cleaned_count++;
        } else {
            ++it;
        }
    }
    
    if (cleaned_count > 0) {
        LOG_INFO("PathRequestManager: Cleaned up {} expired requests", cleaned_count);
    }
    
    return cleaned_count;
}

size_t PathRequestManager::GetActiveRequestCount() const {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    
    size_t active_count = 0;
    for (const auto& pair : requests_) {
        if (pair.second->state == PathRequestState::kPending ||
            pair.second->state == PathRequestState::kProcessing) {
            active_count++;
        }
    }
    
    return active_count;
}

size_t PathRequestManager::GetTotalRequestCount() const {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    return requests_.size();
}

std::string PathRequestManager::GetStatistics() const {
    std::ostringstream oss;
    
    oss << "PathRequestManager Statistics:\n";
    oss << "  Total Requests: " << total_requests_.load() << "\n";
    oss << "  Completed Requests: " << completed_requests_.load() << "\n";
    oss << "  Failed Requests: " << failed_requests_.load() << "\n";
    oss << "  Cancelled Requests: " << cancelled_requests_.load() << "\n";
    oss << "  Active Requests: " << GetActiveRequestCount() << "\n";
    oss << "  Current Total: " << GetTotalRequestCount();
    
    return oss.str();
}

void PathRequestManager::Clear() {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    requests_.clear();
    LOG_INFO("PathRequestManager: Cleared all requests");
}

std::string PathRequestManager::GenerateUUID() {
    // 简单的UUID生成器，基于时间戳和计数器
    uint64_t counter = request_counter_++;
    uint64_t timestamp = Time::Now().ToMillisecond();
    
    std::ostringstream oss;
    oss << std::hex << timestamp << "-" << std::hex << counter;
    
    return oss.str();
}

bool PathRequestManager::UpdateRequestState(const std::string& uuid, PathRequestState new_state) {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    
    auto it = requests_.find(uuid);
    if (it == requests_.end()) {
        return false;
    }
    
    it->second->state = new_state;
    return true;
}

}  // namespace path
}  // namespace aurora
