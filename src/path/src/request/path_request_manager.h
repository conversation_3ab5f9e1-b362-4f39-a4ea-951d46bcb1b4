// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by Augment Agent on 2025-07-07
//

#ifndef AURORA_PATH_SRC_REQUEST_PATH_REQUEST_MANAGER_H_
#define AURORA_PATH_SRC_REQUEST_PATH_REQUEST_MANAGER_H_

#include <memory>
#include <string>
#include <unordered_map>
#include <mutex>
#include <atomic>

#include "path_module.h"
#include "Time.h"
#include "logger.h"

namespace aurora {
namespace path {

/**
 * 路径请求状态
 */
enum class PathRequestState {
    kPending,       // 等待处理
    kProcessing,    // 正在处理
    kCompleted,     // 已完成
    kFailed,        // 失败
    kCancelled      // 已取消
};

/**
 * 路径请求信息
 */
struct PathRequestInfo {
    std::string uuid;                    // 请求唯一标识
    PathQueryPtr query;                  // 路径查询
    PathRequestState state;              // 请求状态
    uint64_t create_timestamp;           // 创建时间戳
    uint64_t start_timestamp;            // 开始处理时间戳
    uint64_t complete_timestamp;         // 完成时间戳
    std::string error_message;           // 错误信息
    PathResult result;                   // 路径结果
    
    PathRequestInfo() 
        : state(PathRequestState::kPending)
        , create_timestamp(0)
        , start_timestamp(0)
        , complete_timestamp(0) {}
};

/**
 * PathRequestManager - 路径请求管理器
 * 
 * 职责：
 * - 管理路径请求的生命周期
 * - 跟踪请求状态
 * - 提供请求统计信息
 * - 管理请求超时和清理
 */
class PathRequestManager {
public:
    /**
     * 构造函数
     */
    PathRequestManager();
    
    /**
     * 析构函数
     */
    ~PathRequestManager() = default;

    // 禁用拷贝构造和赋值
    PathRequestManager(const PathRequestManager&) = delete;
    PathRequestManager& operator=(const PathRequestManager&) = delete;

    /**
     * 创建新的路径请求
     * @param query 路径查询
     * @return 请求UUID
     */
    std::string CreateRequest(const PathQueryPtr& query);
    
    /**
     * 开始处理请求
     * @param uuid 请求UUID
     * @return true 如果成功开始处理
     */
    bool StartProcessing(const std::string& uuid);
    
    /**
     * 完成请求处理
     * @param uuid 请求UUID
     * @param result 路径结果
     * @return true 如果成功完成
     */
    bool CompleteRequest(const std::string& uuid, const PathResult& result);
    
    /**
     * 标记请求失败
     * @param uuid 请求UUID
     * @param error_message 错误信息
     * @return true 如果成功标记失败
     */
    bool FailRequest(const std::string& uuid, const std::string& error_message);
    
    /**
     * 取消请求
     * @param uuid 请求UUID
     * @return true 如果成功取消
     */
    bool CancelRequest(const std::string& uuid);
    
    /**
     * 获取请求信息
     * @param uuid 请求UUID
     * @return 请求信息指针，如果不存在返回nullptr
     */
    std::shared_ptr<PathRequestInfo> GetRequestInfo(const std::string& uuid) const;
    
    /**
     * 检查请求是否存在
     * @param uuid 请求UUID
     * @return true 如果请求存在
     */
    bool HasRequest(const std::string& uuid) const;
    
    /**
     * 获取请求状态
     * @param uuid 请求UUID
     * @return 请求状态
     */
    PathRequestState GetRequestState(const std::string& uuid) const;
    
    /**
     * 获取最新的查询请求
     * @return 最新的查询请求，如果没有则返回nullptr
     */
    PathQueryPtr GetLatestQuery() const;

    /**
     * 清理过期的请求
     * @param max_age_ms 最大保留时间（毫秒）
     * @return 清理的请求数量
     */
    size_t CleanupExpiredRequests(uint64_t max_age_ms = 300000); // 默认5分钟
    
    /**
     * 获取活跃请求数量
     * @return 活跃请求数量
     */
    size_t GetActiveRequestCount() const;
    
    /**
     * 获取总请求数量
     * @return 总请求数量
     */
    size_t GetTotalRequestCount() const;
    
    /**
     * 获取请求统计信息
     * @return 统计信息字符串
     */
    std::string GetStatistics() const;

    /**
     * 清理所有请求
     */
    void Clear();

private:
    /**
     * 生成唯一的请求UUID
     * @return UUID字符串
     */
    std::string GenerateUUID();
    
    /**
     * 更新请求状态
     * @param uuid 请求UUID
     * @param new_state 新状态
     * @return true 如果成功更新
     */
    bool UpdateRequestState(const std::string& uuid, PathRequestState new_state);

private:
    // 请求信息映射表
    std::unordered_map<std::string, std::shared_ptr<PathRequestInfo>> requests_;
    
    // 保护请求映射表的互斥锁
    mutable std::mutex requests_mutex_;
    
    // 请求计数器
    std::atomic<uint64_t> request_counter_;
    
    // 统计信息
    std::atomic<uint64_t> total_requests_;
    std::atomic<uint64_t> completed_requests_;
    std::atomic<uint64_t> failed_requests_;
    std::atomic<uint64_t> cancelled_requests_;
};

// 类型别名
using PathRequestManagerPtr = std::unique_ptr<PathRequestManager>;

}  // namespace path
}  // namespace aurora

#endif  // AURORA_PATH_SRC_REQUEST_PATH_REQUEST_MANAGER_H_
