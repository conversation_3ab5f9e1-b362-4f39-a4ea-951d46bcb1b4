// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by Augment Agent on 2025-07-07
//

#include "debug_manager.h"
#include "Time.h"
#include <fstream>
#include <iomanip>
#include <sstream>

namespace aurora {
namespace path {

void DebugManager::Initialize() {
    // 从配置中获取调试设置
    debug_enabled_ = PathConfigManager::Instance().IsDebugEnabled();
    debug_base_path_ = PathConfigManager::Instance().GetDebugOutPath();
    
    if (debug_enabled_) {
        // 确保调试输出目录存在
        EnsureDirectoryExists(debug_base_path_);
        LOG_INFO("DebugManager: Debug enabled, output path: {}", debug_base_path_);
    } else {
        LOG_INFO("DebugManager: Debug disabled");
    }
    
    initialized_ = true;
}

bool DebugManager::IsDebugEnabled() const {
    return debug_enabled_;
}

std::string DebugManager::GetDebugOutputPath() const {
    return debug_base_path_;
}

void DebugManager::WritePathQuery(const PathQueryPtr& query, const std::string& uuid) {
    if (!debug_enabled_ || !query) {
        return;
    }
    
    try {
        // 写入通用查询文件
        std::string query_file = debug_base_path_ + "/path_query.geojson";
        GeoJsonWritter::WritePathQuery(query, query_file);
        
        // 写入特定UUID的查询文件
        std::string test_case_dir = CreateTestCaseDirectory(uuid);
        std::string uuid_query_file = test_case_dir + "/path_query.geojson";
        GeoJsonWritter::WritePathQuery(query, uuid_query_file);
        
        LOG_DEBUG("DebugManager: Path query written for UUID: {}", uuid);
    } catch (const std::exception& e) {
        LOG_ERROR("DebugManager: Failed to write path query for UUID {}: {}", uuid, e.what());
    }
}

void DebugManager::WritePathResult(const PathResult& result, const std::string& uuid) {
    if (!debug_enabled_) {
        return;
    }
    
    try {
        // 写入通用结果文件
        std::string result_file = debug_base_path_ + "/path_result.geojson";
        GeoJsonWritter::WritePathResult(result, result_file);
        
        // 写入特定UUID的结果文件
        std::string test_case_dir = CreateTestCaseDirectory(uuid);
        std::string uuid_result_file = test_case_dir + "/path_result.geojson";
        GeoJsonWritter::WritePathResult(result, uuid_result_file);
        
        LOG_DEBUG("DebugManager: Path result written for UUID: {}", uuid);
    } catch (const std::exception& e) {
        LOG_ERROR("DebugManager: Failed to write path result for UUID {}: {}", uuid, e.what());
    }
}

std::string DebugManager::CreateTestCaseDirectory(const std::string& uuid) {
    std::string test_case_dir = debug_base_path_ + "/test_cases/" + uuid;
    EnsureDirectoryExists(test_case_dir);
    return test_case_dir;
}

void DebugManager::WritePerformanceMetrics(const PathEngineMetrics& metrics, const std::string& uuid) {
    if (!debug_enabled_) {
        return;
    }
    
    try {
        std::string metrics_file = CreateTestCaseDirectory(uuid) + "/performance_metrics.json";
        
        std::ofstream file(metrics_file);
        if (!file.is_open()) {
            LOG_ERROR("DebugManager: Failed to open metrics file: {}", metrics_file);
            return;
        }
        
        // 写入JSON格式的性能指标
        file << "{\n";
        file << "  \"uuid\": \"" << uuid << "\",\n";
        file << "  \"timestamp\": \"" << GetTimestampString() << "\",\n";
        file << "  \"metrics\": {\n";
        file << "    \"total_calculations\": " << metrics.total_calculations << ",\n";
        file << "    \"successful_calculations\": " << metrics.successful_calculations << ",\n";
        file << "    \"failed_calculations\": " << metrics.failed_calculations << ",\n";
        file << "    \"total_calculation_time_ms\": " << metrics.total_calculation_time_ms << ",\n";
        file << "    \"min_calculation_time_ms\": " << metrics.min_calculation_time_ms << ",\n";
        file << "    \"max_calculation_time_ms\": " << metrics.max_calculation_time_ms << ",\n";
        file << "    \"average_calculation_time_ms\": " << std::fixed << std::setprecision(2) 
             << metrics.average_calculation_time_ms << ",\n";
        file << "    \"last_calculation_timestamp\": " << metrics.last_calculation_timestamp << "\n";
        file << "  }\n";
        file << "}\n";
        
        file.close();
        LOG_DEBUG("DebugManager: Performance metrics written for UUID: {}", uuid);
    } catch (const std::exception& e) {
        LOG_ERROR("DebugManager: Failed to write performance metrics for UUID {}: {}", uuid, e.what());
    }
}

void DebugManager::WriteDebugInfo(const std::string& content, const std::string& filename, 
                                 const std::string& uuid) {
    if (!debug_enabled_) {
        return;
    }
    
    try {
        std::string file_path;
        if (!uuid.empty()) {
            file_path = CreateTestCaseDirectory(uuid) + "/" + filename;
        } else {
            file_path = debug_base_path_ + "/" + filename;
        }
        
        std::ofstream file(file_path);
        if (!file.is_open()) {
            LOG_ERROR("DebugManager: Failed to open debug file: {}", file_path);
            return;
        }
        
        file << content;
        file.close();
        
        LOG_DEBUG("DebugManager: Debug info written to: {}", file_path);
    } catch (const std::exception& e) {
        LOG_ERROR("DebugManager: Failed to write debug info to {}: {}", filename, e.what());
    }
}

bool DebugManager::IsInitialized() const {
    return initialized_;
}

void DebugManager::EnsureDirectoryExists(const std::string& path) {
    try {
        if (!std::filesystem::exists(path)) {
            std::filesystem::create_directories(path);
            LOG_DEBUG("DebugManager: Created directory: {}", path);
        }
    } catch (const std::filesystem::filesystem_error& e) {
        LOG_ERROR("DebugManager: Failed to create directory {}: {}", path, e.what());
    }
}

std::string DebugManager::GenerateFileName(const std::string& base_name, 
                                          const std::string& uuid, 
                                          const std::string& extension) {
    if (uuid.empty()) {
        return base_name + "." + extension;
    } else {
        return base_name + "_" + uuid + "." + extension;
    }
}

std::string DebugManager::GetTimestampString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    
    return ss.str();
}

}  // namespace path
}  // namespace aurora
