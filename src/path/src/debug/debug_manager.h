// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by Augment Agent on 2025-07-07
//

#ifndef AURORA_PATH_SRC_DEBUG_DEBUG_MANAGER_H_
#define AURORA_PATH_SRC_DEBUG_DEBUG_MANAGER_H_

#include <string>
#include <filesystem>

#include "path_module.h"
#include "geojson_writter.h"
#include "config/path_config.h"
#include "route_algorithm/path_engine.h"
#include "logger.h"

namespace aurora {
namespace path {

/**
 * DebugManager - 调试输出管理器
 * 
 * 职责：
 * - 管理调试文件输出
 * - 控制调试开关
 * - 统一调试文件命名和路径管理
 * - 提供性能指标输出
 */
class DebugManager {
public:
    /**
     * 构造函数
     */
    DebugManager() = default;
    
    /**
     * 析构函数
     */
    ~DebugManager() = default;

    // 禁用拷贝构造和赋值
    DebugManager(const DebugManager&) = delete;
    DebugManager& operator=(const DebugManager&) = delete;

    /**
     * 初始化调试管理器
     */
    void Initialize();
    
    /**
     * 检查调试是否启用
     * @return true 如果调试已启用
     */
    bool IsDebugEnabled() const;
    
    /**
     * 获取调试输出路径
     * @return 调试输出路径
     */
    std::string GetDebugOutputPath() const;
    
    /**
     * 写入路径查询到调试文件
     * @param query 路径查询对象
     * @param uuid 请求唯一标识
     */
    void WritePathQuery(const PathQueryPtr& query, const std::string& uuid);
    
    /**
     * 写入路径结果到调试文件
     * @param result 路径结果对象
     * @param uuid 请求唯一标识
     */
    void WritePathResult(const PathResult& result, const std::string& uuid);
    
    /**
     * 创建测试用例目录
     * @param uuid 请求唯一标识
     * @return 创建的目录路径
     */
    std::string CreateTestCaseDirectory(const std::string& uuid);
    
    /**
     * 写入性能指标
     * @param metrics 性能指标
     * @param uuid 请求唯一标识
     */
    void WritePerformanceMetrics(const PathEngineMetrics& metrics, const std::string& uuid);
    
    /**
     * 写入通用调试信息到文件
     * @param content 调试内容
     * @param filename 文件名
     * @param uuid 请求唯一标识（可选）
     */
    void WriteDebugInfo(const std::string& content, const std::string& filename, 
                       const std::string& uuid = "");

    /**
     * 检查是否已初始化
     * @return true 如果已初始化
     */
    bool IsInitialized() const;

private:
    /**
     * 确保目录存在
     * @param path 目录路径
     */
    void EnsureDirectoryExists(const std::string& path);
    
    /**
     * 生成文件名
     * @param base_name 基础文件名
     * @param uuid 唯一标识
     * @param extension 文件扩展名
     * @return 完整文件名
     */
    std::string GenerateFileName(const std::string& base_name, 
                                const std::string& uuid, 
                                const std::string& extension);
    
    /**
     * 获取时间戳字符串
     * @return 格式化的时间戳字符串
     */
    std::string GetTimestampString() const;

private:
    // GeoJSON 写入器
    GeoJsonWritter geojson_writer_;
    
    // 调试输出基础路径
    std::string debug_base_path_;
    
    // 调试开关状态
    bool debug_enabled_;
    
    // 初始化状态
    bool initialized_;
};

// 类型别名
using DebugManagerPtr = std::unique_ptr<DebugManager>;

}  // namespace path
}  // namespace aurora

#endif  // AURORA_PATH_SRC_DEBUG_DEBUG_MANAGER_H_
