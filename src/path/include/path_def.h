// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-05-14
//

#ifndef AURORA_PATH_INCLUDE_PATH_DEF_H_
#define AURORA_PATH_INCLUDE_PATH_DEF_H_

#include <algorithm>
#include <vector>
#include <string>
#include <memory>

#include "aabb2.h"
#include "pointll.h"

namespace aurora {
namespace path {

enum class PathStrategy {
    kTimeFirst = 0,      // 最短时间优先
    kDistanceFirst = 1,  // 最短距离优先
    KHighWayFirst,       // 高速优先
    kAvoidToll,          // 避开收费路段
};

enum class WayPointType {
    kStartPoint,         // 起始点
    kViaPoint,           // 途经点
    kEndPoint            // 终点
};

enum class LandmarkType {
    kEgo,                // 自车位置
    kClick,              // 地图点击选点
    kPoi                 // POI选点
};

enum class DateTimeType {
    kNoTime,             // 无时间
    kCurrent,            // 当前时间
    kDepartAt,           // 出发时间
    kArriveBy            // 到达时间
};

struct DateTimeOption {
    DateTimeType type = DateTimeType::kCurrent; // 日期时间类型
    std::string value;                          // 日期时间值（ISO 8601格式：YYYY-MM-DDThh:mm）
};

enum class PathTrigger {
    kInitialRouting,      // 初次算路
    kReRouting,           // 偏航重规划
    kRouteRefresh         // 路线刷新
};

enum class PathMode {
    kOffline,           // 离线
    kOnline,            // 在线
    kAuto               // 自动选择
};

struct EdgeInfo {
    uint64_t tile_id;                    // route tile id
    uint32_t id;                         // 边的tile内id
    
    uint64_t forward : 1;               // 边信息是正向还是反向

    // 基础link属性
    uint8_t function_class : 3;          // 功能等级
    uint8_t link_type : 2;               // 链接类型
    uint8_t direction : 2;               // 方向
    uint8_t need_toll : 1;               // 是否收费

    uint32_t start_node_id : 24;         // 起始节点ID
    uint32_t positive_speed_limit : 5;   // 正向速度限制: 0：未定义，1~24表示5~120km/h
    uint32_t is_overhead : 1;            // 是否高架
    uint32_t is_inner_link : 1;          // 是否内部链接
    uint32_t is_separate : 1;            // 是否分隔

    uint32_t end_node_id : 24;           // 终止节点ID
    uint32_t negtive_speed_limit : 5;    // 负向速度限制: 0：未定义，1~24表示5~120km/h
    uint8_t is_area_link : 1;            // 是否区域链接
    uint8_t is_city_link : 1;            // 是否城市链接
    uint8_t is_ramp : 1;                 // 是否匝道

    uint8_t link_form : 5;               // link form
    uint8_t speed_grade : 3;             // 速度等级

    uint16_t length;                     // 长度

    uint8_t forward_lane_count : 2;      // 正向车道数: 0：1车道，1：2车道，2：3车道，3：>=4车道
    uint8_t backward_lane_count : 2;     // 反向车道数: 0：1车道，1：2车道，2：3车道，3：>=4车道
    uint8_t lane_count : 4;              // 总车道数: 0~15表示1~16个车道

    uint8_t road_class : 4;              // 道路等级
    uint8_t is_left : 1;                 // 是否左侧
    uint8_t has_turn_rule : 1;           // 是否有转弯规则
    uint8_t is_time_limit : 1;           // 是否有时间限制
    uint8_t is_all_day_limit : 1;        // 是否全天限制

    uint8_t is_building : 1;             // 是否建筑物
    uint8_t is_paved : 1;                // 是否铺装
    uint8_t is_gate : 1;                 // 是否门/闸
    uint8_t no_crossing : 1;             // 是否禁止穿越
    uint8_t is_private : 1;              // 是否私有

    std::vector<PointLL> geos;           // 几何形状
};
using EdgeInfoPtr = std::shared_ptr<EdgeInfo>;

struct Candidate {
    double heading;      // 朝向角，从正北顺时针计算（弧度rad）
    EdgeInfoPtr link;    // 道路信息
    double offset;       // 沿通行方向道路偏移量, 单位m
    double proj_dis;     // 到道路投影点的距离
    uint64_t proj_index; // 投影segment的起始index
    PointLL proj_pt;     // 道路投影点
    double confidence;   // 匹配置信度
};

struct PathLandmark {
    bool valid;                        // 是否填充有效坐标
    WayPointType waypoint_type;        // 起点/途经点/终点
    LandmarkType landmark_type;        // 地标类型
    PointLL pt;                        // 需要匹配的经纬度坐标
    std::string name;                  // 地标名称
    std::vector<Candidate> candidates; // 候选匹配结果
};
using PathLandmarkPtr = std::shared_ptr<PathLandmark>;

struct RestrictionOption {
    std::vector<uint64_t> edges;
    std::vector<uint64_t> tile_ids;
    std::vector<PointLL> polygon;
};
using RestrictionOptionPtr = std::shared_ptr<RestrictionOption>;

struct PathQuery {
    PathStrategy strategy;                     // 路径规划策略
    DateTimeOption date_option;                // 日期时间选项
    PathTrigger trigger;                       // 算路触发类型
    PathMode mode;                             // 算路模式：在线、离线
    RestrictionOption restrict_option;         // 限行选项

    std::vector<PathLandmarkPtr> path_points;    // 起点/途经点/终点
};
using PathQueryPtr = std::shared_ptr<PathQuery>;

struct ResultMetadata {
    std::string build_version;     // 构建版本
    std::string data_version;      // 数据版本
    uint64_t query_time_ms;        // 算路时间（毫秒）
};


struct Section {
    uint32_t index;        // 对应links的起始索引
    uint32_t num;          // 两个算路点之间的link数量
    double length;         // 长度，单位m
    double time;           // 时间，单位s
    double start_offset;   // 沿着通行方向从link开始节点的偏移量，单位m
    double end_offset;     // 沿着通行方向的结束偏移量，单位m
};

struct PathInfo {
    uint64_t path_id;                // 路径唯一标识符
    double length;                   // 路径总长度，单位m
    double travel_time;              // 时间花费，单位s
    uint32_t traffic_light_num;      // 途经红绿灯数量
    std::vector<Section> sections;   // 路径section列表
    std::vector<EdgeInfoPtr> links;  // 路径link列表
    std::vector<PointLL> points;     // WGS84坐标系下的路径点列表
    AABB2<PointLL> bbox;             // MBR
};

struct PathResult {
    std::string uuid;              // 结果唯一标识符，与算路请求对应
    std::string status;            // 状态信息
    std::string tag;               // 路线标签：如 高速最多，限速多等
    int32_t code;                  // 结果状态码
    ResultMetadata metadata;       // 元数据信息
    std::vector<PathInfo> paths;   // 规划路径结果列表
};
using PathResultPtr = std::shared_ptr<PathResult>;

}  // namespace path
}  // namespace aurora

#endif  // AURORA_PATH_INCLUDE_PATH_DEF_H_