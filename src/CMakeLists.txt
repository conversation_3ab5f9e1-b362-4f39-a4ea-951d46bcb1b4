include_directories(${CMAKE_CURRENT_SOURCE_DIR})

include_directories(base/include)
include_directories(data_provider/include)
include_directories(guidance/include)
include_directories(path/include)
include_directories(location/include)
include_directories(search/include)

add_subdirectory(base)
add_subdirectory(data_provider)
add_subdirectory(guidance)
add_subdirectory(map)
add_subdirectory(search)
add_subdirectory(path)
add_subdirectory(location)
