#ifndef MAP_SRC_BASE_INCLUDE_ERRORCODE_H
#define MAP_SRC_BASE_INCLUDE_ERRORCODE_H

namespace aurora {

enum ErrorCode : int32_t {
    //=======================================================
    //  common error code
    //=======================================================    
    kErrorCodeFailed = -1,            // 失败
    kErrorCodeOk = 0,                 // 成功
    kErrorCodePending,                // 
    kErrorCodeNotSupport,             // not supported
    kErrorCodeDoing,                  // 处理中
    kErrorCodeNotInit,                // 未初始化
    kErrorCodeInvalidParam,           // 无效参数
    kErrorCodeParamNullPointer,       // 参数空指针
    kErrorCodeNetworkUnavailable,     // 网络不可用
    kErrorCodeNetworkTimeout,         // 网络超时
    kErrorCodeDIrectoryNotExist,      // 目录不存在
    kErrorCodeFileNotExist,           // 文件不存在
    kErrorCodeMemAllocError,          // 内存分配失败
    kErrorCodeCallSeqenceError,       // 


    //=======================================================
    //  data provider error code
    //======================================================= 
    kErrorCodeFileOpStart = 0x0200,  
    kErrorCodeFileOpen,
    kErrorCodeFileRead,
    kErrorCodeFileSeek,
    kErrorCodeFileOpEnd = kErrorCodeFileOpStart + 0x0100,

    //=======================================================
    //  data provider error code
    //=======================================================    
    kErrorCodeDataProviderStart = 0x0300,
    kErrorCodeDataProviderEnd = kErrorCodeDataProviderStart + 0x0100,


    //=======================================================
    //  map error code
    //=======================================================    
    kErrorCodeRenderStart,
    kErrorCodeRenderEnd = kErrorCodeRenderStart + 0x0100,

    
    //=======================================================
    //  search error code
    //=======================================================    
    kErrorCodeSearchStart,
    kErrorCodeSearchEnd = kErrorCodeSearchStart + 0x0100, 


    //=======================================================
    //  dr error code
    //=======================================================
    kErrorCodeDRStart,
    kErrorCodeDREnd = kErrorCodeDRStart + 0x0100,


    //=======================================================
    //  map matching error code
    //=======================================================
    kErrorCodeMMStart,
    kErrorCodeMMEnd = kErrorCodeMMStart + 0x0100,


    //=======================================================
    //  path error code
    //=======================================================
    kErrorCodePathStart,
    kErrorCodePathStartPointError, // 起点不在有效范围
    kErrorCodePathViaPointError,   // 途径地不在有效范围
    kErrorCodePathEndPointError,   // 终点不在有效发范围
    kErrorCodePathStartNoRoad,     // 起点不在Road上
    kErrorCodePathViaNoRoad,       // 途径地不在Road上
    kErrorCodePathEndNoRoad,       // 终点不在Road上
    kErrorCodePathOnlineError,     // 在线算路失败
    kErrorCodePathOfflineError,    // 离线算路失败
    kErrorCodePathUserCalcel,      // 用户取消算路
    kErrorCodePathTooFar,          // 路线太远
    kErrorCodePathLackStartData,   // 起点范围缺少数据
    kErrorCodePathLackWayCityData, // 途径地缺少数据
    kErrorCodePathLackEndCityData, // 终点缺少数据
    KErrorCodePathViaPointTooMuch, // 途径地数目过多
    KErrorCodePathFormPath,        // 路线生成错误
    kErrorCodePathEnd = kErrorCodePathStart + 0x0100,

    //=======================================================
    //  guide error code
    //=======================================================
    kErrorCodeGuideStart,
    kErrorCodeGuideMissLocationIF,
    kErrorCodeGuideMissPathIF,
    kErrorCodeGuideContinueInvalidError,
    kErrorCodeGuidePathIdNotExist,
    kErrorCodeGuideNavigationMissPath,
    kErrorCodeGuideCruiseExistPath,
    kErrorCodeGuideListenerNotFound,
    kErrorCodeGuideBuildManeuverError,
    kErrorCodeGuideCreateNarrativeError,
    kErrorCodeGuideEnd = kErrorCodeGuideStart + 0x100,

    //=======================================================
    //  traffic error code
    //=======================================================
    kErrorCodeTrafficStart,
    kErrorCodeTrafficEnd
};

}  // namespace aurora 
#endif  // MAP_SRC_BASE_INCLUDE_ERRORCODE_H
/* EOF */
