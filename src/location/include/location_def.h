#ifndef MAP_SRC_LOCATION_INCLUDE_LOCATION_DEF_H
#define MAP_SRC_LOCATION_INCLUDE_LOCATION_DEF_H

#include <cstdint>
#include <string>
#include <vector>

#include "base/include/pointll.h"
#include "data_provider/include/route_data/feature/topol_edge.h"

namespace aurora {
namespace loc {

/**
 * @brief Enumeration for GNSS positioning modes
 */
enum class GnssMode {
  kNotFix = 0,  ///< No position fix
  kTowD = 1,    ///< 2D position fix
  kThreeD = 2   ///< 3D position fix
};

/**
 * @brief GNSS position information structure
 */
struct GnssPosInfo {
  uint64_t timestamp;   ///< Timestamp in milliseconds since Unix epoch (Jan 1, 1970)
  GnssMode mode;   ///< GNSS positioning mode
  PointLL lnglat;  ///< Longitude/Latitude coordinates
  double speed;    ///< Speed in m/s (negative values indicate reverse movement)
  double heading;  ///< Heading in degrees [0,360), 0 is true north, clockwise
  double pitch;    ///< Pitch angle [-90,90], positive for uphill, negative for
                   ///< downhill
  double altitude;  ///< Altitude in meters
  int num_sats;     ///< Number of visible satellites
  int num_used;     ///< Number of satellites used in position fix

  // PointLL   origin_pos;          ///< Original GPS position (for GPS track)
  // bool      is_dr_result;        ///< Whether this is a DR (Dead Reckoning)
  // result float     confidence;          ///< Confidence score (normalized
  // from satellite count,
  //                                ///< DR and GPS information interval, etc.)
};

/**
 * @brief Vehicle position and movement information
 */
struct CarPosInfo {
  double speed;           ///< Vehicle speed in m/s
  double heading;         ///< Vehicle heading in degrees [0,360)
  double pitch;           ///< Vehicle pitch angle [-90,90]
  double accel;           ///< Vehicle acceleration in m/s²
  double steering_angle;  ///< Steering wheel angle in degrees
};

/**
 * @brief Parallel road type enumeration
 *
 * Defines various types of parallel roads in navigation system,
 * such as main roads, auxiliary roads, elevated upper/lower roads, etc.
 */
enum class ParallelRoadType {
  kNonParallel = 0,         ///< Non-parallel road
  kMainRoad = 1 << 0,       ///< Main road
  kAuxiliaryRoad = 1 << 1,  ///< Auxiliary road
  kElevatedUpper = 1 << 2,  ///< Upper elevated road
  kElevatedLower = 1 << 3,  ///< Lower elevated road
  kUnknown = 1 << 4         ///< Unknown type
};

/**
 * @brief Road matching candidate point
 *
 * Represents a potential match point when projecting vehicle position onto road
 * network
 */
struct MatchCandidate {
  PointLL proj_pos;  ///< Projected point coordinates

  uint64_t tile_id;  ///< Tile ID where the match occurs
  uint32_t link_id;  ///< Link ID
  uint8_t dir;       ///< Direction (0: forward, 1: reverse)
  double offset;     ///< Offset along link in meters

  // uint8_t road_status;  ///< Road status (0: normal, 1: under construction,
  // 2: closed)
  aurora::parser::TopolEdgeBase topo_edge;  ///< Base link information structure

  uint64_t path_id;       ///< Path ID containing this link
  int32_t path_link_idx;  ///< Index of this link in the path

  ParallelRoadType nearby_road_type;  ///< Type of nearby parallel road (can be
                                      ///< selected via bit operations)

  float confidence;  ///< Matching confidence score [0-1]

};


/**
 * @brief Road matching result structure
 */
struct MatchResult {
  GnssPosInfo origin_pos;  ///< Original GNSS position information

  MatchCandidate road_match_info;  ///< Road matching information

  CarPosInfo car_pos_info;  ///< Vehicle position and movement data

  uint64_t timestamp;  ///< Timestamp in milliseconds

  bool on_road ;  ///< Whether vehicle is on road (1: on road, 0: off road)
  // bool on_route : 1;            ///< Whether vehicle is on planned route
  bool reroute;  ///< Whether rerouting is needed (1: yes, 0: no)

};
}  // namespace loc
}  // namespace aurora

#endif  // MAP_SRC_LOCATION_INCLUDE_LOCATION_DEF_H
/* EOF */
