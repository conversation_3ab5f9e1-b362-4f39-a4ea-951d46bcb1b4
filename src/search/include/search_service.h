#ifndef SEARCH_INCLUDE_SEARCH_SERVICE_H
#define SEARCH_INCLUDE_SEARCH_SERVICE_H
#include <memory>

#include "search_def.h"
namespace aurora::search {

class SearchManager;
/**
 * @brief Main interface for map search services.
 * @details Provides access to various geospatial search operations including
 * text search, place details, autocomplete, structured queries, and reverse
 * geocoding.
 */
class SearchService final {
 public:
  SearchService();
  ~SearchService();
  /**
   * @brief Initializes the search service.
   * @param config Path to the search service configuration file
   * @return TaskId positive value indicates success, negative value indicates
   * failure
   */
  int Init(const std::string &config);

  /**
   * @brief Destroys the search service and releases resources.
   * @return TaskId positive value indicates success, negative value indicates
   * failure
   */
  int Destroy();

  /**
   * @brief Performs text-based search for points of interest.
   * @param request Search criteria including query text and optional filters
   * @param handler Callback handler to process search results
   * @return TaskId positive value indicates success, negative value indicates
   * failure
   */
  int SearchByText(const SearchByTextRequestPtr &request,
                   const SearchByTextResponseHandlerPtr &handler) const;

  /**
   * @brief Retrieves detailed information for a specific place.
   * @param request Request containing place identifier
   * @param handler Callback handler to receive detailed place data
   * @return TaskId positive value indicates success, negative value indicates
   * failure
   *
   * When the request contains multiple place IDs, the service will try to
   * return details for all specified places.
   * If any place ID is invalid or not found, the service will return
   * just the valid ones.
   */
  int GetDetail(const GetDetailRequestPtr &request,
                const GetDetailResponseHandlerPtr &handler) const;

  /**
   * @brief Provides autocomplete suggestions for search queries.
   * @param request Partial query text and optional parameters
   * @param handler Callback handler to process autocomplete results
   * @return TaskId positive value indicates success, negative value indicates
   * failure
   */
  int Autocomplete(const AutocompleteRequestPtr &request,
                   const AutocompleteResponseHandlerPtr &handler) const;

  /**
   * @brief Performs reverse geocoding to find nearby places.
   * @param request Geographic coordinates and search parameters
   * @param handler Callback handler to receive nearby place information
   * @return TaskId positive value indicates success, negative value indicates
   * failure
   */
  int ReverseGeocode(const ReverseGeocodeRequestPtr &request,
                     const ReverseGeocodeResponseHandlerPtr &handler) const;

 private:
  std::shared_ptr<SearchManager> search_manager_;
  bool CheckEnv(const SearchRequestBasePtr &request, const ResponseHandlerBasePtr &handler) const;
};

}  // namespace aurora::search

#endif  // SEARCH_INCLUDE_SEARCH_SERVICE_H
