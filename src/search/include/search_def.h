#ifndef SEARCH_INCLUDE_SEARCH_DEF_H
#define SEARCH_INCLUDE_SEARCH_DEF_H

#include <pointll.h>

#include <memory>
#include <string>
#include <vector>

namespace aurora::search {
/**
 * @brief Contains basic information about a place.
 */
struct PlaceBrief {
  /**
   * @brief Unique identifier for the place.
   */
  std::string id;

  /**
   * @brief Geographic coordinates of the place.
   */
  PointLL location;

  /**
   * @brief name of the place.
   */
  std::string name;

  /**
   * @brief Physical address of the place.
   */
  std::string address;

  /**
   * @brief Category of the place.
   */
  std::string category;
};

/**
 * @brief Contains detailed information about a place.
 */
struct PlaceDetail : PlaceBrief {
  /**
   * @brief URL or path to a representative photo of the place.
   */
  std::string photo;

  /**
   * @brief The administrative area where the place is located.
   */
  std::string admin_code;
};

/**
 * @brief Abstract base geographic boundaries.
 */
class Bounds {
 public:
  virtual ~Bounds() = default;
};
using BoundsPtr = std::shared_ptr<Bounds>;

/**
 * @brief Creates a circular geographic boundary.
 * @param center Center point of the circular area
 * @param radius Radius of the circle in meters
 * @return Created circular bounds object
 */
BoundsPtr CreateCircularBounds(const PointLL& center, double radius);

/**
 * @brief Creates a rectangular geographic boundary.
 * @param north_east Northeast corner of the rectangular area
 * @param south_west Southwest corner of the rectangular area
 * @return Created rectangular bounds object
 */
BoundsPtr CreateRectangularBounds(const PointLL& north_east, const PointLL& south_west);

/**
 * @brief Creates a polyline-based geographic boundary.
 * @param points List of coordinates defining the polyline path
 * @param width Width of the boundary area around the polyline in meters
 * @return Created polyline bounds object
 */
BoundsPtr CreatePolylineBounds(const std::vector<PointLL>& points, double width);

enum class SearchMode {
  kOnline,           /**< Search using only the online engine */
  kOffline,          /**< Search using only the offline engine */
  kOnlinePreferred,  /**< Prefer online; use offline if online times out */
  kOfflinePreferred, /**< Prefer offline; use online if offline unavailable */
};

struct SearchRequestBase {
  SearchMode search_mode;
  /**
   * @brief User current location coordinates
   */
  std::shared_ptr<PointLL> current_location;

  virtual ~SearchRequestBase() = default;
};
using SearchRequestBasePtr = std::shared_ptr<SearchRequestBase>;

enum class RankPreference {
  kRelevance, /**< Prioritize results by relevance score */
  kDistance,  /**< Prioritize results by distance to current location */
};

/**
 * @brief Request structure for text-based search operations.
 */
struct SearchByTextRequest final : SearchRequestBase {
  /**
   * @brief User-provided search query text
   */
  std::string query;

  /**
   * @brief Geographical bounding box restriction for search area
   */
  BoundsPtr location_restriction;

  /**
   * @brief List of categories to include in search results
   */
  std::vector<std::string> included_categories;

  /**
   * @brief List of categories to include in search results
   */
  std::vector<std::string> excluded_categories;

  /**
   * @brief Ranking preference
   */
  RankPreference rank_preference;

  /**
   * @brief Maximum number of results to return (default 10)
   */
  int page_size = 10;

  /**
   * @brief 0-based
   */
  int page_num = 0;
};
using SearchByTextRequestPtr = std::shared_ptr<SearchByTextRequest>;

/**
 * @brief Request structure for retrieving detailed place information.
 */
struct GetDetailRequest final : SearchRequestBase {
  /**
   * @brief Unique identifiers of the place to retrieve details for
   */
  std::vector<std::string> place_ids;
};
using GetDetailRequestPtr = std::shared_ptr<GetDetailRequest>;

/**
 * @brief Request structure for autocomplete suggestions.
 */
struct AutocompleteRequest final : SearchRequestBase {
  /**
   * @brief Unique session identifier for request tracking
   */
  std::string query;

  std::shared_ptr<PointLL> location_bias;

  /**
   * @brief Maximum number of autocomplete results to return (default 10)
   */
  int max_results = 10;
};
using AutocompleteRequestPtr = std::shared_ptr<AutocompleteRequest>;

/**
 * @brief Request structure for reverse geocoding operations.
 */
struct ReverseGeocodeRequest final : SearchRequestBase {
  /**
   * @brief Geographic coordinates to convert to human-readable address
   */
  PointLL location;
};
using ReverseGeocodeRequestPtr = std::shared_ptr<ReverseGeocodeRequest>;

/**
 * @brief Base structure for Response
 */
struct SearchResponseBase {
  /**
   * @brief TaskId positive value indicates success, negative value indicates
   */
  int task_id;

  virtual ~SearchResponseBase() = default;
};

/**
 * @brief Response structure for text-based search operations.
 */
struct SearchByTextResponse final : SearchResponseBase {
  /**
   * @brief List of place brief information matching the search text
   */
  std::vector<PlaceBrief> place_briefs;
};
using SearchByTextResponsePtr = std::shared_ptr<SearchByTextResponse>;

/**
 * @brief Response structure for place detail requests.
 */
struct GetDetailResponse final : SearchResponseBase {
  /** Detailed information of the requested place */
  std::vector<PlaceDetail> place_details;
};
using GetDetailResponsePtr = std::shared_ptr<GetDetailResponse>;

/**
 * @brief Response structure for autocomplete suggestions.
 */
struct AutocompleteResponse final : SearchResponseBase {
  /** List of place brief information for autocomplete suggestions */
  std::vector<PlaceBrief> place_briefs;
};
using AutocompleteResponsePtr = std::shared_ptr<AutocompleteResponse>;

/**
 * @brief Response structure for reverse geocoding operations.
 */
struct ReverseGeocodeResponse final : SearchResponseBase {
  /**
   * @brief List of place brief information near the specified geographic
   * coordinates
   */
  std::vector<PlaceBrief> place_briefs;
};
using ReverseGeocodeResponsePtr = std::shared_ptr<ReverseGeocodeResponse>;

/**
 * @brief Base class for handling search API responses.
 */
class ResponseHandlerBase {
 public:
  virtual ~ResponseHandlerBase() = default;

  /**
   * @brief Called when an API request fails.
   * @param error_code Numerical error code
   * @param error_message Human-readable error description
   */
  virtual void OnFailure(int error_code, const std::string& error_message) = 0;

  /**
   * @brief Called when an API request is cancelled.
   */
  virtual void OnCancel() = 0;
};
using ResponseHandlerBasePtr = std::shared_ptr<ResponseHandlerBase>;

class SearchByTextResponseHandler : public ResponseHandlerBase {
 public:
  /**
   * @brief Called when an API request completes successfully.
   * @param response The response data object
   */
  virtual void OnSuccess(const SearchByTextResponsePtr& response) = 0;
};
using SearchByTextResponseHandlerPtr = std::shared_ptr<SearchByTextResponseHandler>;

class GetDetailResponseHandler : public ResponseHandlerBase {
 public:
  /**
   * @brief Called when an API request completes successfully.
   * @param response The response data object
   */
  virtual void OnSuccess(const GetDetailResponsePtr& response) = 0;
};
using GetDetailResponseHandlerPtr = std::shared_ptr<GetDetailResponseHandler>;

class AutocompleteResponseHandler : public ResponseHandlerBase {
 public:
  /**
   * @brief Called when an API request completes successfully.
   * @param response The response data object
   */
  virtual void OnSuccess(const AutocompleteResponsePtr& response) = 0;
};
using AutocompleteResponseHandlerPtr = std::shared_ptr<AutocompleteResponseHandler>;

class ReverseGeocodeResponseHandler : public ResponseHandlerBase {
 public:
  /**
   * @brief Called when an API request completes successfully.
   * @param response The response data object
   */
  virtual void OnSuccess(const ReverseGeocodeResponsePtr& response) = 0;
};
using ReverseGeocodeResponseHandlerPtr = std::shared_ptr<ReverseGeocodeResponseHandler>;

}  // namespace aurora::search

#endif  // SEARCH_INCLUDE_SEARCH_DEF_H
