#include <logger.h>
#include <utils/data_service_wrapper.h>

#include <bitset>

#include "analysis/word_segmenter.h"
#include "search_offline_engine.h"
#include "src/common/search_config.h"
#include "utils/geohash.h"
#include "utils/string_utils.h"

using namespace aurora::search;

void TestGetDetail(const std::string& place_id) {
  const auto manager = std::make_shared<SearchOfflineEngine>();
  auto config = std::make_shared<SearchConfig>("../data/search");
  manager->Init(config);
  const auto request = std::make_shared<GetDetailRequest>();
  request->place_ids.push_back(place_id);
  const auto response = std::make_shared<GetDetailResponse>();
  if (!manager->GetDetail(request, response)) {
    LOG_ERROR("No place: {}", place_id);
  }
  auto& place = response->place_details[0];
  LOG_INFO("id: {}, name: {}, longitude: {}, latitude: {}, address: {}", place.id, place.name,
           place.location.lng(), place.location.lat(), place.address);
}
void TestGeohash() {
  double lng = 114.297822;
  double lat = 22.689199;
  double radius = 10000;
  Mbr mbr = GenerateMbr(lng, lat, radius);
  LOG_INFO("{}, {}: {}, {}", mbr.minx(), mbr.miny(), mbr.maxx(), mbr.maxy());
  std::vector<std::string> geohashes;
  GeoHashForBoundLongitudeLatitude(mbr, &geohashes);
  for (auto& geohash : geohashes) {
    LOG_INFO(geohash);
  }
  std::string val;
  GeoHashEncode(lng, lat, 5, &val);
  LOG_INFO("{}, {}, {}, {}, {}", lng, lat, radius, geohashes.size(), val);
}

uint32_t BitMakeUp(const int idx, const int bit_number) {
  uint32_t res = 0;
  for (int i = bit_number - 1; i >= 0; --i) {
    res = (res << 2) + ((idx >> i) & 1);
  }

  return res;
}

void TestBitMakeUp() {
  int bit_number = 3;
  int total = 1 << bit_number;
  for (int i = 0; i < total; ++i) {
    uint32_t suffix = BitMakeUp(i, bit_number);
    auto binary = std::bitset<6>(suffix);
    LOG_INFO("{} -> {}", std::bitset<3>(i).to_string(), binary.to_string());
  }
}

void TestSearchByText() {
  const auto engine = std::make_shared<SearchOfflineEngine>();
  auto config = std::make_shared<SearchConfig>("../data/search");
  DataService::Instance().Init(config);
  config->Load();
  engine->Init(config);

  const auto request = std::make_shared<SearchByTextRequest>();

  request->page_size = 10;
  request->query = "天安门";
  // request->current_location =
  // std::make_shared<aurora::PointLL>(113.819147, 22.675354);
  request->location_restriction = CreateCircularBounds({113.819147, 22.675354}, 10000);
  const auto response = std::make_shared<SearchByTextResponse>();
  engine->SearchByText(request, response);
  for (auto& [id, location, name, address, category] : response->place_briefs) {
    LOG_DEBUG("id: {}, name: {}, address: {}, category: {}, location: {}", id, name, address,
              category, location.ToString());
  }
}

void TestWordSegment(const std::vector<std::string>& texts) {
  WordSegmenter word_segmenter;
  word_segmenter.Init("/home/<USER>/Codes/map_engine/distribution/data/search/nlp/cppjieba");
  for (auto& text : texts) {
    std::vector<std::string> words;
    word_segmenter.Segment(text, &words);
  }
}

struct TestNode {
  std::vector<void*> vec;
};

void TestVector() {
  TestNode node;
  LOG_INFO("Node size = {}, vec {}, vec size = {}, vec capacity = {}", sizeof(node.vec),
           sizeof(node), node.vec.size(), node.vec.capacity());
  for (int i = 0; i < 10; ++i) {
    node.vec.push_back(nullptr);
    LOG_INFO("{}: Node size = {}, vec size = {}, vec capacity = {}", i + 1, sizeof(node),
             node.vec.size(), node.vec.capacity());
  }
}

int main() {
  aurora::logger::get().set_level(spdlog::level::debug);
  TestSearchByText();
  // TestVector();
  // TestGeohash();
  // TestBitMakeUp();
  // TestWordSegment({"肯德基(宝龙店)", "肯德基(坪洲百货店)", "肯德基(松柏店)",
  // "肯德基(世贸中心店)", "肯德基(六约餐厅)-洗手间",
  // "肯德基(投资大厦店)", "肯德基(南联DT餐厅)"});

  return EXIT_SUCCESS;
}