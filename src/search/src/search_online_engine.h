#ifndef SEARCH_SRC_SEARCH_ONLINE_ENGINE_H
#define SEARCH_SRC_SEARCH_ONLINE_ENGINE_H
#include <memory>

#include "include/search_def.h"
#include "search_engine.h"

namespace aurora::search {
class SearchOnlineEngine final : public SearchEngine {
 public:
  using Ptr = std::shared_ptr<SearchOnlineEngine>;
  bool SearchByText(const SearchByTextRequestPtr& request,
                    const SearchByTextResponsePtr& response) override;

  bool GetDetail(const GetDetailRequestPtr& request, const GetDetailResponsePtr& response) override;

  bool Autocomplete(const AutocompleteRequestPtr& request,
                          const AutocompleteResponsePtr& response) override;

  bool ReverseGeocode(const ReverseGeocodeRequestPtr& request,
                      const ReverseGeocodeResponsePtr& response) override;

  bool Init(const std::shared_ptr<SearchConfig>& config) override;
};
}  // namespace aurora::search
#endif  // SEARCH_SRC_SEARCH_ONLINE_ENGINE_H