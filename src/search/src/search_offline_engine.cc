#include "search_offline_engine.h"

#include <logger.h>

#include "analysis/query_analyzer.h"
#include "common/parsed_query.h"
#include "common/search_config.h"
#include "data/data_manager.h"
#include "data/place_table/place_table.h"
#include "limonp/StringUtil.hpp"
#include "rank/rank_manager.h"
#include "repository/repository.h"
#include "retrieval/search_by_text_retriever.h"
#include "spdlog/stopwatch.h"
#include "utils/geohash.h"
#include "utils/log_utils.h"

namespace aurora::search {
namespace {
void PrintRankInfo(const std::vector<RankingPlace>& ranking_places,
                   const std::vector<PlaceBrief>& place_briefs) {
  const size_t n = std::min(ranking_places.size(), place_briefs.size());
  for (size_t i = 0; i < n; ++i) {
    auto& ranking_place = ranking_places[i];
    auto& brief = place_briefs[i];
    LOG_INFO("{} | {}", LogUtils::ToString(ranking_place), LogUtils::ToString(brief));
  }
}
}  // namespace
bool SearchOfflineEngine::Init(const std::shared_ptr<SearchConfig>& config) {
  spdlog::stopwatch sw;
  config_ = config;

  query_analyzer_ = std::make_shared<QueryAnalyzer>(config);
  if (!query_analyzer_->Init()) {
    return false;
  }

  data_manager_ = std::make_shared<DataManager>(config);
  if (!data_manager_->Init()) {
    LOG_WARN("Failed to create DataManager");
    return false;
  }

  rank_manager_ = std::make_shared<RankManager>();
  if (!rank_manager_->Init(config)) {
    LOG_WARN("Failed to create RankManager");
    return false;
  }

  repository_ = std::make_shared<Repository>();
  if (!repository_->Init(config)) {
    LOG_ERROR("Failed to create FileRepository");
    return false;
  }
  LOG_INFO("SearchOfflineEngine init success, take {} seconds", sw);
  return true;
}

bool SearchOfflineEngine::SearchByText(const SearchByTextRequestPtr& request,
                                       const SearchByTextResponsePtr& response) {
  spdlog::stopwatch sw;
  if (request == nullptr || response == nullptr) {
    return false;
  }
  UpdateCurrentLocation(request);

  const auto parsed_query = query_analyzer_->Analyze(request);
  if (parsed_query == nullptr) {
    LOG_WARN("Failed to analyze query: {}", request->query);
    return false;
  }
  LOG_INFO("SearchByTextRequest: query: {}, bounds: {}", parsed_query->query,
           parsed_query->location_restriction->ToString());
  auto retrievers = AssembleSearchByTextRetrievers(parsed_query);
  std::vector<RankingPlace> ranking_places;
  for (auto& retriever : retrievers) {
    retriever->RetrievePlaces(parsed_query, &ranking_places);
  }

  if (!rank_manager_->RankPlaces(&ranking_places)) {
    LOG_WARN("Failed to rank places");
    return false;
  }

  PaginatePlaces(request->page_num, request->page_size, &ranking_places);
  auto place_briefs = &response->place_briefs;
  place_briefs->reserve(ranking_places.size());
  for (auto& ranking_place : ranking_places) {
    place_briefs->emplace_back(ranking_place.retriever->GetPlaceBrief(ranking_place.brief_id));
  }

  if (config_->print_rank_info) {
    PrintRankInfo(ranking_places, response->place_briefs);
  }

  LOG_INFO("SearchByText takes: {} seconds", sw);
  return true;
}

bool SearchOfflineEngine::GetDetail(const GetDetailRequestPtr& request,
                                    const GetDetailResponsePtr& response) {
  if (request == nullptr || response == nullptr) {
    return false;
  }
  UpdateCurrentLocation(request);

  std::unordered_map<std::string, DetailDataSource> data_sources;
  for (auto& place_id : request->place_ids) {
    std::vector<std::string> cities = data_manager_->GetPossibleCities(place_id);
    for (auto& city : cities) {
      auto it = data_sources.find(city);
      if (it == data_sources.end()) {
        it = data_sources.emplace(city, data_manager_->GetDetailDataSource(city)).first;
      }
      it->second.place_ids.push_back(place_id);
    }
  }

  std::unordered_map<std::string, PlaceDetail> found_places;
  for (auto& [city, data_source] : data_sources) {
    if (data_source.place_table == nullptr || data_source.place_ids.empty()) {
      continue;
    }
    auto& place_table = data_source.place_table;
    for (auto& place_id : data_source.place_ids) {
      if (found_places.count(place_id) != 0) {
        continue;
      }
      PlaceDetail place_detail;
      if (place_table->GetPlaceDetail(place_id, &place_detail)) {
        found_places[place_id] = std::move(place_detail);
      }
    }
  }

  response->place_details.reserve(found_places.size());
  for (auto& place_id : request->place_ids) {
    auto it = found_places.find(place_id);
    if (it != found_places.end()) {
      response->place_details.emplace_back(it->second);
    }
  }

  return true;
}

bool SearchOfflineEngine::Autocomplete(const AutocompleteRequestPtr& request,
                                       const AutocompleteResponsePtr& response) {
  if (request == nullptr || response == nullptr) {
    return false;
  }
  UpdateCurrentLocation(request);
  return repository_->AutocompleteSearch(request, response);
}

bool SearchOfflineEngine::ReverseGeocode(const ReverseGeocodeRequestPtr& request,
                                         const ReverseGeocodeResponsePtr& response) {
  if (request == nullptr || response == nullptr) {
    return false;
  }
  UpdateCurrentLocation(request);
  auto& location = request->location;

  std::string geo_cell;
  if (GeoHashEncode(location.lng(), location.lat(), kMaxGeohashLength, &geo_cell) < 0) {
    LOG_INFO("Invalid location: {}", request->location.ToString());
    return false;
  }
  auto data_sources = data_manager_->GetReverseGeocodeDataSource(geo_cell);
  std::vector<PlaceDetailReaderPtr> nearby_places;
  for (auto& data_source : data_sources) {
    data_source.place_table->GetGeoHashData(geo_cell, nearby_places);
  }

  std::sort(nearby_places.begin(), nearby_places.end(),
            [&location](const PlaceDetailReaderPtr& lhs, const PlaceDetailReaderPtr& rhs) {
              double left_dis = location.DistanceSquared(lhs->GetLocation());
              double right_dis = location.DistanceSquared(rhs->GetLocation());
              return left_dis < right_dis;
            });
  int len = nearby_places.size() < 10 ? nearby_places.size() : 10;
  for (int i = 0; i < len; i++) {
    PlaceBrief brief;
    brief.id = std::to_string(nearby_places[i]->GetPlaceId());
    brief.location = PointLL(nearby_places[i]->GetLng(), nearby_places[i]->GetLat());
    brief.name = nearby_places[i]->GetName();
    brief.address = nearby_places[i]->GetAddress();
    brief.category = std::to_string(nearby_places[i]->GetCategory());
    response->place_briefs.push_back(brief);
  }
  return true;
}

void SearchOfflineEngine::UpdateCurrentLocation(const SearchRequestBasePtr& request) const {
  if (request->current_location != nullptr) {
    data_manager_->UpdateCurrentCity(*request->current_location);
  }
}

std::vector<SearchByTextRetrieverPtr> SearchOfflineEngine::AssembleSearchByTextRetrievers(
    const std::shared_ptr<ParsedQuery>& parsed_query) {
  std::vector<SearchByTextDataSource> data_sources =
      data_manager_->GetTextSearchDataSource(parsed_query->location_restriction);

  std::vector<SearchByTextRetrieverPtr> retrievers;
  retrievers.reserve(data_sources.size());
  for (auto& data_source : data_sources) {
    retrievers.emplace_back(std::make_shared<SearchByTextRetriever>(
        data_source, rank_manager_, config_->level_one_pruning_threshold,
        config_->level_two_pruning_threshold, DecideMaxRetrievalCount(data_source.city_type)));
  }
  return retrievers;
}

int SearchOfflineEngine::DecideMaxRetrievalCount(CityType city_type) const {
  // reduce max_retrieval_count for non-primary city
  if (city_type == CityType::kNational) {
    return config_->max_retrieval_count / 4;
  }
  if (city_type == CityType::kNormal) {
    return config_->max_retrieval_count / 4;
  }
  return config_->max_retrieval_count;
}

void SearchOfflineEngine::PaginatePlaces(int page_num, int page_size,
                                         std::vector<RankingPlace>* ranking_places) const {
  int start_index = page_num * page_num;
  int end_index = std::min(start_index + page_size, static_cast<int>(ranking_places->size()));
  if (start_index >= ranking_places->size()) {
    return;
  }
  *ranking_places =
      std::vector(ranking_places->begin() + start_index, ranking_places->begin() + end_index);
}

}  // namespace aurora::search