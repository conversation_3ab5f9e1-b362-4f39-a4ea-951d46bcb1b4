#ifndef SEARCH_REPOSITORY_BUILD_CONFIG_DEFINE_H_
#define SEARCH_REPOSITORY_BUILD_CONFIG_DEFINE_H_
#include <memory>
#include <string>
namespace aurora::search {
struct PlaceTableLayout {
  std::string detail_index_dir;

  std::string primary_key_index;
  std::string place_table;

  PlaceTableLayout()
      : detail_index_dir("detail_index"),
        primary_key_index("place_detail_index"),
        place_table("place_detail") {}
};
using PlaceTableLayoutPtr = std::shared_ptr<PlaceTableLayout>;

struct PrefixIndexLayout {
  std::string prefix_index_dir;

  std::string py_hz_file_index;
  std::string place_name_sort_file;
  std::string trie_index_name;
  std::string pinyin_file;
  std::string hanzi_pinyin;

  PrefixIndexLayout()
      : prefix_index_dir("prefix_index"),
        py_hz_file_index("pinyin_hanzi"),
        place_name_sort_file("place_name_sort"),
        trie_index_name("trie_index"),
        pinyin_file("pinyin_file"),
        hanzi_pinyin("hanzi_pinyin") {}
};
using PrefixIndexLayoutPtr = std::shared_ptr<PrefixIndexLayout>;

struct InvertIndexLayout {
  std::string invert_index_dir;

  std::string term_block;
  std::string term_index;
  std::string geo_trie;
  std::string term_data;
  std::string brief_all;

  InvertIndexLayout()
      : invert_index_dir("invert_index"),
        term_block("term_block"),
        term_index("term_index"),
        geo_trie("geo_data"),
        term_data("term_data"),
        brief_all("brief_all") {}
};
using InvertIndexLayoutPtr = std::shared_ptr<InvertIndexLayout>;

struct DataFolderLayout {
  PlaceTableLayoutPtr place_table_layout;
  PrefixIndexLayoutPtr prefix_index_layout;
  InvertIndexLayoutPtr invert_index_layout;

  DataFolderLayout()
      : place_table_layout(std::make_shared<PlaceTableLayout>()),
        prefix_index_layout(std::make_shared<PrefixIndexLayout>()),
        invert_index_layout(std::make_shared<InvertIndexLayout>()) {}
};

}  // namespace aurora::search
#endif
