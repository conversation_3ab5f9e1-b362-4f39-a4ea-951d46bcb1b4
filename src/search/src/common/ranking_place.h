#ifndef SEARCH_SRC_COMMON_RANKING_PLACE_H
#define SEARCH_SRC_COMMON_RANKING_PLACE_H
#include <memory>
#include <utility>

#include "pointll.h"

namespace aurora::search {
class SearchByTextRetriever;
using SearchByTextRetrieverPtr = std::shared_ptr<SearchByTextRetriever>;
struct RankingPlace {
  SearchByTextRetrieverPtr retriever;
  uint32_t brief_id;
  uint32_t category;
  PointLL location;
  double name_tf_idf_score;
  double address_tf_idf_score;
  double distance_score;
  double title_hit_score;

  explicit RankingPlace(SearchByTextRetrieverPtr search_by_text_retriever, uint32_t id,
                        uint32_t cat, double lng, double lat)
      : retriever(std::move(search_by_text_retriever)),
        brief_id(id),
        category(cat),
        location(lng, lat),
        name_tf_idf_score(0.0),
        address_tf_idf_score(0.0),
        distance_score(0.0),
        title_hit_score(0) {}
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_COMMON_RANKING_PLACE_H
