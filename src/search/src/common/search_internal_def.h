#ifndef SEARCH_SRC_COMMON_CONSTANTS_H
#define SEARCH_SRC_COMMON_CONSTANTS_H

#include <aabb2.h>
#include <pointll.h>

#include <memory>

namespace aurora::search {
constexpr double kMinLng = -180.0;
constexpr double kMaxLng = 180.0;
constexpr double kMinLat = -90.0;
constexpr double kMaxLat = 90.0;

constexpr uint32_t kMaxLineLength = 1024;  // 一行数据最大长度
constexpr uint32_t kMaxGeohashLength = 8;

// 1经度或纬度的距离跨度，粗略，单位米
constexpr uint32_t kDistanceSpanPerDegree = 111133;

constexpr uint32_t kInvalidBriefId = std::numeric_limits<uint32_t>::max();

enum class FieldFlagBit {
  kName = 0,
  kAddress,
};

using Mbr = AABB2<PointLL>;

struct TermInfo {
  using Ptr = std::shared_ptr<TermInfo>;
  double weight_name;     // term在name中的权重
  double weight_address;  // term在address中的权重
  uint8_t field_flag;
};

struct BandingBound {
  double *point_array_x;  // x coordinate array
  double *point_array_y;  // y coordinate array
  uint32_t size;          // the size of point_array_x or point_array_y;
  uint32_t width;         // the width of banding
  bool only_right;        // whether only right in banding links

  BandingBound()
      : point_array_x(nullptr), point_array_y(nullptr), size(0), width(0), only_right(false) {}

  BandingBound(double *x, double *y, uint32_t s, uint32_t w)
      : point_array_x(x), point_array_y(y), size(s), width(w), only_right(false) {}
};
}  // namespace aurora::search

#endif  // SEARCH_SRC_COMMON_CONSTANTS_H