#ifndef SEARCH_SRC_COMMON_PARSED_QUERY_H
#define SEARCH_SRC_COMMON_PARSED_QUERY_H

#include <memory>
#include <string>
#include <unordered_set>
#include <vector>

#include "search_def.h"
namespace aurora::search {
class BoundsInternal;
struct Term {
  std::string text;
  std::vector<std::string> synonyms;
  explicit Term(std::string t) : text(std::move(t)) {}
};

struct ParsedQuery {
  std::string query;
  std::vector<Term> terms;
  std::shared_ptr<PointLL> current_location;
  std::shared_ptr<BoundsInternal> location_restriction;
  std::vector<uint32_t> include_categories;
  std::unordered_set<uint32_t> exclude_categories;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_COMMON_PARSED_QUERY_H
