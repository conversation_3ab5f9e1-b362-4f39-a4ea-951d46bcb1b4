#ifndef SEARCH_SRC_SEARCH_ENGINE_H
#define SEARCH_SRC_SEARCH_ENGINE_H

#include "search_def.h"
namespace aurora::search {
struct SearchConfig;

class SearchEngine {
 public:
  virtual ~SearchEngine() = default;
  virtual bool SearchByText(const SearchByTextRequestPtr& request,
                            const SearchByTextResponsePtr& response) = 0;

  virtual bool GetDetail(const GetDetailRequestPtr& request,
                         const GetDetailResponsePtr& response) = 0;

  virtual bool Autocomplete(const AutocompleteRequestPtr& request,
                                  const AutocompleteResponsePtr& response) = 0;

  virtual bool ReverseGeocode(const ReverseGeocodeRequestPtr& request,
                              const ReverseGeocodeResponsePtr& response) = 0;

  virtual bool Init(const std::shared_ptr<SearchConfig>& config) = 0;

 protected:
  std::shared_ptr<SearchConfig> config_;
};

}  // namespace aurora::search

#endif