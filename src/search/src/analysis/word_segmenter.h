#ifndef SEARCH_SRC_ANALYSIS_WORD_SEGMENTER_H
#define SEARCH_SRC_ANALYSIS_WORD_SEGMENTER_H
#include <memory>
#include <vector>
namespace cppjieba {
class Jieba;
}  // namespace cppjieba

namespace aurora::search {
class WordSegmenter {
 public:
  WordSegmenter() = default;
  bool Init(const std::string &dict_path);

  ~WordSegmenter() = default;

  void Segment(const std::string &text, std::vector<std::string> *words) const;

 private:
  std::string Preprocess(const std::string &text) const;
  void Cut(const std::string &sentence, std::vector<std::string> *words) const;
  std::shared_ptr<cppjieba::Jieba> jieba_;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_ANALYSIS_WORD_SEGMENTER_H