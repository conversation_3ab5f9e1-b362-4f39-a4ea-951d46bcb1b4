#include "synonym_dict.h"

#include <fstream>

#include "common/parsed_query.h"
#include "execution/search_task_base.h"
#include "utils/signature.h"
#include "utils/string_utils.h"
namespace aurora::search {
bool SynonymDict::Init(const std::string& dict_path) {
  std::ifstream file(dict_path);
  if (!file.is_open()) {
    return false;
  }

  std::string line;

  while (std::getline(file, line)) {
    // Split the line by tab character
    std::istringstream iss(line);
    std::vector<std::string> synonyms = StringUtils::SplitString(line, ',');

    if (synonyms.empty()) {
      continue;  // Skip empty lines
    }

    uint32_t group_id = synonym_groups_.size();
    for (const auto& word : synonyms) {
      uint64_t sign;
      if (Create64BitSignature(word.c_str(), &sign)) {
        synonym_index_[sign] = group_id;
      }
    }
    // Add the synonym group to synonym_data_
    synonym_groups_.emplace_back(std::move(synonyms));
  }

  file.close();
  return true;
}
void SynonymDict::AddSynonyms(Term* term) const {
  uint64_t sign;
  if (!Create64BitSignature(term->text.c_str(), &sign)) {
    return;
  }

  if (synonym_index_.count(sign) != 0) {
    auto& group = synonym_groups_[synonym_index_.at(sign)];
    term->synonyms.push_back(term->text);
    for (auto& word : group) {
      if (word != term->text) {
        term->synonyms.push_back(word);
      }
    }
  }
}
}  // namespace aurora::search
