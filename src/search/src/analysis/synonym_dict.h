#ifndef SEARCH_SRC_ANALYSIS_SYNONYM_DICT_H
#define SEARCH_SRC_ANALYSIS_SYNONYM_DICT_H
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

namespace aurora::search {
struct Term;
class SynonymDict {
 public:
  using Ptr = std::shared_ptr<SynonymDict>;
  bool Init(const std::string& dict_path);
  void AddSynonyms(Term* term) const;

 private:
  std::unordered_map<uint64_t, uint32_t> synonym_index_;
  std::vector<std::vector<std::string>> synonym_groups_;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_ANALYSIS_SYNONYM_DICT_H
