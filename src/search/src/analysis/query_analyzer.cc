#include "query_analyzer.h"

#include <logger.h>

#include "common/bounds.h"
#include "common/parsed_query.h"
#include "common/search_config.h"
#include "synonym_dict.h"
#include "utils/data_operation.h"
#include "utils/file_utils.h"
#include "word_segmenter.h"

namespace aurora::search {
bool QueryAnalyzer::Init() {
  word_segmenter_ = std::make_shared<WordSegmenter>();
  if (!word_segmenter_->Init(config_->jieba_dir)) {
    LOG_ERROR("Failed to initialize word segmenter");
    return false;
  }
  LOG_INFO("Initialized word segmenter");

  synonym_dict_ = std::make_shared<SynonymDict>();
  if (!synonym_dict_->Init(config_->synonym_dict)) {
    LOG_ERROR("Failed to initialize word segmenter");
    return false;
  }
  LOG_INFO("Initialized word segmenter");

  return true;
}

QueryAnalyzer::QueryAnalyzer(std::shared_ptr<SearchConfig> config) : config_(std::move(config)) {}
std::shared_ptr<ParsedQuery> QueryAnalyzer::Analyze(const SearchByTextRequestPtr &request) const {
  auto parsed_query = std::make_shared<ParsedQuery>();
  parsed_query->query = request->query;

  std::vector<std::string> raw_terms;
  word_segmenter_->Segment(request->query, &raw_terms);
  for (auto &raw_term : raw_terms) {
    parsed_query->terms.emplace_back(std::move(raw_term));
  }
  for (auto &term : parsed_query->terms) {
    synonym_dict_->AddSynonyms(&term);
  }

  parsed_query->current_location = request->current_location;
  if (request->location_restriction == nullptr) {
    if (request->current_location != nullptr) {
      auto bounds = std::make_shared<InfiniteBounds>(*request->current_location);
      parsed_query->location_restriction = bounds;
      LOG_INFO("No bounds provided, using current location as center: {}", bounds->ToString());
    } else {
      auto bounds = std::make_shared<EmptyBounds>();
      parsed_query->location_restriction = bounds;
      LOG_INFO("No bounds provided, using {}", bounds->ToString());
    }
  } else {
    parsed_query->location_restriction =
        std::dynamic_pointer_cast<BoundsInternal>(request->location_restriction);
  }

  for (auto &category : request->included_categories) {
    parsed_query->include_categories.push_back(DataOperation::ToInternalCategory(category));
  }

  for (auto &category : request->excluded_categories) {
    parsed_query->exclude_categories.insert(DataOperation::ToInternalCategory(category));
  }
  return parsed_query;
}

}  // namespace aurora::search
