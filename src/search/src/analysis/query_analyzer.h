#ifndef SEARCH_SRC_ANALYSIS_QUERY_ANALYZER_H
#define SEARCH_SRC_ANALYSIS_QUERY_ANALYZER_H
#include <memory>

#include "search_def.h"

namespace aurora::search {
struct ParsedQuery;
struct SearchConfig;
class SynonymDict;
class WordSegmenter;

class QueryAnalyzer {
 public:
  explicit QueryAnalyzer(std::shared_ptr<SearchConfig> config);
  bool Init();

  std::shared_ptr<ParsedQuery> Analyze(const SearchByTextRequestPtr& request) const;

 private:
  std::shared_ptr<SearchConfig> config_;
  std::shared_ptr<WordSegmenter> word_segmenter_;
  std::shared_ptr<SynonymDict> synonym_dict_;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_ANALYSIS_QUERY_ANALYZER_H
