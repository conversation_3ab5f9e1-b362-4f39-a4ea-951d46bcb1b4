#include "word_segmenter.h"

#include <logger.h>

#include <cppjieba/Jieba.hpp>

#include "utils/string_utils.h"

namespace aurora::search {
namespace {
void LeftTrim(std::string &s) {
  s.erase(s.begin(),
          std::find_if(s.begin(), s.end(), [](unsigned char ch) { return !std::isspace(ch); }));
}

void RightTrim(std::string &s) {
  s.erase(
      std::find_if(s.rbegin(), s.rend(), [](unsigned char ch) { return !std::isspace(ch); }).base(),
      s.end());
}
void TrimInplace(std::string &s) {
  LeftTrim(s);
  RightTrim(s);
}

void ExtractBracket(const std::string &input_string, std::vector<std::string> *no_bracket_parts) {
  std::size_t start_pos = 0;
  std::vector<std::pair<size_t, size_t>> bracket_pairs;
  while (true) {
    size_t left_pos = input_string.find('(', start_pos);
    if (left_pos == std::string::npos) break;

    size_t right_pos = input_string.find(')', left_pos);
    if (right_pos == std::string::npos) break;

    bracket_pairs.emplace_back(left_pos, right_pos);
    start_pos = right_pos;
  }

  std::string bracket_removed = input_string;
  for (auto iter = bracket_pairs.rbegin(); iter != bracket_pairs.rend(); ++iter) {
    bracket_removed.replace(iter->first, iter->second - iter->first + 1, " ");
  }
  no_bracket_parts->push_back(bracket_removed);

  for (auto &[start, end] : bracket_pairs) {
    no_bracket_parts->push_back(input_string.substr(start + 1, end - start - 1));
  }
  for (auto &str : *no_bracket_parts) {
    TrimInplace(str);
  }
}
}  // namespace
bool WordSegmenter::Init(const std::string &dict_path) {
  jieba_ = std::make_shared<cppjieba::Jieba>(
      dict_path + "/jieba.dict.utf8", dict_path + "/hmm_model.utf8", dict_path + "/user.dict.utf8",
      dict_path + "/idf.utf8", dict_path + "/stop_words.utf8");
  return true;
}

void WordSegmenter::Segment(const std::string &text, std::vector<std::string> *words) const {
  std::string normalized_text = Preprocess(text);
  std::vector<std::string> no_bracket_parts;
  ExtractBracket(normalized_text, &no_bracket_parts);
  for (auto &part : no_bracket_parts) {
    Cut(part, words);
  }
  LOG_INFO("Origin: [{}], normalized: [{}], segmented: [{}]", text, normalized_text,
           StringUtils::Join(words->begin(), words->end(), "|"));
}

std::string WordSegmenter::Preprocess(const std::string &text) const {
  std::vector<uint32_t> unicode = StringUtils::Utf8ToUnicode(text);
  for (uint32_t &ch : unicode) {
    // Full-width space
    if (ch == 0x3000) {
      ch = 0x20;  // Convert to half-width space
    }
    // Full-width ASCII characters
    else if (ch >= 0xFF01 && ch <= 0xFF5E) {
      ch -= 0xFF00;  // Convert to half-width ASCII
    }
    // Uppercase ASCII characters
    if (ch >= 'A' && ch <= 'Z') {
      ch += 32;  // Convert to lowercase
    }
    // Other characters remain unchanged
  }
  return StringUtils::UnicodeToUtf8(unicode);
}

void WordSegmenter::Cut(const std::string &sentence, std::vector<std::string> *words) const {
  std::vector<std::string> cut_result;
  jieba_->Cut(sentence, cut_result);
  for (auto &word : cut_result) {
    TrimInplace(word);
    if (word.empty()) {
      continue;
    }
    if (word.size() == 1 && std::ispunct(word[0])) {
      continue;
    }
    words->emplace_back(word);
  }
}

}  // namespace aurora::search