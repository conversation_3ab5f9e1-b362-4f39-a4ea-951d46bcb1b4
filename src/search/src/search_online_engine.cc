#include "search_online_engine.h"

#include <logger.h>

#include "common/search_config.h"
#include "online/search_by_text_task.h"
namespace aurora::search {

bool SearchOnlineEngine::Init(const std::shared_ptr<SearchConfig>& config) {
  LOG_INFO("Search manager initialized successfully");
  config_ = config;
  return true;
}
bool SearchOnlineEngine::SearchByText(const SearchByTextRequestPtr& request,
                                      const SearchByTextResponsePtr& response) {
  LOG_DEBUG("SearchOnlineEngine::SearchByText");
  auto task = std::make_shared<SearchByTextTask>();
  task->request = request;
  task->response = response;
  task->config_ = config_;
  if (task->Execute() == 0) {
    LOG_DEBUG("SearchOnlineEngine::SearchByText success ------");
    return true;
  }
  return false;
}

bool SearchOnlineEngine::GetDetail(const GetDetailRequestPtr& request,
                                   const GetDetailResponsePtr& response) {
  LOG_DEBUG("SearchOnlineEngine::GetDetail");
  return false;
}

bool SearchOnlineEngine::Autocomplete(const AutocompleteRequestPtr& request,
                                            const AutocompleteResponsePtr& response) {
  LOG_DEBUG("SearchOnlineEngine::AutocompleteSearch");
  return false;
}

bool SearchOnlineEngine::ReverseGeocode(const ReverseGeocodeRequestPtr& request,
                                        const ReverseGeocodeResponsePtr& response) {
  return false;
}

}  // namespace aurora::search