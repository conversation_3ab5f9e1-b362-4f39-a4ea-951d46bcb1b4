#ifndef SEARCH_SRC_DATA_DATA_SOURCE_H
#define SEARCH_SRC_DATA_DATA_SOURCE_H

#include <memory>

#include "data_def.h"

namespace aurora::search {
class PlaceTable;
class InvertIndex;
class AutocompleteRetriever;

struct AutocompleteDataSource {
  std::shared_ptr<AutocompleteRetriever> prefix_index;
  std::shared_ptr<PlaceTable> place_table;
};

struct SearchByTextDataSource {
  std::string city_code;
  CityType city_type;
  std::shared_ptr<InvertIndex> invert_index;
  std::shared_ptr<PlaceTable> place_table;
};

struct DetailDataSource {
  std::string city_code;
  CityType city_type;
  std::shared_ptr<PlaceTable> place_table;
  std::vector<std::string> place_ids;
};

struct ReverseGeocodeDataSource {
  std::string city_code;
  CityType city_type;
  std::shared_ptr<PlaceTable> place_table;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_DATA_SOURCE_H
