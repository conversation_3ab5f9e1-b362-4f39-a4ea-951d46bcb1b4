#include "geocoder.h"

#include <logger.h>

#include "common/search_internal_def.h"
#include "utils/geohash.h"
namespace aurora::search {

Geocoder::Geocoder(std::shared_ptr<SearchConfig> config) : config_(std::move(config)) {}

bool Geocoder::Init() {
  // TODO: Initialize geocoder with necessary data sources
  return true;
}

std::vector<std::string> Geocoder::GetPossibleCities(const std::string& place_id) const {
  return DoGetPossibleCities(place_id);
}

std::vector<std::string> Geocoder::GetPossibleCities(double longitude, double latitude) const {
  std::string geo_cell;
  if (GeoHashEncode(longitude, latitude, kMaxGeohashLength, &geo_cell) < 0) {
    LOG_WARN("Failed to encode geohash for coordinates: ({}, {})", longitude, latitude);
    return {};
  }
  return DoGetPossibleCities(geo_cell);
}

std::vector<std::string> Geocoder::DoGetPossibleCities(const std::string& prefix) const {
  return {"440300", "310000"};
}

}  // namespace aurora::search
