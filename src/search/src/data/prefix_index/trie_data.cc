#include "trie_data.h"

#include <fcntl.h>
#include <logger.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>

#include "utils/string_utils.h"

namespace aurora::search {
namespace {
int BinSearchTrie(const TrieIndex* trie_index, int len, const char* word) {
  if (len == 0 || trie_index == nullptr) {
    return -1;
  }

  int left = 0;
  int right = len;
  int mid;
  int tmp;
  const TrieIndex* center = nullptr;

  while (left < right) {
    mid = (right + left) / 2;
    center = trie_index + mid;
    tmp = strncmp(word, center->word, MAX_CHAR_LEN);
    if (tmp == 0) {
      return mid;
    } else if (tmp > 0) {
      left = mid + 1;
    } else {
      right = mid;
    }
  }
  return -1;
}
}  // namespace

TrieData::TrieData()
    : trie_file_(nullptr), file_num_(0), trie_pinyin_(nullptr), trie_pinyin_size_(0) {}
TrieData::~TrieData() {
  if (file_num_ != 0) {
    for (int i = 0; i < file_num_; ++i) {
      munmap(trie_file_[i].trie_index, trie_file_[i].size);
    }
    delete[] trie_file_;
  }
  munmap(trie_pinyin_, trie_pinyin_size_ * sizeof(IndexResult));
}

int TrieData::GetFileNum(const char* path, const char* trie_file) {
  int result = 0;
  char filename[MAX_FILENAME_LEN];
  while (true) {
    snprintf(filename, MAX_FILENAME_LEN, "%s/%s_%d", path, trie_file, result);
    if (access(filename, R_OK) == -1) {
      break;
    }
    ++result;
  }
  return result;
}

int TrieData::LoadIndex(const char* path, const char* trie_file, const char* hanzi_pinyin) {
  // 1.get the num of trie index file
  file_num_ = GetFileNum(path, trie_file);
  if (file_num_ <= 0) {
    return -1;
  }
  trie_file_ = new TrieFileMap[file_num_];

  // 2.mmap the trie index file
  char filename[MAX_FILENAME_LEN];
  int fp;
  struct stat sb;
  void* tmp;
  for (int i = 0; i < file_num_; ++i) {
    snprintf(filename, MAX_FILENAME_LEN, "%s/%s_%d", path, trie_file, i);
    if ((fp = open(filename, O_RDONLY)) == -1) {
      LOG_FATAL("open file {} error!!", filename);
      return -1;
    }
    if (stat(filename, &sb) == -1) {
      LOG_FATAL("open file {} error!!", filename);
      return -1;
    }
    tmp = mmap(nullptr, sb.st_size, PROT_READ, MAP_PRIVATE, fp, 0);
    if (tmp == (void*)-1) {
      LOG_FATAL("open file {} error!!", filename);
      return -1;
    }
    trie_file_[i].trie_index = (TrieIndex*)tmp;
    trie_file_[i].size = sb.st_size;
    int len = trie_file_[i].size / sizeof(TrieIndex);

    if (close(fp) == -1) {
      LOG_WARN("close file {} error!!", filename);
    }
  }

  snprintf(filename, MAX_FILENAME_LEN, "%s/%s", path, hanzi_pinyin);
  if ((fp = open(filename, O_RDONLY)) == -1) {
    LOG_FATAL("open file {} error!!", filename);
    return -1;
  }
  if (stat(filename, &sb) == -1) {
    LOG_FATAL("open file {} error!!", filename);
    return -1;
  }
  tmp = mmap(nullptr, sb.st_size, PROT_READ, MAP_PRIVATE, fp, 0);
  if (tmp == (void*)-1) {
    LOG_FATAL("open file {} error!!", filename);
    return -1;
  }
  trie_pinyin_ = (IndexResult*)tmp;
  trie_pinyin_size_ = sb.st_size / sizeof(IndexResult);
  return 0;
}

/**
 * @brief  SearchTrie       搜索
 *
 * @param woras[in]         前缀词
 * @param trie_start[in]   从指定位置开始检索
 * @param trie_result[out]
 * 检索结果(成功返回正确结果，不成功返回最大符合前缀结果)
 *
 * @returns   -1 ：不存在; 0 ：成功
 */
int TrieData::SearchTrie(const char* words, const IndexResult* trie_start,
                         IndexResult* trie_result) const {
  if (trie_result == nullptr) {
    return -1;
  }

  char one_word[MAX_CHAR_LEN];
  int word_off = 0;
  int deep = 0;
  int trie_off = 0;

  if (trie_file_ == nullptr) {
    LOG_ERROR("TrieData::SearchTrie error trie_file_ is nullptr");
    return -1;
  }

  int trie_len = trie_file_[deep].size / sizeof(TrieIndex);
  int ret = 0;
  const TrieIndex* aim = nullptr;
  // 有指定起始节点
  if (trie_start != nullptr) {
    deep = trie_start->deep + 1;
    aim = GetTrieIndex(*trie_start);
    if (aim != nullptr) {
      trie_off = aim->child_off;
      trie_len = aim->child_len;
      trie_result->deep = trie_start->deep;
      trie_result->index_id = trie_start->index_id;
    }
  }
  while (deep < file_num_) {
    // 当前层起始位置和字获取
    word_off = GetNewWord(words, word_off, one_word);
    if (word_off == -1) {
      ret = 0;
      break;
    }
    aim = trie_file_[deep].trie_index + trie_off;

    // 当前层搜索
    trie_off = BinSearchTrie(aim, trie_len, one_word);
    if (trie_off == -1) {
      ret = -1;
      break;
    }
    aim = aim + trie_off;

    // 前往下一层搜索
    trie_result->deep = deep;
    trie_result->index_id = aim - trie_file_[deep].trie_index;

    trie_len = aim->child_len;
    trie_off = aim->child_off;
    ++deep;
  }

  if (deep == file_num_ && word_off != -1) {
    ret = -1;
  }

  return ret;
}
}  // namespace aurora::search
