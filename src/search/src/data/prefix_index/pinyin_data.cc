#include "pinyin_data.h"

#include <fcntl.h>
#include <logger.h>
#include <string.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>


namespace aurora::search {
namespace {
int BinSearchPinYin(const PinYinIndex* pinyin_index, int len, char letter) {
  if (len == 0 || pinyin_index == nullptr) {
    return -1;
  }

  int left = 0;
  int right = len;
  int mid;
  const PinYinIndex* center = nullptr;

  while (left < right) {
    mid = (right + left) / 2;
    center = pinyin_index + mid;
    if (center->code == letter) {
      return mid;
    } else if (center->code < letter) {
      left = mid + 1;
    } else {
      right = mid;
    }
  }
  return -1;
}
}  // namespace

PinYinData::PinYinData()
    : pinyin_file_(nullptr), file_num_(0), pinyin_trie_(nullptr), pinyin_trie_size_(0) {}

PinYinData::~PinYinData() {
  if (file_num_ != 0) {
    for (int i = 0; i < file_num_; ++i) {
      munmap(pinyin_file_[i].pinyin_index, pinyin_file_[i].size);
    }
    delete[] pinyin_file_;
  }
  munmap(pinyin_trie_, pinyin_trie_size_ * sizeof(IndexResult));
}

int PinYinData::GetFileNum(const char* path, const char* pinyin_file) {
  int result = 0;
  char filename[MAX_FILENAME_LEN];
  while (true) {
    snprintf(filename, MAX_FILENAME_LEN, "%s/%s_%d", path, pinyin_file, result);
    if (access(filename, R_OK) == -1) {
      break;
    }
    ++result;
  }
  return result;
}

int PinYinData::LoadIndex(const char* path, const char* pinyin_file, const char* pinyin_hanzi) {
  // 1.get the num of pinyin index file
  file_num_ = GetFileNum(path, pinyin_file);
  if (file_num_ <= 0) {
    return -1;
  }
  pinyin_file_ = new PinYinFileMap[file_num_];
  // 2.mmap the pinyin index file
  char filename[MAX_FILENAME_LEN];
  int fp;
  struct stat sb;
  void* tmp;
  for (int i = 0; i < file_num_; ++i) {
    snprintf(filename, MAX_FILENAME_LEN, "%s/%s_%d", path, pinyin_file, i);
    if ((fp = open(filename, O_RDONLY)) == -1) {
      LOG_FATAL("open file {} error!!", filename);
      return -1;
    }
    if (stat(filename, &sb) == -1) {
      LOG_FATAL("open file {} error!!", filename);
      return -1;
    }
    tmp = mmap(nullptr, sb.st_size, PROT_READ, MAP_PRIVATE, fp, 0);
    if (tmp == (void*)-1) {
      LOG_FATAL("open file {}} error!!", filename);
      return -1;
    }
    pinyin_file_[i].pinyin_index = (PinYinIndex*)tmp;
    pinyin_file_[i].size = sb.st_size;

    if (close(fp) == -1) {
      LOG_WARN("close file {} error!!", filename);
    }
  }

  snprintf(filename, MAX_FILENAME_LEN, "%s/%s", path, pinyin_hanzi);
  if ((fp = open(filename, O_RDONLY)) == -1) {
    LOG_FATAL("open file {} error!!", filename);
    return -1;
  }
  if (stat(filename, &sb) == -1) {
    LOG_FATAL("open file {} error!!", filename);
    return -1;
  }
  tmp = mmap(nullptr, sb.st_size, PROT_READ, MAP_PRIVATE, fp, 0);
  if (tmp == (void*)-1) {
    LOG_FATAL("open file {} error!!", filename);
    return -1;
  }
  pinyin_trie_ = (IndexResult*)tmp;
  pinyin_trie_size_ = sb.st_size / sizeof(IndexResult);
  return 0;
}

int PinYinData::SearchPinYin(const char* words, const IndexResult* pinyin_start,
                             IndexResult* pinyin_result) const {
  if (pinyin_result == nullptr) {
    return -1;
  }

  // 开始节点为空
  int pinyin_len = strlen(words);
  int pinyin_off = 0;
  const PinYinIndex* aim = nullptr;
  int deep = 0;
  int index_off = 0;
  int index_len = pinyin_file_[deep].size / sizeof(PinYinIndex);
  int ret = 0;

  // 存在其他开始节点
  if (pinyin_start != nullptr) {
    aim = GetPinYinIndex(*pinyin_start);
    if (aim != nullptr) {
      deep = pinyin_start->deep + 1;
      index_off = aim->child_off;
      index_len = aim->child_len;

      pinyin_result->deep = pinyin_start->deep;
      pinyin_result->index_id = pinyin_start->index_id;
    }
  }
  while (deep < file_num_ && pinyin_off < pinyin_len) {
    // 当前层起始位置和拼音获取
    aim = pinyin_file_[deep].pinyin_index + index_off;

    // 当前层搜索
    index_off = BinSearchPinYin(aim, index_len, words[pinyin_off]);
    if (index_off == -1) {
      ret = -1;
      break;
    }
    aim = aim + index_off;
    // 前往下一层搜索
    pinyin_result->deep = deep;
    pinyin_result->index_id = aim - pinyin_file_[deep].pinyin_index;

    index_len = aim->child_len;
    index_off = aim->child_off;
    ++deep;
    ++pinyin_off;
  }

  if (deep == file_num_ && pinyin_off < pinyin_len) {
    return -1;
  }
  return ret;
}
int PinYinData::ExpandSearchPinYin(const char* words, const IndexResult* pinyin_start,
                                   IndexResult* pinyin_result) const {
  if (pinyin_result == nullptr) {
    return -1;
  }

  // 开始节点为空
  uint32_t pinyin_len = strlen(words);
  uint32_t pinyin_off = 0;
  const PinYinIndex* aim = nullptr;
  uint32_t deep = 0;
  int index_off = 0;
  uint32_t index_len = pinyin_file_[deep].size / sizeof(PinYinIndex);
  int ret = 0;

  // 存在其他开始节点
  if (pinyin_start != nullptr) {
    aim = GetPinYinIndex(*pinyin_start);
    if (aim != nullptr) {
      deep = pinyin_start->deep + 1;
      index_off = aim->child_off;
      index_len = aim->child_len;

      pinyin_result->deep = pinyin_start->deep;
      pinyin_result->index_id = pinyin_start->index_id;
    }
  }

  // MAP_LOG_DEBUG("search the words %s", words);
  while (deep < file_num_ && pinyin_off < pinyin_len) {
    // 当前层起始位置和拼音获取
    aim = pinyin_file_[deep].pinyin_index + index_off;
    const PinYinIndex* last_aim = aim;

    // 当前层搜索
    // MAP_LOG_DEBUG("in deep %d,search %c", deep, words[pinyin_off]);
    index_off = BinSearchPinYin(aim, index_len, words[pinyin_off]);

    while (index_off == -1) {
      // 当前层搜索失败，但是当前层向下只有一个子节点，往后扩展
      if (deep + 1 < file_num_ && index_len == 1) {
        pinyin_result->deep = deep;
        pinyin_result->index_id = aim - pinyin_file_[deep].pinyin_index;
        ++deep;

        index_off = last_aim->child_off;
        index_len = last_aim->child_len;
        aim = pinyin_file_[deep].pinyin_index + index_off;
        index_off = BinSearchPinYin(aim, index_len, words[pinyin_off]);
        last_aim = aim;
      } else
        // 搜索无结果，结束
        if (index_len != 0) {
          ret = -1;
          break;
        }
        // 搜索下一节点不存在，结束
        else {
          deep = file_num_;
          break;
        }
    }

    if (ret == -1 || deep == file_num_) {
      break;
    }
    aim = aim + index_off;
    // MAP_LOG_DEBUG("in deep %c,pinyin_index_id:%d", words[pinyin_off],
    // aim - pinyin_file_[deep].pinyin_index);

    // 前往下一层搜索
    pinyin_result->deep = deep;
    pinyin_result->index_id = aim - pinyin_file_[deep].pinyin_index;

    index_len = aim->child_len;
    index_off = aim->child_off;
    ++deep;
    ++pinyin_off;
  }

  if (deep == file_num_) {
    return ret;
  }

  if (pinyin_off < pinyin_len) {
    return -1;
  }
  return ret;
}

}  // namespace aurora::search