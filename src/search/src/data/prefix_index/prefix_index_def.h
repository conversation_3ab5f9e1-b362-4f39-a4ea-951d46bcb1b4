﻿#ifndef SEARCH_SRC_DATA_PREFIX_INDEX_PREFIX_INDEX_DEF_H
#define SEARCH_SRC_DATA_PREFIX_INDEX_PREFIX_INDEX_DEF_H

#include <cstdint>
namespace aurora::search {

const int MAX_FILENAME_LEN = 1024;
const int MAX_LINE_LEN = 10240;
const int MAX_POI_LINE = 20000000;  // poi数量（用于排序缓存）
const int MAX_CHAR_LEN = 4;         // 单个字最大字节数

// 前缀索引节点结构
struct TrieIndex {
  char word[MAX_CHAR_LEN];  // 字

  uint32_t child_off;  // 子节点在下一层偏移
  uint32_t child_len;  // 子节点在下一层数量

  uint32_t geo_off;  // geohash索引对应位置

  uint32_t poi_off;  // 定位poi链
  uint32_t poi_len;
};

// trie_index节点文件映射
struct TrieFileMap {
  TrieIndex* trie_index;  // 指向trie文件初始位置
  uint32_t size;          // trie_index文件大小
};

// 检索结果信息
struct IndexResult {
  uint32_t deep : 6;       // 对应结果的深度
  uint32_t index_id : 26;  // 对应结果的行号
};

// 拼音索引节点结构
struct PinYinIndex {
  uint32_t child_off;  // 定位下一级
  uint32_t child_len;

  uint32_t py_hz_off;  // 定位到汉字
  uint32_t py_hz_len;

  char code;  // 拼音的字母
};

// pinyin_index节点文件映射
struct PinYinFileMap {
  PinYinIndex* pinyin_index;  // 指向pinyin_index文件初始位置
  uint32_t size;              // pinyin_index文件大小
};

// geohash索引节点结构
struct GeoHashIndex {
  char code;

  uint32_t child_off : 24;  // 子节点
  uint32_t sibling_off;     // 兄弟节点

  uint32_t g_poi_off;  // 对应geohash和poi对应文件中偏移
  uint32_t g_poi_len;  // 对应geohash和poi对应文件中长度
};
// geohash和poi对应文件格式如下
// poi_id poi_id poi_id...               poi_id为行号，即第几个poi

struct GeoFileMap {
  GeoHashIndex* geohash_index;
  uint32_t size;
};

// geohash_index 到poi的映射
struct GeoPOIMap {
  uint32_t* poi_off;
  uint32_t size;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_PREFIX_INDEX_PREFIX_INDEX_DEF_H
