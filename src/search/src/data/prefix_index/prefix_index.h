﻿#ifndef SEARCH_SRC_DATA_PREFIX_INDEX_PREFIX_INDEX_H
#define SEARCH_SRC_DATA_PREFIX_INDEX_PREFIX_INDEX_H

#include <memory>

#include "common/search_config.h"
#include "data/search_data.h"
#include "pinyin_data.h"
#include "trie_data.h"

namespace aurora::search {

class PlaceTable;
class PlaceDetailReader;
struct PrefixIndexLayout;
struct SearchConfig;

class PrefixIndex final : public SearchData {
 public:
  PrefixIndex(std::shared_ptr<SearchConfig> config, const std::string& city_code);
  ~PrefixIndex() override;
  bool Load() override;

  const TrieData* trie_data() const { return trie_data_; }
  const PinYinData* pinyin_data() const { return pinyin_data_; }

  // TODO(ZQ): delete
  uint32_t TempGetMappingOffset(uint32_t k) const;

 private:
  std::shared_ptr<PrefixIndexLayout> folder_layout_;

  TrieData* trie_data_;
  PinYinData* pinyin_data_;
  uint32_t* poi_lines_;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_PREFIX_INDEX_PREFIX_INDEX_H
