#ifndef SEARCH_SUG_PIN_YIN_DATA_H
#define SEARCH_SUG_PIN_YIN_DATA_H

#include "prefix_index_def.h"

namespace aurora::search {

class PinYinData {
 public:
  PinYinData();
  ~PinYinData();

  int LoadIndex(const char* path, const char* pinyin_file, const char* pinyin_hanzi);
  int SearchPinYin(const char* words, const IndexResult* pinyin_start,
                   IndexResult* pinyin_result) const;
  int ExpandSearchPinYin(const char* words, const IndexResult* pinyin_start,
                         IndexResult* pinyin_result) const;

  const PinYinIndex* GetPinYinIndex(const IndexResult& pinyin_result) const {
    return GetPinYinIndex(pinyin_result.deep, pinyin_result.index_id);
  }

  const PinYinIndex* GetPinYinIndex(int deep, int pinyin_id) const {
    if (deep < 0 || deep >= file_num_) {
      return nullptr;
    }
    if (pinyin_id < 0 || pinyin_id >= pinyin_file_[deep].size / sizeof(PinYinIndex)) {
      return nullptr;
    }
    return pinyin_file_[deep].pinyin_index + pinyin_id;
  }

  const IndexResult* GetIndexResult(int offset) const {
    if (pinyin_trie_ == nullptr || offset < 0 || offset >= pinyin_trie_size_) {
      return nullptr;
    }
    return pinyin_trie_ + offset;
  }

 private:
  int GetFileNum(const char* path, const char* pinyin_file);

  PinYinFileMap* pinyin_file_;  // 前缀索引文件
  int file_num_;                // 前缀索引文件数量
  IndexResult* pinyin_trie_;    // 拼音到汉字索引对应文件
  int pinyin_trie_size_;        // 拼音前缀到汉字索引数量
};

}  // namespace aurora::search

#endif  // SEARCH_SUG_PIN_YIN_DATA_H
