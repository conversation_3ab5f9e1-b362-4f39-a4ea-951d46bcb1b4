#ifndef SEARCH_SUG_TRIE_DATA_H
#define SEARCH_SUG_TRIE_DATA_H

#include "prefix_index_def.h"

namespace aurora::search {

class TrieData {
 public:
  TrieData();
  ~TrieData();

  int LoadIndex(const char* path, const char* trie_file, const char* hanzi_pinyin);

  int SearchTrie(const char* words, const IndexResult* trie_start, IndexResult* trie_result) const;

  const TrieIndex* GetTrieIndex(const IndexResult& trie_result) const {
    return GetTrieIndex(trie_result.deep, trie_result.index_id);
  }

  const TrieIndex* GetTrieIndex(int deep, int trie_id) const {
    if (deep >= file_num_ || deep < 0) {
      return nullptr;
    }
    if (trie_id < 0 || trie_id >= trie_file_[deep].size / sizeof(TrieIndex)) {
      return nullptr;
    }
    return trie_file_[deep].trie_index + trie_id;
  }

  const IndexResult* GetPinYinIndex(const IndexResult& index_result) const {
    int line = 0;
    for (int i = 0; i < index_result.deep; ++i) {
      if (i < file_num_) {
        line += trie_file_[i].size / sizeof(TrieIndex);
      }
    }
    return trie_pinyin_ + line + index_result.index_id;
  }

 private:
  int GetFileNum(const char* path, const char* trie_file);

  TrieFileMap* trie_file_;  // 前缀索引文件
  int file_num_;            // 前缀索引文件数量
  IndexResult* trie_pinyin_;
  int trie_pinyin_size_;
};

}  // namespace aurora::search

#endif
