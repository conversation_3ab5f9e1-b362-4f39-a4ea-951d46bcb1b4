﻿#include "prefix_index.h"

#include <logger.h>
#include <sys/stat.h>

#include "common/data_folder_layout.h"
#include "data/place_table/place_table.h"
#include "utils/file_utils.h"

namespace aurora::search {
PrefixIndex::PrefixIndex(std::shared_ptr<SearchConfig> config, const std::string& city_code)
    : SearchData(std::move(config), city_code),
      folder_layout_(config_->data_folder_layout->prefix_index_layout),
      trie_data_(nullptr),
      pinyin_data_(nullptr),
      poi_lines_(nullptr) {}

PrefixIndex::~PrefixIndex() {
  delete[] poi_lines_;
  delete trie_data_;
  delete pinyin_data_;
}

bool PrefixIndex::Load() {
  std::string index_directory = FileUtils::JoinPath(
      FileUtils::JoinPath(config_->data_dir, city_code_), folder_layout_->prefix_index_dir);
  const char* prefix_file_path = index_directory.c_str();
  const char* trie_file = folder_layout_->trie_index_name.c_str();
  const char* hanzi_pinyin = folder_layout_->hanzi_pinyin.c_str();
  const char* pinyin_file = folder_layout_->pinyin_file.c_str();
  const char* pinyin_hanzi = folder_layout_->py_hz_file_index.c_str();
  const char* poi_off_file = folder_layout_->place_name_sort_file.c_str();

  trie_data_ = new TrieData();
  LOG_DEBUG("prefix_file_path: {}, {}, {}", prefix_file_path, trie_file, hanzi_pinyin);
  if (trie_data_->LoadIndex(prefix_file_path, trie_file, hanzi_pinyin) == -1) {
    LOG_FATAL("Load trie_index failed");
    return false;
  }

  pinyin_data_ = new PinYinData();
  if (pinyin_data_->LoadIndex(prefix_file_path, pinyin_file, pinyin_hanzi) == -1) {
    LOG_FATAL("Load pinyin_index failed");
    return false;
  }

  // load sug poi
  char filename[MAX_FILENAME_LEN];
  FILE* fp = nullptr;
  struct stat sb;

  snprintf(filename, MAX_FILENAME_LEN, "%s/%s", prefix_file_path, poi_off_file);
  if ((fp = fopen(filename, "rb")) == nullptr) {
    LOG_FATAL("Load index to poi failed");
    return false;
  }
  if (stat(filename, &sb) == -1) {
    return false;
  }
  LOG_INFO("load index to place success");

  uint32_t poi_num = sb.st_size / sizeof(uint32_t);
  poi_lines_ = new uint32_t[poi_num];
  for (uint32_t j = 0; j < sb.st_size / sizeof(uint32_t); ++j) {
    fread(&poi_lines_[j], sizeof(uint32_t), 1, fp);
  }
  fclose(fp);
  return true;
}

uint32_t PrefixIndex::TempGetMappingOffset(uint32_t k) const { return poi_lines_[k]; }

}  // namespace aurora::search
