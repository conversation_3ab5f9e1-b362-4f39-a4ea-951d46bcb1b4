#ifndef SEARCH_SRC_DATA_GEO_CODER_H
#define SEARCH_SRC_DATA_GEO_CODER_H
#include <memory>
#include <string>
#include <vector>

namespace aurora::search {
struct SearchConfig;

class Geocoder {
 public:
  explicit Geocoder(std::shared_ptr<SearchConfig> config);
  bool Init();

  std::vector<std::string> GetPossibleCities(const std::string& place_id) const;

  std::vector<std::string> GetPossibleCities(double longitude, double latitude) const;

 private:
  std::vector<std::string> DoGetPossibleCities(const std::string& prefix) const;

  std::shared_ptr<SearchConfig> config_;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_GEO_CODER_H
