#ifndef SEARCH_SRC_DATA_SEARCH_DATA_H
#define SEARCH_SRC_DATA_SEARCH_DATA_H
#include <memory>
#include <string>

namespace aurora::search {
struct SearchConfig;

class SearchData {
 public:
  SearchData(std::shared_ptr<SearchConfig> config, const std::string& city_code);
  virtual ~SearchData() = default;
  virtual bool Load() = 0;

 protected:
  std::string city_code_;
  std::shared_ptr<SearchConfig> config_;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_SEARCH_DATA_H
