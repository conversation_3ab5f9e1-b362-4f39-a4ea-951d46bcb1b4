#ifndef SEARCH_SRC_DATA_PREFIX_INDEX_LOADER_H
#define SEARCH_SRC_DATA_PREFIX_INDEX_LOADER_H
#include <memory>

#include "data_loader.h"

namespace aurora::search {
class PrefixIndexData;

class PrefixIndexLoader final : public DataLoader {
 public:
  ~PrefixIndexLoader() override = default;

  void InvalidateCities(const CityVector& invalid_cities) override;

  std::shared_ptr<PrefixIndexData> Get(const std::string& city);
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_PREFIX_INDEX_LOADER_H
