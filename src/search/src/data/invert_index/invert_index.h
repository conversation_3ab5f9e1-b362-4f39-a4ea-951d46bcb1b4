#ifndef SEARCH_SRC_DATA_INVERT_INDEX_INVERT_INDEX_H
#define SEARCH_SRC_DATA_INVERT_INDEX_INVERT_INDEX_H

#include <memory>
#include <vector>

#include "data/search_data.h"
#include "invert_index_def.h"
#include "utils/mmap_file.h"

namespace aurora::search {
struct SearchConfig;
struct InvertIndexLayout;

class InvertIndex final : public SearchData {
 public:
  InvertIndex(std::shared_ptr<SearchConfig> config, const std::string& city_code);
  ~InvertIndex() = default;
  bool Load();

  bool Contains(const std::string& city_code) const;
  const std::string& city_code() const;
  TermIndex* GetIndexByTerm(const std::string& term) const;
  const TermBrief* GetTermBrief(uint32_t brief_id) const;
  const TermData* LocateTermDataByIndex(const TermIndex& term_index) const;
  const GeoTrieNode* LocateGeoTrieByIndex(const TermIndex& term_index) const;

 private:
  bool LoadIndex(const std::string& index_directory);
  bool LoadData(const std::string& index_directory);
  bool LoadBriefAll(const std::string& index_directory);

  TermIndex* GetIndexBySign(uint32_t sign1, uint32_t sign2) const;

  std::shared_ptr<InvertIndexLayout> folder_layout_;

  using TermBlockFile = MMapFile<TermBlock>;
  TermBlockFile term_block_file_;

  using TermIndexFile = MMapFile<TermIndex>;
  std::vector<TermIndexFile> term_index_files_;

  using TermDataFile = MMapFile<TermData>;
  std::vector<TermDataFile> term_data_files_;

  using GeoTrieFile = MMapFile<GeoTrieNode>;
  std::vector<GeoTrieFile> geo_trie_files_;

  using TermBriefFile = MMapFile<TermBrief>;
  TermBriefFile term_brief_file_;
};
}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_INVERT_INDEX_INVERT_INDEX_H