#ifndef SEARCH_SRC_DATA_INVERT_INDEX_GEO_TRIE_H
#define SEARCH_SRC_DATA_INVERT_INDEX_GEO_TRIE_H
#include <memory>
#include <string>

namespace aurora::search {
class GeocodeStack;
class GeoTrieNode;

class GeoTrie {
 public:
  GeoTrie(const GeoTrieNode* data, uint32_t trie_len);

  bool FastSeek(const std::string& geo_cell);
  bool Seek(const std::string& geo_cell);
  void EnsureSeekPerformed(const std::string& geo_cell);

  bool HasNext() const;
  std::string GetNextCell() const;

  uint32_t GetChainOffset() const;
  uint32_t GetChainLen() const;

  void Display() const;

 private:
  void Display(uint32_t pos, const std::string& prev) const;

  const GeoTrieNode* trie_;
  uint32_t trie_len_;

  std::string matched_geo_cell_;
  uint32_t pos_;
  std::shared_ptr<GeocodeStack> stack_;
};
}  // namespace aurora::search
#endif  // SEARCH_SRC_DATA_INVERT_INDEX_GEO_TRIE_H