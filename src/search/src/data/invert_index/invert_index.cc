#include "data/invert_index/invert_index.h"

#include <dirent.h>
#include <logger.h>

#include <cstdio>

#include "common/data_folder_layout.h"
#include "common/search_config.h"
#include "common/search_internal_def.h"
#include "utils/file_utils.h"
#include "utils/signature.h"

namespace aurora::search {
namespace {
uint32_t BinSearchBlock(const TermBlock *blocks, const uint32_t len, const uint32_t sign1,
                        const uint32_t sign2) {
  if (!blocks) {
    return -1;
  }
  if (blocks->sign1 > sign1 || (blocks->sign1 == sign1 && blocks->sign2 > sign2)) {
    return -1;
  }
  uint32_t left = 0;
  uint32_t right = len;
  const TermBlock *mid = nullptr;
  while (right - left > 1) {
    const size_t center = (right + left) / 2;
    mid = blocks + center;
    if (mid->sign1 == sign1 && mid->sign2 == sign2) {
      return center;
    }
    if (mid->sign1 > sign1 || (mid->sign1 == sign1 && mid->sign2 > sign2)) {
      right = center;
    } else {
      left = center;
    }
  }
  return left;
}

uint32_t BinSearchIndex(const TermIndex *indexes, const uint32_t len, const uint32_t sign1,
                        const uint32_t sign2) {
  if (!indexes || len == 0) {
    return -1;
  }
  uint32_t left = 0;
  uint32_t right = len;
  const TermIndex *mid = nullptr;
  while (left < right) {
    const uint32_t center = (left + right) / 2;
    mid = indexes + center;
    if (mid->sign1 == sign1 && mid->sign2 == sign2) {
      return center;
    }
    if (mid->sign1 > sign1 || (mid->sign1 == sign1 && mid->sign2 > sign2)) {
      right = center;
    } else {
      left = center + 1;
    }
  }
  return -1;
}

}  // namespace

InvertIndex::InvertIndex(std::shared_ptr<SearchConfig> config, const std::string &city_code)
    : SearchData(std::move(config), city_code),
      folder_layout_(config_->data_folder_layout->invert_index_layout) {}

bool InvertIndex::Load() {
  std::string index_directory = FileUtils::JoinPath(
      FileUtils::JoinPath(config_->data_dir, city_code_), folder_layout_->invert_index_dir);
  if (!LoadIndex(index_directory)) {
    LOG_ERROR("Load index failed!");
    return false;
  }
  if (!LoadData(index_directory)) {
    LOG_ERROR("Load data failed!");
    return false;
  }

  if (!LoadBriefAll(index_directory)) {
    LOG_ERROR("Load brief all failed!");
    return false;
  }
  return true;
}

bool InvertIndex::Contains(const std::string &city_code) const { return city_code_ == city_code; }

const std::string &InvertIndex::city_code() const { return city_code_; }

TermIndex *InvertIndex::GetIndexByTerm(const std::string &term) const {
  uint32_t sign1 = 0;
  uint32_t sign2 = 0;
  if (!Create64BitSignature(term.c_str(), term.size(), &sign1, &sign2)) {
    LOG_WARN("Failed to create signature: {}", term);
    return nullptr;
  }
  LOG_DEBUG("keyword: {}, sign:[{}, {}]", term, sign1, sign2);
  return GetIndexBySign(sign1, sign2);
}

bool InvertIndex::LoadIndex(const std::string &index_directory) {
  if (!term_block_file_.Load(FileUtils::JoinPath(index_directory, folder_layout_->term_block))) {
    return false;
  }

  const int num_file = FileUtils::GetFileNum(index_directory, folder_layout_->term_index);
  term_index_files_.reserve(num_file);
  for (int i = 0; i < num_file; ++i) {
    term_index_files_.emplace_back();
    if (!term_index_files_.back().Load(
            FileUtils::JoinPath(index_directory, folder_layout_->term_index, i))) {
      return false;
    }
  }
  return true;
}

bool InvertIndex::LoadData(const std::string &index_directory) {
  const int term_data_file_num = FileUtils::GetFileNum(index_directory, folder_layout_->term_data);
  term_data_files_.reserve(term_data_file_num);
  for (int i = 0; i < term_data_file_num; ++i) {
    term_data_files_.emplace_back();
    if (!term_data_files_.back().Load(
            FileUtils::JoinPath(index_directory, folder_layout_->term_data, i))) {
      return false;
    }
  }

  const int geo_trie_file_num = FileUtils::GetFileNum(index_directory, folder_layout_->geo_trie);

  geo_trie_files_.reserve(geo_trie_file_num);
  for (int i = 0; i < geo_trie_file_num; ++i) {
    geo_trie_files_.emplace_back();
    if (!geo_trie_files_.back().Load(
            FileUtils::JoinPath(index_directory, folder_layout_->geo_trie, i))) {
      return false;
    }
  }
  return true;
}

bool InvertIndex::LoadBriefAll(const std::string &index_directory) {
  if (!term_brief_file_.Load(FileUtils::JoinPath(index_directory, folder_layout_->brief_all))) {
    return false;
  }
  return true;
}

TermIndex *InvertIndex::GetIndexBySign(const uint32_t sign1, const uint32_t sign2) const {
  // search in block
  const uint32_t num_block =
      BinSearchBlock(term_block_file_.data(), term_block_file_.size(), sign1, sign2);
  if (num_block == -1) {
    return nullptr;
  }
  if (num_block >= term_index_files_.size()) {
    return nullptr;
  }

  // search in index;
  const uint32_t num_index = BinSearchIndex(term_index_files_[num_block].data(),
                                            term_index_files_[num_block].size(), sign1, sign2);
  if (num_index == -1) {
    return nullptr;
  }

  return term_index_files_[num_block].Get(num_index);
}

const TermBrief *InvertIndex::GetTermBrief(uint32_t brief_id) const {
  if (brief_id > term_brief_file_.size()) {
    LOG_WARN("brief_id: {} out of range {}", brief_id, term_brief_file_.size());
    return nullptr;
  }
  return term_brief_file_.Get(brief_id);
}

const TermData *InvertIndex::LocateTermDataByIndex(const TermIndex &term_index) const {
  if (term_index.num_file >= term_data_files_.size() ||
      term_index.off >= term_data_files_[term_index.num_file].size()) {
    return nullptr;
  }
  return term_data_files_[term_index.num_file].Get(term_index.off);
}

const GeoTrieNode *InvertIndex::LocateGeoTrieByIndex(const TermIndex &term_index) const {
  if (term_index.num_file >= geo_trie_files_.size() ||
      term_index.trie_off >= geo_trie_files_[term_index.num_file].size()) {
    return nullptr;
  }
  return geo_trie_files_[term_index.num_file].Get(term_index.trie_off);
}

}  // namespace aurora::search
