#ifndef SEARCH_SRC_DATA_INVERT_INDEX_INVERT_INDEX_DEF_H
#define SEARCH_SRC_DATA_INVERT_INDEX_INVERT_INDEX_DEF_H
#include <string>

namespace aurora::search {
// term数据的1级索引
struct TermBlock {
  uint32_t sign1;  // 签名高16位
  uint32_t sign2;  // 签名低16位
};

struct TermIndex {
  uint32_t sign1;  // 签名
  uint32_t sign2;
  uint32_t off;           // term偏移
  uint32_t len : 25;      // term数量
  uint32_t num_file : 7;  // term和geohash文件号
  uint32_t trie_off;      // geohash生成trie树文件中偏移
  uint32_t trie_len;      // geohash生成trie树后节点数量
};

struct GeoTrieNode {
  uint32_t sibling_pos;  // 兄弟节点位置
  uint32_t child_pos;    // 儿子节点位置
  uint32_t off;
  uint32_t len : 24;
  uint32_t code : 8;  // 节点字母
};

struct TermData {
  uint32_t brief_id;
  double weight_name;
  double weight_address;
  uint8_t field_flag;
};

struct TermBrief {
  uint64_t uid;
  uint32_t category;
  uint32_t name_sign;

  double lng;
  double lat;
};

inline std::string GetCategoryTerm(uint32_t code) {
  return "[" + std::to_string(code) + "]";
}
}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_INVERT_INDEX_INVERT_INDEX_DEF_H