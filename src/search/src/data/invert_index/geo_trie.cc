#include "geo_trie.h"

#include <logger.h>

#include "invert_index_def.h"

namespace aurora::search {
class GeocodeStack {
  static constexpr uint32_t kMaxDepth = 16;

 public:
  GeocodeStack() : poses_{}, codes_{}, top_(0) {}
  bool IsNotEmpty() const { return top_ != 0; }

  void Push(const uint32_t pos, const uint32_t code) {
    if (top_ < kMaxDepth) {
      poses_[top_] = pos;
      codes_[top_] = static_cast<char>(code);
      ++top_;
    }
  }

  uint32_t Pop() {
    if (top_ > 0) {
      --top_;
      return poses_[top_];
    }
    LOG_WARN("Pop empty stack!");
    return 0;
  }

  uint32_t Top() const {
    if (top_ > 0) {
      return poses_[top_ - 1];
    }
    LOG_WARN("Get top from empty stack!");
    return 0;
  }

  void Clear() { top_ = 0; }
  std::string Value() const { return {codes_, top_}; }

 private:
  uint32_t poses_[kMaxDepth];
  char codes_[kMaxDepth];
  uint32_t top_;
};

GeoTrie::GeoTrie(const GeoTrieNode *data, uint32_t trie_len)
    : trie_(data), trie_len_(trie_len), pos_(trie_len_), stack_(std::make_shared<GeocodeStack>()) {}

bool GeoTrie::FastSeek(const std::string &geo_cell) {
  if (trie_ == nullptr || trie_len_ <= 0 || geo_cell.empty()) {
    return false;
  }

  auto node = trie_;
  bool matched = false;
  for (uint32_t i = 0; i < geo_cell.size(); ++i) {
    // find matched sibling
    while (node->code < geo_cell[i]) {
      const uint32_t sibling_pos = node->sibling_pos;
      if (sibling_pos == 0) {
        break;
      }
      node = trie_ + sibling_pos;
    }

    // the current code is not matched
    if (node->code != geo_cell[i]) {
      break;
    }

    if (i == geo_cell.size() - 1) {
      matched = true;  // match exactly
      break;
    }

    const uint32_t child_pos = node->child_pos;
    if (child_pos == 0) {
      matched = true;  // a larger cell match with geo_cell
      break;
    }
    // 继续从孩子中匹配后续的字符
    node = trie_ + child_pos;
  }

  if (matched) {
    pos_ = node - trie_;
    matched_geo_cell_ = geo_cell;
    return true;
  }
  return false;
}

bool GeoTrie::Seek(const std::string &geo_cell) {
  if (trie_ == nullptr || trie_len_ <= 0 || geo_cell.empty()) {
    return false;
  }

  stack_->Clear();
  pos_ = trie_len_;  // set it to invalid value
  const GeoTrieNode *node = trie_;
  stack_->Push(0, node->code);

  bool matched = false;
  uint32_t i = 0;
  for (i = 0; i < geo_cell.size(); ++i) {
    // find matched sibling
    while (node->code < geo_cell[i]) {
      const uint32_t sibling_pos = node->sibling_pos;
      if (sibling_pos == 0) {
        break;
      }
      stack_->Pop();
      node = trie_ + sibling_pos;
      stack_->Push(sibling_pos, node->code);
    }

    // the current code is not matched
    if (node->code != geo_cell[i]) {
      break;
    }

    if (i == geo_cell.size() - 1) {
      matched = true;  // match exactly
      break;
    }

    const uint32_t child_pos = node->child_pos;
    if (child_pos == 0) {
      matched = true;  // a larger cell match with geo_cell
      break;
    }
    // 继续从孩子中匹配后续的字符
    node = trie_ + child_pos;
    stack_->Push(child_pos, node->code);
  }

  if (matched) {
    pos_ = stack_->Top();
    matched_geo_cell_ = geo_cell;
    return true;
  }

  // two situations:
  // 1. node->code > geo_cell[i], stack's value is already larger than geo_cell,
  // no work to do
  // 2. node->code < geo_cells[i], we need to find a larger geo_cell in trie
  if (node->code < geo_cell[i]) {
    // except the last character, all code in stack is same with geo_cell,
    // we just have to pop until the node with sibling
    while (stack_->IsNotEmpty()) {
      uint32_t top_pos = stack_->Pop();

      uint32_t next_pos = (trie_ + top_pos)->sibling_pos;
      if (next_pos != 0) {
        stack_->Push(next_pos, (trie_ + next_pos)->code);
        break;
      }
    }
  }

  if (stack_->IsNotEmpty()) {
    pos_ = stack_->Top();
  }

  return false;
}

void GeoTrie::EnsureSeekPerformed(const std::string &geo_cell) {
  if (matched_geo_cell_ != geo_cell) {
    LOG_INFO("Finish skipped seek process: {}", geo_cell);
    FastSeek(geo_cell);
  }
}

bool GeoTrie::HasNext() const { return stack_->IsNotEmpty(); }

std::string GeoTrie::GetNextCell() const {
  if (stack_->IsNotEmpty()) {
    return stack_->Value();
  }
  return "";
}

uint32_t GeoTrie::GetChainOffset() const {
  if (pos_ < trie_len_) {
    const GeoTrieNode *current_node = trie_ + pos_;
    return current_node->off;
  }

  return 0;
}

uint32_t GeoTrie::GetChainLen() const {
  if (pos_ < trie_len_) {
    const GeoTrieNode *current_node = trie_ + pos_;
    return current_node->len;
  }

  return 0;
}

void GeoTrie::Display() const { Display(0, ""); }

void GeoTrie::Display(uint32_t pos, const std::string &prev) const {
  const GeoTrieNode *node = trie_ + pos;
  std::string current = prev + static_cast<char>(node->code);
  LOG_DEBUG("{} has [{}, {})", current, node->off, node->off + node->len);
  if (node->sibling_pos != 0) {
    Display(node->sibling_pos, prev);
  }
  if (node->child_pos != 0) {
    Display(node->child_pos, current);
  }
}

}  // namespace aurora::search
