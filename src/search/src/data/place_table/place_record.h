#ifndef SEARCH_SRC_DATA_PLACE_TABLE_PLACE_RECORD_H
#define SEARCH_SRC_DATA_PLACE_TABLE_PLACE_RECORD_H

#include <string>

#include "place_table_def.h"
#include "pointll.h"

namespace aurora::search {

class PlaceDetailReader {
 public:
  explicit PlaceDetailReader(u_int32_t len);
  ~PlaceDetailReader();

  void* buffer() const { return buffer_; }
  u_int32_t buffer_size() const { return buffer_size_; }

  PointLL GetLocation() const;
  uint64_t GetPlaceId() const;
  uint32_t GetBrand() const;
  uint32_t GetAdcode() const;
  uint32_t GetCategory() const;
  double GetLng() const;
  double GetLat() const;
  std::string GetName() const;
  std::string GetNamePy() const;
  std::string GetAbbreviation() const;
  std::string GetAbbreviationPy() const;
  std::string GetAddress() const;
  std::string GetAddressPy() const;
  std::string GetAlias() const;
  std::string GetAliasPy() const;
  std::string GetTelephone() const;
  std::string GetOpenTime() const;
  std::string GetDistrict() const;

  std::string ToString() const;
  void FieldsToString() const;

 private:
  std::string GetString(PlaceDetailField filed) const;

  void* buffer_ = nullptr;
  u_int32_t buffer_size_ = 0;
};
using PlaceDetailReaderPtr = std::shared_ptr<PlaceDetailReader>;

}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_PLACE_TABLE_PLACE_RECORD_H