#include "place_record.h"

#include <logger.h>
namespace aurora::search {

PlaceDetailReader::PlaceDetailReader(uint32_t len) {
  buffer_size_ = len;
  if (len > 0) {
    buffer_ = malloc(len);
  }
}

PlaceDetailReader::~PlaceDetailReader() {
  if (buffer_ != nullptr) {
    free(buffer_);
    buffer_ = nullptr;
  }
  buffer_size_ = 0;
}

PointLL PlaceDetailReader::GetLocation() const { return {GetLng(), GetLat()}; }

uint64_t PlaceDetailReader::GetPlaceId() const {
  if (buffer_ == nullptr) {
    return 0;
  }
  return static_cast<PlaceDetailFixedInfo*>(buffer_)->place_id;
}
uint32_t PlaceDetailReader::GetBrand() const {
  if (buffer_ == nullptr) {
    return 0;
  }
  return static_cast<PlaceDetailFixedInfo*>(buffer_)->brand;
}
uint32_t PlaceDetailReader::GetAdcode() const {
  if (buffer_ == nullptr) {
    return 0;
  }
  return static_cast<PlaceDetailFixedInfo*>(buffer_)->adcode;
}
uint32_t PlaceDetailReader::GetCategory() const {
  if (buffer_ == nullptr) {
    return 0;
  }
  return static_cast<PlaceDetailFixedInfo*>(buffer_)->category;
}
double PlaceDetailReader::GetLng() const {
  if (buffer_ == nullptr) {
    return 0;
  }
  return static_cast<PlaceDetailFixedInfo*>(buffer_)->lng;
}
double PlaceDetailReader::GetLat() const {
  if (buffer_ == nullptr) {
    return 0;
  }
  return static_cast<PlaceDetailFixedInfo*>(buffer_)->lat;
}
std::string PlaceDetailReader::GetName() const { return GetString(PlaceDetailField::kName); }

std::string PlaceDetailReader::GetNamePy() const { return GetString(PlaceDetailField::kNamePy); }

std::string PlaceDetailReader::GetAbbreviation() const {
  return GetString(PlaceDetailField::kAbbr);
}
std::string PlaceDetailReader::GetAbbreviationPy() const {
  return GetString(PlaceDetailField::kAbbrPy);
}

std::string PlaceDetailReader::GetAddress() const { return GetString(PlaceDetailField::kAddress); }

std::string PlaceDetailReader::GetAddressPy() const {
  return GetString(PlaceDetailField::kAddressPy);
}

std::string PlaceDetailReader::GetAlias() const { return GetString(PlaceDetailField::kAlias); }

std::string PlaceDetailReader::GetAliasPy() const { return GetString(PlaceDetailField::kAliasPy); }

std::string PlaceDetailReader::GetTelephone() const {
  return GetString(PlaceDetailField::kTelephone);
}

std::string PlaceDetailReader::GetOpenTime() const {
  return GetString(PlaceDetailField::kOpenTime);
}

std::string PlaceDetailReader::GetDistrict() const {
  return GetString(PlaceDetailField::kDistrict);
}

std::string PlaceDetailReader::GetString(PlaceDetailField filed) const {
  if (buffer_ == nullptr || filed >= PlaceDetailField::kEnd) {
    return "";
  }
  uint32_t* p = (uint32_t*)((char*)buffer_ + sizeof(PlaceDetailFixedInfo));
  uint32_t off = p[static_cast<uint32_t>(filed)] +
                 sizeof(uint32_t) * static_cast<uint32_t>(PlaceDetailField::kSize);
  uint32_t len = p[static_cast<uint32_t>(filed) + 1] - p[static_cast<uint32_t>(filed)];
  if (len <= 0) {
    return "";
  }
  return std::string((char*)p + off, len);
}

void PlaceDetailReader::FieldsToString() const {
  auto fixed = static_cast<PlaceDetailFixedInfo*>(buffer_);
  LOG_DEBUG("PlaceDetailReader: place_id={}, brand={}, adcode={}, category={}, lng={}, lat={}",
            fixed->place_id, fixed->brand, fixed->adcode, fixed->category, fixed->lng, fixed->lat);
  for (uint32_t i = 0; i < static_cast<uint32_t>(PlaceDetailField::kEnd); i++) {
    LOG_DEBUG("PlaceDetailReader field: {}, value: {}", i,
              GetString(static_cast<PlaceDetailField>(i)).c_str());
  }
}

std::string PlaceDetailReader::ToString() const {
  return std::string("place_id=") + std::to_string(GetPlaceId()) +
         ", brand=" + std::to_string(GetBrand()) + ", adcode=" + std::to_string(GetAdcode()) +
         ", category=" + std::to_string(GetCategory()) + ", lng=" + std::to_string(GetLng()) +
         ", lat=" + std::to_string(GetLat()) + ", name=\"" + GetName() + "\"" + ", name_py=\"" +
         GetNamePy() + "\"" + ", abbreviation=\"" + GetAbbreviation() + "\"" +
         ", abbreviation_py=\"" + GetAbbreviationPy() + "\"" + ", address=\"" + GetAddress() +
         "\"" + ", address_py=\"" + GetAddressPy() + "\"" + ", alias=\"" + GetAlias() + "\"" +
         ", alias_py=\"" + GetAliasPy() + "\"" + ", telephone=\"" + GetTelephone() + "\"" +
         ", open_time=\"" + GetOpenTime() + "\"" + ", district=\"" + GetDistrict() + "\"";
}

}  // namespace aurora::search
