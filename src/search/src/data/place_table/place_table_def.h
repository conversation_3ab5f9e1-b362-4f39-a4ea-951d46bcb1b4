#ifndef SEARCH_SRC_DATA_PLACE_TABLE_PLACE_TABLE_DEF_H
#define SEARCH_SRC_DATA_PLACE_TABLE_PLACE_TABLE_DEF_H
#include <memory>

namespace aurora::search {

constexpr uint32_t kPlaceDetailDataVersion = 1;

struct PlaceDetailDataHeader {
  uint32_t version;
  uint32_t data_size;
  PlaceDetailDataHeader() : version(kPlaceDetailDataVersion), data_size(0) {}
  explicit PlaceDetailDataHeader(uint32_t data_size_)
      : version(kPlaceDetailDataVersion), data_size(data_size_) {}
};

struct PlaceDetailIndexHeder {
  uint32_t version;
  uint32_t data_size;
  PlaceDetailIndexHeder() : version(kPlaceDetailDataVersion), data_size(0) {}
  explicit PlaceDetailIndexHeder(uint32_t data_size_)
      : version(kPlaceDetailDataVersion), data_size(data_size_) {}
};

enum class PlaceDetailField : uint32_t {
  kName = 0,
  kNamePy,
  kAbbr,
  kAbbr<PERSON>y,
  k<PERSON><PERSON><PERSON>,
  kAddressPy,
  kAlias,
  kAliasPy,
  kTelephone,
  kOpenTime,
  kDistrict,
  kEnd,
  kSize
};

struct PlaceDetailFixedInfo {
  uint64_t place_id;
  uint32_t brand;
  uint32_t adcode;
  uint32_t category;
  double lng;
  double lat;
  // uint32_t offsets[static_cast<int>(PlaceDetailField::kSize)];
};

struct PlaceDetailIndex {
  uint64_t id;
  // poi 开始位置
  uint32_t offset;
  // poi 长度
  uint32_t len;

  PlaceDetailIndex(uint64_t place_id, uint32_t place_len, uint32_t place_offset)
      : id(place_id), offset(place_offset), len(place_len) {}

  PlaceDetailIndex() : id(0), offset(0), len(0) {}
};

}  // namespace aurora::search
#endif  // SEARCH_SRC_DATA_PLACE_TABLE_PLACE_TABLE_DEF_H