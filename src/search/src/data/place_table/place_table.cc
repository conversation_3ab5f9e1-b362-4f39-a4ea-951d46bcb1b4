#include "place_table.h"

#include <fcntl.h>
#include <logger.h>
#include <sys/stat.h>
#include <unistd.h>

#include <algorithm>
#include <array>

#include "common/data_folder_layout.h"
#include "common/search_config.h"
#include "common/search_internal_def.h"
#include "search_def.h"
#include "utils/data_operation.h"
#include "utils/file_utils.h"
#include "utils/geohash.h"

namespace aurora::search {

PlaceTable::PlaceTable(std::shared_ptr<SearchConfig> config, const std::string& city_code)
    : SearchData(std::move(config), city_code),
      layout_(config_->data_folder_layout->place_table_layout) {}

PlaceTable::~PlaceTable() {
  if (index_mem_ != nullptr) {
    free(index_mem_);
    index_mem_ = nullptr;
  }
  if (fp_data != -1) {
    if (close(fp_data) == -1) {
      LOG_WARN("close file data error");
    }
    fp_data = -1;
  }
}

bool PlaceTable::Load() {
  std::string detail_dir = FileUtils::JoinPath(FileUtils::JoinPath(config_->data_dir, city_code_),
                                               layout_->detail_index_dir);
  std::string data_file = FileUtils::JoinPath(detail_dir, layout_->place_table);
  std::string index_file = FileUtils::JoinPath(detail_dir, layout_->primary_key_index);

  // todo index ==> b+ 树
  int fp;
  size_t len;
  // index
  if ((fp = open(index_file.c_str(), O_RDONLY)) == -1) {
    LOG_ERROR("open file {} error!!", index_file);
    return false;
  }
  len = sizeof(PlaceDetailIndexHeder);
  ssize_t read_len = pread(fp, &index_header_, len, 0);
  if (read_len != len) {
    LOG_ERROR("read PlaceDetailIndexHeder size error {} !=  {}", read_len, len);
    return false;
  }
  len = index_header_.data_size;
  index_mem_ = static_cast<PlaceDetailIndex*>(malloc(len));
  read_len = pread(fp, index_mem_, len, sizeof(PlaceDetailIndexHeder));
  LOG_DEBUG("read PlaceDetailIndex size {}", index_header_.data_size / sizeof(PlaceDetailIndex));
  if (read_len != len) {
    LOG_ERROR("read PlaceDetailIndex size error {} !=  {}", read_len, len);
    return false;
  }
  if (close(fp) == -1) {
    LOG_WARN("close file index_file: {} error", index_file);
  }
  // data
  if ((fp_data = open(data_file.c_str(), O_RDONLY)) == -1) {
    LOG_ERROR("open file {} error!!", data_file);
    return false;
  }
  len = sizeof(PlaceDetailDataHeader);
  read_len = pread(fp_data, &data_header_, len, 0);
  if (read_len != len) {
    LOG_ERROR("read size error {}  != {}", read_len, len);
    return false;
  }
  return true;
}

uint32_t PlaceTable::GetSize() const { return index_header_.data_size / sizeof(PlaceDetailIndex); }

bool PlaceTable::GetPlaceDetail(const std::string& place_id, PlaceDetail* place_detail) {
  const uint64_t id = DataOperation::ToInternalPlaceId(place_id);
  std::shared_ptr<PlaceDetailReader> detail = GetDetail(id);
  if (detail == nullptr) {
    return false;
  }
  place_detail->id = place_id;
  place_detail->location = PointLL(detail->GetLng(), detail->GetLat());
  place_detail->name = detail->GetName();
  place_detail->address = detail->GetAddress();
  place_detail->category = std::to_string(detail->GetCategory());
  return true;
}

std::shared_ptr<PlaceDetailReader> PlaceTable::GetDetail(PlaceDetailIndex& index) const {
  if (index.len == 0 || fp_data == -1) {
    return nullptr;
  }

  auto detail_reader = std::make_shared<PlaceDetailReader>(index.len);
  ssize_t read_len = pread(fp_data, detail_reader->buffer(), index.len,
                           index.offset + sizeof(PlaceDetailDataHeader));
  if (read_len != index.len) {
    LOG_ERROR("read PlaceDetailIndex size error {} !=  {}", read_len, index.len);
    return nullptr;
  }
  return std::move(detail_reader);
}

std::shared_ptr<PlaceDetailReader> PlaceTable::GetPlaceDetailFromOffset(uint32_t idx) const {
  if (idx >= index_header_.data_size) {
    return nullptr;
  }
  return GetDetail(index_mem_[idx]);
}

std::shared_ptr<PlaceDetailReader> PlaceTable::GetDetail(uint64_t place_id) const {
  PlaceDetailIndex index;
  if (Search(place_id, index)) {
    return std::move(GetDetail(index));
  }
  return nullptr;
}

void PlaceTable::GetGeoHashData(const std::string& geohash,
                                std::vector<std::shared_ptr<PlaceDetailReader>>& list) const {
  uint64_t geohash_id = 0;

  uint32_t len = std::min(static_cast<uint32_t>(geohash.size()), kMaxGeohashLength);
  for (uint32_t i = 0; i < len; i++) {
    uint32_t tmp = GetBase32IndexFast(geohash[i]);
    geohash_id = (geohash_id << 5) | tmp;
  }
  uint32_t total = GetSize();
  uint64_t left = 0, right = total;
  uint64_t mid;
  uint64_t start = 0, end = 0;

  while (left < right) {
    mid = (left + right) >> 1;
    if ((index_mem_[mid].id >> 24) < geohash_id) {
      left = mid + 1;
    } else {
      right = mid;
    }
  }
  start = left;

  left = 0, right = total;
  while (left < right) {
    mid = (left + right) >> 1;
    if ((index_mem_[mid].id >> 24) > geohash_id) {
      right = mid;
    } else {
      left = mid + 1;
    }
  }
  end = left - 1;

  for (uint64_t i = start; i <= end; i++) {
    list.push_back(GetDetail(index_mem_[i]));
  }
}

bool PlaceTable::Search(uint64_t place_id, PlaceDetailIndex& index) const {
  if (index_mem_ == nullptr) {
    LOG_ERROR("detail index mem is null");
    return false;
  }

  uint32_t left = 0, right = index_header_.data_size / sizeof(PlaceDetailIndex);
  while (left < right) {
    uint32_t mid = (left + right) >> 1;
    if (index_mem_[mid].id > place_id) {
      right = mid;
    } else if (index_mem_[mid].id < place_id) {
      left = mid + 1;
    } else {
      index = index_mem_[mid];
      return true;
    }
  }
  return false;
}

uint32_t PlaceTable::GetBase32IndexFast(char c) {
  static std::array<uint32_t, 256> lookup_table = []() {
    std::array<uint32_t, 256> table = {0};
    for (uint32_t i = 0; i < 32; ++i) {
      char ch = kGeohashBase32Codes[i];
      table[static_cast<unsigned char>(ch)] = i;
    }
    return table;
  }();

  uint32_t lower_c = static_cast<unsigned char>(std::tolower(c));
  return lookup_table[lower_c];
}

}  // namespace aurora::search