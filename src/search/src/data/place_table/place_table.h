#ifndef SEARCH_SRC_DATA_PLACE_TABLE_PLACE_TABLE_H
#define SEARCH_SRC_DATA_PLACE_TABLE_PLACE_TABLE_H

#include <memory>
#include <string>
#include <vector>

#include "common/search_config.h"
#include "data/place_table/place_record.h"
#include "data/place_table/place_table_def.h"
#include "data/search_data.h"
#include "search_def.h"

namespace aurora::search {
struct PlaceTableLayout;
struct PlaceDetail;

class PlaceTable final : public SearchData {
 public:
  PlaceTable(std::shared_ptr<SearchConfig> config, const std::string& city_code);
  ~PlaceTable() override;
  bool Load() override;

  uint32_t GetSize() const;

  bool GetPlaceDetail(const std::string& place_id, PlaceDetail* place_detail);
  std::shared_ptr<PlaceDetailReader> GetDetail(PlaceDetailIndex& index) const;
  std::shared_ptr<PlaceDetailReader> GetPlaceDetailFromOffset(uint32_t idx) const;
  std::shared_ptr<PlaceDetailReader> GetDetail(uint64_t place_id) const;

  void GetGeoHashData(const std::string& geohash,
                      std::vector<std::shared_ptr<PlaceDetailReader>>& list) const;

 private:
  bool Search(uint64_t place_id, PlaceDetailIndex& index) const;
  static uint32_t GetBase32IndexFast(char c);

  std::shared_ptr<PlaceTableLayout> layout_;

  // index
  PlaceDetailIndexHeder index_header_;
  PlaceDetailIndex* index_mem_ = nullptr;
  // data
  PlaceDetailDataHeader data_header_;
  int fp_data = -1;
};
}  // namespace aurora::search

#endif  // SEARCH_SRC_DATA_PLACE_TABLE_PLACE_TABLE_H
