#ifndef SEARCH_SRC_DATA_CACHE_H
#define SEARCH_SRC_DATA_CACHE_H
#include <iostream>
#include <list>
#include <unordered_map>

#include "data_def.h"
#include <logger.h>
#include "magic_enum/magic_enum.hpp"
namespace aurora::search {
template <typename KeyType, typename ValueType>
class LRUCache {
 public:
  explicit LRUCache(size_t capacity)
      : capacity_(capacity),
        primary_node_(cache_list_.end()),
        national_node_(cache_list_.end()) {}

  void Put(const KeyType& key, const ValueType& value, CityType node_type) {
    // If the key already exists, update the value and move it to the front
    auto it = cache_map_.find(key);
    if (it != cache_map_.end()) {
      cache_list_.erase(it->second);
      cache_map_.erase(it);
    }

    if (node_type == CityType::kNational) {
      if (national_node_ != cache_list_.end()) {
        // Remove the existing national node
        RemoveNode(national_node_);
      }
      cache_list_.emplace_front(key, value, node_type);
      national_node_ = cache_list_.begin();  // Update the national node pointer
    } else if (node_type == CityType::kPrimary) {
      if (primary_node_ != cache_list_.end()) {
        // Downgrade the existing primary node to normal
        primary_node_->node_type = CityType::kNormal;
      }
      cache_list_.emplace_front(key, value, node_type);
      primary_node_ = cache_list_.begin();  // Update the primary node pointer
    } else {
      cache_list_.emplace_front(key, value, node_type);
    }

    cache_map_[key] = cache_list_.begin();

    // If the total size exceeds the capacity, evict normal nodes
    while (cache_list_.size() > capacity_) {
      EvictNormalNode();
    }
  }

  ValueType Get(const KeyType& key, CityType new_node_type) {
    auto it = cache_map_.find(key);
    if (it == cache_map_.end()) {
      return nullptr;
    }

    auto node = it->second;

    // Update the node type if it changes between Primary and Normal
    if (new_node_type == CityType::kPrimary && node->node_type == CityType::kNormal) {
      if (primary_node_ != cache_list_.end()) {
        // Downgrade the existing primary node to normal
        primary_node_->node_type = CityType::kNormal;
      }
      node->node_type = CityType::kPrimary;
      primary_node_ = node;
    } else if (new_node_type == CityType::kNormal &&
               node->node_type == CityType::kPrimary) {
      node->node_type = CityType::kNormal;
      primary_node_ = cache_list_.end();  // Clear primary node pointer
    }

    // Move the accessed node to the front of the list
    cache_list_.splice(cache_list_.begin(), cache_list_, node);
    return node->value;
  }

  void DeleteByKey(const KeyType& key) {
    auto it = cache_map_.find(key);
    if (it != cache_map_.end()) {
      auto node = it->second;

      // Handle special cases for national and primary nodes
      if (node == national_node_) {
        national_node_ = cache_list_.end();
      } else if (node == primary_node_) {
        primary_node_ = cache_list_.end();
      }

      RemoveNode(node);
    }
  }

  void Display() const {
    for (const auto& node : cache_list_) {
      LOG_INFO("Key: {}, Type: {}", node.key, node.node_type);
    }
  }

 private:
  struct Node {
    KeyType key;
    ValueType value;
    CityType node_type;

    Node(const KeyType& k, const ValueType& v, CityType nt)
        : key(k), value(v), node_type(nt) {}
  };
  using NodePtr = typename std::list<Node>::iterator;

  size_t capacity_;
  std::list<Node> cache_list_;
  std::unordered_map<KeyType, NodePtr> cache_map_;
  NodePtr primary_node_;
  NodePtr national_node_;

  void RemoveNode(NodePtr node) {
    cache_map_.erase(node->key);
    cache_list_.erase(node);
  }

  void EvictNormalNode() {
    for (auto it = cache_list_.rbegin(); it != cache_list_.rend(); ++it) {
      if (it->node_type == CityType::kNormal) {
        cache_map_.erase(it->key);
        cache_list_.erase(std::next(it).base());
        return;
      }
    }
  }
};

}  // namespace aurora::search
#endif  // SEARCH_SRC_DATA_CACHE_H
