#include "string_utils.h"

#include <cstdint>
#include <cstring>
#include <sstream>

namespace aurora::search {

std::vector<std::string> StringUtils::SplitString(const char* str, char delim) {
  std::vector<std::string> fields;
  std::stringstream ss(str);
  std::string item;
  while (std::getline(ss, item, delim)) {
    fields.push_back(item);
  }
  return fields;
}

std::vector<std::string> StringUtils::SplitString(const std::string& str, char delim) {
  return SplitString(str.c_str(), delim);
}

std::vector<uint32_t> StringUtils::Utf8ToUnicode(const std::string& utf8_str) {
  std::vector<uint32_t> unicode;
  size_t i = 0;
  while (i < utf8_str.size()) {
    uint32_t ch = 0;
    unsigned char byte = utf8_str[i];
    if ((byte & 0x80) == 0) {  // 1-byte (ASCII)
      ch = byte;
      i += 1;
    } else if ((byte & 0xE0) == 0xC0) {  // 2-byte
      ch = ((utf8_str[i] & 0x1F) << 6) | (utf8_str[i + 1] & 0x3F);
      i += 2;
    } else if ((byte & 0xF0) == 0xE0) {  // 3-byte
      ch =
          ((utf8_str[i] & 0x0F) << 12) | ((utf8_str[i + 1] & 0x3F) << 6) | (utf8_str[i + 2] & 0x3F);
      i += 3;
    } else if ((byte & 0xF8) == 0xF0) {  // 4-byte
      ch = ((utf8_str[i] & 0x07) << 18) | ((utf8_str[i + 1] & 0x3F) << 12) |
           ((utf8_str[i + 2] & 0x3F) << 6) | (utf8_str[i + 3] & 0x3F);
      i += 4;
    }
    unicode.push_back(ch);
  }
  return unicode;
}

std::string StringUtils::UnicodeToUtf8(const std::vector<uint32_t>& unicode) {
  std::string utf8_str;
  for (uint32_t ch : unicode) {
    if (ch <= 0x7F) {  // 1-byte (ASCII)
      utf8_str += static_cast<char>(ch);
    } else if (ch <= 0x7FF) {  // 2-byte
      utf8_str += static_cast<char>(0xC0 | ((ch >> 6) & 0x1F));
      utf8_str += static_cast<char>(0x80 | (ch & 0x3F));
    } else if (ch <= 0xFFFF) {  // 3-byte
      utf8_str += static_cast<char>(0xE0 | ((ch >> 12) & 0x0F));
      utf8_str += static_cast<char>(0x80 | ((ch >> 6) & 0x3F));
      utf8_str += static_cast<char>(0x80 | (ch & 0x3F));
    } else if (ch <= 0x10FFFF) {  // 4-byte
      utf8_str += static_cast<char>(0xF0 | ((ch >> 18) & 0x07));
      utf8_str += static_cast<char>(0x80 | ((ch >> 12) & 0x3F));
      utf8_str += static_cast<char>(0x80 | ((ch >> 6) & 0x3F));
      utf8_str += static_cast<char>(0x80 | (ch & 0x3F));
    }
  }
  return utf8_str;
}

int GetByteNum(unsigned char k) {
  if (k == 0) {
    return 0;
  }
  int bit = 0;
  while (k > 0x7f) {
    k = k << 1;
    ++bit;
  }
  if (bit == 0) {
    return 1;
  }
  return bit;
}

int GetNextChar(const char* in, int off, char* out) {
  if (off < 0) {
    return 0;
  }
  int byte_num = GetByteNum(in[off]);
  strncpy(out, &in[off], byte_num);
  out[byte_num] = '\0';
  return byte_num;
}

unsigned int ToUnicode(char* word) {
  unsigned int a;
  unsigned char t = word[0];
  a = t & 0x0f;
  t = word[1];
  a = (a << 6) + (t & 0x3f);
  t = word[2];
  a = (a << 6) + (t & 0x3f);
  return a;
}

int GetNewWord(const char* content, int off, char* word) {
  if (word == nullptr) {
    return -1;
  }
  int add = GetNextChar(content, off, word);  // 字长
  int p = add + off;                          // 当前字位置
  while (add != 0) {
    word[add] = '\0';
    if (add == 3) {
      // u4e00-u9fa5 〇=u3007
      unsigned int a = ToUnicode(word);
      if ((a >= 0x4e00 && a <= 0x9fa5) || a == 0x3007) {
        return p;
      }
    }
    if (word[0] >= 'A' && word[0] <= 'Z') {
      word[0] = word[0] + 32;  //'a' - 'A';
      return p;
    }
    if (word[0] >= 'a' && word[0] <= 'z') {
      return p;
    }
    if (word[0] >= '0' && word[0] <= '9') {
      return p;
    }
    add = GetNextChar(content, p, word);
    p = add + p;
  }
  return -1;
}

std::string DealString(const std::string& str) {
  char* word = new char[str.size() + 1];
  const char* s = str.c_str();
  int p = GetNewWord(s, 0, word);
  std::string result("");
  while (p != -1) {
    result += word;
    p = GetNewWord(s, p, word);
  }
  delete[] word;
  return result;
}

}  // namespace aurora::search