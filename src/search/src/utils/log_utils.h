#ifndef SEARCH_SRC_UTILS_LOG_UTILS_H
#define SEARCH_SRC_UTILS_LOG_UTILS_H
#include <optional>
#include <string>

#include "common/ranking_place.h"
#include "search_def.h"
namespace aurora::search {
class LogUtils {
 public:
  static std::string ToString(const RankingPlace& ranking_place);
  static std::string ToString(const PlaceBrief& place_brief);
  static std::string ToString(const std::optional<std::string>& value);
};
}  // namespace aurora::search

#endif  // SEARCH_SRC_UTILS_LOG_UTILS_H
