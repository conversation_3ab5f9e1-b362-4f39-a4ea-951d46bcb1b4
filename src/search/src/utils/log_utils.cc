#include "log_utils.h"

#include "spdlog/fmt/bundled/xchar.h"

namespace aurora::search {
std::string LogUtils::ToString(const RankingPlace& ranking_place) {
  return fmt::format(
      "brief_id  = {}, name = {}, address = {}, distance = {}, title = {}, "
      "category = {}",
      ranking_place.brief_id, ranking_place.name_tf_idf_score, ranking_place.address_tf_idf_score,
      ranking_place.distance_score, ranking_place.title_hit_score, ranking_place.category);
}
std::string LogUtils::ToString(const PlaceBrief& place_brief) {
  return fmt::format("id = {}, location = {}, name = {}, address = {}, category = {}",
                     place_brief.id, place_brief.location.ToString(), place_brief.name,
                     place_brief.address, place_brief.category);
}
std::string LogUtils::ToString(const std::optional<std::string>& value) {
  return value.has_value() ? value.value() : "EMPTY";
}
}  // namespace aurora::search
