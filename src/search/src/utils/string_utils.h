#ifndef SEARCH_SRC_UTILS_STRING_UTILS_H
#define SEARCH_SRC_UTILS_STRING_UTILS_H

#include <stdint.h>

#include <string>
#include <vector>

#include "execution/search_task_base.h"

namespace aurora::search {
class StringUtils {
 public:
  static std::vector<std::string> SplitString(const char* str, char delim);

  static std::vector<std::string> SplitString(const std::string& str, char delim);

  static std::vector<uint32_t> Utf8ToUnicode(const std::string& utf8_str);
  static std::string UnicodeToUtf8(const std::vector<uint32_t>& unicode);

  template <typename Iterator>
  static std::string Join(Iterator begin, Iterator end, const std::string& sep) {
    std::ostringstream oss;
    for (Iterator it = begin; it != end; ++it) {
      if (it != begin) {
        oss << sep;
      }
      oss << *it;
    }
    return oss.str();
  }
};

/**
 * @brief  GetByteNum 获取该utf-8编码的字节数量
 *
 * @param k[in] 首字节编码
 *
 * @returns 字节数量
 */
int GetByteNum(unsigned char k);

/**
 * @brief  GetNextChar 从字符串指定位置获取一个utf8字符
 *
 * @param in[in]        输入字符串
 * @param off[in]       获取的起始偏移
 * @param out[out]      输出字符串
 *
 * @returns  输出字符数量
 */
int GetNextChar(const char* in, int off, char* out);

/**
 * @brief  GetNewWord 获取新的字
 * @note 字只有a-z,0-9或者3个字节的字（大写字母转小写）
 *
 * @param content
 * @param line[in]  行内容
 * @param off[in]       偏移
 * @param word[out]     输出字
 *
 * @returns   新的行偏移
 */
int GetNewWord(const char* content, int off, char* word);

/**
 * @brief  DealString 处理字符串，去掉特殊字符
 *
 * @param str[in]
 *
 * @returns   处理后的字符串
 */
std::string DealString(const std::string& str);

}  // namespace aurora::search

#endif  // SEARCH_SRC_UTILS_STRING_UTILS_H
