#include "geohash.h"

#include <logger.h>

#include <cmath>
#include <set>

#include "common/search_internal_def.h"

namespace aurora::search {
namespace {
constexpr double kEarthCircle = 40000000.0;  // 地球周长
constexpr uint32_t kMaxGeohashDepth = 20;    // 最大geohash深度层级

// 对bound切分的最小geohash编码长度对应的level
// geohash编码位数  1   2   3   4   5   6
// 经度占位         3   5   8   10  13  15
// 纬度占位         2   5   7   10  12  15
// 目前最小取5位
constexpr uint32_t kMaxGeoHashLevelBits = 13;

struct BitSet {
  uint32_t value;
  uint8_t len;

  BitSet() : value(0), len(0) {}
  BitSet(uint32_t v, uint8_t l) : value(v), len(l) {}

  BitSet ExtendLeft() const { return AddBit(true); }
  BitSet ExtendRight() const { return AddBit(false); }

 private:
  BitSet AddBit(bool left) const {
    uint32_t new_value = (value << 2) + (left ? 0 : 1);
    uint8_t new_len = len == 0 ? 1 : (len + 2);
    return {new_value, new_len};
  }
};

struct BitSetContainer {
  BitSet bitset_values[64];  // 对于一般的bound，定义为64已经足够
  uint16_t bitset_pos[64] = {};
  int size = 0;

  void Clear() { size = 0; }

  int AddValue(int pos, const BitSet& value) {
    if (size < 64) {
      bitset_pos[size] = pos;
      bitset_values[size] = value;
      size++;
      return 0;
    }

    return -1;
  }
};

struct BitResult {
  BitSet bitset_values[64];
  int count;

  BitResult() : count(0) {}

  int AddOneResult(const BitSet& value) {
    if (count < 64) {
      bitset_values[count++] = value;
      return 0;
    }

    return -1;
  }
};

// calculate how the give [start, end] range is encoded in lon or lat
// we split to level 5 geohash at most, but not always to the deepest level,
// e.g. if a range in level fully covered in level 3,
// it will not further split into level 4 or 4
void ConvertCode(double start, double end, double min, double max, bool even, BitResult* result) {
  BitSetContainer nodes[2];
  BitSetContainer* parents = &nodes[0];
  BitSetContainer* children = &nodes[1];

  parents->AddValue(0, {});
  // we use 32-bit encoding, so 5 bits group generate 1 geohash code, lng fist,
  // for lng, one code should generate for: 3/5/8/10/13/15 bits
  // for lat, one code should generate for: 2/5/7/10/12/15 bits
  int target_level = even ? 3 : 2;
  // when this level is reached, the range will not be split anymore
  int stop_split_level = -1;

  // geohash经度是5位的时候，经度13位，纬度12位
  const int max_level = even ? kMaxGeoHashLevelBits : kMaxGeoHashLevelBits - 1;

  for (int level = 0; level <= max_level; ++level) {
    if (target_level < level) {
      target_level += even ? 2 : 3;
      even = !even;
    }

    const int num = 1 << level;
    const double span = (max - min) / num;
    for (int i = 0; i < parents->size; i++) {
      int parent_pos = parents->bitset_pos[i];
      double s = min + span * parent_pos;
      double e = min + span * (parent_pos + 1);
      double m = (s + e) / 2;

      double inter_s = std::max(start, s);
      double inter_e = std::min(end, e);
      double inter_ratio = (inter_e - inter_s) / span;

      const bool reach_target_level = level == target_level;
      const bool fully_covered = reach_target_level && start <= s && e <= end;
      const bool mostly_covered = reach_target_level && inter_ratio >= 0.95;
      const bool reach_max_level = level == max_level;
      const bool reach_stop_level = level == stop_split_level;

      BitSet current = parents->bitset_values[i];
      // when the max_level is reached, no children will be added for splitting
      // for max_level, bit_set len for lng is 25, for lat is 23
      if (fully_covered || mostly_covered || reach_max_level || reach_stop_level) {
        result->AddOneResult(current);

        if (stop_split_level == -1) {
          // one result is added at this level,
          // at next target level, further split is stopped
          stop_split_level = target_level + (even ? 2 : 3);
        }
        continue;
      }

      const int child_pos = parent_pos * 2;
      if (end <= m) {
        children->AddValue(child_pos, current.ExtendLeft());
      } else if (start >= m) {
        children->AddValue(child_pos + 1, current.ExtendRight());
      } else {
        children->AddValue(child_pos, current.ExtendLeft());
        children->AddValue(child_pos + 1, current.ExtendRight());
      }
    }
    std::swap(parents, children);
    children->Clear();
  }
}

// Converts each bit of the integer `idx` into a 2-bit representation:
// `0` becomes `00`, and `1` becomes `01`
// Process `bit_number` bits from `idx` starting from the most significant bit
// if bit_number is 3:
// 0 -> 000 -> 000000
// 1 -> 001 -> 000001
// 2 -> 010 -> 000100
// 3 -> 011 -> 000101
// 4 -> 100 -> 010000
// 5 -> 101 -> 010001
// 6 -> 110 -> 010100
// 7 -> 111 -> 010101
uint32_t BitMakeUp(const uint32_t idx, const uint8_t bit_number) {
  uint32_t res = 0;
  for (int i = bit_number - 1; i >= 0; --i) {
    res = (res << 2) + ((idx >> i) & 1);
  }

  return res;
}

// @magic
// lng-bits, lng-len; lat-bits, lat-len, how to combine?
//    3    ,    5   ;    2    ,    3   , lng-bits | (lat-bits << 1)
//    5    ,    9   ;    5    ,    9   , (lng-bits << 1) | lat-bits
//    8    ,    15  ;    7    ,    13  , lng-bits | (lat-bits << 1)
//    10   ,    19  ;    10   ,    19  , (lng-bits << 1) | lat-bits
//    13   ,    25  ;    12   ,    23  , lng-bits | (lat-bits << 1)
std::string BitEncode(uint32_t lng_value, uint32_t lat_value, uint8_t lng_len, uint8_t lat_len) {
  if (lat_len % 10 == 3) {
    lat_value = lat_value << 1;
  }

  if (lng_len % 10 == 9) {
    lng_value = lng_value << 1;
  }

  const int geohash_len = (lng_len + 1) / 5;
  char buffer[geohash_len + 1] = {};
  uint32_t geohash_bit = lng_value | lat_value;
  for (int i = 0; i < geohash_len; ++i) {
    const uint32_t code = geohash_bit & 0x1f;
    buffer[geohash_len - 1 - i] = kGeohashBase32Codes[code];
    geohash_bit = geohash_bit >> 5;
  }
  return buffer;
}

void GenGeoHashCode(const BitSet& lng, const BitSet& lat, std::vector<std::string>& result) {
  uint32_t lng_value = lng.value;
  uint32_t lat_value = lat.value;

  if (std::abs(lng.len - lat.len) <= 2) {
    // when lng and lat are at the same depth,
    // the bit-set length difference is at most 2
    result.emplace_back(BitEncode(lng_value, lat_value, lng.len, lat.len));
  } else if (lng.len > lat.len) {
    // extend the latitude bits, check BitEncode comment for the magic
    const uint8_t new_lat_len = (lng.len % 10 == 9) ? lng.len : lng.len - 2;
    const uint32_t lat_value_prefix = lat_value << (new_lat_len - lat.len);
    const uint8_t diff = (new_lat_len - lat.len) / 2;
    const uint32_t max_value = 1 << diff;
    for (uint32_t i = 0; i < max_value; i++) {
      const uint32_t suffix = BitMakeUp(i, diff);
      result.emplace_back(BitEncode(lng_value, lat_value_prefix | suffix, lng.len, new_lat_len));
    }
  } else if (lat.len > lng.len) {
    // extend the longitude bits, check BitEncode comment for the magit
    const uint8_t new_lng_len = (lat.len % 10 == 9) ? lat.len : lat.len + 2;
    const uint32_t lng_value_prefix = lng_value << (new_lng_len - lng.len);
    const int diff = (new_lng_len - lng.len) / 2;
    const int max_value = 1 << diff;
    std::string temp;
    for (int i = 0; i < max_value; i++) {
      const uint32_t suffix = BitMakeUp(i, diff);
      result.emplace_back(BitEncode(lng_value_prefix | suffix, lat_value, new_lng_len, lat.len));
    }
  } else {
    assert(false && "Unexpected branch reached in GenGeoHashCode");
  }
}

void JoinBitResult(const BitResult& lng_result, const BitResult& lat_result,
                   std::vector<std::string>& result) {
  for (int i = 0; i < lng_result.count; ++i) {
    const BitSet& lng = lng_result.bitset_values[i];
    for (int j = 0; j < lat_result.count; ++j) {
      const BitSet& lat = lat_result.bitset_values[j];
      GenGeoHashCode(lng, lat, result);
    }
  }
}

bool PointInRange(double lng, double lat, double last_lng, double last_lat, const double lng_gap,
                  const double lat_gap, double width_lng, double width_lat) {
  if (last_lng + width_lng <= lng && lng < last_lng + lng_gap - width_lng &&
      last_lat + width_lat <= lat && lat < last_lat + lat_gap - width_lat) {
    return true;
  }

  return false;
}
}  // namespace

int GeoHashEncode(const double longitude, const double latitude, const uint32_t depth,
                  char* geohash) {
  if (geohash == nullptr) {
    return -1;
  }

  double min_lng = kMinLng;
  double max_lng = kMaxLng;
  double min_lat = kMinLat;
  double max_lat = kMaxLat;

  double mid = 0.0;
  bool is_lng = true;
  int num_bits = 0;
  int hash_index = 0;

  uint32_t output_length = 0;
  while (output_length < depth) {
    if (is_lng) {
      mid = (max_lng + min_lng) / 2;
      if (longitude > mid) {
        hash_index = (hash_index << 1) + 1;
        min_lng = mid;
      } else {
        hash_index = (hash_index << 1) + 0;
        max_lng = mid;
      }
    } else {
      mid = (max_lat + min_lat) / 2;
      if (latitude > mid) {
        hash_index = (hash_index << 1) + 1;
        min_lat = mid;
      } else {
        hash_index = (hash_index << 1) + 0;
        max_lat = mid;
      }
    }
    is_lng = !is_lng;

    ++num_bits;
    if (num_bits == 5) {
      geohash[output_length] = kGeohashBase32Codes[hash_index];
      ++output_length;
      num_bits = 0;
      hash_index = 0;
    }
  }

  geohash[output_length] = '\0';
  return 0;
}

int GeoHashEncode(const double longitude, const double latitude, const uint32_t depth,
                  std::string* geohash) {
  if (geohash == nullptr) {
    return -1;
  }

  if (depth > kMaxGeohashDepth) {
    return -1;
  }

  char code[kMaxGeohashDepth + 1] = {};
  const int ret = GeoHashEncode(longitude, latitude, depth, code);
  geohash->clear();
  geohash->assign(code);

  return ret;
}
int GeoHashForBand(const BandingBound& band, std::vector<std::string>* geohash_blocks) {
  const double* array_x = band.point_array_x;
  const double* array_y = band.point_array_y;
  uint32_t array_length = band.size;
  uint32_t width = band.width;

  if (array_x == nullptr || array_y == nullptr || array_length <= 0 || width <= 0 ||
      geohash_blocks == nullptr) {
    return -1;
  }

  std::set<std::string> geohash_blocks_set;
  double longitude = 0.0;
  double latitude = 0.0;
  /// 按照Geohash精度为5位时，经度精度为13位，纬度精度为12位（base32编码，5位编一个码）
  const uint32_t precision = 5;                                // Geohash精度位数
  const uint32_t longitude_bits = (precision * 5 + 1) / 2;     // 经度精度位数
  const uint32_t latitude_bits = precision * 5 / 2;            // 纬度精度位数
  const double longitude_gap = 360.0 / (1 << longitude_bits);  // 经度最小单位跨度
  const double latitude_gap = 360.0 / (1 << latitude_bits);    // 纬度最小单位跨度
  // width的经度宽度，纬度越高经度跨度越大，这里使用余弦值
  const double width_longitude = width * 360.0 / kEarthCircle;
  // width的纬度宽度
  const double width_latitude = width * 360.0 / kEarthCircle;

  double last_longitude = 0.0;
  double last_latitude = 0.0;
  std::string hash_code;

  for (uint32_t i = 0; i < array_length; ++i) {
    longitude = array_x[i];
    latitude = array_y[i];
    // Mercator2LongitudeLatitude(x, y, &longitude, &latitude);
    // 当前点再前一个点的geohash块中则忽略
    if (PointInRange(longitude, latitude, last_longitude, last_latitude, longitude_gap,
                     latitude_gap, width_longitude, width_latitude)) {
      continue;
    }

    // 舍弃"小数点后的内容，向下去整"
    double longitude_low = (int)(longitude / longitude_gap) * longitude_gap;
    double latitude_low = (int)(latitude / latitude_gap) * latitude_gap;
    if (GeoHashEncode(longitude, latitude, precision, &hash_code) < 0) {
      LOG_WARN("create geohash code failed:(%lf, %lf).", longitude, latitude);
    } else {
      geohash_blocks_set.insert(hash_code);
    }

    // 经度或者纬度相同，表明前后两个块连续
    bool is_neighbour = false;
    if (last_longitude - 0.01 < longitude_low && longitude_low < last_longitude + 0.01) {
      if (longitude_low + width_longitude > longitude) {
        // 离左侧块近，向左扩展
        if (GeoHashEncode(longitude - longitude_gap, latitude, precision, &hash_code) < 0) {
          LOG_WARN("create geohash code failed: (%lf, %lf)", longitude, latitude);
        } else {
          geohash_blocks_set.insert(hash_code);
        }
      }

      if (longitude_low + longitude_gap - width_longitude < longitude) {
        // 离右侧块近，向右扩展
        if (GeoHashEncode(longitude + longitude_gap, latitude, precision, &hash_code) < 0) {
          LOG_WARN("create geohash code failed: (%lf, %lf)", longitude, latitude);
        } else {
          geohash_blocks_set.insert(hash_code);
        }
      }
      is_neighbour = true;
    }
    if (last_latitude - 0.01 < latitude_low && latitude_low < last_latitude + 0.01) {
      if (latitude_low + width_latitude > latitude) {
        // 向下扩展
        if (GeoHashEncode(longitude, latitude - latitude_gap, precision, &hash_code) < 0) {
          LOG_WARN("create geohash code failed: (%lf, %lf)", longitude, latitude);
        } else {
          geohash_blocks_set.insert(hash_code);
        }
      }

      if (latitude_low + latitude_gap - width_latitude < latitude) {
        // 向上扩展
        if (GeoHashEncode(longitude, latitude + latitude_gap, precision, &hash_code) < 0) {
          LOG_WARN("create geohash code failed: (%lf, %lf)", longitude, latitude);
        } else {
          geohash_blocks_set.insert(hash_code);
        }
      }
      is_neighbour = true;
    }

    // 对角关系，不相邻，需要平滑过度
    if (!is_neighbour) {
      if (GeoHashEncode(longitude_low + longitude_gap / 2, latitude_low, precision, &hash_code) <
          0) {
        LOG_WARN("create geohash code failed: (%lf, %lf)", longitude, latitude);
      } else {
        geohash_blocks_set.insert(hash_code);
      }

      if (GeoHashEncode(longitude_low, latitude_low + latitude_gap / 2, precision, &hash_code) <
          0) {
        LOG_WARN("create geohash code failed: (%lf, %lf)", longitude, latitude);
      } else {
        geohash_blocks_set.insert(hash_code);
      }
    }

    // 第一块（起点），将周围的8个块添加进来
    if (last_latitude < 0.000001 && last_longitude < 0.000001) {
      if ((longitude_low + width_longitude > longitude) &&
          (latitude_low + width_latitude > latitude)) {
        // 左下
        if (GeoHashEncode(longitude - longitude_gap, latitude - latitude_gap, precision,
                          &hash_code) < 0) {
          LOG_WARN("create geohash code failed:(%lf, %lf).", longitude, latitude);
        } else {
          geohash_blocks_set.insert(hash_code);
        }
      }

      if ((longitude_low + width_longitude > longitude) &&
          (latitude_low + latitude_gap - width_latitude < latitude)) {
        // 左上
        if (GeoHashEncode(longitude - longitude_gap, latitude + latitude_gap, precision,
                          &hash_code) < 0) {
          LOG_WARN("create geohash code failed:(%lf, %lf).", longitude, latitude);
        } else {
          geohash_blocks_set.insert(hash_code);
        }
      }

      if ((longitude_low + longitude_gap - width_longitude < longitude) &&
          (latitude_low + width_latitude > latitude)) {
        // 右下
        if (GeoHashEncode(longitude + longitude_gap, latitude - latitude_gap, precision,
                          &hash_code) < 0) {
          LOG_WARN("create geohash code failed:(%lf, %lf).", longitude, latitude);
        } else {
          geohash_blocks_set.insert(hash_code);
        }
      }

      if ((longitude_low + longitude_gap - width_longitude < longitude) &&
          (latitude_low + latitude_gap - width_latitude < latitude)) {
        // 右上
        if (GeoHashEncode(longitude + longitude_gap, latitude + latitude_gap, precision,
                          &hash_code) < 0) {
          LOG_WARN("create geohash code failed:(%lf, %lf).", longitude, latitude);
        } else {
          geohash_blocks_set.insert(hash_code);
        }
      }

      if (latitude_low + width_latitude > latitude) {
        // 正下
        if (GeoHashEncode(longitude, latitude - latitude_gap, precision, &hash_code) < 0) {
          LOG_WARN("create geohash code failed:(%lf, %lf).", longitude, latitude);
        } else {
          geohash_blocks_set.insert(hash_code);
        }
      }

      if (latitude_low + latitude_gap - width_latitude < latitude) {
        // 正上
        if (GeoHashEncode(longitude, latitude + latitude_gap, precision, &hash_code) < 0) {
          LOG_WARN("create geohash code failed:(%lf, %lf).", longitude, latitude);
        } else {
          geohash_blocks_set.insert(hash_code);
        }
      }

      if (longitude_low + width_longitude > longitude) {
        // 正左
        if (GeoHashEncode(longitude - longitude_gap, latitude, precision, &hash_code) < 0) {
          LOG_WARN("create geohash code failed:(%lf, %lf).", longitude, latitude);
        } else {
          geohash_blocks_set.insert(hash_code);
        }
      }

      if (longitude_low + longitude_gap - width_longitude < longitude) {
        // 正右
        if (GeoHashEncode(longitude - longitude_gap, latitude, precision, &hash_code) < 0) {
          LOG_WARN("create geohash code failed:(%lf, %lf).", longitude, latitude);
        } else {
          geohash_blocks_set.insert(hash_code);
        }
      }
    }

    last_longitude = longitude_low;
    last_latitude = latitude_low;
  }

  geohash_blocks->insert(geohash_blocks->begin(), geohash_blocks_set.begin(),
                         geohash_blocks_set.end());
  return 0;
}

int GeoHashForBand(const double* array_x, const double* array_y, uint32_t array_length,
                   uint32_t width, std::vector<std::string>* geohash_blocks) {
  if (array_x == nullptr || array_y == nullptr || array_length == 0) {
    return -1;
  }

  BandingBound band;
  band.point_array_x = const_cast<double*>(array_x);
  band.point_array_y = const_cast<double*>(array_y);
  band.size = array_length;
  band.width = width;

  return GeoHashForBand(band, geohash_blocks);
}

int GeoHashForBoundLongitudeLatitude(const Mbr& mbr, std::vector<std::string>* geohash_blocks) {
  BitResult lng_result;
  BitResult lat_result;
  ConvertCode(mbr.minx(), mbr.maxx(), kMinLng, kMaxLng, true, &lng_result);
  ConvertCode(mbr.miny(), mbr.maxy(), kMinLat, kMaxLat, false, &lat_result);
  JoinBitResult(lng_result, lat_result, *geohash_blocks);
  return 0;
}

Mbr GenerateMbr(double longitude, double latitude, double radius) {
  const double delta_degree = radius / kDistanceSpanPerDegree;
  return {longitude - delta_degree, latitude - delta_degree, longitude + delta_degree,
          latitude + delta_degree};
}
}  // namespace aurora::search
