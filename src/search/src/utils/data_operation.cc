#include "data_operation.h"

#include <logger.h>
namespace aurora::search {

std::string DataOperation::ToExternalPlaceId(uint64_t place_id) {
  return std::to_string(place_id);
}

uint64_t DataOperation::ToInternalPlaceId(const std::string& place_id) {
  try {
    return std::stoull(place_id);
  } catch (const std::exception& e) {
    LOG_WARN("Invalid place id: {}, error: {}", place_id, e.what());
    return 0;
  }
}

std::string DataOperation::ToExternalCategory(uint32_t category) {
  return std::to_string(category);
}

uint32_t DataOperation::ToInternalCategory(const std::string& category) {
  try {
    return std::stoul(category);
  } catch (const std::exception& e) {
    LOG_WARN("Invalid category: {}, error: {}", category, e.what());
    return 0;
  }
}

bool DataOperation::TestField(uint32_t field, FieldFlagBit bit) {
  return (field & (1U << static_cast<uint32_t>(bit))) != 0;
}

}  // namespace aurora::search