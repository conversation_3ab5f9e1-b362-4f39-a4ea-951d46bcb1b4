#include "data_service_wrapper.h"

#include <logger.h>

#include "common/search_config.h"
namespace aurora::search {
DataService& DataService::Instance() {
  static DataService instance;
  return instance;
}

bool DataService::Init(std::shared_ptr<SearchConfig> config) {
  config_ = std::move(config);
  return true;
}

std::string DataService::GetCityCode(const double lng, const double lat) const {
  // todo user data service to get city code by lng and lat
  return GetCityCode();
}

std::string DataService::GetCityCode() const {
  if (config_ != nullptr && config_->debug_mode) {
    return config_->force_city_code;
  }
  return "440300";
}

DataService::DataService() = default;

DataService::~DataService() = default;

}  // namespace aurora::search