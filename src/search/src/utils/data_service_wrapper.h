#ifndef SEARCH_SRC_UTILS_DATA_SERVICE_WRAPPER_H
#define SEARCH_SRC_UTILS_DATA_SERVICE_WRAPPER_H

#include <memory>
#include <string>

namespace aurora::search {
struct SearchConfig;

class DataService {
 public:
  static DataService& Instance();
  bool Init(std::shared_ptr<SearchConfig> config);
  std::string GetCityCode(double lng, double lat) const;
  std::string GetCityCode() const;

  DataService(const DataService&) = delete;
  DataService& operator=(const DataService&) = delete;

 private:
  DataService();
  ~DataService();
 std::shared_ptr<SearchConfig> config_;
};
}  // namespace aurora::search

#endif  // SEARCH_SRC_UTILS_DATA_SERVICE_WRAPPER_H