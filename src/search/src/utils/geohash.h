#ifndef SEARCH_SRC_UTILS_GEOHASH_H
#define SEARCH_SRC_UTILS_GEOHASH_H

#include <string>
#include <vector>

#include "common/search_internal_def.h"

namespace aurora::search {
constexpr char kGeohashBase32Codes[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'b',
                                        'c', 'd', 'e', 'f', 'g', 'h', 'j', 'k', 'm', 'n', 'p',
                                        'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'};
int GeoHashEncode(double longitude, double latitude, uint32_t depth, char* geohash);
int GeoHashEncode(double longitude, double latitude, uint32_t depth, std::string* geohash);
Mbr GenerateMbr(double longitude, double latitude, double radius);

int GeoHashForBand(const double* array_x, const double* array_y, uint32_t array_length,
                   uint32_t width, std::vector<std::string>* geohash_blocks);

int GeoHashForBand(const BandingBound& band, std::vector<std::string>* geohash_blocks);

int GeoHashForBoundLongitudeLatitude(const Mbr& mbr, std::vector<std::string>* geohash_blocks);
}  // namespace aurora::search

#endif  // SEARCH_SRC_UTILS_GEOHASH_H