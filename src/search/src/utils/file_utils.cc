#include "file_utils.h"

#include <logger.h>
#include <unistd.h>

#include <filesystem>
namespace aurora::search {
namespace fs = std::filesystem;

std::string FileUtils::JoinPath(const std::string& base,
                                const std::string& relative) {
  try {
    fs::path result = fs::path(base) / relative;
    return GetCleanPath(result.string());
  } catch (const fs::filesystem_error& e) {
    LOG_WARN("Failed to join [{}] and [{}], error {}", base, relative,
             e.what());
    return "";
  }
}

std::string FileUtils::JoinPath(const std::string& base,
                                const std::string& relative,
                                const int num_file) {
  return JoinPath(base, relative + "_" + std::to_string(num_file));
}

std::string FileUtils::NormalizePath(const std::string& path) {
  try {
    fs::path p = fs::weakly_canonical(path);
    return p.string();
  } catch (const fs::filesystem_error& e) {
    LOG_ERROR("Failed to normalize path {}, error {}", path, e.what());
    return path;
  }
}
int FileUtils::GetFileNum(const std::string& base,
                          const std::string& relative) {
  int num_file = 0;
  while (true) {
    std::string file_path = JoinPath(base, relative, num_file);
    if (access(file_path.c_str(), R_OK) == -1) {
      break;
    }
    ++num_file;
  }
  return num_file;
}

std::string FileUtils::GetCleanPath(const std::string& relative_path) {
  try {
    return NormalizePath(fs::absolute(relative_path).string());
  } catch (const fs::filesystem_error& e) {
    LOG_WARN("Failed to get relative path {}, error {}", relative_path,
             e.what());
    return relative_path;
  }
}
bool FileUtils::IsAbsolutePath(const std::string& path) {
  return fs::path(path).is_absolute();
}
}  // namespace aurora::search
