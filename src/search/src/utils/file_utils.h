#ifndef SEARCH_SRC_UTILS_FILE_UTILS_H
#define SEARCH_SRC_UTILS_FILE_UTILS_H
#include <logger.h>


#include <string>

namespace aurora::search {
class FileUtils {
 public:
  static std::string JoinPath(const std::string& base, const std::string& relative);
  static std::string JoinPath(const std::string& base, const std::string& relative, int num_file);
  static std::string GetCleanPath(const std::string& relative_path);
  static bool IsAbsolutePath(const std::string& path);
  static std::string NormalizePath(const std::string& path);
  // TODO(WS): Write file num to meta, do not use access
  static int GetFileNum(const std::string& base, const std::string& relative);
};
}  // namespace aurora::search

#endif  // SEARCH_SRC_UTILS_FILE_UTILS_H
