#include "search_task_factory.h"

#include "search_task_base.h"

namespace aurora::search {
SearchTaskFactory::SearchTaskFactory(std::shared_ptr<SearchEngine> offline_engine,
                                     std::shared_ptr<SearchEngine> online_engine,
                                     std::shared_ptr<BS::thread_pool<>> thread_pool)
    : offline_engine_(std::move(offline_engine)),
      online_engine_(std::move(online_engine)),
      thread_pool_(std::move(thread_pool)) {}

std::shared_ptr<ISearchTaskBase> SearchTaskFactory::CreateTextSearchTask(
    int task_id, const SearchByTextRequestPtr& request,
    const SearchByTextResponseHandlerPtr& handler) {
  return std::make_shared<
      SearchTaskBase<SearchByTextRequest, SearchByTextResponse, SearchByTextResponseHandler>>(
      offline_engine_, online_engine_, thread_pool_, task_id, request, handler,
      &SearchEngine::SearchByText);
}

std::shared_ptr<ISearchTaskBase> SearchTaskFactory::CreateGetDetailTask(
    int task_id, const GetDetailRequestPtr& request, const GetDetailResponseHandlerPtr& handler) {
  return std::make_shared<
      SearchTaskBase<GetDetailRequest, GetDetailResponse, GetDetailResponseHandler>>(
      offline_engine_, online_engine_, thread_pool_, task_id, request, handler,
      &SearchEngine::GetDetail);
}

std::shared_ptr<ISearchTaskBase> SearchTaskFactory::CreateAutocompleteTask(
    int task_id, const AutocompleteRequestPtr& request,
    const AutocompleteResponseHandlerPtr& handler) {
  return std::make_shared<
      SearchTaskBase<AutocompleteRequest, AutocompleteResponse, AutocompleteResponseHandler>>(
      offline_engine_, online_engine_, thread_pool_, task_id, request, handler,
      &SearchEngine::Autocomplete);
}

std::shared_ptr<ISearchTaskBase> SearchTaskFactory::CreateReverseGeocodeTask(
    int task_id, const ReverseGeocodeRequestPtr& request,
    const ReverseGeocodeResponseHandlerPtr& handler) {
  return std::make_shared<
      SearchTaskBase<ReverseGeocodeRequest, ReverseGeocodeResponse, ReverseGeocodeResponseHandler>>(
      offline_engine_, online_engine_, thread_pool_, task_id, request, handler,
      &SearchEngine::ReverseGeocode);
}

}  // namespace aurora::search
