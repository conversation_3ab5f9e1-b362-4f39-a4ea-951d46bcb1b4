#include "search_task_manager.h"

#include <logger.h>

#include <memory>

#include "search_task_factory.h"

constexpr int kMaxTaskThreadCount = 4;
constexpr int kMaxSearchTaskThreadCount = 10;

namespace aurora::search {
SearchTaskManager::SearchTaskManager(std::shared_ptr<SearchEngine> offline_engine,
                                     std::shared_ptr<SearchEngine> online_engine) {
  thread_pool_ = std::make_shared<BS::thread_pool<>>(kMaxTaskThreadCount);
  search_task_pool_ = std::make_shared<BS::thread_pool<>>(kMaxSearchTaskThreadCount);
  task_factory_ = std::make_shared<SearchTaskFactory>(std::move(offline_engine),
                                                      std::move(online_engine), thread_pool_);
}

SearchTaskManager::~SearchTaskManager() = default;

int SearchTaskManager::AddSearchByTextTask(const SearchByTextRequestPtr& request,
                                           const SearchByTextResponseHandlerPtr& handler) {
  int task_id = GenerateTaskId();
  auto task = task_factory_->CreateTextSearchTask(task_id, request, handler);
  auto future = thread_pool_->submit_task([task]() { task->Execute(); });
  return task_id;
}

int SearchTaskManager::AddGetDetailTask(const GetDetailRequestPtr& request,
                                        const GetDetailResponseHandlerPtr& handler) {
  int task_id = GenerateTaskId();
  auto task = task_factory_->CreateGetDetailTask(task_id, request, handler);
  auto future = thread_pool_->submit_task([task]() { task->Execute(); });
  return task_id;
}
int SearchTaskManager::AddAutocompleteTask(const AutocompleteRequestPtr& request,
                                           const AutocompleteResponseHandlerPtr& handler) {
  int task_id = GenerateTaskId();
  auto task = task_factory_->CreateAutocompleteTask(task_id, request, handler);
  auto future = thread_pool_->submit_task([task]() { task->Execute(); });
  return task_id;
}

int SearchTaskManager::AddReverseGeocodeTask(const ReverseGeocodeRequestPtr& request,
                                             const ReverseGeocodeResponseHandlerPtr& handler) {
  int task_id = GenerateTaskId();
  auto task = task_factory_->CreateReverseGeocodeTask(task_id, request, handler);
  auto future = thread_pool_->submit_task([task]() { task->Execute(); });
  return task_id;
}

int SearchTaskManager::GenerateTaskId() { return task_id_.fetch_add(1); }

}  // namespace aurora::search
