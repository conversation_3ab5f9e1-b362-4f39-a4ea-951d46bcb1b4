#ifndef SEARCH_SRC_SEARCH_TASK_MANAGER_H
#define SEARCH_SRC_SEARCH_TASK_MANAGER_H

#include <memory>

#include "BS_thread_pool.hpp"
#include "search_def.h"
#include "search_engine.h"
#include "search_task_base.h"

using priority_t = std::int8_t;

namespace aurora::search {
class SearchTaskFactory;

class SearchTaskManager {
 public:
  SearchTaskManager(std::shared_ptr<SearchEngine> offline_engine,
                    std::shared_ptr<SearchEngine> online_engine);
  ~SearchTaskManager();

  int AddSearchByTextTask(const SearchByTextRequestPtr& request,
                          const SearchByTextResponseHandlerPtr& handler);
  int AddGetDetailTask(const GetDetailRequestPtr& request,
                       const GetDetailResponseHandlerPtr& handler);
  int AddAutocompleteTask(const AutocompleteRequestPtr& request,
                          const AutocompleteResponseHandlerPtr& handler);
  int AddReverseGeocodeTask(const ReverseGeocodeRequestPtr& request,
                            const ReverseGeocodeResponseHandlerPtr& handler);

 private:
  ThreadPoolPtr thread_pool_;
  ThreadPoolPtr search_task_pool_;
  std::atomic<int> task_id_{1};
  std::shared_ptr<SearchTaskFactory> task_factory_;

  int GenerateTaskId();
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_SEARCH_TASK_MANAGER_H