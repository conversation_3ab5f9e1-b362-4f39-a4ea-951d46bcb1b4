#ifndef SEARCH_SRC_TASK_SEARCH_TASK_DEF_H
#define SEARCH_SRC_TASK_SEARCH_TASK_DEF_H
#include "BS_thread_pool.hpp"

namespace aurora::search {

constexpr int kSearchTaskMaxSeconds = 5;
constexpr int kSearchTaskIntervalMilliseconds = 100;

enum class SearchTaskStatus {
  Initialized,  // 初始化状态
  Running,      // 运行中状态
  TimedOut,     // 超时
  Failed,       // 失败
  Success       // 成功
};

enum class TaskType {
  kSearchByText,
  kGetDetail,
  kReverseGeocode,
  kAutocomplete,
  kCount
};

using ThreadPoolPtr = std::shared_ptr<BS::thread_pool<>>;

}  // namespace aurora::search

#endif