#ifndef SEARCH_SRC_TASK_SEARCH_TASK_FACTORY_H
#define SEARCH_SRC_TASK_SEARCH_TASK_FACTORY_H

#include <search_def.h>

#include <memory>

#include "BS_thread_pool.hpp"

namespace aurora::search {
class ISearchTaskBase;
class SearchEngine;

class SearchTaskFactory {
 public:
  SearchTaskFactory(std::shared_ptr<SearchEngine> offline_engine,
                    std::shared_ptr<SearchEngine> online_engine,
                    std::shared_ptr<BS::thread_pool<>> thread_pool);

  std::shared_ptr<ISearchTaskBase> CreateTextSearchTask(
      int task_id, const SearchByTextRequestPtr& request,
      const SearchByTextResponseHandlerPtr& handler);

  std::shared_ptr<ISearchTaskBase> CreateGetDetailTask(int task_id,
                                                       const GetDetailRequestPtr& request,
                                                       const GetDetailResponseHandlerPtr& handler);

  std::shared_ptr<ISearchTaskBase> CreateAutocompleteTask(
      int task_id, const AutocompleteRequestPtr& request,
      const AutocompleteResponseHandlerPtr& handler);

  std::shared_ptr<ISearchTaskBase> CreateReverseGeocodeTask(
      int task_id, const ReverseGeocodeRequestPtr& request,
      const ReverseGeocodeResponseHandlerPtr& handler);

 private:
  std::shared_ptr<SearchEngine> offline_engine_;
  std::shared_ptr<SearchEngine> online_engine_;
  std::shared_ptr<BS::thread_pool<>> thread_pool_;
};

}  // namespace aurora::search
#endif