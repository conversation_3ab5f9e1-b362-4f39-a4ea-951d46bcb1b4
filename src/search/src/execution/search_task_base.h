#ifndef SEARCH_SRC_TASK_SEARCH_TASK_BASE_H
#define SEARCH_SRC_TASK_SEARCH_TASK_BASE_H

#include <logger.h>

#include "search_def.h"
#include "search_engine.h"
#include "search_task_def.h"

namespace aurora::search {

class ISearchTaskBase {
 public:
  virtual ~ISearchTaskBase() = default;
  virtual int Execute() = 0;
};

template <typename RequestType, typename ResponseType, typename HandlerType>
class SearchTaskBase : public ISearchTaskBase {
 public:
  using RequestPtr = std::shared_ptr<RequestType>;
  using ResponsePtr = std::shared_ptr<ResponseType>;
  using HandlerPtr = std::shared_ptr<HandlerType>;
  using EngineFunc = bool (SearchEngine::*)(const RequestPtr&, const ResponsePtr&);
  SearchTaskBase(std::shared_ptr<SearchEngine> offline_engine,
                 std::shared_ptr<SearchEngine> online_engine, ThreadPoolPtr task_pool, int task_id,
                 RequestPtr request, HandlerPtr handler, EngineFunc engine_func)
      : task_id_(task_id),
        request_(std::move(request)),
        handler_(std::move(handler)),
        engine_func_(engine_func),
        offline_engine_(std::move(offline_engine)),
        online_engine_(std::move(online_engine)),
        status_(SearchTaskStatus::Initialized),
        thread_pool_(std::move(task_pool)) {}

  ~SearchTaskBase() override = default;
  int Execute() final {
    SetupContext();

    if (primary_ == nullptr && secondary_ == nullptr) {
      LOG_ERROR("SearchTaskBase::error primary_ and secondary_ is null");
      return -1;
    }

    if (primary_ != nullptr) {
      primary_->future = thread_pool_->submit_task([this] { return DoExecute(primary_); });
    }

    if (secondary_ != nullptr) {
      secondary_->future = thread_pool_->submit_task([this] { return DoExecute(secondary_); });
    }

    auto start_time = std::chrono::steady_clock::now();

    while (std::chrono::steady_clock::now() - start_time <
           std::chrono::seconds(kSearchTaskMaxSeconds)) {
      if (primary_->future.wait_for(std::chrono::seconds(0)) == std::future_status::ready) {
        if (primary_->future.get()) {
          LOG_INFO("SearchTaskBase::Run::Run::ExecuteSuccessHandler primary_");
          return ExecuteSuccessHandler(primary_);
        }
        if (secondary_ == nullptr) {
          LOG_ERROR("SearchTaskBase::Run OnFailure total 1 , 1 failed");
          handler_->OnFailure(-1, "total 1 , 1 failed");
          return 0;
        }
        if (secondary_->future.wait_for(std::chrono::seconds(0)) == std::future_status::ready) {
          if (secondary_->future.get()) {
            return ExecuteSuccessHandler(secondary_);
          }
          LOG_ERROR("SearchTaskBase::Run OnFailure total 2, 2 filed");
          handler_->OnFailure(-1, "total 2, 2 filed");
          return 0;
        }
      }

      std::this_thread::sleep_for(std::chrono::milliseconds(kSearchTaskIntervalMilliseconds));
    }
    if (secondary_ != nullptr &&
        secondary_->future.wait_for(std::chrono::seconds(0)) == std::future_status::ready) {
      if (secondary_->future.get()) {
        LOG_INFO("SearchTaskBase::Run::Run::ExecuteSuccessHandler secondary_");
        return ExecuteSuccessHandler(secondary_);
      }
      LOG_ERROR("SearchTaskBase::Run OnFailure total 2, 1 timeout, 1 fialed");
      handler_->OnFailure(-1, "total 2, 1 timeout, 1 fialed");
      return 0;
    }
    LOG_ERROR("SearchTaskBase::Run OnFailure total 2, 2 timeout");
    handler_->OnFailure(-1, "total 2, 2 timeout");
    return 0;
  }

  struct Context {
    std::shared_ptr<SearchEngine> engine;
    std::future<bool> future;
    ResponsePtr response;
    explicit Context(std::shared_ptr<SearchEngine> eng) : engine(std::move(eng)) {}
  };
  using ContextPtr = std::shared_ptr<Context>;

 protected:
  int task_id_;
  RequestPtr request_;
  HandlerPtr handler_;

 private:
  virtual bool DoExecute(const ContextPtr& context) {
    auto response = std::make_shared<ResponseType>();
    response->task_id = task_id_;
    const bool ret = (context->engine.get()->*engine_func_)(request_, response);
    context->response = response;
    return ret;
  }
  int ExecuteSuccessHandler(const ContextPtr& context) {
    if (!context->response || !handler_) {
      return -1;
    }
    handler_->OnSuccess(context->response);
    return 0;
  }

  void SetupContext() {
    if (request_->search_mode == SearchMode::kOnline) {
      primary_ = std::make_shared<Context>(online_engine_);
    } else if (request_->search_mode == SearchMode::kOffline) {
      primary_ = std::make_shared<Context>(offline_engine_);
    } else if (request_->search_mode == SearchMode::kOfflinePreferred) {
      primary_ = std::make_shared<Context>(offline_engine_);
      secondary_ = std::make_shared<Context>(online_engine_);
    } else {
      primary_ = std::make_shared<Context>(online_engine_);
      secondary_ = std::make_shared<Context>(offline_engine_);
    }
  }

  EngineFunc engine_func_;
  ContextPtr primary_ = nullptr;
  ContextPtr secondary_ = nullptr;
  std::shared_ptr<SearchEngine> offline_engine_;
  std::shared_ptr<SearchEngine> online_engine_;
  SearchTaskStatus status_;
  ThreadPoolPtr thread_pool_;
};

}  // namespace aurora::search
#endif