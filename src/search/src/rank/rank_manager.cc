#include "rank_manager.h"

#include <logger.h>

#include <algorithm>

#include "common/parsed_query.h"
#include "common/search_config.h"
#include "distance_calculator.h"
#include "rank/rank_policy_factory.h"
#include "retrieval/expr_tree.h"
#include "score_calculator.h"
#include "tf_idf_calculator.h"
#include "title_hit_calculator.h"

namespace aurora::search {

bool RankManager::Init(const std::shared_ptr<SearchConfig> &config) {
  if (config == nullptr) {
    LOG_ERROR("Invalid config");
    return false;
  }

  // TODO(WS): init rank manager with config
  AddCalculator(std::make_shared<TitleHitCalculator>());
  AddCalculator(std::make_shared<TfIdfCalculator>());
  AddCalculator(std::make_shared<DistanceCalculator>());
  rank_policy_ = RankPolicyFactory::Instance().Create(config->rank_policy);
  if (rank_policy_ == nullptr) {
    LOG_ERROR("Failed to create rank policy: {}", config->rank_policy);
    return false;
  }

  for (const auto &ranker : calculators_) {
    if (!ranker->Init()) {
      LOG_ERROR("Failed to init ranker: {}", ranker->name());
      return false;
    }
  }

  return true;
}

void RankManager::AddCalculator(std::shared_ptr<ScoreCalculator> calculator) {
  calculators_.emplace_back(std::move(calculator));
}

bool RankManager::RankPlaces(std::vector<RankingPlace> *ranking_places) const {
  if (ranking_places->empty()) {
    LOG_WARN("Empty rank_places: skip ranking process");
    return true;
  }
  return rank_policy_->RankPlaces(ranking_places);
}

void RankManager::CalculateScores(const std::shared_ptr<ParsedQuery> &parsed_query,
                                  const std::shared_ptr<ExprTree> &expr_tree,
                                  const TermBrief &term_brief, RankingPlace *ranking_place) const {
  for (auto &factor : calculators_) {
    factor->CalculateScore(parsed_query, expr_tree, term_brief, ranking_place);
  }
}
}  // namespace aurora::search
