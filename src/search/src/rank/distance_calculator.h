#ifndef SEARCH_SRC_RANK_DISTANCE_CALCULATOR_H
#define SEARCH_SRC_RANK_DISTANCE_CALCULATOR_H
#include "score_calculator.h"
namespace aurora::search {

class DistanceCalculator final : public ScoreCalculator {
 public:
  bool Init() override;
  void CalculateScore(const std::shared_ptr<ParsedQuery>& parsed_query,
                      const std::shared_ptr<ExprTree>& expr_tree, const TermBrief& term_brief,
                      RankingPlace* ranking_place) override;
  std::string name() const override;
};
}  // namespace aurora::search

#endif  // SEARCH_SRC_RANK_DISTANCE_CALCULATOR_H
