#include "title_hit_calculator.h"

#include <logger.h>

#include "common/parsed_query.h"
#include "common/ranking_place.h"
#include "data/invert_index/invert_index_def.h"
#include "retrieval/expr_tree.h"
#include "utils/data_operation.h"
#include "utils/signature.h"

namespace aurora::search {
bool TitleHitCalculator::Init() { return true; }

void TitleHitCalculator::CalculateScore(const std::shared_ptr<ParsedQuery>& parsed_query,
                                        const std::shared_ptr<ExprTree>& expr_tree,
                                        const TermBrief& term_brief, RankingPlace* ranking_place) {
  ranking_place->title_hit_score =
      static_cast<double>(CalculateTitleHitType(parsed_query, expr_tree, term_brief));
}

std::string TitleHitCalculator::name() const {
  static std::string name = "TitleHitCalculator";
  return name;
}
TitleHitCalculator::TitleHitType TitleHitCalculator::CalculateTitleHitType(
    const std::shared_ptr<ParsedQuery>& parsed_query, const std::shared_ptr<ExprTree>& expr_tree,
    const TermBrief& term_brief) const {
  const uint32_t name_sign = term_brief.name_sign;
  uint32_t query_sign;

  Create32BitSignature(parsed_query->query.c_str(), &query_sign);
  if (name_sign == query_sign) {
    return TitleHitType::kMatchExactly;
  }

  bool contain_all_terms = true;
  bool contain_some_terms = false;

  // TODO(WS): make it elegant and consider third level AND
  for (auto& node : expr_tree->root()->GetChildren()) {
    if (!DataOperation::TestField(node->term_info().field_flag, FieldFlagBit::kName)) {
      contain_all_terms = false;
    } else {
      contain_some_terms = true;
    }
  }
  if (contain_all_terms) {
    return TitleHitType::kContainAllTerms;
  }
  if (contain_some_terms) {
    return TitleHitType::kContainAllTerms;
  }
  return TitleHitType::kContainNoTerms;
}
}  // namespace aurora::search