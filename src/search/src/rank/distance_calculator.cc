#include "distance_calculator.h"

#include <logger.h>

#include "common/bounds.h"
#include "common/parsed_query.h"
#include "common/ranking_place.h"
#include "util.h"

namespace aurora::search {
bool DistanceCalculator::Init() { return true; }
void DistanceCalculator::CalculateScore(const std::shared_ptr<ParsedQuery>& parsed_query,
                                        const std::shared_ptr<ExprTree>& expr_tree,
                                        const TermBrief& term_brief, RankingPlace* ranking_place) {
  const auto& bounds = parsed_query->location_restriction;
  double distance =
      bounds->CalculateDistance(ranking_place->location.lng(), ranking_place->location.lat());
  distance = std::min(distance, bounds->GetMaximalAllowDistance());
  ranking_place->distance_score = 1.0 - distance / bounds->GetMaximalAllowDistance();
}

std::string DistanceCalculator::name() const {
  static std::string name = "DistanceCalculator";
  return name;
}
}  // namespace aurora::search
