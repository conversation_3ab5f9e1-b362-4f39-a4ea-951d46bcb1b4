#ifndef SEARCH_SRC_RANK_WEIGHTED_POLICY_H
#define SEARCH_SRC_RANK_WEIGHTED_POLICY_H
#include "rank_policy.h"

namespace aurora::search {
struct RankingPlace;

class WeightedPolicy final : public RankPolicy {
 public:
  ~WeightedPolicy() override;
  bool RankPlaces(std::vector<RankingPlace>* ranking_places) const override;
  static const char* StaticName() { return "weighted_policy"; }
};
}  // namespace aurora::search

#endif  // SEARCH_SRC_RANK_WEIGHTED_POLICY_H
