#ifndef SEARCH_SRC_RANK_TITLE_HIT_CALCULATOR_H
#define SEARCH_SRC_RANK_TITLE_HIT_CALCULATOR_H

#include "score_calculator.h"

namespace aurora::search {

class TitleHitCalculator final : public ScoreCalculator {
 public:
  enum class TitleHitType {
    kContainNoTerms = 0,   // title contains no query terms
    kContainPartialTerms,  // title contains partial query terms
    kContainAllTerms,      // title contains all query terms
    kMatchExactly,         // title match query exactly
  };
  bool Init() override;
  void CalculateScore(const std::shared_ptr<ParsedQuery>& parsed_query,
                      const std::shared_ptr<ExprTree>& expr_tree, const TermBrief& term_brief,
                      RankingPlace* ranking_place) override;
  std::string name() const override;

 private:
  TitleHitType CalculateTitleHitType(const std::shared_ptr<ParsedQuery>& parsed_query,
                                     const std::shared_ptr<ExprTree>& expr_tree,
                                     const TermBrief& term_brief) const;
};

}  // namespace aurora::search
#endif  // SEARCH_SRC_RANK_TITLE_HIT_CALCULATOR_H
