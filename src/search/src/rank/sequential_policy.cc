#include "sequential_policy.h"

#include <algorithm>

#include "rank_policy_factory.h"
namespace aurora::search {
SequentialPolicy::~SequentialPolicy() = default;
bool SequentialPolicy::RankPlaces(std::vector<RankingPlace>* ranking_places) const {
  if (!ranking_places || ranking_places->empty()) return false;
  std::sort(ranking_places->begin(), ranking_places->end(),
            [](const RankingPlace& lhs, const RankingPlace& rhs) {
              // If both have title_hit_score == 0, sort by address_tf_idf_score
              // then distance_score
              if (lhs.title_hit_score == 0.0 && rhs.title_hit_score == 0.0) {
                if (lhs.address_tf_idf_score != rhs.address_tf_idf_score)
                  return lhs.address_tf_idf_score > rhs.address_tf_idf_score;
                return lhs.distance_score > rhs.distance_score;
              }
              if (lhs.title_hit_score != rhs.title_hit_score)
                return lhs.title_hit_score > rhs.title_hit_score;
              if (lhs.name_tf_idf_score != rhs.name_tf_idf_score)
                return lhs.name_tf_idf_score > rhs.name_tf_idf_score;
              return lhs.distance_score > rhs.distance_score;
            });
  return true;
}
}  // namespace aurora::search

REGISTER_RANK_POLICY(SequentialPolicy)