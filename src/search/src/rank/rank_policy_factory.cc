#include "rank_policy_factory.h"

namespace aurora::search {
RankPolicyFactory& RankPolicyFactory::Instance() {
  static RankPolicyFactory instance;
  return instance;
}

void RankPolicyFactory::Register(const std::string& name, Creator creator) {
  creators_[name] = std::move(creator);
}

std::shared_ptr<RankPolicy> RankPolicyFactory::Create(const std::string& name) const {
  auto it = creators_.find(name);
  if (it != creators_.end()) {
    return it->second();
  }
  return nullptr;
}
}  // namespace aurora::search
