#include "tf_idf_calculator.h"

#include <logger.h>

#include "common/ranking_place.h"
#include "retrieval/expr_tree.h"
#include "utils/data_operation.h"
namespace aurora::search {
bool TfIdfCalculator::Init() { return true; }
void TfIdfCalculator::CalculateScore(const std::shared_ptr<ParsedQuery>& parsed_query,
                                     const std::shared_ptr<ExprTree>& expr_tree,
                                     const TermBrief& term_brief, RankingPlace* ranking_place) {
  // TODO(WS): make it elegant and consider third level AND
  for (auto& node : expr_tree->root()->GetChildren()) {
    const auto term_info = node->term_info();
    if (DataOperation::TestField(term_info.field_flag, FieldFlagBit::kName)) {
      ranking_place->name_tf_idf_score += term_info.weight_name;
    }
    if (DataOperation::TestField(term_info.field_flag, FieldFlagBit::kAddress)) {
      ranking_place->address_tf_idf_score += term_info.weight_address;
    }
  }
}

std::string TfIdfCalculator::name() const {
  static std::string name = "TfIdfCalculator";
  return name;
}
}  // namespace aurora::search
