#include "weighted_policy.h"

#include <algorithm>

#include "rank_policy_factory.h"
namespace aurora::search {
WeightedPolicy::~WeightedPolicy() = default;
bool WeightedPolicy::RankPlaces(
    std::vector<RankingPlace> *ranking_places) const {
  std::sort(ranking_places->begin(), ranking_places->end(),
            [](const RankingPlace &lhs, const RankingPlace &rhs) {
              return lhs.name_tf_idf_score > rhs.name_tf_idf_score;
            });
  return true;
}
}  // namespace aurora::search
REGISTER_RANK_POLICY(WeightedPolicy);
