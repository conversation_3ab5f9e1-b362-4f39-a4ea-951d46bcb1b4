#ifndef SEARCH_SRC_RANK_RANK_POLICY_FACTORY_H
#define SEARCH_SRC_RANK_RANK_POLICY_FACTORY_H
#include <functional>
#include <memory>

#include "rank_policy.h"

namespace aurora::search {
class RankPolicyFactory {
 public:
  using Creator = std::function<std::shared_ptr<RankPolicy>()>;

  static RankPolicyFactory& Instance();

  void Register(const std::string& name, Creator creator);

  std::shared_ptr<RankPolicy> Create(const std::string& name) const;

 private:
  std::unordered_map<std::string, Creator> creators_;
};
}  // namespace aurora::search

// Macro for registration
#define REGISTER_RANK_POLICY(POLICY_CLASS)                                    \
  namespace {                                                                 \
  struct POLICY_CLASS##Register {                                             \
    POLICY_CLASS##Register() {                                                \
      aurora::search::RankPolicyFactory::Instance().Register(                 \
          aurora::search::POLICY_CLASS::StaticName(),                         \
          []() { return std::make_shared<aurora::search::POLICY_CLASS>(); }); \
    }                                                                         \
  };                                                                          \
  static POLICY_CLASS##Register global_##POLICY_CLASS##_register;             \
  }

#endif  // SEARCH_SRC_RANK_RANK_POLICY_FACTORY_H
