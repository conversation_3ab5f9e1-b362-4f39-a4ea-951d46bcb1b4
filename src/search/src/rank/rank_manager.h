#ifndef SEARCH_SRC_RANK_RANKER_MANAGER_H
#define SEARCH_SRC_RANK_RANKER_MANAGER_H

#include <memory>
#include <vector>

namespace aurora::search {
class ExprTree;
struct ParsedQuery;
class RankPolicy;
struct RankingPlace;
struct RequestContext;
class ScoreCalculator;
struct SearchConfig;
struct TermBrief;

class RankManager {
 public:
  RankManager() = default;
  bool Init(const std::shared_ptr<SearchConfig>& config);
  bool RankPlaces(std::vector<RankingPlace>* ranking_places) const;
  void CalculateScores(const std::shared_ptr<ParsedQuery>& parsed_query,
                       const std::shared_ptr<ExprTree>& expr_tree, const TermBrief& term_brief,
                       RankingPlace* ranking_place) const;

 private:
  void AddCalculator(std::shared_ptr<ScoreCalculator> calculator);
  std::vector<std::shared_ptr<ScoreCalculator>> calculators_;
  std::shared_ptr<RankPolicy> rank_policy_;
};
using RankManagerPtr = std::shared_ptr<RankManager>;
}  // namespace aurora::search

#endif  // SEARCH_SRC_RANKER_RANKER_MANAGER_H