#ifndef SEARCH_SRC_RANK_SCORE_CALCULATOR_H
#define SEARCH_SRC_RANK_SCORE_CALCULATOR_H

#include <memory>
#include <string>

namespace aurora::search {
class ExprTree;
struct ParsedQuery;
struct TermBrief;
struct RankingPlace;

class ScoreCalculator {
 public:
  ScoreCalculator() = default;
  virtual ~ScoreCalculator() = default;
  virtual bool Init() = 0;
  virtual void CalculateScore(const std::shared_ptr<ParsedQuery>& parsed_query,
                              const std::shared_ptr<ExprTree>& expr_tree,
                              const TermBrief& term_brief, RankingPlace* ranking_place) = 0;

  virtual std::string name() const = 0;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_RANK_SCORE_CALCULATOR_H
