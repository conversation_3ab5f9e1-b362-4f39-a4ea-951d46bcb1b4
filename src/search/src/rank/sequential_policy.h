#ifndef SEARCH_SRC_RANK_SEQUENTIAL_POLICY_H
#define SEARCH_SRC_RANK_SEQUENTIAL_POLICY_H
#include "rank_policy.h"

namespace aurora::search {
class SequentialPolicy final : public RankPolicy {
 public:
  ~SequentialPolicy() override;
  bool RankPlaces(std::vector<RankingPlace>* ranking_places) const override;
  static const char* StaticName() { return "sequential_policy"; }
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_RANK_SEQUENTIAL_POLICY_H
