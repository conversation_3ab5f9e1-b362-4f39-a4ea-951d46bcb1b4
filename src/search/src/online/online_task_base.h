#ifndef SEARCH_SRC_ONLINE_ONLINE_TASK_BASE_H
#define SEARCH_SRC_ONLINE_ONLINE_TASK_BASE_H

#include <memory>
#include <string>

#include "common/bounds.h"
namespace aurora::search {
struct SearchConfig;

struct SearchOnlineRequestBase {
  using Ptr = std::shared_ptr<SearchOnlineRequestBase>;
  std::string model_type;
  SearchOnlineRequestBase() : model_type("fast_mode") {}
  virtual ~SearchOnlineRequestBase() = default;
};

struct SearchOnlineQueryResponse : SearchOnlineRequestBase {
  using Ptr = std::shared_ptr<SearchOnlineQueryResponse>;
  std::string query;
  ~SearchOnlineQueryResponse() override = default;
  virtual std::string MakeBody() = 0;
};

class OnlineTaskBase {
 public:
  virtual ~OnlineTaskBase() = default;
  virtual int Execute() = 0;

  std::shared_ptr<SearchConfig> config_;
};
}  // namespace aurora::search

#endif