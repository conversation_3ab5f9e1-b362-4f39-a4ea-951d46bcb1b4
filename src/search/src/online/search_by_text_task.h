#ifndef SEARCH_SRC_ONLINE_SEARCH_BY_TEXT_TASK_H
#define SEARCH_SRC_ONLINE_SEARCH_BY_TEXT_TASK_H

#include <memory>

#include "online_task_base.h"
#include "search_def.h"

namespace aurora::search {

struct SearchByTextOnlineRequest final : SearchOnlineQueryResponse {
  using Ptr = std::shared_ptr<SearchByTextOnlineRequest>;
  int top_k;
  BoundsInternalPtr bounds;

  std::string MakeBody() override;
};

class SearchByTextTask final : public OnlineTaskBase {
 public:
  SearchByTextTask();
  ~SearchByTextTask() override;
  int Execute() override;
  SearchByTextRequestPtr request;
  SearchByTextResponsePtr response;

 private:
  bool ParseResponse(const std::string& jsonStr) const;
};

}  // namespace aurora::search

#endif