#include "search_by_text_task.h"

#include <logger.h>
#include <rapidjson/stringbuffer.h>
#include <rapidjson/writer.h>

#include <string>

#include "common/search_config.h"
#include "httplib.h"
#include "online_def.h"
#include "utils/json_utils.h"

using namespace std;

namespace aurora::search {

std::string SearchByTextOnlineRequest::MakeBody() {
  rapidjson::Document doc;
  doc.SetObject();
  auto& allocator = doc.GetAllocator();
  doc.AddMember("query", rapidjson::Value(query.c_str(), allocator), allocator);
  doc.AddMember("top_k", top_k, allocator);
  doc.AddMember("model_type", rapidjson::Value(model_type.c_str(), allocator), allocator);
  if (bounds->type() == BoundsInternal::Type::kCircular) {
    auto circular_bounds = std::dynamic_pointer_cast<CircularBounds>(bounds);
    if (circular_bounds) {
      const PointLL& center = circular_bounds->center();
      rapidjson::Value lng_lat_v(rapidjson::kArrayType);
      lng_lat_v.PushBack(center.lng(), doc.GetAllocator());
      lng_lat_v.PushBack(center.lat(), doc.GetAllocator());
      doc.AddMember("lng_lat", lng_lat_v, allocator);
    }
  }
  rapidjson::StringBuffer buffer;
  rapidjson::Writer writer(buffer);
  doc.Accept(writer);
  return buffer.GetString();
}

SearchByTextTask::SearchByTextTask() = default;
SearchByTextTask::~SearchByTextTask() = default;

int SearchByTextTask::Execute() {
  if (config_ == nullptr) {
    LOG_ERROR("SearchByTextTask config is null");
    return -1;
  }
  httplib::Client cli(config_->online_engine_host, config_->online_engine_port);
  const auto req = std::make_shared<SearchByTextOnlineRequest>();
  req->query = request->query;
  req->top_k = request->page_size;
  if (request->location_restriction != nullptr) {
    req->bounds = std::dynamic_pointer_cast<BoundsInternal>(request->location_restriction);
  }

  const std::string body = req->MakeBody();
  LOG_DEBUG("request body: {}", body.c_str());
  const auto res = cli.Post(kSearchByTextEndpoint, body, kContentType);
  if (res && res->status == kHttpSuccessStatus && ParseResponse(res->body)) {
    LOG_DEBUG("response Execute: ------------ kHttpSuccessStatus {} ", kHttpSuccessStatus);
    return 0;
  }
  LOG_DEBUG("response Execute: 55555 ");
  return -1;
}

bool SearchByTextTask::ParseResponse(const std::string& jsonStr) const {
  using namespace rapidjson;
  Document doc;
  doc.Parse(jsonStr.c_str());
  if (doc.HasParseError()) {
    LOG_ERROR("parse json error");
    return false;
  }
  if (!doc.IsArray()) {
    LOG_ERROR("doc root is not array");
    return false;
  }
  for (int i = 0; i < doc.Size(); i++) {
    const Value& element = doc[i];
    if (!element.IsObject()) {
      LOG_ERROR("element[{}] is not object", i);
      continue;
    }

    PlaceBrief brief;
    brief.id = JsonUtils::ParseString(element, "poi_id");
    brief.name = JsonUtils::ParseString(element, "name");
    brief.address = JsonUtils::ParseString(element, "addr");
    brief.category = JsonUtils::ParseString(element, "poi_type");
    std::string x = JsonUtils::ParseString(element, "x_coord");
    std::string y = JsonUtils::ParseString(element, "y_coord");
    try {
      brief.location = PointLL(std::stod(x), std::stod(y));
    } catch (const std::exception& e) {
      LOG_ERROR("Parse poi location error: {}", e.what());
      continue;
    }
    response->place_briefs.push_back(brief);
  }

  return true;
}

}  // namespace aurora::search
