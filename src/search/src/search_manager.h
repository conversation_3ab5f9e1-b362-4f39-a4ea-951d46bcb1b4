
#ifndef SEARCH_SRC_SEARCH_MANAGER_H
#define SEARCH_SRC_SEARCH_MANAGER_H

#include <atomic>
#include <memory>

#include "search_def.h"
namespace aurora::search {
struct SearchConfig;
struct SearchEngine;
struct SearchTaskManager;

class SearchManager {
 public:
  using Ptr = std::shared_ptr<SearchManager>;
  SearchManager();
  ~SearchManager();

  int Init(const std::string &search_dir);

  int Destroy();

  int SearchByText(const SearchByTextRequestPtr &request,
                   const SearchByTextResponseHandlerPtr &handler);

  int GetDetail(const GetDetailRequestPtr &request, const GetDetailResponseHandlerPtr &handler);

  int Autocomplete(const AutocompleteRequestPtr &request,
                   const AutocompleteResponseHandlerPtr &handler);

  int ReverseGeocode(const ReverseGeocodeRequestPtr &request,
                     const ReverseGeocodeResponseHandlerPtr &handler);

 private:
  bool CheckEnv(const SearchRequestBasePtr &request, const ResponseHandlerBasePtr &handler);

  std::shared_ptr<SearchTaskManager> search_task_manager_;
  std::shared_ptr<SearchEngine> search_offline_engine_;
  std::shared_ptr<SearchEngine> search_online_engine_;
  std::shared_ptr<SearchConfig> search_config_;

  std::atomic<bool> is_init_{false};
};

}  // namespace aurora::search

#endif