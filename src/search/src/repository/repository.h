#ifndef SEARCH_SRC_REPOSITORY_REPOSITORY_H
#define SEARCH_SRC_REPOSITORY_REPOSITORY_H

#include <memory>

#include "search_def.h"

namespace aurora::search {
struct DataFolderLayout;
struct PlaceDetail;
class PlaceTable;
class AutocompleteRetriever;
struct SearchConfig;

class Repository final {
 public:
  using Ptr = std::shared_ptr<Repository>;
  Repository() = default;
  ~Repository() = default;

  bool Init(const std::shared_ptr<SearchConfig>& config);
  bool AutocompleteSearch(const AutocompleteRequestPtr& request,
                          const AutocompleteResponsePtr& response);

 private:
  std::shared_ptr<PlaceTable> detail_data_;
  std::shared_ptr<AutocompleteRetriever> autocomplete_retriever_;
  std::shared_ptr<DataFolderLayout> build_config_info_;
};
}  // namespace aurora::search
#endif  // SEARCH_SRC_REPOSITORY_REPOSITORY_H