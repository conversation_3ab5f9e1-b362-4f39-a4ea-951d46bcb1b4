#include "repository.h"

#include <logger.h>

#include <magic_enum/magic_enum.hpp>

#include "../retrieval/autocomplete_retriever.h"
#include "common/data_folder_layout.h"
#include "common/search_internal_def.h"
#include "data/place_table/place_table.h"
#include "utils/data_service_wrapper.h"

namespace aurora::search {

bool Repository::Init(const std::shared_ptr<SearchConfig>& config) {
  if (config == nullptr) {
    LOG_ERROR("Invalid config");
    return false;
  }
  build_config_info_ = std::make_shared<DataFolderLayout>();
  const std::string city_code = DataService::Instance().GetCityCode();
  detail_data_ = std::make_shared<PlaceTable>(config, city_code);
  if (!detail_data_->Load()) {
    return false;
  }

  autocomplete_retriever_ = std::make_shared<AutocompleteRetriever>();
  if (autocomplete_retriever_->Init(config, detail_data_) != 0) {
    return false;
  }
  return true;
}

bool Repository::AutocompleteSearch(const AutocompleteRequestPtr& request,
                                    const AutocompleteResponsePtr& response) {
  if (autocomplete_retriever_ == nullptr || request == nullptr || response == nullptr) {
    return false;
  }
  return autocomplete_retriever_->Extend(request, response);
}
}  // namespace aurora::search