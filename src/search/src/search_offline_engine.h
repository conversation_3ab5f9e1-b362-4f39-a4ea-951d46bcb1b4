#ifndef SEARCH_SRC_SEARCH_OFFLINE_ENGINE_H
#define SEARCH_SRC_SEARCH_OFFLINE_ENGINE_H
#include <memory>

#include "data/data_def.h"
#include "include/search_def.h"
#include "search_engine.h"

namespace aurora::search {
class DataManager;
class SearchByTextRetriever;
struct RankingPlace;
class Repository;
class RankManager;
struct ParsedQuery;
class QueryAnalyzer;
class SearchOfflineEngine final : public SearchEngine {
 public:
  using Ptr = std::shared_ptr<SearchOfflineEngine>;
  bool Init(const std::shared_ptr<SearchConfig>& config) override;
  bool SearchByText(const SearchByTextRequestPtr& request,
                    const SearchByTextResponsePtr& response) override;

  bool GetDetail(const GetDetailRequestPtr& request, const GetDetailResponsePtr& response) override;

  bool Autocomplete(const AutocompleteRequestPtr& request,
                    const AutocompleteResponsePtr& response) override;

  bool ReverseGeocode(const ReverseGeocodeRequestPtr& request,
                      const ReverseGeocodeResponsePtr& response) override;

 private:
  void UpdateCurrentLocation(const SearchRequestBasePtr& request) const;

  std::vector<std::shared_ptr<SearchByTextRetriever>> AssembleSearchByTextRetrievers(
      const std::shared_ptr<ParsedQuery>& parsed_query);
  int DecideMaxRetrievalCount(CityType city_type) const;
  void PaginatePlaces(int page_num, int page_size, std::vector<RankingPlace>* ranking_places) const;

  std::shared_ptr<QueryAnalyzer> query_analyzer_;
  std::shared_ptr<DataManager> data_manager_;
  std::shared_ptr<Repository> repository_;
  std::shared_ptr<RankManager> rank_manager_;
};
}  // namespace aurora::search
#endif  // SEARCH_SRC_SEARCH_OFFLINE_ENGINE_H