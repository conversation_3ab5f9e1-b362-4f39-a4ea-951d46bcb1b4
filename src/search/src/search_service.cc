
#include "search_service.h"

#include <logger.h>

#include "magic_enum/magic_enum.hpp"
#include "search_manager.h"
#include "spdlog/stopwatch.h"
#include "utils/string_utils.h"

namespace aurora::search {

SearchService::SearchService() { search_manager_ = std::make_shared<SearchManager>(); }
SearchService::~SearchService() = default;

int SearchService::Init(const std::string &config) {
  spdlog::stopwatch sw;
  if (search_manager_->Init(config)) {
    LOG_ERROR("Init failed with: {}", config);
    return -1;
  }
  LOG_INFO("SearchService init success, take {} seconds: {}", sw, config);
  return 0;
}

int SearchService::Destroy() { return 0; }

int SearchService::SearchByText(const SearchByTextRequestPtr &request,
                                const SearchByTextResponseHandlerPtr &handler) const {
  if (!CheckEnv(request, handler)) {
    return -1;
  }
  LOG_INFO("SearchByText: query{}, mode:{}", request->query,
           magic_enum::enum_name(request->search_mode));
  return search_manager_->SearchByText(request, handler);
}

int SearchService::GetDetail(const GetDetailRequestPtr &request,
                             const GetDetailResponseHandlerPtr &handler) const {
  if (!CheckEnv(request, handler)) {
    return -1;
  }
  LOG_INFO("GetDetail: place_ids{}, mode:{}",
           StringUtils::Join(request->place_ids.begin(), request->place_ids.end(), ","),
           magic_enum::enum_name(request->search_mode));
  return search_manager_->GetDetail(request, handler);
}

int SearchService::Autocomplete(const AutocompleteRequestPtr &request,
                                const AutocompleteResponseHandlerPtr &handler) const {
  if (!CheckEnv(request, handler)) {
    return -1;
  }
  LOG_INFO("Autocomplete: query = {}, mode = {}", request->query,
           magic_enum::enum_name(request->search_mode));
  return search_manager_->Autocomplete(request, handler);
}

int SearchService::ReverseGeocode(const ReverseGeocodeRequestPtr &request,
                                  const ReverseGeocodeResponseHandlerPtr &handler) const {
  if (!CheckEnv(request, handler)) {
    return -1;
  }
  LOG_INFO("ReverseGeocode: lng: {}, lat: {}, search_mode: {}", request->location.lng(),
           request->location.lat(), magic_enum::enum_name(request->search_mode));
  return search_manager_->ReverseGeocode(request, handler);
}

bool SearchService::CheckEnv(const SearchRequestBasePtr &request,
                             const ResponseHandlerBasePtr &handler) const {
  if (request == nullptr || handler == nullptr) {
    LOG_ERROR("Request and handler must not be empty.");
    return false;
  }
  return true;
}

}  // namespace aurora::search
