#include "search_by_text_retriever.h"

#include <logger.h>

#include <magic_enum/magic_enum.hpp>

#include "common/bounds.h"
#include "common/parsed_query.h"
#include "common/ranking_place.h"
#include "data/invert_index/invert_index.h"
#include "data/place_table/place_table.h"
#include "rank/rank_manager.h"
#include "retrieval/expr_tree.h"
#include "search/src/utils/data_operation.h"
#include "search/src/utils/string_utils.h"

namespace aurora::search {
namespace {
bool ShortenChainFromOneSide(const std::shared_ptr<ExprTree>& expr_tree,
                             const std::vector<std::string>& geo_cells, bool from_head) {
  if (geo_cells.empty()) {
    return true;
  }

  int pos = from_head ? 0 : static_cast<int>(geo_cells.size()) - 1;
  const int step = from_head ? 1 : -1;

  // find the first or last geo cell that intersects all the chains
  bool found = false;
  while (0 <= pos && pos < geo_cells.size()) {
    if (expr_tree->Intersect(geo_cells[pos])) {
      found = true;
      break;
    }
    pos += step;
  }

  if (!found) {
    return false;
  }
  expr_tree->ShortenChainFromOneSide(geo_cells[pos], from_head);
  return true;
}

uint32_t FindNextBriefId(const std::shared_ptr<ExprTree>& expr_tree, uint32_t brief_id) {
  uint32_t candidate = expr_tree->AdvanceReferenceChainToTargetId(brief_id);

  if (candidate == kInvalidBriefId) {
    // no valid element in reference chain, the searching process is finished
    return kInvalidBriefId;
  }

  uint32_t next_possible_brief_id = expr_tree->AdvanceToTargetId(candidate);

  if (next_possible_brief_id == kInvalidBriefId) {
    // no valid element in expr tree, the searching process is finished
    return kInvalidBriefId;
  }

  if (next_possible_brief_id == candidate) {
    return next_possible_brief_id;
  }
  // retry with next_possible_brief_id
  return FindNextBriefId(expr_tree, next_possible_brief_id);
}

uint32_t FindNextCommonGeoCell(const std::shared_ptr<ExprTree>& expr_tree,
                               const std::vector<std::string>& geo_cells, uint32_t* geo_idx,
                               std::string* geo_cell) {
  // iterate geo_cells until geo_cells[geo_idx] >= geo_cell
  while (*geo_idx < geo_cells.size() && geo_cells[*geo_idx].compare(*geo_cell) < 0) {
    ++(*geo_idx);
  }

  if (*geo_idx >= geo_cells.size()) {
    return kInvalidBriefId;
  }

  geo_cell->assign(geo_cells[*geo_idx]);
  ++(*geo_idx);

  auto [intersect, next_cell] = expr_tree->TryIntersect(*geo_cell);
  if (intersect) {
    return expr_tree->RestChainByGeoCell(*geo_cell);
  }
  if (!next_cell.has_value()) {
    return kInvalidBriefId;
  }

  geo_cell->assign(*next_cell);

  return FindNextCommonGeoCell(expr_tree, geo_cells, geo_idx, geo_cell);
}

}  // namespace

SearchByTextRetriever::SearchByTextRetriever(const SearchByTextDataSource& data_source,
                                             std::shared_ptr<RankManager> rank_manager,
                                             int level_one_pruning_threshold,
                                             int level_two_pruning_threshold,
                                             int max_retrieval_count)
    : city_code_(data_source.city_code),
      city_type_(data_source.city_type),
      info_(fmt::format("[{}: {}]", city_code_, magic_enum::enum_name(city_type_))),
      invert_index_(data_source.invert_index),
      detail_data_(data_source.place_table),
      rank_manager_(std::move(rank_manager)),
      level_one_pruning_threshold_(level_one_pruning_threshold),
      level_two_pruning_threshold_(level_two_pruning_threshold),
      max_retrieval_count_(max_retrieval_count) {}

void SearchByTextRetriever::RetrievePlaces(const std::shared_ptr<ParsedQuery>& parsed_query,
                                           std::vector<RankingPlace>* ranking_places) {
  const auto expr_tree = BuildExprTree(parsed_query);
  if (expr_tree == nullptr) {
    LOG_INFO("{} Failed to build expr tree.", info_);
    return;
  }
  expr_tree->Display();

  if (!RetrieveByExprTree(parsed_query, expr_tree, ranking_places)) {
    LOG_INFO("{} Retrieval failed!", info_);
    return;
  }

  LOG_DEBUG("{} RetrievePlaces finished, max_retrieval_count: {}, result_num: {}", info_,
            max_retrieval_count_, ranking_places->size());
}

PlaceBrief SearchByTextRetriever::GetPlaceBrief(uint32_t brief_id) const {
  PlaceBrief place_brief;
  const TermBrief* brief = invert_index_->GetTermBrief(brief_id);
  place_brief.id = std::to_string(brief->uid);
  place_brief.location = PointLL(brief->lng, brief->lat);
  place_brief.category = DataOperation::ToExternalCategory(brief->category);
  auto detail = detail_data_->GetDetail(brief->uid);
  if (detail != nullptr) {
    place_brief.name = detail->GetName();
    place_brief.address = detail->GetAddress();
  }
  return place_brief;
}

std::shared_ptr<ExprTree> SearchByTextRetriever::BuildExprTree(
    const std::shared_ptr<ParsedQuery>& parsed_query) const {
  if (parsed_query->terms.empty()) {
    LOG_WARN("No keywords or categories");
    return nullptr;
  }

  auto expr_tree = std::make_shared<ExprTree>();
  for (auto& term : parsed_query->terms) {
    auto node = CreateNode(term);
    if (node == nullptr) {
      LOG_INFO("Term not found");
      return nullptr;
    }
    expr_tree->AddNode(node);
  }

  const auto& categories = parsed_query->include_categories;
  if (!categories.empty()) {
    std::vector<std::string> category_terms;
    category_terms.reserve(categories.size());
    for (uint32_t category : categories) {
      category_terms.emplace_back(GetCategoryTerm(category));
    }
    auto or_node = CreateOrNode(category_terms);

    if (or_node != nullptr) {
      expr_tree->AddNode(or_node);
    } else {
      LOG_WARN("All categories missing: {}",
               StringUtils::Join(categories.begin(), categories.end(), ","));
      return nullptr;
    }
  }
  expr_tree->Fixup();
  return expr_tree;
}

std::shared_ptr<ExprTreeNode> SearchByTextRetriever::CreateNode(const Term& term) const {
  if (term.synonyms.empty()) {
    return CreateLeafNode(term.text);
  }
  return CreateOrNode(term.synonyms);
}

std::shared_ptr<ExprTreeNode> SearchByTextRetriever::CreateOrNode(
    const std::vector<std::string>& terms) const {
  const auto or_node = std::make_shared<ExprTreeOrNode>();
  bool child_added = false;
  for (auto& term : terms) {
    auto node = CreateLeafNode(term);
    if (node != nullptr) {
      or_node->AddChildren(node);
      child_added = true;
    }
  }
  return child_added ? or_node : nullptr;
}

std::shared_ptr<ExprTreeNode> SearchByTextRetriever::CreateLeafNode(const std::string& term) const {
  const TermIndex* term_index = invert_index_->GetIndexByTerm(term);
  if (term_index == nullptr) {
    LOG_WARN("{} Term not found: {}", info_, term);
    return nullptr;
  }
  const TermData* term_data = invert_index_->LocateTermDataByIndex(*term_index);
  const GeoTrieNode* geo_trie = invert_index_->LocateGeoTrieByIndex(*term_index);
  return std::make_shared<ExprTreeLeafNode>(term, term_index, term_data, geo_trie);
}

bool SearchByTextRetriever::RetrieveByExprTree(const std::shared_ptr<ParsedQuery>& parsed_query,
                                               const std::shared_ptr<ExprTree>& expr_tree,
                                               std::vector<RankingPlace>* ranking_places) {
  // get the shortest chain index
  const auto reference_chain = expr_tree->GetReferenceChain();
  if (!reference_chain || reference_chain->chain_len() == 0) {
    LOG_WARN("No valid reference chain found");
    return false;
  }

  const auto& bounds = parsed_query->location_restriction;
  std::vector<std::string> geo_cells;
  bounds->CalculateGeohash(&geo_cells);
  const auto pruning_level = DetermineGeoPruningLevel(reference_chain, !geo_cells.empty());
  LOG_INFO("Geo pruning level: {}", magic_enum::enum_name(pruning_level));

  // shorten term chain from first and end geo cells
  if (pruning_level == GeoPruningLevel::kOne) {
    if (!ShortenChainFromOneSide(expr_tree, geo_cells, true) ||
        !ShortenChainFromOneSide(expr_tree, geo_cells, false)) {
      return ranking_places;
    }
  }

  if (pruning_level != GeoPruningLevel::kTwo) {
    uint32_t brief_id = 0;
    while (ranking_places->size() < max_retrieval_count_) {
      uint32_t next_brief_id = FindNextBriefId(expr_tree, brief_id);
      if (next_brief_id == kInvalidBriefId) {
        break;
      }
      brief_id = next_brief_id;
      // found a common brief_id in all the chains
      AddOneResult(expr_tree, parsed_query, brief_id, ranking_places);
      ++brief_id;
    }
  } else {
    uint32_t brief_id = kInvalidBriefId;
    uint32_t geo_idx = 0;
    std::string cur_geo_cell;
    while (ranking_places->size() < max_retrieval_count_) {
      if (brief_id == kInvalidBriefId) {
        // invalid brief_id first time, try update with geo trie
        brief_id = FindNextCommonGeoCell(expr_tree, geo_cells, &geo_idx, &cur_geo_cell);
      }

      if (brief_id == kInvalidBriefId) {
        // still invalid, break the loop
        break;
      }

      uint32_t next_brief_id = FindNextBriefId(expr_tree, brief_id);
      if (next_brief_id != kInvalidBriefId) {
        // found a common brief_id in all the chains
        brief_id = next_brief_id;
        AddOneResult(expr_tree, parsed_query, brief_id, ranking_places);
        ++brief_id;
      } else {
        // current geo cell is used up,
        // set brief_id to invalid, to move to next geo cell
        brief_id = kInvalidBriefId;
        LOG_DEBUG("Finish {} with result_num: {}", cur_geo_cell, ranking_places->size());
      }
    }
  }

  return ranking_places;
}

SearchByTextRetriever::GeoPruningLevel SearchByTextRetriever::DetermineGeoPruningLevel(
    const std::shared_ptr<ExprTreeNode>& reference_chain, bool has_geo_cells) const {
  if (has_geo_cells) {
    if (reference_chain->chain_len() >= level_two_pruning_threshold_) {
      return GeoPruningLevel::kTwo;
    }
    if (reference_chain->chain_len() >= level_one_pruning_threshold_) {
      return GeoPruningLevel::kOne;
    }
  }

  return GeoPruningLevel::kZero;
}

void SearchByTextRetriever::AddOneResult(const std::shared_ptr<ExprTree>& expr_tree,
                                         const std::shared_ptr<ParsedQuery>& parsed_query,
                                         uint32_t brief_id,
                                         std::vector<RankingPlace>* ranking_places) {
  const TermBrief* term_brief = invert_index_->GetTermBrief(brief_id);
  // location restriction doesn't affect national city
  bool is_in_bound =
      city_type_ == CityType::kNational ||
      parsed_query->location_restriction->IsInBounds(term_brief->lng, term_brief->lat);

  if (is_in_bound && parsed_query->exclude_categories.count(term_brief->category) == 0) {
    expr_tree->UpdateWeight();
    auto& ranking_place = ranking_places->emplace_back(
        shared_from_this(), brief_id, term_brief->category, term_brief->lng, term_brief->lat);
    rank_manager_->CalculateScores(parsed_query, expr_tree, *term_brief, &ranking_place);
  }
}

}  // namespace aurora::search
