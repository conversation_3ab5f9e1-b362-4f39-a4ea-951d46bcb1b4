#include "autocomplete_retriever.h"

#include <logger.h>

#include <fstream>
#include <iostream>
#include <string>

#include "autocomplete/mixed_strategy.h"
#include "common/data_folder_layout.h"
#include "common/search_config.h"
#include "data/place_table/place_table.h"
#include "data/prefix_index/prefix_index.h"
#include "utils/data_service_wrapper.h"
#include "utils/string_utils.h"
namespace aurora::search {
struct MatchedPlace {
  PlaceBrief place_brief;
  std::string pinyin;
  uint32_t weight = 0;    // 权值
  uint32_t hit_deep = 0;  // 命中汉字前缀长度
};

int AutocompleteRetriever::Init(std::shared_ptr<SearchConfig> config,
                                std::shared_ptr<PlaceTable> detail_data) {
  strategy_ = std::make_shared<MixedSearch>();
  config_ = std::move(config);
  place_table_ = std::move(detail_data);

  std::string city = DataService::Instance().GetCityCode();

  prefix_index_ = std::make_shared<PrefixIndex>(config_, city);
  prefix_index_->Load();
  return 0;
}

bool AutocompleteRetriever::Extend(const AutocompleteRequestPtr& request,
                                   const AutocompleteResponsePtr& response) {
  std::vector<IndexResult> index_result;
  strategy_->Extend(prefix_index_, request->query.c_str(), &index_result);
  std::vector<MatchedPlace> matched_places;
  ToMatchedPlaces(index_result, &matched_places);
  SortMatchedPlaces(request->query, &matched_places);
  uint32_t result_size =
      matched_places.size() > request->max_results ? request->max_results : matched_places.size();

  response->place_briefs.reserve(result_size);
  for (uint32_t i = 0; i < result_size; ++i) {
    response->place_briefs.emplace_back(std::move(matched_places[i].place_brief));
  }
  return true;
}

bool AutocompleteRetriever::ToMatchedPlaces(const std::vector<IndexResult>& index_results,
                                            std::vector<MatchedPlace>* matched_places) {
  const TrieData* trie_data = prefix_index_->trie_data();
  uint32_t total = 0;
  const TrieIndex* trie_indexs[index_results.size()];

  // record the total
  for (uint32_t i = 0; i < index_results.size(); ++i) {
    trie_indexs[i] = trie_data->GetTrieIndex(index_results[i]);
    if (trie_indexs[i] == nullptr) {
      return false;
    }
    total += trie_indexs[i]->poi_len;
  }

  bool need_adjust = total > max_count_sort_;
  matched_places->reserve(max_count_sort_);

  // collect the poi
  for (uint32_t i = 0; i < index_results.size(); ++i) {
    uint32_t back_count = trie_indexs[i]->poi_len;
    if (need_adjust) {
      back_count = max_count_sort_ * trie_indexs[i]->poi_len / total;
    }
    std::vector<std::string> fields;
    for (uint32_t j = 0; j < back_count; ++j) {
      uint32_t offset = prefix_index_->TempGetMappingOffset(trie_indexs[i]->poi_off + j);

      std::shared_ptr<PlaceDetailReader> detail = place_table_->GetPlaceDetailFromOffset(offset);
      if (detail == nullptr) {
        LOG_INFO("Unable to get place detail from offset: {}", offset);
        continue;
      }
      MatchedPlace matched_place;
      matched_place.place_brief.id = std::to_string(detail->GetPlaceId());
      matched_place.place_brief.name = detail->GetName();
      matched_place.place_brief.address = detail->GetAddress();
      matched_place.place_brief.location.set_lng(detail->GetLng());
      matched_place.place_brief.location.set_lat(detail->GetLat());
      matched_place.place_brief.category = std::to_string(detail->GetCategory());

      matched_place.pinyin = detail->GetNamePy();
      matched_place.hit_deep = index_results[i].deep;
      matched_places->emplace_back(matched_place);
    }
  }
  return true;
}
void AutocompleteRetriever::SortMatchedPlaces(const std::string& query,
                                              std::vector<MatchedPlace>* matched_places) {
  uint32_t weight = 0;
  for (auto& matched_place : *matched_places) {
    // 长度与查询不符合，权值为0
    if (query.size() > matched_place.place_brief.name.size() &&
        query.size() > matched_place.pinyin.size()) {
      matched_place.weight = weight;
      continue;
    }

    weight = 1;
    // 文字位置调权（间隔越远权值越低）
    int dis_weight = 30;
    uint32_t j = 0;
    uint32_t name_pos = 0;
    uint32_t pinyin_pos = 0;
    std::string poi_name = DealString(matched_place.place_brief.name);
    std::string poi_pinyin = DealString(matched_place.pinyin);
    std::string query_deal = DealString(query);
    while (j < query_deal.size()) {
      // 汉字部分
      if (GetByteNum(query_deal[j]) == 3) {
        std::string word = query_deal.substr(j, 3);
        size_t find_pos = poi_name.find(word, name_pos);
        if (find_pos == std::string::npos) {
          weight = 0;
          break;
        }
        dis_weight -= find_pos - name_pos;
        name_pos = find_pos + 3;
        j += 3;
      }
      // 拼音部分
      else if (GetByteNum(query_deal[j]) == 1) {
        size_t find_pos = poi_pinyin.find(query_deal[j], pinyin_pos);
        if (find_pos == std::string::npos) {
          weight = 0;
          break;
        }
        dis_weight -= find_pos - pinyin_pos;
        pinyin_pos = find_pos + 1;
        j += 1;
      }
    }

    if (weight == 0 || dis_weight <= 0) {
      matched_place.weight = 0;
      continue;
    }
    matched_place.weight = dis_weight * 10 / matched_place.place_brief.name.size();
  }

  std::sort(
      matched_places->begin(), matched_places->end(),
      [](const MatchedPlace& lhs, const MatchedPlace& rhs) { return lhs.weight > rhs.weight; });
}

}  // namespace aurora::search