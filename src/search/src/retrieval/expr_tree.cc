#include "expr_tree.h"

#include <logger.h>

#include <cstring>

#include "data/invert_index/geo_trie.h"
#include "data/invert_index/invert_index_def.h"
#include "magic_enum/magic_enum.hpp"

namespace aurora::search {
ExprTreeNode::Type ExprTreeNode::type() const { return type_; }

void ExprTreeNode::Fixup(int level) { level_ = level; }

void ExprTreeNode::Display() const {
  LOG_DEBUG("ExprTree level = {}, type = {}, text = {}, chain_len = {}", level_,
            magic_enum::enum_name(type_), text_, chain_len_);
}

ExprTreeLeafNode::ExprTreeLeafNode(const std::string &keyword, const TermIndex *term_index,
                                   const TermData *term_data, const GeoTrieNode *geo_trie)
    : ExprTreeNode(Type::kLeaf) {
  term_data_ = term_data;
  chain_len_ = term_index->len;
  head_ = 0;
  tail_ = chain_len_;

  geo_trie_ = std::make_shared<GeoTrie>(geo_trie, term_index->trie_len);
  text_ = keyword;
}

bool ExprTreeLeafNode::Intersect(const std::string &geo_cell) {
  return geo_trie_->FastSeek(geo_cell);
}

std::pair<bool, std::optional<std::string>> ExprTreeLeafNode::TryIntersect(
    const std::string &geo_cell) {
  bool intersect = geo_trie_->Seek(geo_cell);
  if (intersect) {
    return {true, std::nullopt};
  }
  if (geo_trie_->HasNext()) {
    return {false, geo_trie_->GetNextCell()};
  }
  return {false, std::nullopt};
}

void ExprTreeLeafNode::ShortenChainFromOneSide(const std::string &geo_cell, bool from_head) {
  geo_trie_->EnsureSeekPerformed(geo_cell);

  uint32_t previous_head = head_;
  uint32_t previous_tail = tail_;
  if (from_head) {
    head_ = geo_trie_->GetChainOffset();
  } else {
    tail_ = geo_trie_->GetChainOffset() + geo_trie_->GetChainLen();
  }
  LOG_DEBUG("Pruned with {} from [{}, {}) to [{}, {}) for term: {}", geo_cell, previous_head,
            previous_tail, head_, tail_, text_);
}

uint32_t ExprTreeLeafNode::ResetChainByGeoCell(const std::string &geo_cell) {
  geo_trie_->EnsureSeekPerformed(geo_cell);
  uint32_t previous_head = head_;
  uint32_t previous_tail = tail_;

  const uint32_t chain_offset = geo_trie_->GetChainOffset();
  const uint32_t chain_len = geo_trie_->GetChainLen();
  if (chain_offset < chain_len_) {
    head_ = chain_offset;
  }

  if (chain_offset + chain_len <= chain_len_) {
    tail_ = chain_offset + chain_len;
  }

  LOG_DEBUG("Reset chain with geo cell {} from [{}, {}) to [{}, {}), term: {}", geo_cell,
            previous_head, previous_tail, head_, tail_, text_);

  if (head_ >= tail_) {
    LOG_WARN("Invalid status!");
    return kInvalidBriefId;
  }
  return current_term_data()->brief_id;
}

uint32_t ExprTreeLeafNode::AdvanceToTargetId(uint32_t target_id, bool use_binary_search) {
  // find the first element equal or larger than given brief_id
  if (use_binary_search) {
    const TermData *result = std::lower_bound(
        term_data_ + head_, term_data_ + tail_, target_id,
        [](const TermData &data, const uint32_t id) { return data.brief_id < id; });
    head_ = result - term_data_;  // update head_ to correct position
  } else {
    while (head_ < tail_ && term_data_[head_].brief_id < target_id) {
      ++head_;
    }
  }

  return head_ < tail_ ? term_data_[head_].brief_id : kInvalidBriefId;
}

void ExprTreeLeafNode::UpdateWeight() {
  const TermData *current = current_term_data();
  term_info_.weight_name = current->weight_name;
  term_info_.weight_address = current->weight_address;
  term_info_.field_flag = current->field_flag;
}
const TermData *ExprTreeLeafNode::current_term_data() const { return term_data_ + head_; }

void ExprTreeAndNode::Fixup(int level) {
  ExprTreeNode::Fixup(level);
  // In AND node, we need to find the intersection for all chains,
  // so we can iterate the shortest chain to find all common elements
  // so the chain length is the shortest child
  chain_len_ = std::numeric_limits<uint32_t>::max();
  text_ = "(";
  for (const auto &child : children_) {
    child->Fixup(level + 1);
    chain_len_ = std::min(chain_len_, child->chain_len());
    if (text_.length() > 1) {
      text_ += " & ";
    }
    text_ += child->text();
  }
  text_ += ")";
}

void ExprTreeAndNode::Display() const {
  ExprTreeNode::Display();
  for (auto &child : children_) {
    child->Display();
  }
}

void ExprTreeAndNode::AddChildren(const ExprTreeNodePtr &child) { children_.push_back(child); }

const std::vector<ExprTreeNodePtr> &ExprTreeAndNode::GetChildren() const { return children_; }

void ExprTreeAndNode::ShortenChainFromOneSide(const std::string &geo_cell, bool from_head) {
  for (auto &child : children_) {
    child->ShortenChainFromOneSide(geo_cell, from_head);
  }
}

uint32_t ExprTreeAndNode::ResetChainByGeoCell(const std::string &geo_cell) {
  uint32_t max_brief_id = 0;
  for (auto &child : children_) {
    uint32_t child_brief_id = child->ResetChainByGeoCell(geo_cell);
    max_brief_id = std::max(max_brief_id, child_brief_id);
  }
  return max_brief_id;
}

bool ExprTreeAndNode::Intersect(const std::string &geo_cell) {
  for (const auto &child : children_) {
    if (!child->Intersect(geo_cell)) {
      return false;
    }
  }
  return true;
}

std::pair<bool, std::optional<std::string>> ExprTreeAndNode::TryIntersect(
    const std::string &geo_cell) {
  for (auto &child : children_) {
    auto [intersect, next_cell] = child->TryIntersect(geo_cell);
    if (!intersect) {
      return {false, next_cell};
    }
  }
  return {true, std::nullopt};
}

uint32_t ExprTreeAndNode::AdvanceToTargetId(uint32_t target_id, bool use_binary_search) {
  uint32_t max_brief_id = 0;
  for (auto &child : children_) {
    uint32_t child_brief_id = child->AdvanceToTargetId(target_id, use_binary_search);
    max_brief_id = std::max(max_brief_id, child_brief_id);
  }
  return max_brief_id;
}

void ExprTreeAndNode::UpdateWeight() {
  // TODO(WS): should we put it into parent node?
  for (auto &child : children_) {
    child->UpdateWeight();
  }
}

void ExprTreeOrNode::Fixup(int level) {
  ExprTreeNode::Fixup(level);
  // In OR node, we need to iterate all chains to do the union operation
  // so the chain length is sum
  chain_len_ = 0;
  text_ = "(";
  for (const auto &child : children_) {
    child->Fixup(level + 1);
    chain_len_ += child->chain_len();
    if (text_.length() > 1) {
      text_ += " | ";
    }
    text_ += child->text();
  }
  text_ += ")";
}

void ExprTreeOrNode::Display() const {
  ExprTreeNode::Display();
  for (auto &child : children_) {
    child->Display();
  }
}

void ExprTreeOrNode::AddChildren(const ExprTreeNodePtr &child) { children_.push_back(child); }

const std::vector<ExprTreeNodePtr> &ExprTreeOrNode::GetChildren() const { return children_; }

void ExprTreeOrNode::ShortenChainFromOneSide(const std::string &geo_cell, bool from_head) {
  for (auto &child : children_) {
    child->ShortenChainFromOneSide(geo_cell, from_head);
  }
}

uint32_t ExprTreeOrNode::ResetChainByGeoCell(const std::string &geo_cell) {
  uint32_t min_brief_id = kInvalidBriefId;
  for (auto &child : children_) {
    uint32_t child_brief_id = child->ResetChainByGeoCell(geo_cell);
    min_brief_id = std::min(min_brief_id, child_brief_id);
  }
  return min_brief_id;
}

bool ExprTreeOrNode::Intersect(const std::string &geo_cell) {
  for (const auto &child : children_) {
    if (child->Intersect(geo_cell)) {
      return true;
    }
  }
  return false;
}

std::pair<bool, std::optional<std::string>> ExprTreeOrNode::TryIntersect(
    const std::string &geo_cell) {
  std::optional<std::string> next_cell = std::nullopt;
  for (const auto &child : children_) {
    // iterate all child to set up geo trie status for possible pruning
    auto [intersect, child_next_cell] = child->TryIntersect(geo_cell);
    if (intersect) {
      return {true, std::nullopt};
    }
    if (child_next_cell.has_value()) {
      if (!next_cell.has_value()) {
        next_cell = child_next_cell;
      } else {
        if (child_next_cell->compare(*next_cell) < 0) {
          next_cell = child_next_cell;
        }
      }
    }
  }
  return {false, next_cell};
}

uint32_t ExprTreeOrNode::AdvanceToTargetId(uint32_t target_id, bool use_binary_search) {
  matched_child_ = nullptr;
  uint32_t min_brief_id = kInvalidBriefId;
  for (auto &child : children_) {
    uint32_t child_brief_id = child->AdvanceToTargetId(target_id, use_binary_search);
    if (child_brief_id < min_brief_id) {
      min_brief_id = child_brief_id;
      matched_child_ = child;
    }
  }
  return min_brief_id;
}

void ExprTreeOrNode::UpdateWeight() {
  if (matched_child_ == nullptr) {
    LOG_FATAL("Update with invalid matched_child");
    return;
  }
  matched_child_->UpdateWeight();
  term_info_ = matched_child_->term_info();
}

ExprTree::ExprTree() : root_(std::make_shared<ExprTreeAndNode>()), reference_chain_(nullptr) {}

void ExprTree::AddNode(const ExprTreeNodePtr &node) const { root_->AddChildren(node); }

ExprTreeNodePtr ExprTree::GetReferenceChain() {
  if (reference_chain_ == nullptr) {
    uint32_t len = std::numeric_limits<uint32_t>::max();
    for (auto &child : root_->GetChildren()) {
      if (child->chain_len() < len) {
        reference_chain_ = child;
        len = child->chain_len();
      }
    }

    LOG_DEBUG("reference_chain: text = {}, chain length = {}.", reference_chain_->text(), len);
  }
  return reference_chain_;
}

void ExprTree::Fixup() const { root_->Fixup(0); }

void ExprTree::Display() const { root_->Display(); }

bool ExprTree::Intersect(const std::string &geo_cell) const { return root_->Intersect(geo_cell); }
std::pair<bool, std::optional<std::string>> ExprTree::TryIntersect(
    const std::string &geo_cell) const {
  return root_->TryIntersect(geo_cell);
}

void ExprTree::ShortenChainFromOneSide(const std::string &geo_cell, bool from_head) const {
  return root_->ShortenChainFromOneSide(geo_cell, from_head);
}

uint32_t ExprTree::RestChainByGeoCell(const std::string &geo_cell) const {
  return root_->ResetChainByGeoCell(geo_cell);
}

uint32_t ExprTree::AdvanceReferenceChainToTargetId(uint32_t target_id) const {
  return reference_chain_->AdvanceToTargetId(target_id, false);
}

uint32_t ExprTree::AdvanceToTargetId(uint32_t target_id) const {
  return root_->AdvanceToTargetId(target_id, true);
}
void ExprTree::UpdateWeight() const { root_->UpdateWeight(); }

}  // namespace aurora::search
