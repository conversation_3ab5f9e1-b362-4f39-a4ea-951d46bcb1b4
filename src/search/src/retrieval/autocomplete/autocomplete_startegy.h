﻿#ifndef SEARCH_SRC_RETRIEVAL_AUTOCOMPLETE_AUTOCOMPLETE_STRATEGY_H
#define SEARCH_SRC_RETRIEVAL_AUTOCOMPLETE_AUTOCOMPLETE_STRATEGY_H

#include <memory>
#include <vector>

namespace aurora::search {
class PrefixIndex;
struct IndexResult;
class AutocompleteStrategy {
 public:
  AutocompleteStrategy() = default;

  virtual ~AutocompleteStrategy() = default;

  virtual int Extend(const std::shared_ptr<PrefixIndex>& prefix_index, const char* words,
                     std::vector<IndexResult>* index_result) = 0;
};

}  // namespace aurora::search
#endif  // SEARCH_SRC_RETRIEVAL_AUTOCOMPLETE_AUTOCOMPLETE_STRATEGY_H
