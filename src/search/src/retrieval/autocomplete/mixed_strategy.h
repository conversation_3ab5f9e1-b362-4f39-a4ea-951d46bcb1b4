﻿#ifndef SEARCH_SRC_RETRIEVAL_AUTOCOMPLETE_MIXED_STRATEGY_H
#define SEARCH_SRC_RETRIEVAL_AUTOCOMPLETE_MIXED_STRATEGY_H

#include "autocomplete_startegy.h"

namespace aurora::search {
class MixedSearch final : public AutocompleteStrategy {
 public:
  MixedSearch() = default;
  ~MixedSearch() override = default;

  int Extend(const std::shared_ptr<PrefixIndex>& prefix_index, const char* words,
             std::vector<IndexResult>* index_result) override;

 private:
  void Filter(const std::shared_ptr<PrefixIndex>& prefix_index,
              const std::vector<IndexResult>& filter, std::vector<IndexResult>* result);
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_RETRIEVAL_AUTOCOMPLETE_MIXED_STRATEGY_H
