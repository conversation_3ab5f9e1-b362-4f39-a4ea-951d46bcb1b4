﻿#include "mixed_strategy.h"

#include <logger.h>
#include <string.h>

#include "data/prefix_index/prefix_index.h"
#include "utils/string_utils.h"

using namespace aurora::search;

namespace aurora::search {

void MixedSearch::Filter(const std::shared_ptr<PrefixIndex>& prefix_index,
                         const std::vector<IndexResult>& filter, std::vector<IndexResult>* result) {
  const TrieData* trie_data = prefix_index->trie_data();

  uint32_t i = 0;
  while (i < result->size()) {
    IndexResult index_result = result->at(i);
    IndexResult filter_start;
    IndexResult filter_end;
    bool is_remove = false;
    for (uint32_t j = 0; j < filter.size(); ++j) {
      filter_start = filter[j];
      filter_end = filter[j];
      int len = 1;

      const TrieIndex* start = trie_data->GetTrieIndex(filter_start);
      const TrieIndex* end = trie_data->GetTrieIndex(filter_end);

      while (start != nullptr && end != nullptr && index_result.deep > filter_start.deep &&
             len > 0) {
        filter_start.deep = filter_start.deep + 1;
        filter_start.index_id = start->child_off;

        filter_end.deep = filter_end.deep + 1;
        filter_end.index_id = end->child_off + end->child_len - 1;

        len = end->child_off + end->child_len - start->child_off;

        start = trie_data->GetTrieIndex(filter_start);
        end = trie_data->GetTrieIndex(filter_end);
      }

      if (index_result.deep == filter_start.deep &&
          index_result.index_id >= filter_start.index_id &&
          index_result.index_id <= filter_end.index_id) {
        break;
      }
      is_remove = true;
      result->erase(result->begin() + i);
      break;
    }
    if (!is_remove) {
      ++i;
    }
  }
}

int MixedSearch::Extend(const std::shared_ptr<PrefixIndex>& prefix_index, const char* words,
                        std::vector<IndexResult>* index_result) {
  std::vector<IndexResult> filter;
  std::vector<IndexResult> all_result;
  std::vector<IndexResult> one_result;
  const PinYinData* pinyin_data = prefix_index->pinyin_data();
  const TrieData* trie_data = prefix_index->trie_data();

  std::vector<const PinYinIndex*> pinyin_result;
  std::vector<const PinYinIndex*> pinyin_expand;

  index_result->clear();
  char new_words[100];
  int off = 0;
  while (off != -1) {
    filter.clear();

    // check filter
    for (uint32_t i = 0; i < all_result.size(); ++i) {
      filter.push_back(all_result[i]);
    }

    one_result.clear();
    // get pinyin
    IndexResult tmp_result;
    int l = 0;
    char tmp[5];
    int next_off = GetNewWord(words, off, tmp);
    while (next_off != -1 && strlen(tmp) == 1) {
      off = next_off;
      new_words[l] = tmp[0];
      ++l;
      next_off = GetNewWord(words, off, tmp);
    }
    new_words[l] = '\0';

    if (l != 0) {
      // search pinyin
      if (all_result.size() == 0) {
        if (pinyin_data->ExpandSearchPinYin(new_words, nullptr, &tmp_result) == -1) {
          break;
        }
        const PinYinIndex* pinyin_index = pinyin_data->GetPinYinIndex(tmp_result);

        // pinyin success， but no hanzi
        pinyin_result.clear();
        pinyin_expand.clear();
        pinyin_result.push_back(pinyin_index);
        if (pinyin_index->py_hz_len == 0) {
          uint32_t l = 0;
          uint32_t num = 0;
          uint32_t pinyin_deep = tmp_result.deep;
          while (l < pinyin_result.size()) {
            num = pinyin_result.size();
            ++pinyin_deep;
            for (uint32_t i = l; i < num; ++i) {
              tmp_result.deep = pinyin_deep;
              for (uint32_t j = 0; j < pinyin_result[i]->child_len; ++j) {
                tmp_result.index_id = pinyin_result[i]->child_off + j;
                pinyin_index = pinyin_data->GetPinYinIndex(tmp_result);

                if (pinyin_index->py_hz_len == 0) {
                  pinyin_result.push_back(pinyin_index);
                } else {
                  pinyin_expand.push_back(pinyin_index);
                }
              }
            }
            l = num;
          }
        } else {
          pinyin_expand.push_back(pinyin_index);
        }

        for (uint32_t i = 0; i < pinyin_expand.size(); ++i) {
          pinyin_index = pinyin_expand[i];
          const IndexResult* pyhz = pinyin_data->GetIndexResult(pinyin_index->py_hz_off);
          for (uint32_t i = 0; i < pinyin_index->py_hz_len; ++i) {
            one_result.push_back(pyhz[i]);
          }
        }

      } else {
        for (uint32_t i = 0; i < all_result.size(); ++i) {
          const IndexResult* hzpy = trie_data->GetPinYinIndex(all_result[i]);
          if (pinyin_data->ExpandSearchPinYin(new_words, &hzpy[i], &tmp_result) == -1) {
            continue;
          }

          const PinYinIndex* pinyin_index = pinyin_data->GetPinYinIndex(tmp_result);

          // pinyin success， but no hanzi
          pinyin_result.clear();
          pinyin_expand.clear();
          pinyin_result.push_back(pinyin_index);
          if (pinyin_index->py_hz_len == 0) {
            uint32_t l = 0;
            uint32_t num = 0;
            uint32_t pinyin_deep = tmp_result.deep;
            while (l < pinyin_result.size()) {
              num = pinyin_result.size();
              ++pinyin_deep;
              for (uint32_t i = l; i < num; ++i) {
                tmp_result.deep = pinyin_deep;
                for (uint32_t j = 0; j < pinyin_result[i]->child_len; ++j) {
                  tmp_result.index_id = pinyin_result[i]->child_off + j;
                  pinyin_index = pinyin_data->GetPinYinIndex(tmp_result);

                  if (pinyin_index->py_hz_len == 0) {
                    pinyin_result.push_back(pinyin_index);
                  } else {
                    pinyin_expand.push_back(pinyin_index);
                  }
                }
              }
              l = num;
            }
          } else {
            pinyin_expand.push_back(pinyin_index);
          }

          for (uint32_t i = 0; i < pinyin_expand.size(); ++i) {
            pinyin_index = pinyin_expand[i];
            const IndexResult* pyhz = pinyin_data->GetIndexResult(pinyin_index->py_hz_off);
            for (uint32_t i = 0; i < pinyin_index->py_hz_len; ++i) {
              one_result.push_back(pyhz[i]);
            }
          }
        }
      }

      // save the result
      all_result.clear();
      if (one_result.size() == 0) {
        return 0;
      }
      // no result
      for (uint32_t i = 0; i < one_result.size(); ++i) {
        all_result.push_back(one_result[i]);
      }
      one_result.clear();
    }
    if (next_off == -1) {
      break;
    }

    // get hanzi
    l = 0;
    next_off = GetNewWord(words, off, tmp);
    while (next_off != -1 && strlen(tmp) == 3) {
      off = next_off;
      strcpy(&new_words[l], tmp);
      l += 3;
      next_off = GetNewWord(words, off, tmp);
    }
    new_words[l] = '\0';
    if (l != 0) {
      // search hanzi
      if (all_result.size() == 0) {
        if (trie_data->SearchTrie(new_words, nullptr, &tmp_result) != -1) {
          one_result.push_back(tmp_result);
        }
      } else {
        for (uint32_t i = 0; i < all_result.size(); ++i) {
          if (trie_data->SearchTrie(new_words, &all_result[i], &tmp_result) != -1) {
            one_result.push_back(tmp_result);
          }
        }
      }

      // save the result
      all_result.clear();
      // no result
      if (one_result.size() == 0) {
        return 0;
      }
      for (uint32_t i = 0; i < one_result.size(); ++i) {
        all_result.push_back(one_result[i]);
      }
      one_result.clear();
    }
  }
  for (auto i : all_result) {
    index_result->push_back(i);
  }
  Filter(prefix_index, filter, index_result);
  return 0;
}

}  // namespace aurora::search
