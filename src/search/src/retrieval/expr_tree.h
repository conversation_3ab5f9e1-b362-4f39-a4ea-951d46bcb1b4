#ifndef SEARCH_SRC_RETRIEVAL_EXPR_TREE_H
#define SEARCH_SRC_RETRIEVAL_EXPR_TREE_H
#include <memory>
#include <optional>
#include <string>

#include "common/search_internal_def.h"
#include "expr_tree.h"

namespace aurora::search {
class GeoTrie;
class GeoTrieNode;
class TermIndex;
class TermData;

class ExprTreeNode : public std::enable_shared_from_this<ExprTreeNode> {
 public:
  enum class Type {
    kLeaf,
    kAnd,
    kOr,
  };
  explicit ExprTreeNode(Type type) : type_(type), level_(-1) {}
  virtual ~ExprTreeNode() = default;

  Type type() const;
  virtual void Fixup(int level);
  virtual void Display() const;

  virtual bool Intersect(const std::string& geo_cell) = 0;
  virtual std::pair<bool, std::optional<std::string>> TryIntersect(const std::string& geo_cell) = 0;

  virtual void ShortenChainFromOneSide(const std::string& geo_cell, bool from_head) = 0;
  virtual uint32_t ResetChainByGeoCell(const std::string& geo_cell) = 0;

  // if there's no valid element, return kInvalidBriefId
  // if common brief_id doesn't exist, return next possible brief_id
  // if common brief_id exists in all chains, return the brief_id itself

  // advance chain head to put chain head to lower bound of  the target id,
  // return brief_id of the found element,
  // otherwise return kInvalidBriefId
  virtual uint32_t AdvanceToTargetId(uint32_t target_id, bool use_binary_search) = 0;
  virtual void UpdateWeight() = 0;

  uint32_t chain_len() const { return chain_len_; }
  const std::string& text() const { return text_; }
  const TermInfo& term_info() const { return term_info_; }

 protected:
  Type type_;
  int level_;
  TermInfo term_info_{};
  std::string text_;
  uint32_t chain_len_ = 0;
};
using ExprTreeNodePtr = std::shared_ptr<ExprTreeNode>;

class ExprTreeLeafNode final : public ExprTreeNode {
 public:
  ~ExprTreeLeafNode() override = default;
  ExprTreeLeafNode(const std::string& keyword, const TermIndex* term_index,
                   const TermData* term_data, const GeoTrieNode* geo_trie);

  bool Intersect(const std::string& geo_cell) override;
  std::pair<bool, std::optional<std::string>> TryIntersect(const std::string& geo_cell) override;

  void ShortenChainFromOneSide(const std::string& geo_cell, bool from_head) override;
  uint32_t ResetChainByGeoCell(const std::string& geo_cell) override;

  uint32_t AdvanceToTargetId(uint32_t target_id, bool use_binary_search) override;
  void UpdateWeight() override;

  const TermData* term_data() const { return term_data_; }
  const TermData* current_term_data() const;

 private:
  const TermData* term_data_ = nullptr;
  std::shared_ptr<GeoTrie> geo_trie_;
  uint32_t head_ = 0;
  uint32_t tail_ = 0;
};

class ExprTreeAndNode final : public ExprTreeNode {
 public:
  ExprTreeAndNode() : ExprTreeNode(Type::kAnd) {}
  ~ExprTreeAndNode() override = default;

  void Fixup(int level) override;
  void Display() const override;

  void AddChildren(const ExprTreeNodePtr& child);
  const std::vector<ExprTreeNodePtr>& GetChildren() const;

  void ShortenChainFromOneSide(const std::string& geo_cell, bool from_begin) override;
  uint32_t ResetChainByGeoCell(const std::string& geo_cell) override;

  bool Intersect(const std::string& geo_cell) override;
  std::pair<bool, std::optional<std::string>> TryIntersect(const std::string& geo_cell) override;

  uint32_t AdvanceToTargetId(uint32_t target_id, bool use_binary_search) override;
  void UpdateWeight() override;

 private:
  std::vector<ExprTreeNodePtr> children_;
};

class ExprTreeOrNode final : public ExprTreeNode {
 public:
  ExprTreeOrNode() : ExprTreeNode(Type::kOr) {}
  ~ExprTreeOrNode() override = default;

  void Fixup(int level) override;
  void Display() const override;

  void AddChildren(const ExprTreeNodePtr& child);
  const std::vector<ExprTreeNodePtr>& GetChildren() const;

  void ShortenChainFromOneSide(const std::string& geo_cell, bool from_begin) override;
  uint32_t ResetChainByGeoCell(const std::string& geo_cell) override;

  bool Intersect(const std::string& geo_cell) override;
  std::pair<bool, std::optional<std::string>> TryIntersect(const std::string& geo_cell) override;

  uint32_t AdvanceToTargetId(uint32_t target_id, bool use_binary_search) override;
  void UpdateWeight() override;

 private:
  std::vector<ExprTreeNodePtr> children_;
  ExprTreeNodePtr matched_child_;
};

class ExprTree {
 public:
  ExprTree();

  void AddNode(const ExprTreeNodePtr& node) const;
  ExprTreeNodePtr GetReferenceChain();

  void Fixup() const;
  void Display() const;

  bool Intersect(const std::string& geo_cell) const;
  std::pair<bool, std::optional<std::string>> TryIntersect(const std::string& geo_cell) const;

  void ShortenChainFromOneSide(const std::string& geo_cell, bool from_head) const;
  uint32_t RestChainByGeoCell(const std::string& geo_cell) const;

  uint32_t AdvanceReferenceChainToTargetId(uint32_t target_id) const;
  uint32_t AdvanceToTargetId(uint32_t target_id) const;

  void UpdateWeight() const;

  const std::shared_ptr<ExprTreeAndNode>& root() const { return root_; }

 private:
  std::shared_ptr<ExprTreeAndNode> root_;
  ExprTreeNodePtr reference_chain_;
};
using ExprTreePtr = std::shared_ptr<ExprTree>;
}  // namespace aurora::search
#endif  // SEARCH_SRC_RETRIEVAL_EXPR_TREE_H