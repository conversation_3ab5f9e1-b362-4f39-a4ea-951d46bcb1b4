#ifndef SEARCH_SRC_RETRIEVAL_AUTOCOMPLETE_RETRIEVER_H
#define SEARCH_SRC_RETRIEVAL_AUTOCOMPLETE_RETRIEVER_H

#include <memory>

#include "search_def.h"

namespace aurora::search {
struct MatchedPlace;
struct IndexResult;
struct SearchConfig;
struct DataFolderLayout;
class AutocompleteStrategy;
class PrefixIndex;
class PlaceTable;

class AutocompleteRetriever {
 public:
  int Init(std::shared_ptr<SearchConfig> config, std::shared_ptr<PlaceTable> detail_data);

  bool Extend(const AutocompleteRequestPtr& request, const AutocompleteResponsePtr& response);

 private:
  bool ToMatchedPlaces(const std::vector<IndexResult>&, std::vector<MatchedPlace>* matched_places);
  void SortMatchedPlaces(const std::string& query, std::vector<MatchedPlace>* matched_places);
  int max_count_sort_ = 200;
  std::shared_ptr<AutocompleteStrategy> strategy_;
  std::shared_ptr<PrefixIndex> prefix_index_;
  std::shared_ptr<PlaceTable> place_table_;

  std::shared_ptr<SearchConfig> config_;
};

}  // namespace aurora::search

#endif  // SEARCH_SRC_RETRIEVAL_AUTOCOMPLETE_RETRIEVER_H
