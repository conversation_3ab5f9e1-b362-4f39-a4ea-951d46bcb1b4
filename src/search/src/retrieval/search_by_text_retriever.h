#ifndef SEARCH_SRC_RETRIEVAL_TEXT_SEARCH_RETRIEVER_H
#define SEARCH_SRC_RETRIEVAL_TEXT_SEARCH_RETRIEVER_H
#include <memory>

#include "data/data_source.h"

namespace aurora::search {
class ExprTree;
class ExprTreeNode;
class InvertIndex;
struct ParsedQuery;
struct PlaceBrief;
class PlaceTable;
struct RankingPlace;
class RankManager;
struct Term;

class SearchByTextRetriever : public std::enable_shared_from_this<SearchByTextRetriever> {
 public:
  SearchByTextRetriever(const SearchByTextDataSource& data_source,
                        std::shared_ptr<RankManager> rank_manager, int level_one_pruning_threshold,
                        int level_two_pruning_threshold, int max_retrieval_count);

  void RetrievePlaces(const std::shared_ptr<ParsedQuery>& parsed_query,
                      std::vector<RankingPlace>* ranking_places);

  PlaceBrief GetPlaceBrief(uint32_t brief_id) const;

 private:
  std::shared_ptr<ExprTree> BuildExprTree(const std::shared_ptr<ParsedQuery>& parsed_query) const;
  std::shared_ptr<ExprTreeNode> CreateNode(const Term& term) const;
  std::shared_ptr<ExprTreeNode> CreateOrNode(const std::vector<std::string>& terms) const;
  std::shared_ptr<ExprTreeNode> CreateLeafNode(const std::string& term) const;
  bool RetrieveByExprTree(const std::shared_ptr<ParsedQuery>& parsed_query,
                          const std::shared_ptr<ExprTree>& expr_tree,
                          std::vector<RankingPlace>* ranking_places);

  // In level 0, wo do no pruning with geo cells.
  // In level 1, we find the first geo cell intersects all chains to prune the
  // chain head, and use last geo cell to prune the chain tail.
  // In level 2, we iterate over geo cells to find every cell that intersects
  // all the chains. Reset head and tail of chain, and do the normal work.
  enum class GeoPruningLevel {
    kZero = 0,
    kOne,
    kTwo,
  };
  GeoPruningLevel DetermineGeoPruningLevel(const std::shared_ptr<ExprTreeNode>& reference_chain,
                                           bool has_geo_cells) const;

  void AddOneResult(const std::shared_ptr<ExprTree>& expr_tree,
                    const std::shared_ptr<ParsedQuery>& parsed_query, uint32_t brief_id,
                    std::vector<RankingPlace>* ranking_places);

  std::string city_code_;
  CityType city_type_;
  std::string info_;
  std::shared_ptr<InvertIndex> invert_index_;
  std::shared_ptr<PlaceTable> detail_data_;
  std::shared_ptr<RankManager> rank_manager_;

  int level_one_pruning_threshold_;
  int level_two_pruning_threshold_;

  // we must set retrieval count large enough, to included more matched place
  int max_retrieval_count_;
};
using SearchByTextRetrieverPtr = std::shared_ptr<SearchByTextRetriever>;
}  // namespace aurora::search

#endif  // SEARCH_SRC_RETRIEVAL_TEXT_SEARCH_RETRIEVER_H
