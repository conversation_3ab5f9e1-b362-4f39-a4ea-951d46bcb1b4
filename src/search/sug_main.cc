#include <logger.h>

#include <iostream>
#include <memory>
#include <string>

#include "search_def.h"
#include "search_service.h"
#include "src/online/httplib.h"

using namespace std;
using namespace aurora;
using namespace aurora::search;

std::shared_ptr<SearchService> search_service;

int InitSearchService(const std::string &config_file);
int main() {
  aurora::logger::get().set_level(spdlog::level::debug);
  LOG_INFO("Hello from map_sug_search_service!");
  // httplib::Client cli("www.baidu.com");
  // auto res = cli.Get("/");
  // if (res && res->status == 200) {
  //   LOG_INFO("res: {}", res->body);
  // };
  httplib::Client cli("127.0.0.1", 8503);

  string body = R"({
        "query": "找一家靠近海边的餐厅",
        "top_k": 20,
        "model_type": "fast_mode"
    })";
  auto res = cli.Post("/search/searching/", body.c_str(), "application/json");
  if (res && res->status == 200) {
    LOG_FATAL("res: [{}]", res->body);
  } else {
    LOG_FATAL("请求失败！状态码: {}",
              res ? std::to_string(res->status) : "连接错误");
  }

  return 0;
}

int InitSearchService(const std::string &config_file) {
  search_service = std::make_shared<SearchService>();
  LOG_DEBUG("init search service config_file [{}]", config_file);
  return search_service->Init(config_file);
}
