#ifndef MAP_SRC_GUIDE_INCLUDE_GUIDE_MODULE_H
#define MAP_SRC_GUIDE_INCLUDE_GUIDE_MODULE_H

#include <string>

#include "module.h"
#include "guidance_listener.h"
#include "guidance_param.h"
#include "path_module.h"
#include "location_def.h"

namespace aurora {
namespace guide {

class GuidanceControl;
class AURORA_EXPORT GuidanceModule : public Module {
public:
    GuidanceModule();
    virtual ~GuidanceModule();

    virtual int32_t  Prepare(const std::string &config);
    virtual int32_t  Init(const InterfaceFinder &finder);
    virtual int32_t  Start();
    virtual int32_t  Stop();
    virtual int32_t  UnInit();

    virtual ModuleInitStatus IsInit() const;
    virtual std::shared_ptr<IInterface> GetInterface() const;

protected:

private:
    ModuleInitStatus status_;
    std::shared_ptr<GuidanceControl> control_;
};

class AURORA_EXPORT IGuidance : public IInterface {
public:
    virtual ~IGuidance() = default;

    /**
     * @brief 参数设置/读取
     */
    virtual int32_t SetParam(const Param &param) = 0;
    virtual Param   GetParam(const ParamType &type) const = 0;

    /**
     * @brief 设置导航路线(主路线+备选路线，包括主路线index)
     */
    virtual int32_t SetNavPath(path::PathQueryPtr query,path::PathResultPtr result, uint64_t main_path_id) = 0;

    /**
     * @brief 切换主路线ID
     */
    virtual int32_t SwitchMainPathId(uint64_t target_path_id) = 0;

    /**
     * @brief 请求路线转向详情
     */
    virtual int32_t RequestPathManeuverDetail(uint64_t path_id) = 0;

    /**
     * @brief 更新匹配结果
     */
    virtual int32_t UpdateMapMatchingPos(const loc::MatchResult &map_result) = 0;

    /**
     * @brief 开始导航
     */
    virtual int32_t StartNavigation(NavigationMode type) = 0;

    /**
     * @brief 结束导航
     */
    virtual int32_t StopNavigation() = 0;

    /**
     * @brief 暂停导航
     */
    virtual int32_t PauseNavigation() = 0;

     /**
      * @brief 恢复导航
      */
    virtual int32_t ResumeNavigation() = 0;

    /**
     * @brief 手动触发导航播报提示
     */
    virtual int32_t PlayNavigationManual() = 0;

    /**
     * @brief 添加/删除导航模式观察者
     */
    virtual int32_t AddListener(IGuidanceListenerPtr obs) = 0;
    virtual int32_t RemoveListener(IGuidanceListenerPtr obs) = 0;

};  // class IGuide

}
}

#endif  // MAP_SRC_GUIDE_INCLUDE_GUIDE_MODULE_H
/* EOF */
