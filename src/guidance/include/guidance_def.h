#ifndef MAP_SRC_GUIDE_INCLUDE_GUIDE_DEF_H
#define MAP_SRC_GUIDE_INCLUDE_GUIDE_DEF_H

#include <cstdint>
#include <string>
#include <vector>
#include <memory>

#include "path_def.h"

namespace aurora {
namespace guide {

enum DistanceUnits {
    kDistanceKilometers = 0,
    KDistanceMiles
};

struct GuidanceOptions {
    DistanceUnits distance_unit;
    std::string   language_tag;
};

enum NavigationMode {
    kNavigationModeNone = 0,    // 
    kNavigationModeGPS,         // 真实导航
    kNavigationModeSimulation,  // 模拟导航
    kNavigationModeCruise       // 巡航模式
};

// Guide表示一个单一的导航动作，该结构体包括了引导播报&显示所需要的所有结构信息
enum class ManeuverType {
    kTypeNone = 0,             //无效
    // 八方向 
    kTypeContinue,             //直行
    kTypeSlightRight,          //右前方
    kTypeRight,                //右转 
    kTypeSharpRight,           //右后 
    kTypeLeftUTurn,            //左掉头
    kTypeRightUTurn,           //右掉头
    kTypeSharpLeft,            //左后
    kTypeLeft,                 //左转
    kTypeSlightLeft,           //左前

    // 标准环岛
    kTypeRoundStart,                // TODO:delete later
    kTypeRSideRoundStraight,        //绕环岛直行 （右行逆时针）
    kTypeRSideRoundRight,           //绕环岛右转（右行逆时针）
    kTypeRSideRoundLeft,            //绕环岛左转（右行逆时针）
    kTypeRSideRoundUTurn,           //绕环岛调头（右行逆时针）
    kTypeLSideRoundStraight,        //绕环岛直行（左行顺时针）
    kTypeLSideRoundRight,           //绕环岛右转（左行顺时针）
    kTypeLSideRoundLeft,            //绕环岛左转（左行顺时针）
    kTypeLSideRoundUTurn,           //绕环岛调头（左行顺时针）

    // 普通环岛
    kTypeLSideRoundIn,         //进入环岛，走第几出口（左行逆时针）
    kTypeLSideRoundOut,        //环岛出口提示（左行逆时针）
    kTypeRSideRoundIn,         //进入环岛，走第几出口（右行逆时针） 
    kTypeRSideRoundOut,        //环岛出口提示（右行逆时针）

    kTypeRoundEnd,             // TODO: delete later
    
    kTypeStart,                // 开始位置
    kTypeStartLeft,            // 开始位置在路线左侧 
    kTypeStartRight,           // 开始位置在路线右侧
    kTypeViaPoint,             // 途径地
    kTypeViaPointLeft,         // 途径地在路线左侧
    kTypeViaPointRight,        // 途径地在路线右侧
    kTypeDestination,          // 目的地
    kTypeDestinationLeft,      // 目的地在路线左侧
    kTypeDestinationRight,     // 目的地在路线右侧
    
#if 0
    // 二分歧
    kType2BranchLeft,          //标准二分歧靠左
    kType2BranchRight,         //标准二分歧靠右
    // 三分歧
    kType3BranchLeft,          //三分歧靠左行驶
    kType3BranchMiddle,        //三分歧中间行驶
    kType3BranchRight,         //三分歧靠右行驶
#endif

    // 分歧(居中，靠左，靠右)
    kTypeStayStraight,
    kTypeStayRight,
    kTypeStayLeft,

    // 匝道
    kTypeStraightToRamp,       // 直行上匝道
    kTypeRightToRamp,          // 右转上匝道
    kTypeLeftToRamp,           // 左转上匝道

    // 高速出口
    kTypeExitLeft,
    kTypeExitRight,

#if 0
    // 封闭路(高速路)&汇入&汇出
    kTyepeExitBranchMainLeft,         // 封闭路分歧靠左本线行驶
    kTypeExitBranchMainRight,        // 封闭路分歧靠右本线行驶
    kTypeExitBranchRampLeft,         // 封闭路分歧靠左Ramp道（IC、JCT、SAPA）行驶
    kTypeExitBranchRampRight,        // 封闭路分歧靠右Ramp道（IC、JCT、SAPA）行驶
#endif


    kTypeMerge,                      // 道路汇入
    kTypeMergeToLeft,                // 道路向左侧汇入
    kTypeMergeToRight,               // 道路向右侧汇入
 
    kType2StraightLeft,              // 存在两个直行，靠左直行 
    kType2StraightRight,             // 存在两个直行，靠右直行
    
    kTypeInter3StraightLeft,         //普通多分叉路口，存在三个直行，靠左直行
    kTypeInter3StraightMiddle,       //普通多分叉路口，存在三个直行，居中直行
    kTypeInter3StraightRight,        //普通多分叉路口，存在三个直行，靠右直行
    
    kTypeInter2LeftLeft,             //普通多分叉路口，存在两个左转，靠左左转
    kTypeInter2LeftRight,            //普通多分叉路口，存在两个左转，靠右左转

    kTypeInter3LeftLeft,             //普通多分叉路口，存在三个左转，靠左左转
    kTypeInter3LeftMiddle,           //普通多分叉路口，存在三个左转，靠中央左转
    kTypeInter3LeftRight,            //普通多分叉路口，存在三个左转，靠右左转
    
    kTypeInter2RightLeft,            //普通多分叉路口，存在两个右转，靠左右转
    kTypeInter2RightRight,           //普通多分叉路口，存在两个右转，靠右右转
    kTypeInter3RightLeft,            //普通多分叉路口，存在三个右转，靠左右转
    kTypeInter3RightMiddle,          //普通多分叉路口，存在三个右转，靠中央右转
    kTypeInter3RightRight,           //普通多分叉路口，存在三个右转，靠右右转
    
    kTypeRoundOut2BranchLeft,         //环岛出口2条路靠左
    kTypeRoundOut2BranchRight,        //环岛出口2条路靠右
    kTypeRoundOut3BranchLeft,         //环岛出口3条路靠左
    kTypeRoundOut3BranchMiddle,       //环岛出口3条路靠中
    kTypeRoundOut3BranchRight,        //环岛出口3条路靠右
    
    kTypeLeftUTurn2BranchLeft,       //左掉头2条路靠左
    kTypeLeftUTurn2BranchRight,      //左掉头2条路靠左
    kTypeLeftUTurn3BranchLeft,       //左掉头3条路靠左
    kTypeLeftUTurn3BranchMiddle,     //左掉头3条路居中
    kTypeLeftUTurn3BranchRight,      //左掉头3条路靠右
    
    kTypeRightUTurn2BranchRight,      //右掉头2条路靠左
    kTypeRightUTurn3BranchLeft,       //右掉头3条路靠左
    kTypeRightUTurn3BranchMiddle,     //右掉头3条路居中
    kTypeRightUTurn3BranchRight,      //右掉头3条路靠右
    kTypeNum
};

enum class AuxManeuverType {
    kAuxTypeNone = 0,
    kAuxTypeInTunnel,            // 进入隧道
    kAuxTypeInBridge,            // 进入大桥
    kAuxTypeInTollStation,       // 进入收费站
    kAuxTypeInServiceArea,        // 进入服务区
    kAuxTypeNum
};

enum class CardinalDirection { // 基础方位
    kNorth = 0,
    kNorthEast,
    kEast,
    kSouthEast,
    kSouth,
    kSouthWest,
    kWest,
    kNorthWest
};


struct TimeAndDist {
    int32_t sec;    // unit: second
    float   dis;    // unit: meter
};

// 方向路牌信息
struct NavigationExitDirectionInfo {
    int type;           // 方向路牌类型
    int maneuver_id;    // 转向id
    int remain_dist;    // 剩余距离,unit:m
    std::vector<std::string> exit_name_infos;   // 出口编号名字数组
    std::vector<std::string> direction_infos;   // 路牌方向名字数组
};
using NavigationExitDirectionInfoPtr = std::shared_ptr<NavigationExitDirectionInfo>;

// 路口导航信息
struct GuidePoint {// 导航引导点

};

struct InfoPoint { // 信息提示点

};

// 导航面板信息
struct NavigationInfo {
    int64_t         path_id;            // 路线id
    NavigationMode  mode;               // 导航模式
    TimeAndDist     path_remain;        // 路线剩余距离&时间
    std::vector<TimeAndDist> via_remain; // 途径地剩余距离&时间
    int32_t         remain_light_num;    // 红绿灯数数量
    int32_t         cur_segment_idx;     
    int32_t         cur_link_idx;
    std::string     cur_road_name;

    int32_t         drive_time;     // 已经行驶的时间
    int32_t         drive_dist;     // 已经行驶的距离
    int32_t         city_code;      // 行政区划编码
    int32_t         maneuver_id;    // 转向id

    std::vector<GuidePoint> next_guide_points;  // 临接机动点
    NavigationExitDirectionInfo *exit_direction_info; // 方向路牌信息
};
using NavigationInfoPtr = std::shared_ptr<NavigationInfo>;

// 车道类型
enum class LaneAction : uint8_t {
    kLaneActionNULL             = 0,  // 无对应车道
    kLaneActionAhead            = 1,     // 直行
    kLaneActionLeft             = 2,     // 左转
    kLaneActionAheadLeft        = 3,     // 直行+左转
    kLaneActionRight            = 4,     // 右转
    kLaneActionAheadRight       = 5,     // 直行+右转
    kLaneActionLUTurn           = 6,     // 左掉头
    kLaneActionLeftRight        = 7,     // 左转+右转
    kLaneActionAheadLeftRight   = 8,     // 直行+左转+右转
    kLaneActionRUTurn           = 9,     // 右掉头
    kLaneActionAheadLUTurn      = 10,    // 直行+左掉头
    kLaneActionAheadRUTurn      = 11,    // 直行+右掉头
    kLaneActionLeftLUTurn       = 12,    // 左转+左掉头
    kLaneActionRightRUTurn      = 13,    // 右转+右掉头
    kLaneActionLeftInAhead      = 14,    // 无效，保留
    kLaneActionLeftLUturn       = 15,    // 无效，保留
    kLaneActionReserved         = 16,    // 保留
    kLaneActionAheadLeftLUTurn  = 17,    // 直行+左转+左掉头
    kLaneActionRightLUTurn      = 18,    // 右转+左掉头
    kLaneActionLeftRightLUTurn  = 19,    // 左转+右转+左掉头
    kLaneActionAheadRightLUTurn = 20,    // 直行+右转+左掉头
    kLaneActionLeftRUTurn       = 21,    // 左转+右掉头
    kLaneActionBus              = 22,    // 公交车道
    kLaneActionEmpty            = 23,    // 空车道
    kLaneActionVariable         = 24,    // 可变车道
    kLaneActionDedicated        = 25,    // 专用车道
    kLaneActionTidal            = 26,    // 潮汐车道
    kLaneActionHov              = 27,    // HOV车道
};

// 扩展车道信息
enum class ExtenLaneAction : uint8_t {
    kExtenLaneActionNULL = 0,       // 无对应扩展车道
    kExtenLaneActionNormal,         // 普通 ，即无扩展车道
    kExtenLaneActionLeft,           // 左扩展
    kExtenLaneActionRight,          // 右扩展
    kExtenLaneActionLeftNarrow,     // 左侧车道变窄
    kExtenLaneActionRightNarrow,    // 右侧车道变窄
};

// 车道类别
enum class LaneCategoryType : uint8_t {
    kLaneTypeNull = 0,      // 无对应车道
    kLaneTypeNormal,        // 普通车道类型
    kLaneTypeBus,           // 公交车专用车道类型
    kLaneTypeTidal,         // 潮汐车道类型
    kLaneTypeVariable,      // 可变车道类型
    kLaneTypeOther,         // 其他专用车道类型
};

// 车道信息
struct NavigationLaneInfo {
    std::string             id;
    std::vector<LaneAction> back_lanes;     // 背景车道信息
    std::vector<LaneAction> front_lanes;    // 前景车道信息
};
using NavigationLaneInfoPtr = std::shared_ptr<NavigationLaneInfo>;

// 路口放大图
enum JunctionViewType {
    kNone = 0,
    kRealView,        // 实景图
    kDiagramView,     // 模式图
    kDirectionBoard,  // 方向看板
};

struct JunctionViewInfo {
    std::string         id;
    int32_t             type;   // 实景图/模式图/方向看板
    std::vector<int8_t> back_view_data;
    std::vector<int8_t> front_view_data;
};

using JunctionViewInfoPtr = std::shared_ptr<JunctionViewInfo>;

// 高速看板(服务区、收费站、检查站)
enum class NavFacilityType : uint8_t{
    kFacilityTypeNone = 0,       // 空
    kNavFacilityTypeServiceArea, // 服务区
    kNavFacilityTypeTollGate,    // 收费站
    kNavFacilityTypeCheckPoint,  // 检查站
};

struct NavigationFacility {
    NavFacilityType type;
    int32_t         remain_dist; // 剩余距离
    int32_t         remain_time; // 剩余时间
    std::string     name;
    double          pos_lon;
    double          pos_lat;
    int8_t          is_charge : 1;
    int8_t          status    : 3; //
    int8_t          reserve   : 4;

    int16_t         charge_num;
    int16_t         charge_ava_num;
};
using NavigationFacilityPtr = std::shared_ptr<NavigationFacility>;

enum class TTSScenePlay : int8_t {
    kTTSScenePlayNull  = -1,
    kTTSScenePlayJoinLeft,      // 左侧车辆汇入
    kTTSScenePlayJoinRight,     // 右侧车辆汇入
    kTTSScenePlayZigzag,        // 急转弯
    kTTSScenePlayAccident,      // 事故多发地
    kTTSScenePlayRockFall,      // 注意落石
    kTTSScenePlayRailway,       // 铁路道口
    kTTSScenePlayVillage,       // 村庄
    kTTSScenePlaySchool,        // 学校
    kTTSScenePlayReady,         // 准备就绪
    kTTSScenePlaySpeedCamera,   // 测速摄像
    kTTSScenePlayServiceArea,   // 服务区
    kTTSScenePlayDeviation,     // 偏航
};

// 停止导航的code
enum class NavigationStopCode {
    kStopCodeNone = 0,      // 空
    kStopCodeDestination,   // 到达目的地
    kStopCodeAppTrigger,    // 应用触发
};

struct SoundInfo {
    std::string text;
};

using SoundInfoPtr = std::shared_ptr<SoundInfo>;

struct CruiseInfo {};
using CruiseInfoPtr = std::shared_ptr<CruiseInfo>;

struct CameraInfo {};
using CameraInfoPtr = std::shared_ptr<CameraInfo>;

struct SAPAInfo {};
using SAPAInfoPtr = std::shared_ptr<SAPAInfo>;

struct LightBarDetail {};
using LightBarDetailPtr = std::shared_ptr<LightBarDetail>;


struct NavigationCongestionInfo {};
using NavigationCongestionInfoPtr = std::shared_ptr<NavigationCongestionInfo>;

struct CruiseCongestionInfo {};
using CruiseCongestionInfoPtr = std::shared_ptr<CruiseCongestionInfo>;

struct PathTmcEventInfo {};
using PathTmcEventInfoPtr = std::shared_ptr<PathTmcEventInfo>;

struct TollStainoInfo {};
using TollStainoInfoPtr = std::shared_ptr<TollStainoInfo>;

struct ManeuverDetail {
    int32_t         type;
    std::string     action;
    uint32_t        length;
    uint32_t        time;
    std::string     begin_name;
    std::string     name;
    std::string     end_name;
    std::vector<PointXY<double>> geos;
};

}  // namespace guide
}  // namespace aurora

#endif  // MAP_SRC_GUIDE_INCLUDE_GUIDE_DEF_H
/* EOF */
