#include "guidance_module.h"
#include "errorcode.h"
#include "guidance/src/guidance_control.h"


namespace aurora {
namespace guide {

GuidanceModule::GuidanceModule() 
: Module(ModuleId::kModuleIdGuide)
, status_(ModuleInitStatus::kModuleNotInit)
, control_(nullptr) {}


GuidanceModule::~GuidanceModule() {
    UnInit();
}

int32_t  GuidanceModule::Prepare(const std::string &config) {
    control_ = std::make_shared<GuidanceControl>();
    if (control_ == nullptr) {
        return ErrorCode::kErrorCodeMemAllocError;
    }

    // TODO: prepare
    int32_t ret = ErrorCode::kErrorCodeOk;
    if((ret = control_->Prepare(config)) == ErrorCode::kErrorCodeOk) {
        status_ = ModuleInitStatus::kModuleInitDoing;
    }
    return ret;
}


int32_t  GuidanceModule::Init(const InterfaceFinder &finder) {
    if (control_ == nullptr) {
        return ErrorCode::kErrorCodeNotInit;
    }

    if (status_ == ModuleInitStatus::kModuleInitDoing) {
        int32_t ret = control_->Init(finder);
        if (ret == ErrorCode::kErrorCodeOk) {
            status_ = ModuleInitStatus::kModuleInitDone;
        }
        return ret;
    }
    return ErrorCode::kErrorCodeCallSeqenceError;
}

int32_t  GuidanceModule::Start() {
    if (control_ == nullptr) {
        return ErrorCode::kErrorCodeNotInit;
    }
    return control_->Start();
}
    
int32_t  GuidanceModule::Stop() {
    if (control_ == nullptr) {
        return ErrorCode::kErrorCodeNotInit;
    }
    return control_->Stop();
}
    
int32_t  GuidanceModule::UnInit() {
    if (control_ != nullptr) {
        control_->UnInit();
    }
    status_ = ModuleInitStatus::kModuleNotInit;
    return ErrorCode::kErrorCodeOk;
}
    
ModuleInitStatus GuidanceModule::IsInit() const {
    return status_;
}

std::shared_ptr<IInterface> GuidanceModule::GetInterface() const {
    return std::dynamic_pointer_cast<IInterface>(control_);
}

}  // namespace guide
}  // namespace aurora
/* EOF */
