#include "guidance/src/data/enhance_lane_info.h"
#include "guidance/src/common/guide_log.h"
#include "guidance/src/data/lane_type_wrapper.h"

namespace aurora {
namespace guide {

EnhanceLaneRelation::EnhanceLaneRelation(const DirectEdgeId &to_edge_id, uint64_t relation)
: to_direct_id_(to_edge_id) {
    d_.v = relation;
}

EnhanceLaneRelation::TargetLaneType EnhanceLaneRelation::GetLaneRelation(int32_t lane_index) const {
    if (lane_index >= 16) {
        GUIDE_ASSERT(false);
        return TargetLaneType::kValueNotRecommend;
    }

    int32_t byte_index = lane_index /2;

    if (lane_index%2 == 0) {
        return static_cast<TargetLaneType>(d_.e[byte_index].l);
    }

    return static_cast<TargetLaneType>(d_.e[byte_index].h);
}

const DirectEdgeId& EnhanceLaneRelation::GetToDirectEdgeId() const {
    return to_direct_id_;
}


EnhanceLaneInfo::EnhanceLaneInfo(const DirectEdgeId &from_edge_id, DirectEdgeId *to_edge_id, EnhanceDataProvider *provider) 
: from_edge_id_(from_edge_id) {
    GUIDE_ASSERT(provider != nullptr);
    
    if (to_edge_id == nullptr) {
        valid_ = false;
        lane_num_ = 0;
    } else {
        valid_ = true;
        to_edge_id_ = *to_edge_id;
        
        BuildLaneInfo(provider);

#if 0
        GetBackLaneActions();
        GetFrontLaneActions();
#endif
    }
}

int32_t EnhanceLaneInfo::GetRelationNum() const {
    return relations_.size();
}

int32_t EnhanceLaneInfo::GetFromEdgeLaneNum() const {
    return lane_num_;
}

EnhanceLaneRelation* EnhanceLaneInfo::GetRelationToEdgeId() {
    if (!valid_) {
        return nullptr;
    }

    for (int32_t index = 0; index < relations_.size(); ++index) {
        if (to_edge_id_ == relations_[index].GetToDirectEdgeId()) {
            return (relations_.data() + index);
        }
    }

#if 0
    // 掉头场景数据暂时没有推荐车道
    GUIDE_ASSERT(false);
#endif
    return nullptr;
}

std::vector<LaneAction> EnhanceLaneInfo::GetBackLaneActions() {
    std::vector<LaneAction> results;

    for (int32_t index = 0; index < lane_num_; ++index) {
        LaneTypeWrapper wrapper(lane_types_[index]);
        results.emplace_back(wrapper.ToLaneAction());
    }

    return results;
}


std::vector<LaneAction> EnhanceLaneInfo::GetFrontLaneActions() {
    std::vector<LaneAction> results;
    EnhanceLaneRelation* relation = GetRelationToEdgeId();

    if (relation != nullptr) {
        for (int32_t index = 0; index < lane_num_; ++index) {
            EnhanceLaneRelation::TargetLaneType type = relation->GetLaneRelation(index);
            switch (type) {
            case EnhanceLaneRelation::TargetLaneType::kValueNotRecommend:
                results.emplace_back(LaneAction::kLaneActionNULL);
                break;

            case EnhanceLaneRelation::TargetLaneType::kValueEmptyLane:
                results.emplace_back(LaneAction::kLaneActionEmpty);
                break;
            
            case EnhanceLaneRelation::TargetLaneType::kValueStraight:
                results.emplace_back(LaneAction::kLaneActionAhead);
                break;

            case EnhanceLaneRelation::TargetLaneType::kValueLeft:
                results.emplace_back(LaneAction::kLaneActionLeft);
                break;

            case EnhanceLaneRelation::TargetLaneType::kValueRight:
                results.emplace_back(LaneAction::kLaneActionRight);
                break;

            case EnhanceLaneRelation::TargetLaneType::kValueUTurn: // TODO: 区分左右掉头
                results.emplace_back(LaneAction::kLaneActionLUTurn);
                break;

            case EnhanceLaneRelation::TargetLaneType::kValueSlightLeft:
                results.emplace_back(LaneAction::kLaneActionLeft);
                break;

            case EnhanceLaneRelation::TargetLaneType::kValueSlightRight:
                results.emplace_back(LaneAction::kLaneActionRight);
                break;

            default:
                GUIDE_ASSERT(false);
                results.emplace_back(LaneAction::kLaneActionNULL);
                break;
            }
        } // for
    } else { // relation: null
        for (int32_t index = 0; index < lane_num_; ++index) {
            results.emplace_back(LaneAction::kLaneActionNULL);
        }
    }

    return results;
}

std::string EnhanceLaneInfo::GetId() const {
    return from_edge_id_.ToString() + "_" + to_edge_id_.ToString();
}


void EnhanceLaneInfo::BuildLaneInfo(EnhanceDataProvider *provider) {
    LaneInfo *lane_info = provider->GetLaneInfo(from_edge_id_);
    GUIDE_ASSERT(lane_info != nullptr);
    GUIDE_ASSERT(lane_info->GetBaseInfo() != nullptr);

    GUIDE_ASSERT(lane_info->GetBaseInfo()->in_edge_id == from_edge_id_.edge_id.feature_id);
    GUIDE_ASSERT(!(lane_info->GetBaseInfo()->in_edge_dir) == from_edge_id_.forward);

    relations_.clear();

    if ((lane_info != nullptr) && (lane_info->GetBaseInfo() != nullptr)) {
        lane_num_ = lane_info->GetBaseInfo()->lane_count + 1; // 数据编译中：0-15 表示 1-16
        GUIDE_ASSERT(lane_num_ > 0);
        int32_t array_size = sizeof(lane_types_)/sizeof(lane_types_[0]);
        GUIDE_ASSERT(array_size >= lane_num_);

        if (lane_num_ > array_size) {
            GUIDE_LOG_ERROR("lane info lane count error ...");
            lane_num_ = array_size;
        }

        for (int32_t idx = 0; idx < lane_num_; ++idx) {
            lane_types_[idx] = lane_info->GetBaseInfo()->lane_type[idx];
        }

        int32_t relation_num = lane_info->GetBaseInfo()->lane_relation_count;
        for (int32_t index = 0; index < relation_num; ++index) {
            const LaneRelation *relation = lane_info->GetLaneRelation(index);
            GUIDE_ASSERT(relation != nullptr);
            if (relation != nullptr) {

                // 更新目标Edge的TileId(数据保证LaneInfo不会跨城市，否则此处admincode会存在问题)
                DirectEdgeId target_edge_id;
                target_edge_id.edge_id.tile_id = from_edge_id_.edge_id.tile_id;
                target_edge_id.edge_id.tile_id.mesh_row = relation->out_mesh_row;
                target_edge_id.edge_id.tile_id.mesh_col = relation->out_mesh_col;
                target_edge_id.edge_id.tile_id.tile_id = relation->out_tile_id;
                target_edge_id.edge_id.feature_id = relation->out_edge_id;
                target_edge_id.forward = relation->out_edge_dir ? false : true;

                relations_.emplace_back(target_edge_id, relation->relations);
            } else {
                GUIDE_ASSERT(false);
            }
        }

    } // if
}


}  // namespace guide
}  // namespace aurora
/* EOF */
