#include "guidance/src/data/path_data_manager.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

PathDataManager::PathDataManager(EnhanceDataProvider* provider, PathQueryPtr query, PathResultPtr result) 
: main_path_id_(PathID::kInvalidPathId)
, provider_(provider)
, path_result_(result) {
    query_ = std::make_shared<EnhancePathQuery>(query);
}

PathDataManager::~PathDataManager() {
}

bool PathDataManager::SwitchMainPath(uint64_t target_path_id) {
    if (target_path_id == main_path_id_) {
        return true;
    }

    if (path_result_ == nullptr) {
        GUIDE_LOG_ERROR("SwitchMainPath path result is nullptr .");
        return false;
    }

    for (const auto& path : path_result_->paths) {
        if (path.path_id == target_path_id) {
            main_path_id_ = target_path_id;
            enhance_path_result_ = std::make_shared<EnhancePathResult>(provider_);
            if (!enhance_path_result_->Build(query_, path)) {
                GUIDE_LOG_ERROR("build path error .");
                return false;
            }
            return true;
        }
    }

    main_path_id_ = PathID::kInvalidPathId;
    GUIDE_LOG_ERROR("SwitchMainPath error .");
    return false;
}

void PathDataManager::UpdateManeuvers(uint64_t path_id, const std::list<Maneuver> &maneuvers) {
    GUIDE_ASSERT(path_id == main_path_id_);
    
    if (path_id != main_path_id_) {
        GUIDE_LOG_ERROR("path id error: path_id:{}, main_path_id:{}", path_id, main_path_id_);
    } else {
        maneuvers_ = maneuvers;
    }
}

const std::list<Maneuver>& PathDataManager::GetManeuvers() const {
    return maneuvers_;
}

void PathDataManager::DeletePathResult() {
    main_path_id_ = PathID::kInvalidPathId;

    query_ = nullptr;
    enhance_path_result_ = nullptr;
    path_result_ = nullptr;
}

uint64_t PathDataManager::GetMainPathId() const {
    return main_path_id_;
}
    
EnhancePathResultPtr PathDataManager::GetEnhancePathResult() {
    return enhance_path_result_;
}

EnhancePathQueryPtr PathDataManager::GetEnhancePathQuery() {
    return query_;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
