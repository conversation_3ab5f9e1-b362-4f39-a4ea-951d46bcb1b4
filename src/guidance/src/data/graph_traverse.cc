#include "guidance/src/data/graph_traverse.h"
#include <queue>
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

GraphTraverse::GraphTraverse(EnhanceDataProvider* provider) 
: provider_(provider) {
    root_ = nullptr;
}

GraphTraverse::~GraphTraverse() {
    if (root_ != nullptr) {
        ReleaseGraph(root_);
        root_ = nullptr;
    }
}

void GraphTraverse::ExcludeDirectEdgeIds(const std::set<DirectEdgeId> &ids) {
    exclude_edges_ = ids;
}

bool GraphTraverse::Travsrse(const RouteNodeId &start_node) {
    CreateGraph(provider_, start_node);

    if (root_) {
        branches_.clear();
        for (auto next : root_->nexts) {
            std::vector<TraverseEdgeMeta> branch;
            TraverseGraph(next, branch);
        }
        return true;
    }

    return false;
}

TraverseEdge* GraphTraverse::CreateEmptyTraverseEdge() {
    TraverseEdge* edge = new TraverseEdge();
    GUIDE_ASSERT(edge != nullptr);
    if (edge != nullptr) {
        edge->meta.acc_distance = 0;
        edge->meta.main_distance = 0;
        edge->meta.start_node_id = -1;
        edge->meta.end_node_id = -1;
        edge->meta.valid = false;
    }
    return edge;
}

TraverseEdge* GraphTraverse::CreateTraverseEdge(TraverseEdge* parent, const DirectEdgeId &direct_id) {
    TraverseEdge *ret = new TraverseEdge();
    GUIDE_ASSERT(ret != nullptr);
    if (ret == nullptr) {
        return ret;
    }

    TopolEdge* topo_edge = provider_->GetTopoEdge(direct_id.edge_id);
    ret->meta.direct_edge_id = direct_id;
    ret->meta.inner_edge = topo_edge->GetBaseInfo()->is_inner_edge;
    ret->meta.start_node_id = topo_edge->GetBaseInfo()->start_node_id;
    ret->meta.end_node_id = topo_edge->GetBaseInfo()->end_node_id;
    ret->meta.acc_distance = parent->meta.main_distance + topo_edge->GetBaseInfo()->length;
    ret->meta.main_distance = parent->meta.main_distance;
    ret->meta.valid = true;
    if (!ret->meta.inner_edge) {
        ret->meta.main_distance += topo_edge->GetBaseInfo()->length;
    }
    parent->nexts.emplace_back(ret);

    return ret;
}

bool GraphTraverse::CreateGraph(EnhanceDataProvider *provider, const RouteNodeId &node_id) {
    std::set<RouteNodeId> visit_set;
    std::queue<TraverseEdge*> queue_data;

    if (root_ != nullptr) {
        ReleaseGraph(root_);
    }

    root_ = CreateEmptyTraverseEdge();
    visit_set.insert(node_id);

    std::vector<DirectEdgeId> next_direct_ids;
    if (!provider_->GetNodeOutEdgeIds(node_id, next_direct_ids)) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("GetNodeOutEdgeIds error .");
        return false;
    }

    for (const auto& direct_id : next_direct_ids) {
        if (exclude_edges_.count(direct_id) > 0) {
            continue;
        }

        TraverseEdge* edge = CreateTraverseEdge(root_, direct_id);
        if (edge != nullptr) {
            queue_data.push(edge);
        }
    }

    while (!queue_data.empty()) {
        TraverseEdge* parent = queue_data.front();
        queue_data.pop();

        if (parent == nullptr) {
            continue;
        }

        if (IsStopExpand(parent)) {
            continue;
        }

        RouteNodeId node_id = RouteNodeId(parent->meta.direct_edge_id.edge_id.tile_id, parent->meta.end_node_id);
        if (!parent->meta.direct_edge_id.forward) {
            node_id = RouteNodeId(parent->meta.direct_edge_id.edge_id.tile_id, parent->meta.start_node_id);
        }

        if (visit_set.count(node_id) > 0) { // loop
            parent->meta.valid = false;
            continue;
        }
        visit_set.insert(node_id);

        next_direct_ids.clear();
        provider->GetNodeOutEdgeIds(node_id, next_direct_ids);
        int32_t valid_num = 0;
        for (const auto& next_direct_id : next_direct_ids) {
            if (exclude_edges_.count(next_direct_id) > 0) {
                continue;
            }

            valid_num++;
            TraverseEdge* next_edge = CreateTraverseEdge(parent, next_direct_id);
            if (next_edge != nullptr) {
                queue_data.push(next_edge);
            }
        }

        if (valid_num == 0) {
            parent->meta.valid = false;
        }
    }
    return true;
}

bool GraphTraverse::TraverseGraph(TraverseEdge* node, std::vector<TraverseEdgeMeta>& direct_vec) {

    if (!node->meta.valid) {
        return true;
    }

    direct_vec.emplace_back(node->meta);
    
    if (node->nexts.empty()) {
        branches_.emplace_back(direct_vec);
    } else {
        for (auto next : node->nexts) {
            TraverseGraph(next, direct_vec);
        }
    }

    direct_vec.pop_back();
    return true;
}

int GraphTraverse::GetBranchNum() const {
    return branches_.size();
}

std::vector<TraverseEdgeMeta>* GraphTraverse::GetBranch(int32_t index) {
    if (index < branches_.size()) {
        return (branches_.data() + index);
    }
    return nullptr;
}

bool GraphTraverse::IsStopExpand(TraverseEdge* edge) {
    static float kMainRoadDistance = 30;
    if (!edge->meta.inner_edge && edge->meta.main_distance >= kMainRoadDistance) {
        return true;
    }
    return false;
}

void GraphTraverse::ReleaseGraph(TraverseEdge* node) {
    if (node == nullptr) {
        return;
    }

    for (auto next : node->nexts) {
        ReleaseGraph(next);
    }
    delete node;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
