#ifndef MAP_SRC_GUIDE_SRC_DATA_DATA_MANAGER_H
#define MAP_SRC_GUIDE_SRC_DATA_DATA_MANAGER_H

#include <cstdint>
#include <string>
#include <vector>
#include <memory>
#include <unordered_map>

#include "guidance_def.h"
#include "path_def.h"

#include "guidance/src/common/common_def.h"
#include "guidance/src/data/function_config_list.h"
#include "guidance/src/data/path_data_manager.h"

namespace aurora {
namespace guide {

// TODO: 多线程 & 数据更新时序同步
class DataManager {
public:
    static std::shared_ptr<DataManager> Instance();
    ~DataManager();
    
    bool InitDataProvider(const char* route_data_dir);
    void SetNavigationMode(const NavigationMode mode);
    void SetNavigationStatus(const NavigationStatus status);
    const NavigationMode   GetNavigationMode() const;
    const NavigationStatus GetNavigationStatus() const;

    void UpdatePathDataManager(PathQueryPtr query, PathResultPtr result);
    PathDataManagerPtr GetPathDataManager();

    EnhanceDataProvider* GetEnhanceProvider();

protected:
    DataManager();
    DataManager(const DataManager&) = delete;
    DataManager& operator= (const DataManager&) = delete;

private:
    NavigationMode      navigation_mode_;
    NavigationStatus    navigation_status_;
    EnhanceDataProvider enhance_provider_;
    PathDataManagerPtr  path_data_manager_;
};


}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_DATA_DATA_MANAGER_H
/* EOF */
