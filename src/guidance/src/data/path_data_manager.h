#ifndef MAP_SRC_GUIDE_SRC_DATA_PATH_DATA_MANAGER_H
#define MAP_SRC_GUIDE_SRC_DATA_PATH_DATA_MANAGER_H

#include <memory>
#include <vector>
#include "pointll.h"
#include "path_def.h"

#include "guidance/src/common/common_def.h"
#include "guidance/src/common/maneuver.h"
#include "guidance/src/data/enhance_path_result.h"
#include "guidance/src/data/enhance_data_provider.h"

namespace aurora {
namespace guide {

class PathDataManager {
public:
    PathDataManager(EnhanceDataProvider* provider, PathQueryPtr query, PathResultPtr result);
    ~PathDataManager();

    bool SwitchMainPath(uint64_t target_path_id);

    void UpdateManeuvers(uint64_t path_id, const std::list<Maneuver> &maneuvers);

    const std::list<Maneuver>& GetManeuvers() const;

    uint64_t GetMainPathId() const;
    void DeletePathResult();

    EnhancePathResultPtr GetEnhancePathResult();
    EnhancePathQueryPtr GetEnhancePathQuery();

protected:
    uint64_t main_path_id_;
    EnhanceDataProvider *provider_;
    EnhancePathResultPtr enhance_path_result_;
    EnhancePathQueryPtr query_;
    PathResultPtr path_result_;
    std::list<Maneuver> maneuvers_;
};
using PathDataManagerPtr = std::shared_ptr<PathDataManager>;

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_DATA_PATH_DATA_MANAGER_H
/* EOF */
