#include "guidance/src/data/enhance_junction_view.h"
#include "guidance/src/common/common_def.h"

namespace aurora {
namespace guide {

EnhanceJunctionView::EnhanceJunctionView(JuncviewInfo *junction_view, const DirectEdgeId &from_edge_id, const DirectEdgeId &to_edge_id) 
: from_edge_id_(from_edge_id)
, to_edge_id_(to_edge_id) {

    image_type_ = ImageType::kPNG;
    GUIDE_ASSERT(junction_view != nullptr);
    if (junction_view != nullptr) {
        BuildJunctionView(junction_view);
    }
}

DirectEdgeId EnhanceJunctionView::GetFromEdgeId() const {
    return from_edge_id_;
}
    
DirectEdgeId EnhanceJunctionView::GetToEdgeId() const {
    return to_edge_id_;
}

ImagePtr EnhanceJunctionView::GetBackgroundImage() const {
    return background_ptr_;
}
    
ImagePtr EnhanceJunctionView::GetForegroundImage() const {
    return foreground_ptr_;
}

EnhanceJunctionView::ImageType EnhanceJunctionView::GetImageType() const {
    return EnhanceJunctionView::ImageType::kPNG;
}

JunctionViewType EnhanceJunctionView::GetType() const {
    return type_;
}

std::string EnhanceJunctionView::GetId() const {
    return from_edge_id_.ToString() + "_" + to_edge_id_.ToString() + "_" + std::to_string(type_);
}

void EnhanceJunctionView::BuildJunctionView(JuncviewInfo *junction_view) {
    GUIDE_ASSERT(junction_view != nullptr);
    GUIDE_ASSERT(junction_view->GetBaseInfo() != nullptr);
    GUIDE_ASSERT(from_edge_id_.edge_id.feature_id == junction_view->GetBaseInfo()->in_edge_id);
    GUIDE_ASSERT(from_edge_id_.forward == !(junction_view->GetBaseInfo()->in_edge_dir));
    GUIDE_ASSERT(junction_view->GetBaseInfo()->is_same_mesh);
    GUIDE_ASSERT(junction_view->GetBaseInfo()->is_same_tile);


    GUIDE_ASSERT(to_edge_id_.edge_id.feature_id == junction_view->GetBaseInfo()->out_edge_id);
    GUIDE_ASSERT(to_edge_id_.edge_id.tile_id.mesh_col == junction_view->GetBaseInfo()->out_mesh_col);
    GUIDE_ASSERT(to_edge_id_.edge_id.tile_id.mesh_row == junction_view->GetBaseInfo()->out_mesh_row);

    GUIDE_ASSERT(to_edge_id_.edge_id.tile_id.tile_id == junction_view->GetBaseInfo()->out_tile_id);
    GUIDE_ASSERT(to_edge_id_.edge_id.feature_id == junction_view->GetBaseInfo()->out_edge_id);
    GUIDE_ASSERT(to_edge_id_.forward == !(junction_view->GetBaseInfo()->out_edge_dir));

    if (junction_view != nullptr) {
        background_ptr_ = junction_view->GetBackground();
        foreground_ptr_ = junction_view->GetForeground();

        if (junction_view->GetBaseInfo() == nullptr) {
            type_ = JunctionViewType::kNone;
        } else {
            switch (junction_view->GetBaseInfo()->type) {
            case 0: type_ = JunctionViewType::kRealView; break;
            case 1: type_ = JunctionViewType::kDiagramView; break;
            case 2: type_ = JunctionViewType::kDirectionBoard; break;
            default: type_ = JunctionViewType::kNone; break;
            }
        }

        GUIDE_ASSERT(background_ptr_ != nullptr);
        GUIDE_ASSERT(foreground_ptr_ != nullptr);
    }
}

}  // namespace guide
}  // namespace aurora