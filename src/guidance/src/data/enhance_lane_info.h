#ifndef MAP_SRC_GUIDE_SRC_DATA_ENHANCE_LANE_INFO_H
#define MAP_SRC_GUIDE_SRC_DATA_ENHANCE_LANE_INFO_H

#include <cstdint>
#include <vector>
#include <memory>

#include "guidance_def.h"
#include "route_data/route_data_def.h"
#include "guidance/src/data/enhance_data_provider.h"

namespace aurora {
namespace guide {

class EnhanceLaneRelation {
public:
    enum TargetLaneType {
        kValueNotRecommend = 0,
        kValueEmptyLane,
        kValueStraight,
        kValueLeft,
        kValueRight,
        kValueUTurn,
        kValueSlightLeft,
        kValueSlightRight
    };

    EnhanceLaneRelation(const DirectEdgeId &to_edge_id, uint64_t relation);

    TargetLaneType GetLaneRelation(int32_t lane_index) const;

    const DirectEdgeId& GetToDirectEdgeId() const;

protected:
    struct Element {
        uint8_t  l:4;
        uint8_t  h:4;
    };

    union Data {
        uint64_t v;
        Element e[8];
    };
    Data d_;

    DirectEdgeId to_direct_id_;
};


class EnhanceLaneInfo {
public:
    EnhanceLaneInfo(const DirectEdgeId &direct_id, DirectEdgeId *to_directed_id, EnhanceDataProvider *provider);
    ~EnhanceLaneInfo() = default;

    int32_t GetRelationNum() const;
    int32_t GetFromEdgeLaneNum() const;

    EnhanceLaneRelation* GetRelationToEdgeId();

    std::vector<LaneAction> GetBackLaneActions();
    std::vector<LaneAction> GetFrontLaneActions();

    std::string GetId() const;

protected:
    void BuildLaneInfo(EnhanceDataProvider *provider);

private:
    bool valid_;
    uint8_t  lane_num_;
    LaneType lane_types_[16];
    DirectEdgeId from_edge_id_;
    DirectEdgeId to_edge_id_; 
    std::vector<EnhanceLaneRelation> relations_;

}; // EnhanceLaneInfo

using EnhanceLaneInfoPtr = std::shared_ptr<EnhanceLaneInfo>;

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_DATA_ENHANCE_LANE_INFO_H
/* EOF */
