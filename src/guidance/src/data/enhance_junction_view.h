#ifndef MAP_SRC_GUIDE_SRC_DATA_ENHANCE_JUNCTION_VIEW_H
#define MAP_SRC_GUIDE_SRC_DATA_ENHANCE_JUNCTION_VIEW_H

#include <cstdint>
#include <string>
#include <memory>
#include "guidance_def.h"
#include "route_data/route_data_def.h"
#include "guidance/src/data/enhance_data_provider.h"

namespace aurora {
namespace guide {

class EnhanceJunctionView {
public:
    enum class ImageType {
        kNone = 0,
        kPNG,
    };

    EnhanceJunctionView(JuncviewInfo *junction_view, const DirectEdgeId &from_edge_id, const DirectEdgeId &to_end_id);
    ~EnhanceJunctionView() = default;

    DirectEdgeId GetFromEdgeId() const;
    DirectEdgeId GetToEdgeId() const;

    ImagePtr GetBackgroundImage() const;
    ImagePtr GetForegroundImage() const;

    ImageType GetImageType() const;

    JunctionViewType GetType() const;

    std::string GetId() const;

protected:
    void BuildJunctionView(JuncviewInfo *junction_view);

protected:
    JunctionViewType type_;
    ImageType image_type_;
    DirectEdgeId from_edge_id_;
    DirectEdgeId to_edge_id_;
    ImagePtr background_ptr_;
    ImagePtr foreground_ptr_;
}; // class

using EnhanceJunctionViewPtr = std::shared_ptr<EnhanceJunctionView>;

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_DATA_ENHANCE_JUNCTION_VIEW_H
/* EOF */
