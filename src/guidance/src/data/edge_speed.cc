#include "guidance/src/data/edge_speed.h"
#include "guidance/src/data/directed_edge.h"

namespace aurora {
namespace guide {

static double LINK_SPEED[parser::RoadClass::kRoadClassCount][2][4] = {
        // Highway speeds
        {{22.22f, 27.78f, 30.56f, 30.56f},  // Out city
         {22.22f, 25.00f, 27.78f, 30.56f}}, // In city
        
        // National road speeds
        {{13.89f, 18.06f, 19.44f, 22.22f},  // Out city
         {12.50f, 13.89f, 16.67f, 18.06f}}, // In city
        
        // Provincial road speeds
        {{13.89f, 16.67f, 18.06f, 19.44f},  // Out city
         {12.50f, 13.89f, 15.28f, 16.67f}}, // In city
        
        // County road speeds
        {{11.11f, 12.50f, 13.89f, 15.28f},  // Out city
         {9.72f, 11.11f, 12.50f, 13.89f}},  // In city
        
        // Township road speeds
        {{8.33f, 9.72f, 11.11f, 12.50f},    // Out city
         {8.33f, 9.72f, 11.11f, 12.50f}},   // In city
        
        // Internal road speeds
        {{4.17f, 5.56f, 6.94f, 8.33f},      // Out city
         {4.17f, 5.56f, 6.94f, 8.33f}},     // In city
        
        // City highway speeds
        {{13.89f, 19.44f, 22.22f, 23.61f},  // Out city
         {13.89f, 18.06f, 19.44f, 20.28f}}, // In city
        
        // Main road speeds
        {{11.11f, 13.89f, 16.67f, 18.06f},  // Out city
         {9.72f, 12.50f, 13.89f, 15.28f}},  // In city
        
        // Secondary road speeds
        {{8.33f, 11.11f, 12.50f, 13.89f},   // Out city
         {8.33f, 9.72f, 12.50f, 13.89f}},   // In city
        
        // Normal road speeds
        {{5.56f, 6.94f, 8.33f, 9.72f},      // Out city
         {5.56f, 6.94f, 8.33f, 9.72f}},     // In city
        
        // Narrow path speeds
        {{2.78f, 4.17f, 5.56f, 6.94f},      // Out city
         {2.78f, 4.17f, 5.56f, 6.94f}}      // In city
};

double EdgeSpeed::GetSpeedMPerSec(DirectedEdge *edge) {
    // Special road types
    if (edge->GetEdgeType() == parser::EdgeType::kEdgeTypeFerry) {
        return 6.9f;  // 25km/h
    }

    if (edge->IsAreaEdge()) {
        return 2.77f;  // 5km/h
    }

    if (edge->IsRamp()) {
        return 11.11f;  // 40km/h
    }

    if (edge->GetEdgeForm() == parser::EdgeForm::kEdgeFormJCT) {
        if (edge->GetRoadClass() == parser::RoadClass::kRoadClassHighway) {
            return 22.22f;  // 80km/h
        } else {
            return 11.11f;  // 40km/h
        }
    }

    // Get lane count index
    uint32_t lane_cnt_index = edge->GetForwardLaneNum();
    lane_cnt_index = (lane_cnt_index > 3 ? 3 : lane_cnt_index);
    double rt_speed = LINK_SPEED[edge->GetRoadClass()][edge->IsCityEdge()][lane_cnt_index];

    // Special case adjustments
    if (edge->GetEdgeForm() == parser::EdgeForm::kEdgeFormIC && rt_speed > 8.33f) {
        rt_speed = 8.33f;  // 30km/h
    }

    return rt_speed;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
