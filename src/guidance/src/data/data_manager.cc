#ifndef MAP_SRC_GUIDANCE_SRC_DATA_DATA_MANAGER_H
#define MAP_SRC_GUIDANCE_SRC_DATA_DATA_MANAGER_H

#include "guidance/src/data/data_manager.h"

#include <mutex>
#include "path_def.h"

#include "data_provider.h"
#include "guidance/src/common/guide_log.h"
#include "guidance/src/common/common_def.h"

namespace aurora {
namespace guide {

std::shared_ptr<DataManager> DataManager::Instance() {
    static std::shared_ptr<DataManager> s_instance;
    static std::mutex s_mutex; 

    if (s_instance == nullptr) {
        std::lock_guard<std::mutex> lock(s_mutex);
        if (s_instance == nullptr) {
            s_instance.reset(new DataManager());
            // s_instance = std::make_shared<DataManager>();
        }
    }
    return s_instance;
}

DataManager::DataManager() {
    navigation_status_ = NavigationStatus::kNavigationStatusNone;
    navigation_mode_ = NavigationMode::kNavigationModeNone;
 }

DataManager::~DataManager() {

}

bool DataManager::InitDataProvider(const char* route_data_dir) {
    std::shared_ptr<parser::DataProvider> provider = std::make_shared<parser::DataProvider>();

#if 0
    if ((route_data_dir != nullptr) && !provider->InitRouteParser(route_data_dir)) {
        GUIDE_LOG_ERROR("init parser::DataProvider error: {} .", route_data_dir);
        GUIDE_ASSERT(false);
        return false;
    }
#endif

    enhance_provider_.SetDataProvider(provider);
    return true;
}

void DataManager::SetNavigationMode(const NavigationMode mode) {
    navigation_mode_ = mode;
}

void DataManager::SetNavigationStatus(const NavigationStatus status) {
    navigation_status_ = status;
}

const NavigationMode  DataManager::GetNavigationMode() const {
    return navigation_mode_;
}

const NavigationStatus DataManager::GetNavigationStatus() const {
    return navigation_status_;
}

void DataManager::UpdatePathDataManager(PathQueryPtr query, PathResultPtr result) {
    path_data_manager_ = std::make_shared<PathDataManager>(&enhance_provider_, query, result);
}

PathDataManagerPtr DataManager::GetPathDataManager() {
    return path_data_manager_;
}

EnhanceDataProvider* DataManager::GetEnhanceProvider() {
    return &enhance_provider_;
}

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDANCE_SRC_DATA_DATA_MANAGER_H
/* EOF */
