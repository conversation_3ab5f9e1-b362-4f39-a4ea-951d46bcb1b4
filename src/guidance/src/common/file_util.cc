#include "guidance/src/common/file_util.h"
#include <filesystem>
#include <fstream>

namespace aurora {

std::string FileUtil::ReadTextFile(const std::string &file_name) {
    std::ifstream file(file_name);
    std::string content, line;
    while (std::getline(file, line)) {
        content += line + "\n";
    }
    return content;
}

bool FileUtil::IsFileExist(const std::string &file_name) {
    return std::filesystem::exists(file_name) && !std::filesystem::is_directory(file_name);
}

bool FileUtil::IsDirectoryExist(const std::string &dir) {
    return std::filesystem::exists(dir) && std::filesystem::is_directory(dir);
}

bool FileUtil::CreateDirectory(const std::string &dir) {
    return std::filesystem::create_directories(dir);
}

}  // aurora
/* EOF */
