#ifndef MAP_SRC_GUIDE_SRC_COMMOM_FILE_UTIL_H
#define MAP_SRC_GUIDE_SRC_COMMOM_FILE_UTIL_H

#include <string>

namespace aurora {

class FileUtil {
public:
    FileUtil() = delete;

    static std::string ReadTextFile(const std::string &file_name);
    static bool        IsFileExist(const std::string &file_name);
    static bool        IsDirectoryExist(const std::string &dir);
    static bool        CreateDirectory(const std::string &dir);
};

}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_COMMOM_FILE_UTIL_H
/* EOF */
