#include "guidance/src/common/string_util.h"

namespace aurora {
namespace guide {

size_t StringUtil::GetWordCount(const std::string& street_name) {
    size_t word_count = 0;
    std::string::const_iterator pos = street_name.begin();
    std::string::const_iterator end = street_name.end();

    while (pos != end) {
        // Skip over space, white space, and punctuation
        while (pos != end && ((*pos == ' ') || std::isspace(*pos) || std::ispunct(*pos))) {
            ++pos;
        }

        // Word found - increment
        word_count += (pos != end);

        // Skip over letters in word
        while (pos != end && ((*pos != ' ') && (!std::isspace(*pos)) && (!std::ispunct(*pos)))) {
            ++pos;
        }
    }
    return word_count;
}

std::size_t StringUtil::StrlenUtf8(const std::string& str) {
    std::size_t length = 0;
    for (char c : str) {
        if ((c & 0xC0) != 0x80) {
            ++length;
        }
    }
    return length;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
