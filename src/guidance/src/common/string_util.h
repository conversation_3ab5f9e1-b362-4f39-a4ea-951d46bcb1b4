#ifndef MAP_SRC_GUIDANCE_SRC_COMMOM_STRING_UTIL_H
#define MAP_SRC_GUIDANCE_SRC_COMMOM_STRING_UTIL_H

#include <string>

namespace aurora {
namespace guide {

class StringUtil {
public:
    StringUtil() = delete;

    static size_t GetWordCount(const std::string& street_name);

    static std::size_t StrlenUtf8(const std::string& str);
};

}  // namespace guide
}  // namespace aurora

#endif  // MAP_SRC_GUIDANCE_SRC_COMMOM_STRING_UTIL_H
/* EOF */
