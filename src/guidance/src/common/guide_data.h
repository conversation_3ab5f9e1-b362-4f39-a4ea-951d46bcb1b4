#ifndef MAP_SRC_GUIDE_SRC_COMMOM_GUIDE_DATA_H
#define MAP_SRC_GUIDE_SRC_COMMOM_GUIDE_DATA_H

#include <string>
#include "guidance/src/data/vehicle_position.h"
#include "guidance/src/data/enhance_lane_info.h"
#include "guidance/src/data/enhance_junction_view.h"

namespace aurora {
namespace guide {

struct GuideData {
    VehiclePosition pos;
    std::string voice_text;
    bool        is_arrive_dest;

    EnhanceLaneInfoPtr lane_info;
    EnhanceJunctionViewPtr junction_view;

    GuideData() {
        is_arrive_dest = false;
    }
};

}  // namespace guide
}  // namespace aurora
#endif
/* EOF */