#ifndef MAP_SRC_GUIDANCE_SRC_COMMOM_MANEUVER_H
#define MAP_SRC_GUIDANCE_SRC_COMMOM_MANEUVER_H

#include <cstdint>
#include <string>
#include <vector>
#include <list>
#include <memory>

#include "guidance_def.h"
#include "guidance/src/common/street_names.h"
#include "guidance/src/data/enhance_sign_post.h"
#include "guidance/src/data/enhance_junction_view.h"
#include "guidance/src/data/enhance_facility.h"
#include "guidance/src/data/enhance_toll_station.h"
#include "guidance/src/data/enhance_lane_info.h"
#include "guidance/src/maker/normalize/verbal_text_formatter.h"

namespace aurora {
namespace guide {

struct SignPost {};
struct JunctionView {};

class VerbalTextFormatter;
class Maneuver {
public:
    enum class RelativeDirection : uint8_t {
        kNone = 0,
        kKeepStraight,
        
        // 用于轻微的右转或保持在右侧车道的情况
        // 通常对应较小的转弯角度
        // 在高速公路场景中，常用于"保持右侧"指令
        kKeepRight,

        // 用于明显的右转操作
        // 对应较大的转弯角度（通常45-135度范围）
        // 是标准的右转指令
        kRight,

        KReverse,

        // kLeft 表示标准的左转机动，通常用于
        //    明确的左转指令
        //    转向角度较大的左转（通常在 201-329 度范围内）
        kLeft,
        
        // kKeepLeft 表示保持左侧行驶，通常用于：
        //   分叉路口时选择左侧路径
        //   轻微的左转或保持左侧车道
        //   高速公路出入口的左侧选择
        kKeepLeft
    };

    // warning: 该枚举字段跟json中的"cardinal_directions"有关联，不能单一修改
    enum CardinalDirection : uint8_t { 
        kNorth = 0,
        kNorthEast,
        kEast,
        kEastSouth,
        kSouth,
        kSouthWest,
        kWest,
        kNorthWest
    };

    Maneuver();
    ~Maneuver() = default;

    void SetType(ManeuverType type);
    void SetAuxType(AuxManeuverType aux_type);
    void SetLengthMeter(float meter);
    void SetBeginHeading(uint16_t begin_heading);
    void SetEndHeading(uint16_t end_heading);
    void SetTurnDegree(uint16_t turn_degree);
    void SetBasicTime(float sec);

    void SetBeginNodeIndex(int32_t node_idx);
    void SetEndNodeIndex(int32_t node_idx);
    void SetRamp(bool is_ramp);
    void SetTurnChannel(bool turn_channel);
    void SetRoundAbout(bool roundabout);
    void SetRoundAboutExitCount(uint8_t count);
    void SetStreetIntersection(bool flag);
    void SetDriveOnRight(bool flag);
    void SetPortionToll(bool flag);
    void SetPortionUnpaved(bool flag);
    void SetPortionHighway(bool flag);
    void SetTurnRestriction(bool flag);
    void SetFork(bool flag);
    void SetTee(bool flag);
    void SetIntersectingForwardEdge(bool flag);
    void SetVerbalFormatter(std::shared_ptr<VerbalTextFormatter> formatter);
    void SetInternalLeftTurnCount(int32_t count);
    void SetInternalRightTurnCount(int32_t count);
    void SetBeginCardinalDirection(CardinalDirection begin_cardinal_direction);
    void SetBeginRelativeDirection(RelativeDirection begin_relative_direction);
    void SetBeginIntersectingEdgeNameConsistency(bool consistency);
    void SetMergeToRelativeDirection(RelativeDirection merge_to_relative_direction);
    void SetContainObviousManeuver(bool flag);
    void SetPoints(const std::vector<PointXY<double>> &points);
    void SetPoints(std::vector<PointXY<double>> &&points);
    void SetHasRightOutXEdge(bool flag);
    void SetHasLeftOutXEdge(bool flag);
    void SetToStayOn(bool flag);
    void SetHasCollapsedSmallEndRampFork(bool flag);
    void SetSignPosts(std::vector<EnhanceSignPostPtr> &&sign_posts);

    void SetInstruction(std::string &&instruction);
    void SetVerbalAlertInstruction(std::string &&instruction);
    void SetVerbalPreInstruction(std::string &&instruction);
    void SetVerbalPostInstruction(std::string &&instruction);
    void SetVerbalSuccinctInstruction(std::string &&instruction);

    ManeuverType        GetType() const;
    AuxManeuverType     GetAuxType() const;
    float               GetLengthMeter() const;
    float               GetLengthMiles() const;
    float               GetLengthKilometers() const;
    float               GetBasicTime() const;
    uint16_t            GetBeginHeading() const;
    uint16_t            GetEndHeading() const;
    uint16_t            GetTurnDegree() const;

    int32_t             GetBeginNodeIndex() const;
    int32_t             GetEndNodeIndex() const;
    bool                GetRamp() const;
    bool                GetTurnChannel() const;
    bool                GetRoundAbout() const;
    uint8_t             GetRoundAboutExitCount() const;
    bool                GetStreetIntersection() const;
    bool                GetDriveOnRight() const;
    bool                GetPortionToll() const;
    bool                GetTurnRestriction() const;
    bool                GetPortionUnpaved() const;
    bool                GetPortionHighway() const;
    bool                GetFork() const;
    bool                GetTee() const;
    bool                GetIntersectingForwardEdge() const;
    const VerbalTextFormatter* GetVerbalFormatter() const;
    uint16_t            GetInternalLeftTurnCount() const;
    uint16_t            GetInternalRightTurnCount() const;
    CardinalDirection   GetBeginCardinalDirection() const;
    RelativeDirection   GetBeginRelativeDirection() const;
    bool                GetBeginIntersectingEdgeNameConsistency() const;
    RelativeDirection   GetMergeToRelativeDirection() const;
    bool                GetContainObviousManeuver() const;

    bool                HasInstruction() const;
    bool                HasVerbalAlertInstruction() const;
    bool                HasVerbalPreInstruction() const;
    bool                HasVerbalPostInstruction() const;
    bool                HasVerbalSuccinctInstruction() const;

    const std::string&  GetInstruction() const;
    const std::string&  GetVerbalAlertInstruction() const;
    const std::string&  GetVerbalPreInstruction() const;
    const std::string&  GetVerbalPostInstruction() const;
    const std::string&  GetVerbalSuccinctInstruction() const;

    bool                IsStartType() const;
    bool                IsDestinationType() const;
    bool                IsMergeType() const;
    bool                IsRoundabout() const;

    bool                HasSimilarName(const Maneuver *other_maneuver) const;
    const std::vector<PointXY<double>>& GetPoints() const;
    bool                HasLeftOutXEdge() const;
    bool                HasRightOutXEdge() const;
    bool                GetToStayOn() const;
    bool                GetHasCollapsedSmallEndRampFork() const;

    bool                HasLaneInfo() const;
    const std::vector<EnhanceLaneInfoPtr>& GetLaneInfos() const;
    std::vector<EnhanceLaneInfoPtr>& GetLaneInfos();

    bool                HasSignPost() const;
    const std::vector<EnhanceSignPostPtr>& GetSignPosts() const;
    std::vector<EnhanceSignPostPtr>& GetSignPosts();

    bool                HasJunctionView() const;
    const std::vector<EnhanceJunctionViewPtr>& GetJunctionViews() const;
    std::vector<EnhanceJunctionViewPtr>& GetJunctionViews();

    bool                HasFacility() const;
    const std::vector<EnhanceFacilityPtr>& GetFacilities() const;
    std::vector<EnhanceFacilityPtr>& GetFacilities();
    
    bool                HasTollStation() const;
    const std::vector<EnhanceTollStationPtr>& GetTollStations() const;
    std::vector<EnhanceTollStationPtr>& GetTollStations();

    std::shared_ptr<StreetNames> GetStreetNames();
    const std::shared_ptr<StreetNames> GetStreetNames() const;
    void  SetStreetNames(std::shared_ptr<StreetNames> street_names);
    void  SetStreetNames(const std::vector<std::pair<std::string, bool>>& names);
    void  ClearStreetNames();
    bool  HasStreetNames() const;
    bool  GetIncludeVerbalPreTransitionLength() const;

    std::shared_ptr<StreetNames> GetBeginStreetNames();
    void  SetBeginStreetNames(std::shared_ptr<StreetNames> begin_street_names);
    void  SetBeginStreetNames(const std::vector<std::pair<std::string, bool>>& names);
    void  ClearBeginStreetNames();
    bool  HasBeginStreetNames() const;

    std::shared_ptr<StreetNames> GetCrossStreetNames();
    void  SetCrossStreetNames(std::shared_ptr<StreetNames> cross_street_names);
    void  SetCrossStreetNames(const std::vector<std::pair<std::string, bool>>& names);
    void  ClearCrossStreetNames();
    bool  HasCrossStreetNames() const;

    void  SetHasLongStreetName(bool flag);
    bool  GetHasLongStreetName() const;

    void  SetDistantVerbalMultiCue(bool flag);
    bool  GetDistantVerbalMultiCue();

    void SetImminentVerbalMultiCue(bool flag);
    bool GetImminentVerbalMultiCue();

public:
static std::string     GetCommonBaseNameUtf8(const std::string &lhs, const std::string &rhs);

private:
    ManeuverType    type_;          // 机动点类型定义
    AuxManeuverType aux_type_;      //
    float           length_meter_;  // 从上一个机动点到当前机动点的距离<unit:m>
    float           basic_time_sec_;    // 基础通行时间 <unit:s>

    uint16_t        begin_heading_; // maneuver内第一条link的begin heading
    uint16_t        end_heading_;   // maneuver内最后一条link的end heading
    // uint32_t        begin_link_idx_; 
    // uint32_t        end_link_idx_;
    uint32_t        begin_geo_idx_;
    uint32_t        end_geo_idx_;
    uint16_t        turn_degree_;    //上一个maneuver的end heading跟当前maneuver的begin heading的delta turn

    uint8_t         ramp    :1;
    uint8_t         brigde  :1;
    uint8_t         tunnel  :1;
    uint8_t         service_area:1;
    uint8_t         turn_channel:1;
    uint8_t         round_about:1;
    uint8_t         street_intersection:1;
    uint8_t         drive_right :1;

    uint8_t         portion_toll:1;
    uint8_t         turn_restriction:1;
    uint8_t         portion_unpaved:1;
    uint8_t         portion_highway:1;
    uint8_t         fork:1;
    // 路口处存在一条向前延伸的可通行道路，但路径并不沿着这条最直的道路行进。
    // 这个标志帮助导航系统识别需要特别说明的路口情况。
    uint8_t         intersecting_forward_edge :1; // 
    uint8_t         tee:1;
    uint8_t         begin_intersecting_xedge_name_consistency:1;


    uint8_t         contains_obvious_maneuver :1;
    uint8_t         has_left_out_xedge :1;
    uint8_t         has_right_out_xedge :1;
    uint8_t         to_stay_on :1; // 跟上一个Maneuver 相同street name
    uint8_t         has_collapsed_small_end_ramp_fork :1; // 
    uint8_t         include_verbal_pre_transition_length:1;
    uint8_t         has_long_street_name_:1;
    uint8_t         distant_verbal_multi_cue_: 1;  // 远程语音多重提示：当两个连续的操作动作之间存在可通行的交叉路口时使用

    uint8_t         imminent_verbal_multi_cue_:1;  // 即时语音多重提示：当两个连续的操作动作之间没有可通行的交叉路口时使用
    uint8_t         reserve:7;


    uint8_t         round_about_exit_count_;
    int32_t         begin_node_idx_;
    int32_t         end_node_idx_;

    uint16_t        internal_left_turn_count_;  // 从上一个maneuver的结束点到当前Maneuver的结束需要经过左转个数
    uint16_t        internal_right_turn_count_; // 从上一个maneuver的结束点到当前Maneuver的结束需要经过的右转个数
    RelativeDirection begin_relative_direction_; // 通过turn_degree_计算出来的
    CardinalDirection begin_cardinal_direction_; // maneuver内第一条link的begin heading
    RelativeDirection merge_to_relative_direction_;

    std::shared_ptr<StreetNames> street_names_;
    std::shared_ptr<StreetNames> begin_street_names_;
    std::shared_ptr<StreetNames> cross_street_names_;  // 横向道路名称

    std::vector<EnhanceLaneInfoPtr> lane_infos_;
    std::vector<EnhanceSignPostPtr> sign_posts_;
    std::vector<EnhanceJunctionViewPtr> junction_views_; 
    std::vector<EnhanceFacilityPtr> facilities_;
    std::vector<EnhanceTollStationPtr> toll_stations_; 

    std::string instruction_;   // 用于面板显示,此处暂不使用，导航中提示导航面板信息
    std::string verbal_pre_instruction_;   // 转换前指令，在即将执行操作时播报的主要语音指令
    std::string verbal_post_instruction_;  // 转换后指令，在完成操作后播报，用于确认和后续引导
    std::string verbal_alert_instruction_;  // 警告指令，在即将到达操作点之前提前播报，给用户预警
    std::string verbal_succinct_instruction_; // 简洁的播报指令，用于多重提示场景中的当前指令部分。

    std::vector<PointXY<double>>   geos_;
    std::shared_ptr<VerbalTextFormatter> formatter_;
};

std::string ManeuverTypeToString(ManeuverType type);
using ManeuverConstIt = std::list<Maneuver>::const_iterator;

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDANCE_SRC_COMMOM_MANEUVER_H
/* EOF */
