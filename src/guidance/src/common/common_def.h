#ifndef MAP_SRC_GUIDE_SRC_COMMOM_COMMON_DEF_H
#define MAP_SRC_GUIDE_SRC_COMMOM_COMMON_DEF_H

#include <cstdint>
#include <assert.h>

#include "data_provider.h"
#include "location_def.h"
#include "route_data/route_tile_reader.h"

#if 0
#define GUIDE_ASSERT(exp)  assert((exp))
#else
#define GUIDE_ASSERT(exp)
#endif


namespace aurora {
namespace guide {

using RouteTileId = parser::RouteTileID;
using RouteEdgeId = parser::RouteEdgeID;
using RouteNodeId = parser::RouteNodeID;
using RouteTileReader = parser::RouteTileReader;
using RouteTilePackagePtr = parser::RouteTilePackagePtr;
using TopolEdge = parser::TopolEdge;
using AugmentEdge = parser::AugmentEdge;
using RouteNode = parser::RouteNode;
using RouteTileReaderPtr = std::shared_ptr<RouteTileReader>;
using LaneInfoSet = parser::LaneInfoSet;
using LaneInfo = parser::LaneInfo;
using LaneRelation = parser::LaneRelation;
using LaneType = parser::LaneType;
using EdgeDirection = parser::EdgeDirection;
using SignPostInfo = parser::SignpostInfo;
using SignpostInfoSet = parser::SignpostInfoSet;
using JuncviewInfo = parser::JuncviewInfo;
using JuncviewInfoSet = parser::JuncviewInfoSet;
using ImagePtr = parser::ImagePtr;
using FacilityInfo = parser::FacilityInfo;
using FacilityInfoSet = parser::FacilityInfoSet;
using FacilityType = parser::RoadFacilityType;
using TollGateInfo = parser::TollgateInfo;
using TollGateInfoSet = parser::TollgateInfoSet;
using TollgateGateInfo = parser::TollgateGateInfo;

using MatchResult = loc::MatchResult;
using MatchResultPtr = std::shared_ptr<loc::MatchResult>;

enum class DistaneUnit {
    kKilometer = 0,
    kMile,
};

enum PathID {
    kInvalidPathId = 0,
};

enum TurnType : uint8_t {
    kStraight = 0,
    kSlightRight,
    kRight,
    kSharpRight,
    kReverse,
    kSharpLeft,
    kLeft,
    kSlightLeft
};

enum class RelativeDirection {
    kRelativeStraight = 0,
};

enum class NavigationStatus {
    kNavigationStatusNone = 0,
    kNavigationStatusOn,
    KNavigationStatusOff,
    kNavigationStatusPause
};

struct DirectEdgeId {
    RouteEdgeId edge_id;
    bool forward;

    bool operator< (const DirectEdgeId & direct_id) const {
        if (edge_id.tile_id != direct_id.edge_id.tile_id) {
            return (edge_id.tile_id < direct_id.edge_id.tile_id);
        } else {
            if (edge_id.feature_id != direct_id.edge_id.feature_id) {
                return (edge_id.feature_id < direct_id.edge_id.feature_id);
            } else {
                return (forward < direct_id.forward);
            }
        }
    }

    bool operator == (const DirectEdgeId &obj) const {
        return ((obj.edge_id == edge_id) && (obj.forward == forward));
    }

    bool operator != (const DirectEdgeId &obj) const {
        return !(*this == obj);
    }

    std::string ToString() const {
        return  std::to_string(edge_id.tile_id.value) + "_" + 
                    std::to_string(edge_id.feature_id) + 
                        std::to_string(forward);
    }
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_COMMOM_COMMON_DEF_H
/* EOF */
