#ifndef MAP_SRC_GUIDE_SRC_GUIDANCE_EVENT_H
#define MAP_SRC_GUIDE_SRC_GUIDANCE_EVENT_H

#include <string>
#include <vector>
#include <list>
#include "guidance_listener.h"
#include "loopthread.h"

#include "guidance/src/common/maneuver.h"

namespace aurora {
namespace guide {

class GuidanceEvent {
public:
    GuidanceEvent();
    ~GuidanceEvent();

    void Start();
    void Stop();

    void PlayVoice(const std::string &text);
    void PlayRing(const TTSScenePlay scene_id);
    void StartNavigation(NavigationMode mode, uint64_t path_id);
    void PauseNavigation(NavigationMode mode);
    void ResumeNavigation(NavigationMode mode);
    void StopNavigation(NavigationMode mode, NavigationStopCode code);
    void ArriveDestiontion(NavigationMode mode);
    void SwitchPathStatus(uint64_t path_id, bool success);

    void ShowJunctionView(JunctionViewInfoPtr info);
    void ShowLaneInfo(NavigationLaneInfoPtr info);

    void NotifyPathManeuverDetail(int64_t path_id, const std::list<Maneuver> &maneuver_list);

    int32_t AddListener(IGuidanceListenerPtr obs);
    int32_t RemoveListener(IGuidanceListenerPtr obs);

protected:
    void NotifyPathManeuverDetail(int64_t path_id, const std::vector<ManeuverDetail> &details);
    bool ManeuverDetailCanMerge(const ManeuverDetail &lhs, const ManeuverDetail &rhs);

protected:
    LoopThread thread_;
    std::vector<INavigationListenerPtr> nav_obs_;
    std::vector<ICruiseListenerPtr> cruise_obs_;
    std::vector<ISoundListenerPtr> sound_obs_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDANCE_EVENT_H
/* EOF */
