#include "guidance/src/guide/guide_runtime.h"
#include "guidance_def.h"
#include "guidance/src/common/guide_log.h"
#include "errorcode.h"

namespace aurora {
namespace guide {

GuideRuntime::GuideRuntime(GuidanceEvent *guide_event)
: guide_event_(guide_event) {
    nav_status_ = NavigationStatus::kNavigationStatusNone;
    mode_ = NavigationMode::kNavigationModeNone;
}

GuideRuntime::~GuideRuntime() {

}

int32_t GuideRuntime::Start() {
    return 0;
}

int32_t GuideRuntime::Stop() {
    return 0;
}

bool GuideRuntime::IsGuide() const {
    if ((mode_ == NavigationMode::kNavigationModeGPS || mode_ == NavigationMode::kNavigationModeSimulation ||
            mode_ == NavigationMode::kNavigationModeCruise) && 
                (nav_status_ == NavigationStatus::kNavigationStatusOn)) {
        return true;
    }
    return false;
}


int32_t GuideRuntime::StartNavigation(NavigationMode mode, PathDataManagerPtr path_manager) {

    if (mode == NavigationMode::kNavigationModeGPS ||
        mode == NavigationMode::kNavigationModeSimulation ||
        mode == NavigationMode::kNavigationModeCruise) {

        mode_ = mode;
        nav_status_ = NavigationStatus::kNavigationStatusOn;
        path_manager_ = path_manager;
        tracker_ = AbstractStateTracker::Create(mode, path_manager);
        guide_event_->StartNavigation(mode, path_manager->GetMainPathId());

    } else {
        // ignore
    }

    // TODO:
    return 0;
}
    
int32_t GuideRuntime::StopNavigation() {
    
    // #1. interrupt

    // #2. clear 

    // #3. callback

    tracker_ = nullptr;
    mode_ = NavigationMode::kNavigationModeNone;
    nav_status_ = NavigationStatus::KNavigationStatusOff;
    path_manager_ = nullptr;
    guide_event_->StopNavigation(mode_, NavigationStopCode::kStopCodeAppTrigger);

    debug_ccps_.clear();
    debug_ccps_.shrink_to_fit();

    debug_guide_datas_.clear();
    debug_guide_datas_.shrink_to_fit();

    return 0;
}

int32_t GuideRuntime::PauseNavigation() {
    nav_status_ = NavigationStatus::kNavigationStatusPause;
    // #1. callback
    guide_event_->PauseNavigation(mode_);
    return 0;
}
    
int32_t GuideRuntime::ResumeNavigation() {
    nav_status_ = NavigationStatus::kNavigationStatusOn;
    guide_event_->ResumeNavigation(mode_);
    return 0;
}

int32_t GuideRuntime::DoGuide(const VehiclePosition &ccp) {
    if (tracker_ == nullptr) {
        GUIDE_ASSERT(tracker_ != nullptr);
        GUIDE_LOG_ERROR("tracker_ is nullptr .");
        return ErrorCode::kErrorCodeParamNullPointer;
    }

    GuideData guide_data;
    tracker_->DoGuide(ccp, guide_data);
    ProcessGuideData(guide_data);
    ProcessDebug(ccp, guide_data);
    return 0;
}

void GuideRuntime::ProcessGuideData(const GuideData &guide_data) {
    if (guide_event_ == nullptr) {
        return;
    }

    if (!guide_data.voice_text.empty()) {
        guide_event_->PlayVoice(guide_data.voice_text);
    }

    if (guide_data.is_arrive_dest) {
        guide_event_->ArriveDestiontion(mode_);
        guide_event_->StopNavigation(mode_, NavigationStopCode::kStopCodeDestination);
    }

    if (guide_data.lane_info != nullptr) {
        NavigationLaneInfoPtr lane_info = std::make_shared<NavigationLaneInfo>();
        lane_info->id = guide_data.lane_info->GetId();
        lane_info->back_lanes = guide_data.lane_info->GetBackLaneActions();
        lane_info->front_lanes = guide_data.lane_info->GetFrontLaneActions();
        guide_event_->ShowLaneInfo(lane_info);
    } else {
        guide_event_->ShowLaneInfo(nullptr);
    }

    if (guide_data.junction_view != nullptr) {
        JunctionViewInfoPtr junc_info = std::make_shared<JunctionViewInfo>();
        junc_info->id = guide_data.junction_view->GetId();
        junc_info->type = guide_data.junction_view->GetType();
        if (guide_data.junction_view->GetBackgroundImage() != nullptr) {
            junc_info->back_view_data = *(guide_data.junction_view->GetBackgroundImage());
        }

        if (guide_data.junction_view->GetForegroundImage() != nullptr) {
            junc_info->front_view_data = *(guide_data.junction_view->GetForegroundImage());
        }
        guide_event_->ShowJunctionView(junc_info);

    } else {
        guide_event_->ShowJunctionView(nullptr);
    }
}

void GuideRuntime::ProcessDebug(const VehiclePosition &ccp, const GuideData &guide_data) {
    if (!writter_.IsDebugOn()) {
        return;
    }

    debug_ccps_.emplace_back(ccp);

    if (debug_ccps_.size() % 30 == 0 || guide_data.is_arrive_dest) {
        writter_.WriteMatching(path_manager_->GetMainPathId(), debug_ccps_);
    }

    if (guide_data.is_arrive_dest || !guide_data.voice_text.empty()) {
        debug_guide_datas_.emplace_back(guide_data);
        writter_.WriteGuideData(path_manager_->GetMainPathId(), debug_guide_datas_);
    }

    if (guide_data.junction_view != nullptr) {
        writter_.WriteJunctionView(path_manager_->GetMainPathId(), guide_data.junction_view);
    }
}

}  // namespace guide
}  // namespace aurora
/* EOF */
