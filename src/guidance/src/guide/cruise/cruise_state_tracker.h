#ifndef MAP_SRC_GUIDE_SRC_GUIDE_CRUISE_CRUISE_STATE_TRACKER_H
#define MAP_SRC_GUIDE_SRC_GUIDE_CRUISE_CRUISE_STATE_TRACKER_H

#include "guidance/src/guide/abstract_state_tracker.h"

namespace aurora {
namespace guide {

class CruiseStateTracker : public AbstractStateTracker {
public:
    CruiseStateTracker(PathDataManagerPtr path_manager);
    ~CruiseStateTracker() = default;

    virtual int32_t DoGuide(const VehiclePosition &vehicle_pos, GuideData &guide_data);

};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_CRUISE_CRUISE_STATE_TRACKER_H
/* EOF */
