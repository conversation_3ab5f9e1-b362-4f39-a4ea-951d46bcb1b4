#include "guidance/src/guide/cruise/cruise_state_tracker.h"
#include "guidance/src/common/guide_log.h"
#include "guidance/src/common/common_def.h"

namespace aurora {
namespace guide {

CruiseStateTracker::CruiseStateTracker(PathDataManagerPtr path_manager) 
: AbstractStateTracker(path_manager) {

}

int32_t CruiseStateTracker::DoGuide(const VehiclePosition &vehicle_pos, GuideData &guide_data) {
    GUIDE_ASSERT(false);
    GUIDE_LOG_WARN("cruise do guide is not realize .");
    return 0;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
