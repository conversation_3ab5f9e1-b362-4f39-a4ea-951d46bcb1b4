#include "guidance/src/guide/abstract_state_tracker.h"
#include "guidance/src/guide/cruise/cruise_state_tracker.h"
#include "guidance/src/guide/navigation/nav_state_tracker.h"
#include "guidance/src/common/common_def.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

AbstractStateTracker::AbstractStateTracker(PathDataManagerPtr path_manager) 
: path_manager_(path_manager) {

}


std::shared_ptr<AbstractStateTracker> AbstractStateTracker::Create(const NavigationMode mode, PathDataManagerPtr path_manager) {
    switch (mode) {
        case NavigationMode::kNavigationModeGPS:
        case NavigationMode::kNavigationModeSimulation:
            if (path_manager == nullptr) {
                GUIDE_ASSERT(path_manager != nullptr);
                GUIDE_LOG_ERROR("path_manager is nullptr .");
                return nullptr;
            }
            return std::dynamic_pointer_cast<AbstractStateTracker>(std::make_shared<NavStateTracker>(path_manager));

        case NavigationMode::kNavigationModeCruise:
            return std::dynamic_pointer_cast<AbstractStateTracker>(std::make_shared<CruiseStateTracker>(nullptr));

        case NavigationMode::kNavigationModeNone:
        default: {
            GUIDE_ASSERT(false);
            GUIDE_LOG_ERROR("Create: unexpect mode ...");
            return nullptr;
        }
    }
}

}  // namespace guide
}  // namespace aurora
/* EOF*/
