#ifndef MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_LANE_INFO_STATE_H
#define MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_LANE_INFO_STATE_H


#include "guidance/src/data/enhance_lane_info.h"
#include "guidance/src/guide/navigation/data/maneuver_state.h"

namespace aurora {
namespace guide {

struct LaneInfoState {
    EnhanceLaneInfoPtr ptr;
    float  total_distance;
    float  curr_distance;
};

class ManeuverLaneInfoState {
public:
    ManeuverLaneInfoState(ManeuverState &state);
    ~ManeuverLaneInfoState() = default;

    void UpdateLaneInfos();

    int32_t GetTotalLaneInfoNum() const;
    int32_t GetForwardLaneInfoNum() const;

    const LaneInfoState* GetFirstLaneInfo() const;
    const LaneInfoState* GetLastLaneInfo() const;

    const LaneInfoState* GetPrevLaneInfo() const;
    const LaneInfoState* GetNextLaneInfo() const;
 
protected:
    ManeuverState&   state_;
    std::vector<LaneInfoState> lane_infos_;
};

}  // guide
}  // aurora

#endif // MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_LANE_INFO_STATE_H
/* EOF */
