#ifndef MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_FACILITY_TYPE_PROCESS_H
#define MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_FACILITY_TYPE_PROCESS_H

#include <string>
#include "guidance/src/common/common_def.h"

namespace aurora {
namespace guide {

class FacilityTypeProcess {
public:
    FacilityTypeProcess(FacilityType type);
    bool IsFilter() const;
    std::string ToString() const;

private:
    FacilityType type_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_FACILITY_TYPE_PROCESS_H
/* EOF */
