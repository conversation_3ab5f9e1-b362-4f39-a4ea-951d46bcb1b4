#ifndef MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_FACILITY_STATE_H
#define MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_FACILITY_STATE_H

#include <cstdint>

#include "guidance/src/data/enhance_facility.h"
#include "guidance/src/guide/navigation/data/maneuver_state.h"

namespace aurora {
namespace guide {

struct FacilityState {
    EnhanceFacilityPtr ptr;
    std::string text;
    float  total_distance;
    float  curr_distance;
};

class ManeuverFacilityState {
public:
    ManeuverFacilityState(ManeuverState &state);
    ~ManeuverFacilityState() = default;

    void UpdateFacilities();

    int32_t GetFacilityNum() const;
    const FacilityState* GetFirstFacility() const;
    const FacilityState* GetLastFacility() const;
    const FacilityState* GetNextFacility() const;

protected:
    bool Filter(FacilityType type) const;

protected:
    ManeuverState& state_;
    std::vector<FacilityState> facility_states_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_NAV_DATA_MANEUVER_FACILITY_STATE_H
/* EOF */
