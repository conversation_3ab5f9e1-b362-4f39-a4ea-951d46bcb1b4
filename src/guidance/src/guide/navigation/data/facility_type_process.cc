#include "guidance/src/guide/navigation/data/facility_type_process.h"
#include <map>

namespace aurora {
namespace guide {

static std::map<FacilityType, bool> facility_flag_table = {
    {FacilityType::kUnknown,                    false},
    {FacilityType::kMaxSpeedSign,               false},   ///< 最高限速标志
    {FacilityType::kMinSpeedSign,               false},   ///< 最低限速标志
    {FacilityType::kSpeedReleaseSign,           false},   ///< 限速解除标志
    {FacilityType::kSpeedCamera,                true},    ///< 测速摄像头
    {FacilityType::kViolationCamera,            true},    ///< 违章摄像头
    {FacilityType::kLaneLight,                  false},   ///< 车道指示灯
    {FacilityType::kWaitSignal,                 false},   ///< 等待信号灯
    {FacilityType::kPedestrianLight,            false},   ///< 人行横道灯
    {FacilityType::kPedestrianCrossing,         false},   ///< 人行横道
    {FacilityType::kVariableSpeedLimit,         false},   ///< 可变限速
    {FacilityType::kSchoolZone,                 false},    ///< 注意儿童
    {FacilityType::kRailwayCrossing,            false},   ///< 铁路道口
    {FacilityType::kRockFallLeft,               true},    ///< 注意落石（左侧）
    {FacilityType::kAccidentProne,              true},    ///< 事故易发路段
    {FacilityType::kSlipperyRoad,               false},    ///< 路面易滑
    {FacilityType::kVillage,                    true},    ///< 村庄
    {FacilityType::kOverpass,                   false},   ///< 过街天桥
    {FacilityType::kMannedRailwayCrossing,      false},   ///< 有人看管铁路道口
    {FacilityType::kUnmannedRailwayCrossing,    false},   ///< 无人看管铁路道口
    {FacilityType::kRoadNarrowsBoth,            false},   ///< 道路两侧变窄
    {FacilityType::kSharpLeftTurn,              true},    ///< 向左急转弯
    {FacilityType::kSharpRightTurn,             true},    ///< 向右急转弯
    {FacilityType::kReverseCurve,               true},    ///< 反向弯路
    {FacilityType::kMultipleCurves,             true},    ///< 连续弯路
    {FacilityType::kMergeLeft,                  false},    ///< 左侧合流看板
    {FacilityType::kMergeRight,                 false},    ///< 右侧合流看板
    {FacilityType::kMonitorCamera,              true},    ///< 监控摄像头
    {FacilityType::kBusLaneCamera,              true},    ///< 公交专用道摄像头
    {FacilityType::kNoOvertaking,               false},    ///< 禁止超车
    {FacilityType::kNoOvertakingRelease,        false},   ///< 禁止超车解除
    {FacilityType::kYieldSign,                  false},   ///< 停车让行
    {FacilityType::kSlowYield,                  false},   ///< 减速让行
    {FacilityType::kMeetingYield,               false},   ///< 会车让行
    {FacilityType::kRoadNarrowsRight,           true},    ///< 右侧变窄
    {FacilityType::kRoadNarrowsLeft,            true},    ///< 左侧变窄
    {FacilityType::kNarrowBridge,               false},   ///< 窄桥
    {FacilityType::kAroundRouteBoth,            false},   ///< 左右绕行
    {FacilityType::kAroundRouteLeft,            false},   ///< 左侧绕行
    {FacilityType::kAroundRouteRight,           false},   ///< 右侧绕行
    {FacilityType::kRockFallRight,              true},    ///< 注意落石（右侧）
    {FacilityType::kDangerousCurveLeft,         false},   ///< 榜山险路（左侧）
    {FacilityType::kDangerousCurveRight,        false},   ///< 榜山险路（右侧）
    {FacilityType::kDampingRoadLeft,            false},   ///< 堤坝路（左侧）
    {FacilityType::kDampingRoadRight,           false},   ///< 堤坝路（右侧）
    {FacilityType::kSteepAscent,                true},    ///< 上陡坡
    {FacilityType::kSteepDescent,               true},    ///< 下陡坡
    {FacilityType::kFordRoad,                   false},   ///< 过水路面
    {FacilityType::kUnlevelRoad,                false},   ///< 路面不平
    {FacilityType::kSlowDownHump,               false},   ///< 驼峰路
    {FacilityType::kSlowDown,                   true},    ///< 慢行
    {FacilityType::kDangerAhead,                false},   ///< 注意危险
    {FacilityType::kCrosswind,                  false},   ///< 注意横风
    {FacilityType::kLivestock,                  false},   ///< 注意牲畜
    {FacilityType::kBicycleWarning,             false},   ///< 注意非机动车
    {FacilityType::kTwoWayTraffic,              false},   ///< 双向交通
    {FacilityType::kTunnel,                     false},   ///< 隧道
    {FacilityType::kFerry,                      false},   ///< 渡口
    {FacilityType::kFourwayIntersection,        false},   ///< 十字交叉
    {FacilityType::kTIntersectionLeft,          false},   ///< T形交叉（左侧）
    {FacilityType::kTIntersectionRight,         false},   ///< T形交叉（右侧）
    {FacilityType::kTIntersectionBoth,          false},   ///< T形交叉（左右侧）
    {FacilityType::kYIntersection,              false},   ///< Y形交叉
    {FacilityType::kRoundabout,                 false},   ///< 环形交叉
    {FacilityType::kConstructionZone,           false},   ///< 施工
    {FacilityType::kSlowDownBump,               false},   ///< 减速带
    {FacilityType::kPedestrianWarning,          false},   ///< 注意行人
    {FacilityType::kZoneSpeedStart,             false},   ///< 区间测速开始
    {FacilityType::kZoneSpeedEnd,               false},   ///< 区间测速解除
    {FacilityType::kZoneSpeedSection,           false},   ///< 区间测速路段
    {FacilityType::kEmergencyCamera,            false},   ///< 应急车道电子眼
    {FacilityType::kBicycleLaneCamera,          false},   ///< 非机动车道电子眼
    {FacilityType::kTrafficLightCamera,         false},   ///< 红绿灯电子眼
    {FacilityType::kAccelerationLaneLimit,      false}    ///< 加减速车道限速
};


// TODO:后续会走locale文言配置
static std::map<FacilityType, std::string> facility_lang_table = {
    {FacilityType::kUnknown,                    ""},
    {FacilityType::kMaxSpeedSign,               ""},   ///< 最高限速标志
    {FacilityType::kMinSpeedSign,               ""},   ///< 最低限速标志
    {FacilityType::kSpeedReleaseSign,           ""},   ///< 限速解除标志
    {FacilityType::kSpeedCamera,                "前方有测速拍照"},    ///< 测速摄像头
    {FacilityType::kViolationCamera,            "前方有违章拍照"},    ///< 违章摄像头
    {FacilityType::kLaneLight,                  ""},   ///< 车道指示灯
    {FacilityType::kWaitSignal,                 ""},   ///< 等待信号灯
    {FacilityType::kPedestrianLight,            ""},   ///< 人行横道灯
    {FacilityType::kPedestrianCrossing,         ""},   ///< 人行横道
    {FacilityType::kVariableSpeedLimit,         ""},   ///< 可变限速
    {FacilityType::kSchoolZone,                 ""},    ///< 注意儿童
    {FacilityType::kRailwayCrossing,            ""},   ///< 铁路道口
    {FacilityType::kRockFallLeft,               "前方注意落石"},    ///< 注意落石（左侧）
    {FacilityType::kAccidentProne,              "前方事故易发路段"},    ///< 事故易发路段
    {FacilityType::kSlipperyRoad,               ""},    ///< 路面易滑
    {FacilityType::kVillage,                    "前方村庄"},    ///< 村庄
    {FacilityType::kOverpass,                   ""},   ///< 过街天桥
    {FacilityType::kMannedRailwayCrossing,      ""},   ///< 有人看管铁路道口
    {FacilityType::kUnmannedRailwayCrossing,    ""},   ///< 无人看管铁路道口
    {FacilityType::kRoadNarrowsBoth,            ""},   ///< 道路两侧变窄
    {FacilityType::kSharpLeftTurn,              "前方向左急转弯"},    ///< 向左急转弯
    {FacilityType::kSharpRightTurn,             "前方向右急转弯"},    ///< 向右急转弯
    {FacilityType::kReverseCurve,               "前方反向弯路"},    ///< 反向弯路
    {FacilityType::kMultipleCurves,             "前方连续弯路"},    ///< 连续弯路
    {FacilityType::kMergeLeft,                  ""},    ///< 左侧合流看板
    {FacilityType::kMergeRight,                 ""},    ///< 右侧合流看板
    {FacilityType::kMonitorCamera,              "前方有监控拍照"},    ///< 监控摄像头
    {FacilityType::kBusLaneCamera,              "前方有公交车专用道拍照"},    ///< 公交专用道摄像头
    {FacilityType::kNoOvertaking,               ""},    ///< 禁止超车
    {FacilityType::kNoOvertakingRelease,        ""},   ///< 禁止超车解除
    {FacilityType::kYieldSign,                  ""},   ///< 停车让行
    {FacilityType::kSlowYield,                  ""},   ///< 减速让行
    {FacilityType::kMeetingYield,               ""},   ///< 会车让行
    {FacilityType::kRoadNarrowsRight,           "前方右侧变窄"},    ///< 右侧变窄
    {FacilityType::kRoadNarrowsLeft,            "前方左侧变窄"},    ///< 左侧变窄
    {FacilityType::kNarrowBridge,               ""},   ///< 窄桥
    {FacilityType::kAroundRouteBoth,            ""},   ///< 左右绕行
    {FacilityType::kAroundRouteLeft,            ""},   ///< 左侧绕行
    {FacilityType::kAroundRouteRight,           ""},   ///< 右侧绕行
    {FacilityType::kRockFallRight,              "前方注意右侧落石"},    ///< 注意落石（右侧）
    {FacilityType::kDangerousCurveLeft,         ""},   ///< 榜山险路（左侧）
    {FacilityType::kDangerousCurveRight,        ""},   ///< 榜山险路（右侧）
    {FacilityType::kDampingRoadLeft,            ""},   ///< 堤坝路（左侧）
    {FacilityType::kDampingRoadRight,           ""},   ///< 堤坝路（右侧）
    {FacilityType::kSteepAscent,                "前方上坡"},    ///< 上陡坡
    {FacilityType::kSteepDescent,               "前方下坡"},    ///< 下陡坡
    {FacilityType::kFordRoad,                   ""},   ///< 过水路面
    {FacilityType::kUnlevelRoad,                ""},   ///< 路面不平
    {FacilityType::kSlowDownHump,               ""},   ///< 驼峰路
    {FacilityType::kSlowDown,                   "前方慢行"},    ///< 慢行
    {FacilityType::kDangerAhead,                ""},   ///< 注意危险
    {FacilityType::kCrosswind,                  ""},   ///< 注意横风
    {FacilityType::kLivestock,                  ""},   ///< 注意牲畜
    {FacilityType::kBicycleWarning,             ""},   ///< 注意非机动车
    {FacilityType::kTwoWayTraffic,              ""},   ///< 双向交通
    {FacilityType::kTunnel,                     ""},   ///< 隧道
    {FacilityType::kFerry,                      ""},   ///< 渡口
    {FacilityType::kFourwayIntersection,        ""},   ///< 十字交叉
    {FacilityType::kTIntersectionLeft,          ""},   ///< T形交叉（左侧）
    {FacilityType::kTIntersectionRight,         ""},   ///< T形交叉（右侧）
    {FacilityType::kTIntersectionBoth,          ""},   ///< T形交叉（左右侧）
    {FacilityType::kYIntersection,              ""},   ///< Y形交叉
    {FacilityType::kRoundabout,                 ""},   ///< 环形交叉
    {FacilityType::kConstructionZone,           ""},   ///< 施工
    {FacilityType::kSlowDownBump,               ""},   ///< 减速带
    {FacilityType::kPedestrianWarning,          ""},   ///< 注意行人
    {FacilityType::kZoneSpeedStart,             ""},   ///< 区间测速开始
    {FacilityType::kZoneSpeedEnd,               ""},   ///< 区间测速解除
    {FacilityType::kZoneSpeedSection,           ""},   ///< 区间测速路段
    {FacilityType::kEmergencyCamera,            ""},   ///< 应急车道电子眼
    {FacilityType::kBicycleLaneCamera,          ""},   ///< 非机动车道电子眼
    {FacilityType::kTrafficLightCamera,         ""},   ///< 红绿灯电子眼
    {FacilityType::kAccelerationLaneLimit,      ""}    ///< 加减速车道限速
};


FacilityTypeProcess::FacilityTypeProcess(FacilityType type) 
: type_(type) {

}

bool FacilityTypeProcess::IsFilter() const {
    if (facility_flag_table.count(type_) > 0) {
        return facility_flag_table.at(type_);
    }

    return false;
}
    
std::string FacilityTypeProcess::ToString() const {
    if (facility_lang_table.count(type_) > 0) {
        return facility_lang_table.at(type_);
    }

    return "";
}

}  // namespace guide
}  // namespace aurora
/* EOF */
