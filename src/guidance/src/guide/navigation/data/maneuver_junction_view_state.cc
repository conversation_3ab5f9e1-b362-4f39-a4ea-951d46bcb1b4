#include "guidance/src/guide/navigation/data/maneuver_junction_view_state.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

ManeuverJunctionViewState::ManeuverJunctionViewState(ManeuverState &state)
: state_(state) {

    EnhancePathResultPtr result = state.GetPathManager()->GetEnhancePathResult();
    GUIDE_ASSERT(result != nullptr);

    if (result == nullptr) {
        GUIDE_LOG_ERROR("path result is nullptr .");
        return;
    }

    int32_t snode_index = state.GetManeuverIt()->GetBeginNodeIndex();
    int32_t enode_index = state.GetManeuverIt()->GetEndNodeIndex();

    float acc_dist = 0;
    for (int32_t index = snode_index + 1; index < enode_index; ++index) {
        EnhanceNode *node = result->GetNode(index);
        if (node == nullptr) {
            GUIDE_ASSERT(node != nullptr);
            GUIDE_LOG_ERROR("node is nullptr .");
            return;
        }

        DirectedEdge *prev_edge = result->GetPrevEdge(index);
        if (prev_edge == nullptr) {
            GUIDE_ASSERT(prev_edge != nullptr);
            GUIDE_LOG_ERROR("prev_edge is nullptr .");
            return;
        }

        acc_dist += prev_edge->GetLengthMeter();

        if (node->GetRecommandJunctionView() != nullptr) {
            JunctionViewState junction_view_state;
            junction_view_state.ptr = node->GetRecommandJunctionView();
            junction_view_state.total_distance = acc_dist + state.GetDistanceToSNode();
            junction_views_.emplace_back(junction_view_state);
        }
    }
}

void ManeuverJunctionViewState::UpdateJunctionViews() {
    for (auto &junction_view : junction_views_) {
        junction_view.curr_distance = junction_view.total_distance - state_.GetDriveDistance();
    }
}

int32_t ManeuverJunctionViewState::GetTotalJunctionViewNum() const {
    return junction_views_.size();
}

const JunctionViewState* ManeuverJunctionViewState::GetFirstJunctionView() const {
    if (junction_views_.empty()) {
        return nullptr;
    }

    return &(junction_views_.front());
}

const JunctionViewState* ManeuverJunctionViewState::GetLastJunctionView() const {
    if (junction_views_.empty()) {
        return nullptr;
    }

    return &(junction_views_.back());
}

const JunctionViewState* ManeuverJunctionViewState::GetNextJunctionView() const {
    auto status = state_.GetVehicleManeuverStatus();

    if (status == VehicleOnManeuverStatus::kVehicleOnManeuver) {
        for (int index = 0; index < junction_views_.size(); ++index) {
            if (junction_views_.at(index).curr_distance > 0) {
                if (index > 0) {
                    return &(junction_views_[index]);
                }
            }
        }
    }

    if (status == VehicleOnManeuverStatus::kVehicleNotArriveManeuver) {
        return GetFirstJunctionView();
    }

    return nullptr;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
