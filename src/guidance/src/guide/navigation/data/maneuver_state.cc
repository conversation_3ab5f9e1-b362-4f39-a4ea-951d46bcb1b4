#include "guidance/src/guide/navigation/data/maneuver_state.h"
#include "Time.h"

namespace aurora {
namespace guide {

ManeuverState::ManeuverState(PathDataManagerPtr path_manager, ManeuverConstIt &it) 
: path_manager_(path_manager)
, maneuver_it_(it) {

    far_stamp_ = 0;
    mid_stamp_ = 0;
    near_stamp_ = 0;
    arrive_stamp_ = 0;
    post_stamp_ = 0;

    post_stamp_ = 0;
    remain_time_ = 0;
    total_distance_ = 0xFFFF;
    drive_distance_ = 0;
}

void ManeuverState::UpdateDriveDistance(float drive_distance) {
    drive_distance_ = drive_distance;
}
 
float ManeuverState::GetDriveDistance() const {
    return drive_distance_;
}

void ManeuverState::InitTotalDistane(float distance) {
    total_distance_ = distance;
}

float ManeuverState::GetTotalDistance() const {
    return total_distance_;
}

float ManeuverState::GetDistanceToSNode() const {
    float result = (total_distance_ - drive_distance_);
    // GUIDE_ASSERT(result > 0);
    return result;
}

float ManeuverState::GetDistanceToENode() const {
    float result = (total_distance_ + maneuver_it_->GetLengthMeter() - drive_distance_);
    // GUIDE_ASSERT(result > 0);
    return result;
}

VehicleOnManeuverStatus ManeuverState::GetVehicleManeuverStatus() const {
    float dist_to_snode = GetDistanceToSNode();
    float dist_to_enode = GetDistanceToENode();

    if (dist_to_snode < 0 && dist_to_enode >= 0) {
        return VehicleOnManeuverStatus::kVehicleOnManeuver;
    }

    if (dist_to_enode < 0) {
        return VehicleOnManeuverStatus::kVehiclePassedManeuver;
    }

    if (dist_to_snode > 0) {
        return VehicleOnManeuverStatus::kVehicleNotArriveManeuver;
    }
}


bool ManeuverState::IsFarInstructionTag() const {
    return (far_stamp_ > 0);
}

const std::string& ManeuverState::GetFarInstruction() const {
    if (!maneuver_it_->GetVerbalAlertInstruction().empty()) {
        return maneuver_it_->GetVerbalAlertInstruction();
    }
    return maneuver_it_->GetVerbalPreInstruction();
}

const std::string& ManeuverState::GetFarInstructionWithTag() {
    far_stamp_ = Time::Now().ToMillisecond();
    return GetFarInstruction();
}

bool ManeuverState::IsMiddleInstructionTag() const {
    return (mid_stamp_ > 0);
}

const std::string& ManeuverState::GetMiddleInstruction() const {
    if (!maneuver_it_->GetVerbalAlertInstruction().empty()) {
        return maneuver_it_->GetVerbalAlertInstruction();
    }
    return maneuver_it_->GetVerbalPreInstruction();
}

const std::string& ManeuverState::GetMiddleInstructionWithTag() {
    mid_stamp_ = Time::Now().ToMillisecond();
    return GetMiddleInstruction();
}

bool ManeuverState::IsNearInstructionTag() const {
    return (near_stamp_ > 0);
}

const std::string& ManeuverState::GetNearInstruction() const {
    if (!maneuver_it_->GetVerbalSuccinctInstruction().empty()) {
        return maneuver_it_->GetVerbalSuccinctInstruction();
    }
    return maneuver_it_->GetVerbalPreInstruction();
}

const std::string& ManeuverState::GetNearInstructionWithTag() {
    near_stamp_ = Time::Now().ToMillisecond();
    return GetNearInstruction();
}

bool ManeuverState::IsArriveInstructionTag() const {
    return (arrive_stamp_ > 0);
}

const std::string& ManeuverState::GetArriveInstruction() const {
    return maneuver_it_->GetVerbalPreInstruction();
}

const std::string& ManeuverState::GetArriveInstructionWithTag() {
    arrive_stamp_ = Time::Now().ToMillisecond();
    return GetArriveInstruction();
}

bool ManeuverState::IsPostInstructionTag() const {
    return (post_stamp_ > 0);
}

const std::string& ManeuverState::GetPostInstruction() const {
    return maneuver_it_->GetVerbalPostInstruction();
}
    
const std::string& ManeuverState::GetPostInstructionWithTag() {
    post_stamp_ = Time::Now().ToMillisecond();
    return maneuver_it_->GetVerbalPostInstruction();
}

int32_t ManeuverState::GetVehicleLinkIndex() const {
    int32_t dist_snode = GetDistanceToSNode();
    int32_t dist_enode = GetDistanceToENode();

    if ((dist_snode < 0) && (dist_enode > 0)) {
        int32_t snode_idx = maneuver_it_->GetBeginNodeIndex();
        int32_t enode_idx = maneuver_it_->GetEndNodeIndex();

        float maneuver_offset = std::fabs(dist_snode);
        float acc_offset = 0;
        for (int32_t index = snode_idx; index < enode_idx; ++index) {
            DirectedEdge *edge = path_manager_->GetEnhancePathResult()->GetEdge(index);
            GUIDE_ASSERT(edge != nullptr);

            if(edge == nullptr) {
                break;
            }

            if (acc_offset < maneuver_offset && maneuver_offset <= (acc_offset + edge->GetLengthMeter())) {
                return index;
            }

            acc_offset += edge->GetLengthMeter();
        } // for
    } // if

    GUIDE_ASSERT(false);
    return -1;
}

ManeuverConstIt ManeuverState::GetManeuverIt() {
    return maneuver_it_;
}

PathDataManagerPtr ManeuverState::GetPathManager() {
    return path_manager_;
}

}  // namespace guide
}  // namespace aurora
/* EOF */