#include "guidance/src/guide/navigation/data/maneuver_lane_info_state.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

ManeuverLaneInfoState::ManeuverLaneInfoState(ManeuverState &state) 
: state_(state) {

    EnhancePathResultPtr result = state.GetPathManager()->GetEnhancePathResult();
    GUIDE_ASSERT(result != nullptr);

    if (result == nullptr) {
        GUIDE_LOG_ERROR("path result is nullptr .");
        return;
    }

    int32_t snode_index = state.GetManeuverIt()->GetBeginNodeIndex();
    int32_t enode_index = state.GetManeuverIt()->GetEndNodeIndex();

    float acc_dist = 0;
    for (int32_t index = snode_index; index < enode_index; ++index) {
        DirectedEdge *edge = result->GetEdge(index);
        if (edge == nullptr) {
            GUIDE_ASSERT(edge != nullptr);
            GUIDE_LOG_ERROR("edge is nullptr .");
            return;
        }

        acc_dist += edge->GetLengthMeter();

        if (edge->GetLaneInfo() != nullptr) {
            LaneInfoState lane_state;
            lane_state.ptr = edge->GetLaneInfo();
            lane_state.total_distance = acc_dist + state.GetDistanceToSNode();
            lane_infos_.emplace_back(lane_state);
        }
    }
}

void ManeuverLaneInfoState::UpdateLaneInfos() {
    for (auto &lane_state : lane_infos_) {
        lane_state.curr_distance = lane_state.total_distance - state_.GetDriveDistance();
    }
}

int32_t ManeuverLaneInfoState::GetTotalLaneInfoNum() const {
    return lane_infos_.size();
}

int32_t ManeuverLaneInfoState::GetForwardLaneInfoNum() const {
    for (int index = 0; index < lane_infos_.size(); ++index) {
        if (lane_infos_.at(index).curr_distance > 0) {
            return (lane_infos_.size() - index);
        }
    }
    return 0;
}

const LaneInfoState* ManeuverLaneInfoState::GetFirstLaneInfo() const {
    if (lane_infos_.size() > 0) {
        return  &(lane_infos_.front());
    }

    return nullptr;
}

const LaneInfoState* ManeuverLaneInfoState::GetLastLaneInfo() const {
    if (lane_infos_.size() > 0) {
        return  &(lane_infos_.back());
    }

    return nullptr;
}

const LaneInfoState* ManeuverLaneInfoState::GetPrevLaneInfo() const {

    auto status = state_.GetVehicleManeuverStatus();

    if (status == VehicleOnManeuverStatus::kVehicleOnManeuver) {
        for (int index = 0; index < lane_infos_.size(); ++index) {
            if (lane_infos_.at(index).curr_distance > 0) {
                if (index > 0) {
                    return &(lane_infos_[index - 1]);
                }
            }
        }
        return nullptr;
    }

    if (status == VehicleOnManeuverStatus::kVehiclePassedManeuver) {
        return GetLastLaneInfo();
    }

    // if (status == VehicleOnManeuverStatus::kVehicleNotArriveManeuver) {
    //     return GetFirstLaneInfo();
    // }

    return nullptr;
}

const LaneInfoState* ManeuverLaneInfoState::GetNextLaneInfo() const {
    auto status = state_.GetVehicleManeuverStatus();

    if (status == VehicleOnManeuverStatus::kVehicleOnManeuver) {
        for (int index = 0; index < lane_infos_.size(); ++index) {
            if (lane_infos_.at(index).curr_distance > 0) {
                if (index > 0) {
                    return &(lane_infos_[index]);
                }
            }
        }
    }

    if (status == VehicleOnManeuverStatus::kVehicleNotArriveManeuver) {
        return GetFirstLaneInfo();
    }

    return nullptr;
}


}  // namespace guide
}  // namespace aurora
/* EOF */
