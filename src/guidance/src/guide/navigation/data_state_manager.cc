#include "guidance/src/guide/navigation/data_state_manager.h"
#include "guidance/src/common/guide_log.h"
namespace aurora {
namespace guide {

DataStateManager::DataStateManager(PathDataManagerPtr path_manager) {
    path_manager_ = path_manager;

    GUIDE_ASSERT(path_manager_ != nullptr);

    if (path_manager_ == nullptr) {
        GUIDE_LOG_ERROR("path_manager_ is nullptr .");
        return;
    }

    drive_distance_ = 0;

    BuildManeuverStates();
    BuildManeuverLaneInfoStates();
    BuildManeuverJunctionViewStates();
    BuildManeuverFacilityStates();
    BuildManeuverStationStates();

    // tag: invalid
    cur_it_ = maneuver_states_.end(); 
}

DataStateManager::~DataStateManager() {

}

void DataStateManager::BuildManeuverStates() {
    // init maneuver state
    const std::list<Maneuver>& maneuvers = path_manager_->GetManeuvers();
    for (std::list<Maneuver>::const_iterator it = maneuvers.begin(); it != maneuvers.end(); ++it) {
        maneuver_states_.emplace_back(path_manager_, it);
        // relations_[it] = &(maneuver_states_.back());
    }

    float remain_length = 0;
    for (auto it = maneuver_states_.begin(); it != maneuver_states_.end(); ++it) {
        GUIDE_ASSERT(it->GetManeuverIt() != maneuvers.end());
        it->InitTotalDistane(remain_length);
        remain_length += it->GetManeuverIt()->GetLengthMeter();
    }
}

void DataStateManager::BuildManeuverLaneInfoStates() {
    for (std::list<ManeuverState>::iterator it = maneuver_states_.begin(); it != maneuver_states_.end(); ++it) {
        maneuver_laneinfo_states_.emplace_back(*it);
        lane_info_relations_[it] = &(maneuver_laneinfo_states_.back());
    }
}

void DataStateManager::BuildManeuverJunctionViewStates() {
    for (std::list<ManeuverState>::iterator it = maneuver_states_.begin(); it != maneuver_states_.end(); ++it) {
        maneuver_junction_states_.emplace_back(*it);
        junction_view_relations_[it] = &(maneuver_junction_states_.back());
    }
}

void DataStateManager::BuildManeuverFacilityStates() {
    for (std::list<ManeuverState>::iterator it = maneuver_states_.begin(); it != maneuver_states_.end(); ++it) {
        maneuver_facility_states_.emplace_back(*it);
        facility_relations_[it] = &(maneuver_facility_states_.back());
    }
}

void DataStateManager::BuildManeuverStationStates() {
    for (std::list<ManeuverState>::iterator it = maneuver_states_.begin(); it != maneuver_states_.end(); ++it) {
        maneuver_station_states_.emplace_back(*it);
        station_relations_[it] = &(maneuver_station_states_.back());
    }
}

void DataStateManager::UpdateStateByVehiclePos(const VehiclePosition &ccp) {
    if (!ccp.IsValid()) {
        GUIDE_ASSERT(ccp.IsValid());
        GUIDE_LOG_ERROR("ccp is invalid ...");
        return;
    }

    if (ccp.IsReroute()) {
        GUIDE_LOG_WARN("is reroute ...");
        return;
    }

    UpdateCurrPos(ccp);
    UpdateManeuverStates(ccp);
    UpdateManeuverLaneInfoStates();
    UpdateManeuverJunctionViesStates();
    UpdateManeuverFacilityStates();
    UpdateManeuverStationStates();
}

bool DataStateManager::UpdateCurrPos(const VehiclePosition &ccp) {
    // TODO: 此处需要优化
    bool find = false;
    for (auto it = maneuver_states_.begin(); it != maneuver_states_.end(); ++it) {
        ManeuverConstIt man_it = (*it).GetManeuverIt();
        if ((ccp.GetPathLinkIndex() >= man_it->GetBeginNodeIndex()) && 
                (ccp.GetPathLinkIndex() < man_it->GetEndNodeIndex())) {
            cur_it_ = it;
            find = true;
            break;
        }
    }

    if (!find) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("current pos not found maneuver .");
    }

    if (find) {
        prev_pos_ = ccp;
    }
    return find;
}

void DataStateManager::UpdateManeuverStates(const VehiclePosition &ccp) {
    if (path_manager_ == nullptr) {
        return;
    }

    EnhancePathResultPtr enhance_path = path_manager_->GetEnhancePathResult();
    if (enhance_path == nullptr) {
        return;
    }

    drive_distance_ = 0;
    for (uint32_t idx = 0; idx < ccp.GetPathLinkIndex(); ++idx) {
        drive_distance_ += enhance_path->GetEdge(idx)->GetLengthMeter();
    }
    drive_distance_ += ccp.GetLinkOffset();

    for (auto &state : maneuver_states_) {
        state.UpdateDriveDistance(drive_distance_);
    }
}

void DataStateManager::UpdateManeuverLaneInfoStates() {
    for (auto &maneuver_lane_state : maneuver_laneinfo_states_) {
        maneuver_lane_state.UpdateLaneInfos();
    }
}

void DataStateManager::UpdateManeuverJunctionViesStates() {
    for (auto &junction_state : maneuver_junction_states_) {
        junction_state.UpdateJunctionViews();
    }
}

void DataStateManager::UpdateManeuverFacilityStates() {
    for (auto &facility_state : maneuver_facility_states_) {
        facility_state.UpdateFacilities();
    }
}
    
void DataStateManager::UpdateManeuverStationStates() {
    for (auto & station_state : maneuver_station_states_) {
        station_state.UpdateTollStations();
    }
}


ManeuverState* DataStateManager::GetPrevManeuverState() const {
    if (!prev_pos_.IsValid()) {
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.begin()) {
        ManeuverStateIt prev_it = cur_it_;
        --prev_it;
        return &(*prev_it);
    }
    return nullptr;
}

ManeuverState* DataStateManager::GetCurrManeuverState() const {
    if (!prev_pos_.IsValid()) {
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.end()) {
        return  &(*cur_it_);
    }

    GUIDE_ASSERT(false);
    GUIDE_LOG_ERROR("GetCurrentManeuver is nullptr .");
    return nullptr;
}
    
ManeuverState* DataStateManager::GetNextManeuverState() const {
    if (!prev_pos_.IsValid()) {
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.end()) {
        ManeuverStateIt next_it = cur_it_;
        ++next_it;

        if (next_it != maneuver_states_.end()) {
            return &(*next_it);
        }
        
        return nullptr;
    }

    GUIDE_ASSERT(false);
    GUIDE_LOG_ERROR("GetCurrentManeuver is nullptr .");
    return nullptr;
}

ManeuverLaneInfoState* DataStateManager::GetPrevManeuverLaneInfoState() const {
    if (!prev_pos_.IsValid()) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("prev_pos is invalid .");
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.begin()) {
        ManeuverStateIt prev = cur_it_;
        --prev;
        return lane_info_relations_.at(prev);
    }

    return nullptr;
}

ManeuverLaneInfoState* DataStateManager::GetCurrManeuverLaneInfoState() const {
    if (!prev_pos_.IsValid()) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("prev_pos is invalid .");
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.end()) {
        return  lane_info_relations_.at(cur_it_);
    }
    return nullptr;
}

ManeuverLaneInfoState* DataStateManager::GetNextManeuverLaneInfoState() const {
    if (!prev_pos_.IsValid()) {
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.end()) {
        ManeuverStateIt next_it = cur_it_;
        ++next_it;

        if (next_it != maneuver_states_.end()) {
            return lane_info_relations_.at(next_it);
        }
        return nullptr;
    }

    return nullptr;
}

ManeuverJunctionViewState* DataStateManager::GetPrevManeuverJunctionViewState() const {
    if (!prev_pos_.IsValid()) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("prev_pos is invalid .");
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.begin()) {
        ManeuverStateIt prev = cur_it_;
        --prev;
        return junction_view_relations_.at(prev);
    }

    return nullptr;
}

ManeuverJunctionViewState* DataStateManager::GetCurrManeuverJunctionViewState() const { 
    if (!prev_pos_.IsValid()) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("prev_pos is invalid .");
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.end()) {
        return  junction_view_relations_.at(cur_it_);
    }
    return nullptr;
}

ManeuverJunctionViewState* DataStateManager::GetNextManeuverJunctionViewState() const {
    if (!prev_pos_.IsValid()) {
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.end()) {
        ManeuverStateIt next_it = cur_it_;
        ++next_it;

        if (next_it != maneuver_states_.end()) {
            return junction_view_relations_.at(next_it);
        }
        return nullptr;
    }

    return nullptr;
}

ManeuverFacilityState* DataStateManager::GetPrevManeuverFacilityState() const {
    if (!prev_pos_.IsValid()) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("prev_pos is invalid .");
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.begin()) {
        ManeuverStateIt prev = cur_it_;
        --prev;
        return facility_relations_.at(prev);
    }

    return nullptr;
}

ManeuverFacilityState* DataStateManager::GetCurrManeuverFacilityState() const {
    if (!prev_pos_.IsValid()) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("prev_pos is invalid .");
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.end()) {
        return  facility_relations_.at(cur_it_);
    }
    return nullptr;
}
    
ManeuverFacilityState* DataStateManager::GetNextManeuverFacilityState() const {
    if (!prev_pos_.IsValid()) {
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.end()) {
        ManeuverStateIt next_it = cur_it_;
        ++next_it;

        if (next_it != maneuver_states_.end()) {
            return facility_relations_.at(next_it);
        }
        return nullptr;
    }

    return nullptr;
}

ManeuverTollStationState* DataStateManager::GetPrevManeuverStationState() const {
    if (!prev_pos_.IsValid()) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("prev_pos is invalid .");
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.begin()) {
        ManeuverStateIt prev = cur_it_;
        --prev;
        return station_relations_.at(prev);
    }

    return nullptr;
}

ManeuverTollStationState* DataStateManager::GetCurrManeuverStationState() const {
    if (!prev_pos_.IsValid()) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("prev_pos is invalid .");
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.end()) {
        return  station_relations_.at(cur_it_);
    }
    return nullptr;
}

ManeuverTollStationState* DataStateManager::GetNextManeuverStationState() const {
    if (!prev_pos_.IsValid()) {
        return nullptr;
    }

    if (cur_it_ != maneuver_states_.end()) {
        ManeuverStateIt next_it = cur_it_;
        ++next_it;

        if (next_it != maneuver_states_.end()) {
            return station_relations_.at(next_it);
        }
        return nullptr;
    }

    return nullptr;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
