#ifndef MAP_SRC_GUIDE_SRC_GUIDE_NAV_NAVIGATION_VOICE_PLAY_POLICY_H
#define MAP_SRC_GUIDE_SRC_GUIDE_NAV_NAVIGATION_VOICE_PLAY_POLICY_H

#include <cstdint>
#include <string>
#include "guidance/src/guide/navigation/data_state_manager.h"
#include "guidance/src/common/guide_data.h"

namespace aurora {
namespace guide {

class VoicePlayPolicy {
public:
    VoicePlayPolicy() = default;
    virtual ~VoicePlayPolicy() = default;

    virtual void DoPlay(ManeuverState *curr_man_state, ManeuverState *next_man_state, GuideData &guide_data);

    bool IsOverFar(float dist_to_snode, float dist_to_enode) const;
    bool IsFar(float dist_to_snode, float dist_to_enode) const;
    bool IsMiddle(float dist_to_snode, float dist_to_enode) const;
    bool IsNear(float dist_to_snode, float dist_to_enode) const;
    bool IsPassed(float dist_to_snode, float dist_to_enode) const;
    bool IsArrived(float dist_to_snode, float dist_to_enode) const;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_NAV_NAVIGATION_VOICE_PLAY_POLICY_H
/* EOF */
