#include "guidance/src/guide/navigation/voice_play_policy.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

void VoicePlayPolicy::DoPlay(ManeuverState *curr_man_state, ManeuverState *next_man_state, GuideData &guide_data) {
    
    GUIDE_ASSERT(curr_man_state != nullptr);

    if (curr_man_state != nullptr) {

        if (next_man_state != nullptr && next_man_state->GetManeuverIt()->IsDestinationType()) {
            if (IsArrived(curr_man_state->GetDistanceToSNode(), curr_man_state->GetDistanceToENode()) &&
                !next_man_state->GetArriveInstruction().empty() && 
                    !next_man_state->IsArriveInstructionTag()) {
                
                next_man_state->GetNearInstructionWithTag();

                guide_data.voice_text = next_man_state->GetArriveInstructionWithTag();
                guide_data.is_arrive_dest = true;
                // GUIDE_LOG_INFO("------- arrive destination instruction ----");
                return;
            }
        }

        if (IsPassed(curr_man_state->GetDistanceToSNode(), curr_man_state->GetDistanceToENode())) {
            if (!curr_man_state->GetPostInstruction().empty() && !curr_man_state->IsPostInstructionTag()) {
                guide_data.voice_text = curr_man_state->GetPostInstructionWithTag();
                // GUIDE_LOG_INFO("-------- post instruction ----");
                return;
            }
        } else {
            if (IsOverFar(curr_man_state->GetDistanceToSNode(), curr_man_state->GetDistanceToENode())) {
                return;
            } else {
                curr_man_state->GetPostInstructionWithTag(); // cancel post
            }
        }
    }

    if (next_man_state != nullptr) {
        if (next_man_state->GetManeuverIt()->IsDestinationType()) {
            if (IsArrived(curr_man_state->GetDistanceToSNode(), curr_man_state->GetDistanceToENode()) &&
                !next_man_state->GetArriveInstruction().empty() && 
                    !next_man_state->IsArriveInstructionTag()) {

                next_man_state->GetNearInstructionWithTag();
                guide_data.voice_text = next_man_state->GetArriveInstructionWithTag();
                guide_data.is_arrive_dest = true;
                // GUIDE_LOG_INFO("------- arrive destination instruction ----");
                return;
            }
        }

        if (IsNear(curr_man_state->GetDistanceToSNode(), curr_man_state->GetDistanceToENode())) {
             if (!next_man_state->GetNearInstruction().empty()) {
                if (!next_man_state->IsNearInstructionTag()) {
                    guide_data.voice_text = next_man_state->GetNearInstructionWithTag();
                    // GUIDE_LOG_INFO("---- near instruction ----");
                    return;
                }
            }
        }

        if (IsMiddle(curr_man_state->GetDistanceToSNode(), curr_man_state->GetDistanceToENode())) {
            if (!next_man_state->GetMiddleInstruction().empty()) {
                if (!next_man_state->IsMiddleInstructionTag()) {
                    guide_data.voice_text = next_man_state->GetMiddleInstructionWithTag();
                    // GUIDE_LOG_INFO("---- middle instruction ----");
                    return;
                }
            }
        }

        if (IsFar(curr_man_state->GetDistanceToSNode(), curr_man_state->GetDistanceToENode())) {
            if (!next_man_state->GetFarInstruction().empty()) {
                if (!next_man_state->IsFarInstructionTag()) {

                    guide_data.voice_text = next_man_state->GetFarInstructionWithTag();
                    // GUIDE_LOG_INFO("---- far instruction ----");
                    return;
                }
            }
        }
    }
}

bool VoicePlayPolicy::IsOverFar(float dist_to_snode, float dist_to_enode) const {
    return (dist_to_enode > 2000);
}

bool VoicePlayPolicy::IsFar(float dist_to_snode, float dist_to_enode) const {
    return  (dist_to_snode < -20) && (dist_to_enode >= 800 && dist_to_enode <= 2000);
}

bool VoicePlayPolicy::IsMiddle(float dist_to_snode, float dist_to_enode) const {
    return (dist_to_snode < -20) && (dist_to_enode >= 300 && dist_to_enode <= 600);
}

bool VoicePlayPolicy::IsNear(float dist_to_snode, float dist_to_enode) const {
    return ((dist_to_snode < -20) && (dist_to_enode >= 20 && dist_to_enode <= 100)) || (dist_to_enode < 20);
}

bool VoicePlayPolicy::IsPassed(float dist_to_snode, float dist_to_enode) const {
    return (dist_to_snode < -40);
}

bool VoicePlayPolicy::IsArrived(float dist_to_snode, float dist_to_enode) const {
    return  (dist_to_snode < -20 && dist_to_enode <= 30) || (dist_to_enode > 0 && dist_to_enode < 20);
}


}  // namespace guide
}  // namespace aurora
/* EOF */
