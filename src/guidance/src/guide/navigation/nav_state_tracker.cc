#include "guidance/src/guide/navigation/nav_state_tracker.h"
#include "errorcode.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

NavStateTracker::NavStateTracker(PathDataManagerPtr path_manager)
: AbstractStateTracker(path_manager)
, state_manager_(path_manager_) {

}

int32_t NavStateTracker::DoGuide(const VehiclePosition &vehicle_pos, GuideData &guide_data) {
    GUIDE_ASSERT(path_manager_ != nullptr);

    if (path_manager_ == nullptr) {
        return ErrorCode::kErrorCodeParamNullPointer;    
    }

    if (vehicle_pos.GetPathId() == (uint64_t)(-1)) {
        GUIDE_LOG_INFO("no valid path ...");
        return 0;
    }

    state_manager_.UpdateStateByVehiclePos(vehicle_pos);
    DoGuideManeuvers(vehicle_pos, guide_data);
    DoGuideLaneInfos(vehicle_pos, guide_data);
    DoGuideJunctionViews(vehicle_pos, guide_data);
    return 0;
}

bool NavStateTracker::DoGuideManeuvers(const VehiclePosition &vehicle_pos, GuideData &guide_data) {
    ManeuverState *curr_man_state = state_manager_.GetCurrManeuverState();
    ManeuverState *next_man_state = state_manager_.GetNextManeuverState();

#if 0
    if (curr_man_state != nullptr) {
        GUIDE_LOG_INFO("current maneuver: total={}, drive={}, dis_to_snode={}, snode_idx={}, enode_idx={}, path_index={}",
                curr_man_state->GetTotalDistance(), 
                curr_man_state->GetDriveDistance(),
                curr_man_state->GetDistanceToSNode(),
                curr_man_state->GetManeuverIt()->GetBeginNodeIndex(),
                curr_man_state->GetManeuverIt()->GetEndNodeIndex(),
                vehicle_pos.GetPathLinkIndex());
    }

    if (next_man_state != nullptr) {
        GUIDE_LOG_INFO("next maneuver: total={}, drive={}, dis_to_snode={}, snode_idx={}, enode_idx={}, path_index={}",
            next_man_state->GetTotalDistance(), 
            next_man_state->GetDriveDistance(),
            next_man_state->GetDistanceToSNode(),
            next_man_state->GetManeuverIt()->GetBeginNodeIndex(),
            next_man_state->GetManeuverIt()->GetEndNodeIndex(),
            vehicle_pos.GetPathLinkIndex());
    }

#endif

    GUIDE_ASSERT(curr_man_state != nullptr);
    if (curr_man_state == nullptr) {
        GUIDE_LOG_ERROR("curr_man_state is nullptr .");
        return false;
    }

    if (!curr_man_state->GetManeuverIt()->IsDestinationType()) {
        GUIDE_ASSERT(next_man_state != nullptr);
        if (next_man_state == nullptr) {
            GUIDE_LOG_ERROR("next_man_state is nullptr .");
            return false;
        }
    }

    guide_data.pos = vehicle_pos;
    voice_policy_.DoPlay(curr_man_state, next_man_state, guide_data);
    return true;
}

bool NavStateTracker::DoGuideLaneInfos(const VehiclePosition &vehicle_pos, GuideData &guide_data) {

    ManeuverLaneInfoState *man_lane_info_state = state_manager_.GetCurrManeuverLaneInfoState();
    if (man_lane_info_state == nullptr) {
        GUIDE_ASSERT(false);
        return false;
    }

    const LaneInfoState *lane_state = man_lane_info_state->GetNextLaneInfo();
    if (lane_state == nullptr) {
        return false;
    }

    if (lane_state->curr_distance >= 0 && lane_state->curr_distance <= 200) {
        guide_data.lane_info = lane_state->ptr;
        return true;
    }

    guide_data.lane_info = nullptr;
    return false;
}

bool NavStateTracker::DoGuideJunctionViews(const VehiclePosition &vehicle_pos, GuideData &guide_data) {
    ManeuverJunctionViewState * man_junction_view_state = state_manager_.GetCurrManeuverJunctionViewState();
    if (man_junction_view_state == nullptr) {
        GUIDE_ASSERT(false);
        return false;
    }

    const JunctionViewState *junction_view_state = man_junction_view_state->GetNextJunctionView();
    if (junction_view_state == nullptr) {
        return false;
    }

    if (junction_view_state->curr_distance >= 0 && junction_view_state->curr_distance <= 100) {
        guide_data.junction_view = junction_view_state->ptr;
        return true;
    }

    guide_data.junction_view = nullptr;
    return false;
}

void NavStateTracker::DoGuideFacilities(const VehiclePosition &vehicle_pos, GuideData &guide_data) {
    
}
    
void NavStateTracker::DoGuideTollStations(const VehiclePosition &vehicle_pos, GuideData &guide_data) {

}

}  // namespace guide
}  // namespace aurora
/* EOF */
