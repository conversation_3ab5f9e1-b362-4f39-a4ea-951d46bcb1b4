#ifndef MAP_SRC_GUIDE_SRC_GUIDE_NAV_NAVIGATION_STATE_TRACKER_H
#define MAP_SRC_GUIDE_SRC_GUIDE_NAV_NAVIGATION_STATE_TRACKER_H

#include <list>
#include "guidance/src/common/maneuver.h"
#include "guidance/src/guide/abstract_state_tracker.h"
#include "guidance/src/guide/navigation/data_state_manager.h"
#include "guidance/src/guide/navigation/voice_play_policy.h"

namespace aurora {
namespace guide {

class NavStateTracker : public AbstractStateTracker {
public:
    NavStateTracker(PathDataManagerPtr path_manager);
    ~NavStateTracker() = default;

    virtual int32_t DoGuide(const VehiclePosition &vehicle_pos, GuideData &guide_data);

protected:
    bool DoGuideManeuvers(const VehiclePosition &vehicle_pos, GuideData &guide_data);
    bool DoGuideLaneInfos(const VehiclePosition &vehicle_pos, GuideData &guide_data);
    bool DoGuideJunctionViews(const VehiclePosition &vehicle_pos, GuideData &guide_data);
    void DoGuideFacilities(const VehiclePosition &vehicle_pos, GuideData &guide_data);
    void DoGuideTollStations(const VehiclePosition &vehicle_pos, GuideData &guide_data);

private:
    DataStateManager state_manager_;
    VoicePlayPolicy  voice_policy_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_NAV_NAVIGATION_STATE_TRACKER_H
/* EOF */
