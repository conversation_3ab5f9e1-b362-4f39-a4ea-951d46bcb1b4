// <PERSON><PERSON><PERSON> asserts by default but we dont want to crash running server
// its more useful to throw and catch for our use case
#define RAPIDJSON_ASSERT_THROWS
#undef RAPIDJSON_ASSERT
#define RAPIDJSON_ASSERT(x)                                                                          \
  if (!(x))                                                                                          \
  throw std::logic_error(RAPIDJSON_STRINGIFY(x))
// Because we now throw exceptions, we need to turn off RAPIDJSON_NOEXCEPT
#define RAPIDJSON_HAS_CXX11_NOEXCEPT 0
// Enable std::string overloads
#define RAPIDJSON_HAS_STDSTRING 1


#include "guidance/src/debug/geojson_writter.h"

#include <assert.h>

#include <iostream>
#include <fstream>
#include <sstream>

#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#include "rapidjson/stringbuffer.h"
#include "rapidjson/prettywriter.h"

#include "guidance/src/common/guide_log.h"

#include "guidance/src/common/file_util.h"

namespace aurora {
namespace guide {


static std::string wsl_dir = "/mnt/d/tmp/route/debug/";
static std::string ubuntu_dir = "/mnt/hgfs/tmp/route/debug/"; 
static std::string debug_dir;

GeojsonWritter::GeojsonWritter() {
    debug_ = false;

    if (FileUtil::IsDirectoryExist("/home/<USER>")) {
        if (FileUtil::IsDirectoryExist(wsl_dir)) {
            debug_ = true;
            debug_dir = wsl_dir;
        } else if (FileUtil::IsDirectoryExist(ubuntu_dir)) {
            debug_ = true;
            debug_dir = ubuntu_dir;
        }
    }

#if 0 
    if (!debug_) {
        char *env_val = getenv("guide_debug_mode");
        assert(env_val != nullptr);
        if ((env_val != nullptr) && (std::string(env_val) == "1")) {
            debug_ = true;
            debug_dir = "~/guide_debug_data/";

            if (!FileUtil::IsDirectoryExist(debug_dir)) {
                FileUtil::CreateDirectory(debug_dir);
            }
        }
    }
#endif
}

void GeojsonWritter::SetDataProvider(EnhanceDataProvider *provider) {
    provider_ = provider;
}

bool GeojsonWritter::IsDebugOn() const {
    return debug_;
}

void GeojsonWritter::WriteEnhanceGraph(EnhancePathResultPtr enhance_path) {
    if (!IsDebugOn()) {
        return;
    }

    if (enhance_path == nullptr) {
        GUIDE_ASSERT(false);
        return;
    }

    rapidjson::Document doc;
    doc.SetObject();
    auto& allocator = doc.GetAllocator();
    doc.AddMember("type", "FeatureCollection", allocator);

    rapidjson::Value features(rapidjson::kArrayType);

    // process directed edge
    int32_t node_num = enhance_path->GetNodeNum();
    for (int node_idx = 0; (node_idx + 1) < node_num; ++node_idx) {        
        // add edge info
        rapidjson::Value feature(rapidjson::kObjectType);
        feature.AddMember("type", "Feature", allocator);
            
        rapidjson::Value geometry(rapidjson::kObjectType);
        geometry.AddMember("type", "LineString", allocator);

        rapidjson::Value coordinates(rapidjson::kArrayType);
        DirectedEdge *directed_edge = enhance_path->GetEdge(node_idx);
        GUIDE_ASSERT(directed_edge != nullptr);
        GUIDE_ASSERT(directed_edge->IsValid());

        for (const auto& point : directed_edge->GetGeoPoints()) {
            rapidjson::Value coord(rapidjson::kArrayType);
            coord.PushBack(point.x(), allocator);
            coord.PushBack(point.y(), allocator);
            coordinates.PushBack(coord, allocator);
        }

        geometry.AddMember("coordinates", coordinates, allocator);
        feature.AddMember("geometry", geometry, allocator);
        
        rapidjson::Value properties(rapidjson::kObjectType);

        auto direct_id = directed_edge->GetDirectEdgeId();

        // properties.AddMember("id", rapidjson::Value(edge_info->id.ToString(), allocator), allocator);
        properties.AddMember("mesh_col", direct_id.edge_id.tile_id.mesh_col, allocator);
        properties.AddMember("mesh_row", direct_id.edge_id.tile_id.mesh_row, allocator);
        properties.AddMember("level", direct_id.edge_id.tile_id.level, allocator);
        properties.AddMember("id", direct_id.edge_id.feature_id, allocator);
        properties.AddMember("is_forward", direct_id.forward, allocator);

        std::shared_ptr<StreetNames> street_names = directed_edge->GetStreetNames();

        for (const auto &street_name : *street_names) {
            if (street_name->IsRoadNo()) {
                properties.AddMember("road_no", street_name->Value(), allocator);                
            } else {
                properties.AddMember("road_name", street_name->Value(), allocator);                
            }
        }
        properties.AddMember("path_flag", 1, allocator);
        properties.AddMember("length", directed_edge->GetLengthMeter(), allocator);
        properties.AddMember("start_node", directed_edge->GetStartNodeId(), allocator);
        properties.AddMember("end_node", directed_edge->GetEndNodeId(), allocator);
        properties.AddMember("ramp", directed_edge->IsRamp(), allocator);
        properties.AddMember("inner", directed_edge->IsInnerEdge(), allocator);
        properties.AddMember("begin_heading", directed_edge->GetBeginHeading(), allocator);
        properties.AddMember("end_heading", directed_edge->GetEndHeading(), allocator);


        feature.AddMember("properties", properties, allocator);
        features.PushBack(feature, allocator);
    }

    // process out branch
    for (int32_t node_idx = 0; node_idx < node_num; ++node_idx) {
        EnhanceNode *enhance_node = enhance_path->GetNode(node_idx);

        GUIDE_ASSERT(enhance_node != nullptr);

        if (enhance_node->GetOutIntersectionSize() > 0) {
            int branch_num = enhance_node->GetOutIntersectionSize();
            for (int branch_idx = 0; branch_idx < branch_num; ++branch_idx) {
                const OutIntersectionBranch *branch = enhance_node->GetOutIntersectionBranch(branch_idx);
                GUIDE_ASSERT(branch != nullptr);

                const std::vector<DirectEdgeId>& direct_ids = branch->GetDirectIds();
                for (const auto& direct_id : direct_ids) {
                    rapidjson::Value feature(rapidjson::kObjectType);
                    feature.AddMember("type", "Feature", allocator);
                        
                    rapidjson::Value geometry(rapidjson::kObjectType);
                    geometry.AddMember("type", "LineString", allocator);

                    rapidjson::Value coordinates(rapidjson::kArrayType);

                    TopolEdge *topo_edge = provider_->GetTopoEdge(direct_id.edge_id);
                    AugmentEdge *aug_edge = provider_->GetAugmentEdge(direct_id.edge_id);
                    GUIDE_ASSERT(topo_edge != nullptr);
                    GUIDE_ASSERT(aug_edge != nullptr);
                    
                    for (const auto& point : aug_edge->GetGeoPoints()) {
                        rapidjson::Value coord(rapidjson::kArrayType);
                        coord.PushBack(point.x(), allocator);
                        coord.PushBack(point.y(), allocator);
                        coordinates.PushBack(coord, allocator);
                    }

                    geometry.AddMember("coordinates", coordinates, allocator);
                    feature.AddMember("geometry", geometry, allocator);

                    rapidjson::Value properties(rapidjson::kObjectType);

                    // properties.AddMember("id", rapidjson::Value(edge_info->id.ToString(), allocator), allocator);
                    properties.AddMember("mesh_col", direct_id.edge_id.tile_id.mesh_col, allocator);
                    properties.AddMember("mesh_row", direct_id.edge_id.tile_id.mesh_row, allocator);
                    properties.AddMember("level", direct_id.edge_id.tile_id.level, allocator);
                    properties.AddMember("id", direct_id.edge_id.feature_id, allocator);
                    properties.AddMember("is_forward", direct_id.forward, allocator);
                    
                    properties.AddMember("road_name", std::string(aug_edge->GetLocalName() ? aug_edge->GetLocalName() : ""), allocator);
                    properties.AddMember("road_no", std::string(aug_edge->GetRoadNo() ? aug_edge->GetRoadNo() : ""), allocator);

                    properties.AddMember("path_flag", 0, allocator);
                    properties.AddMember("length", topo_edge->GetBaseInfo()->length, allocator);
                    properties.AddMember("start_node", topo_edge->GetBaseInfo()->start_node_id, allocator);
                    properties.AddMember("end_node", topo_edge->GetBaseInfo()->end_node_id, allocator);
                    properties.AddMember("ramp", topo_edge->GetBaseInfo()->is_ramp, allocator);
                    properties.AddMember("inner", topo_edge->GetBaseInfo()->is_inner_edge, allocator);

                    properties.AddMember("begin_heading", -1, allocator);
                    properties.AddMember("end_heading", -1, allocator);

                    feature.AddMember("properties", properties, allocator);
                    features.PushBack(feature, allocator);
                }
            }
        }
    } // for

    // process in branch (non intersection)
     for (int32_t node_idx = 0; node_idx < node_num; ++node_idx) {
        EnhanceNode *enhance_node = enhance_path->GetNode(node_idx);

        GUIDE_ASSERT(enhance_node != nullptr);

        if (enhance_node->GetInIntersectionSize() > 0) {
            int branch_num = enhance_node->GetInIntersectionSize();
            for (int branch_idx = 0; branch_idx < branch_num; ++branch_idx) {
                const InIntersectionBranch *branch = enhance_node->GetInIntersectionBranch(branch_idx);
                GUIDE_ASSERT(branch != nullptr);

                const std::vector<DirectEdgeId>& direct_ids = branch->GetDirectIds();
                for (const auto& direct_id : direct_ids) {
                    rapidjson::Value feature(rapidjson::kObjectType);
                    feature.AddMember("type", "Feature", allocator);
                        
                    rapidjson::Value geometry(rapidjson::kObjectType);
                    geometry.AddMember("type", "LineString", allocator);

                    rapidjson::Value coordinates(rapidjson::kArrayType);

                    TopolEdge *topo_edge = provider_->GetTopoEdge(direct_id.edge_id);
                    AugmentEdge *aug_edge = provider_->GetAugmentEdge(direct_id.edge_id);
                    GUIDE_ASSERT(topo_edge != nullptr);
                    GUIDE_ASSERT(aug_edge != nullptr);
                    
                    for (const auto& point : aug_edge->GetGeoPoints()) {
                        rapidjson::Value coord(rapidjson::kArrayType);
                        coord.PushBack(point.x(), allocator);
                        coord.PushBack(point.y(), allocator);
                        coordinates.PushBack(coord, allocator);
                    }

                    geometry.AddMember("coordinates", coordinates, allocator);
                    feature.AddMember("geometry", geometry, allocator);

                    rapidjson::Value properties(rapidjson::kObjectType);

                    // properties.AddMember("id", rapidjson::Value(edge_info->id.ToString(), allocator), allocator);
                    properties.AddMember("mesh_col", direct_id.edge_id.tile_id.mesh_col, allocator);
                    properties.AddMember("mesh_row", direct_id.edge_id.tile_id.mesh_row, allocator);
                    properties.AddMember("level", direct_id.edge_id.tile_id.level, allocator);
                    properties.AddMember("id", direct_id.edge_id.feature_id, allocator);
                    properties.AddMember("is_forward", direct_id.forward, allocator);

                    properties.AddMember("road_name", std::string(aug_edge->GetLocalName() ? aug_edge->GetLocalName() : ""), allocator);
                    properties.AddMember("road_no", std::string(aug_edge->GetRoadNo() ? aug_edge->GetRoadNo() : ""), allocator);

                    properties.AddMember("path_flag", 0, allocator);
                    properties.AddMember("length", topo_edge->GetBaseInfo()->length, allocator);
                    properties.AddMember("start_node", topo_edge->GetBaseInfo()->start_node_id, allocator);
                    properties.AddMember("end_node", topo_edge->GetBaseInfo()->end_node_id, allocator);
                    properties.AddMember("ramp", topo_edge->GetBaseInfo()->is_ramp, allocator);
                    properties.AddMember("inner", topo_edge->GetBaseInfo()->is_inner_edge, allocator);
                    properties.AddMember("begin_heading", -1, allocator);
                    properties.AddMember("end_heading", branch->GetEndHeading(), allocator);

                    feature.AddMember("properties", properties, allocator);
                    features.PushBack(feature, allocator);
                }
            }
        }
    } // for

    doc.AddMember("features", features, allocator);

    std::string dir = debug_dir + "/" + std::to_string(enhance_path->GetPathId());
    if (!FileUtil::IsDirectoryExist(dir)) {
        FileUtil::CreateDirectory(dir);
    }

    std::string filename = dir + "/enhance_path_graph.geojson";
    std::ofstream file(filename.c_str());
    if (!file.is_open()) {
        return;
    }

    rapidjson::StringBuffer buffer;
    rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
    doc.Accept(writer);

    file << buffer.GetString();
    file.close();
}

void GeojsonWritter::WriteManeuvers(EnhancePathResultPtr enhance_path, const std::list<Maneuver> &maneuvers) {

      if (!IsDebugOn()) {
        return;
    }

    if (enhance_path == nullptr || maneuvers.empty()) {
        GUIDE_ASSERT(false);
        return;
    }

    rapidjson::Document doc;
    doc.SetObject();
    auto& allocator = doc.GetAllocator();
    doc.AddMember("type", "FeatureCollection", allocator);

    rapidjson::Value features(rapidjson::kArrayType);

    int maneuver_num = maneuvers.size();
    std::list<Maneuver>::const_iterator it = maneuvers.begin();
    int maneuver_id = 0;
    while ( it != maneuvers.end()) {
        const Maneuver &maneuver = *it;

        if (maneuver.GetBeginNodeIndex() == maneuver.GetEndNodeIndex()) {
            // only point
            GUIDE_LOG_INFO("Index:{} Type: {}", maneuver_id, ManeuverTypeToString(maneuver.GetType()));

        } else {
            // add maneuver info
            rapidjson::Value feature(rapidjson::kObjectType);
            feature.AddMember("type", "Feature", allocator);
        
            // add geo info
            rapidjson::Value geometry(rapidjson::kObjectType);
            geometry.AddMember("type", "LineString", allocator);
            
            rapidjson::Value coordinates(rapidjson::kArrayType);

#if 0
            for (const auto &point : maneuver.GetPoints()) {
                rapidjson::Value coord(rapidjson::kArrayType);
                coord.PushBack(point.x(), allocator);
                coord.PushBack(point.y(), allocator);
                coordinates.PushBack(coord, allocator);
            }
#else
            for (int32_t index = maneuver.GetBeginNodeIndex(); index < maneuver.GetEndNodeIndex(); ++index) {
                DirectedEdge *directed_edge = enhance_path->GetEdge(index);
                GUIDE_ASSERT(directed_edge != nullptr);
                GUIDE_ASSERT(directed_edge->IsValid());

                for (const auto& point : directed_edge->GetGeoPoints()) {
                    rapidjson::Value coord(rapidjson::kArrayType);
                    coord.PushBack(point.x(), allocator);
                    coord.PushBack(point.y(), allocator);
                    coordinates.PushBack(coord, allocator);
                } 
            }
#endif
            geometry.AddMember("coordinates", coordinates, allocator);
            feature.AddMember("geometry", geometry, allocator);

            rapidjson::Value properties(rapidjson::kObjectType);
            properties.AddMember("maneuver_id", maneuver_id, allocator);
            properties.AddMember("type", ManeuverTypeToString(maneuver.GetType()), allocator);

            const auto& street_names = maneuver.GetStreetNames();
            int32_t street_name_idx = 0;
            for (const auto &street_name :*street_names) {
                std::string key = "street_name_" + std::to_string(street_name_idx);
                
                switch (street_name_idx)
                {
                case 0:
                    properties.AddMember("street_name_0", street_name->Value(), allocator);
                    break;

                case 1:
                    properties.AddMember("street_name_1", street_name->Value(), allocator);
                    break;
                
                case 2:
                    properties.AddMember("street_name_2", street_name->Value(), allocator);
                    break;

                case 3:
                    properties.AddMember("street_name_3", street_name->Value(), allocator);
                    break;

                case 4:
                    properties.AddMember("street_name_4", street_name->Value(), allocator);
                    break;

                case 5:
                    properties.AddMember("street_name_5", street_name->Value(), allocator);
                    break;

                case 6:
                    properties.AddMember("street_name_6", street_name->Value(), allocator);
                    break;

                default:
                    break;
                }
                ++street_name_idx;
            }

            properties.AddMember("turn_degree", maneuver.GetTurnDegree(), allocator);
            properties.AddMember("begin_heading", maneuver.GetBeginHeading(), allocator);
            properties.AddMember("end_heading", maneuver.GetEndHeading(), allocator);
            properties.AddMember("length", maneuver.GetLengthMeter(), allocator);

            feature.AddMember("properties", properties, allocator);
            features.PushBack(feature, allocator);
        }

        ++it;
        ++maneuver_id;
    }

    doc.AddMember("features", features, allocator);

    std::string dir = debug_dir + "/" + std::to_string(enhance_path->GetPathId());
    if (!FileUtil::IsDirectoryExist(dir)) {
        FileUtil::CreateDirectory(dir);
    }

    std::string filename = dir + "/enhance_maneuver_list.geojson";

    std::ofstream file(filename.c_str());
    if (!file.is_open()) {
        return;
    }

    rapidjson::StringBuffer buffer;
    rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
    doc.Accept(writer);

    file << buffer.GetString();
    file.close();
}

#if 1
void GeojsonWritter::WriteMatching(uint64_t path_id, const std::vector<VehiclePosition> &match_results) {
    if (!IsDebugOn()) {
        return;
    }

    rapidjson::Document doc;
    doc.SetObject();
    auto& allocator = doc.GetAllocator();
    doc.AddMember("type", "FeatureCollection", allocator);

    rapidjson::Value features(rapidjson::kArrayType);

    // process directed edge
    int32_t match_num = match_results.size();
    for (int match_idx = 0; match_idx < match_num; ++match_idx) {        
        // add edge info
        auto& result = match_results[match_idx];

        rapidjson::Value feature(rapidjson::kObjectType);
        feature.AddMember("type", "Feature", allocator);
            
        rapidjson::Value geometry(rapidjson::kObjectType);
        geometry.AddMember("type", "Point", allocator);

        rapidjson::Value coordinates(rapidjson::kArrayType);
        coordinates.PushBack(static_cast<double>(result.GetProj().first), allocator);
        coordinates.PushBack(static_cast<double>(result.GetProj().second), allocator);
        geometry.AddMember("coordinates", coordinates, allocator);
        feature.AddMember("geometry", geometry, allocator);

        rapidjson::Value properties(rapidjson::kObjectType);

        properties.AddMember("valid", result.IsValid(), allocator);
        properties.AddMember("link_idx", result.GetPathLinkIndex(), allocator);
        properties.AddMember("offset", result.GetLinkOffset(), allocator);
        properties.AddMember("speed", result.GetSpeed(), allocator);
        properties.AddMember("stamp", result.GetStamp(), allocator);
        properties.AddMember("is_forward", result.IsForward(), allocator);
        properties.AddMember("onroad", result.IsOnRoad(), allocator);
        properties.AddMember("reroute", result.IsReroute(), allocator);


        feature.AddMember("properties", properties, allocator);
        features.PushBack(feature, allocator);
    }

    doc.AddMember("features", features, allocator);

    std::string dir = debug_dir + "/" + std::to_string(path_id);
    if (!FileUtil::IsDirectoryExist(dir)) {
        FileUtil::CreateDirectory(dir);
    }

    std::string filename = dir + "/match_results.geojson";
    std::ofstream file(filename.c_str());
    if (!file.is_open()) {
        return;
    }

    rapidjson::StringBuffer buffer;
    rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
    doc.Accept(writer);

    file << buffer.GetString();
    file.close();
}
    
void GeojsonWritter::WriteGuideData(uint64_t path_id, const std::vector<GuideData> &guide_datas) {
    if (!IsDebugOn()) {
        return;
    }

    rapidjson::Document doc;
    doc.SetObject();
    auto& allocator = doc.GetAllocator();
    doc.AddMember("type", "FeatureCollection", allocator);

    rapidjson::Value features(rapidjson::kArrayType);

    // process directed edge
    int32_t guide_num = guide_datas.size();
    for (int guide_idx = 0; guide_idx < guide_num; ++guide_idx) {        
        // add edge info
        auto& result = guide_datas[guide_idx];

        rapidjson::Value feature(rapidjson::kObjectType);
        feature.AddMember("type", "Feature", allocator);
            
        rapidjson::Value geometry(rapidjson::kObjectType);
        geometry.AddMember("type", "Point", allocator);

        rapidjson::Value coordinates(rapidjson::kArrayType);
        coordinates.PushBack(static_cast<double>(result.pos.GetProj().first), allocator);
        coordinates.PushBack(static_cast<double>(result.pos.GetProj().second), allocator);

        geometry.AddMember("coordinates", coordinates, allocator);
        feature.AddMember("geometry", geometry, allocator);

        rapidjson::Value properties(rapidjson::kObjectType);

        properties.AddMember("valid", result.pos.IsValid(), allocator);
        properties.AddMember("link_idx", result.pos.GetPathLinkIndex(), allocator);
        properties.AddMember("offset", result.pos.GetLinkOffset(), allocator);
        properties.AddMember("speed", result.pos.GetSpeed(), allocator);
        properties.AddMember("stamp", result.pos.GetStamp(), allocator);
        properties.AddMember("voice", result.voice_text, allocator);


        feature.AddMember("properties", properties, allocator);
        features.PushBack(feature, allocator);
    }

    doc.AddMember("features", features, allocator);

    std::string dir = debug_dir + "/" + std::to_string(path_id);
    if (!FileUtil::IsDirectoryExist(dir)) {
        FileUtil::CreateDirectory(dir);
    }


    std::string filename = dir + "/guide_data.geojson";

    std::ofstream file(filename.c_str());
    if (!file.is_open()) {
        return;
    }

    rapidjson::StringBuffer buffer;
    rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
    doc.Accept(writer);

    file << buffer.GetString();
    file.close();
}
#endif

void GeojsonWritter::WriteJunctionView(uint64_t path_id, EnhanceJunctionViewPtr ptr) {
    if (!IsDebugOn()) {
        return;
    }

    if (ptr == nullptr) {
        return;
    }

    auto back = ptr->GetBackgroundImage();
    auto front = ptr->GetForegroundImage();

    std::string dir = debug_dir + "/" + std::to_string(path_id) + "/junction_views/";
    if (!FileUtil::IsDirectoryExist(dir)) {
        FileUtil::CreateDirectory(dir);
    }

    if (back != nullptr) {
        std::string file_name = dir + ptr->GetId() + "_back.png";
        std::ofstream file(file_name.c_str(), std::ios::binary);
        file.write(reinterpret_cast<const char*>(back->data()), back->size());
        file.close();
    }

    if (front != nullptr) {
        std::string file_name = dir + ptr->GetId() + "_front_.png";
        std::ofstream file(file_name.c_str(), std::ios::binary);
        file.write(reinterpret_cast<const char*>(front->data()), front->size());
        file.close();
    }
}

void GeojsonWritter::WriteTollStation(uint64_t path_id, EnhanceTollStationPtr ptr) {

}

}  // namespace guide
}  // namespace aurora
/* EOF */
