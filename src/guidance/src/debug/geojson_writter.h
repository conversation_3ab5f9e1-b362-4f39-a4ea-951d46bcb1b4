#ifndef MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_GEOJSON_WRITTER_H
#define MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_GEOJSON_WRITTER_H

#include <string>
#include <list>
#include <vector>
#include "guidance/src/data/enhance_data_provider.h"
#include "guidance/src/data/enhance_path_result.h"
#include "guidance/src/common/maneuver.h"
#include "guidance/src/common/guide_data.h"

namespace aurora {
namespace guide {

class GeojsonWritter {
public:
    Geo<PERSON>sonWritter();

    bool IsDebugOn() const;
    void SetDataProvider(EnhanceDataProvider *provider);

    void WriteEnhanceGraph(EnhancePathResultPtr enhance_path);
    void WriteManeuvers(EnhancePathResultPtr enhance_path, const std::list<Maneuver> &maneuvers);

    void WriteMatching(uint64_t path_id, const std::vector<VehiclePosition> &match_results);
    void WriteGuideData(uint64_t path_id, const std::vector<GuideData> &guide_datas);

    void WriteJunctionView(uint64_t path_id, EnhanceJunctionViewPtr ptr);
    void WriteTollStation(uint64_t path_id, EnhanceTollStationPtr ptr);

private:
    bool debug_;
    EnhanceDataProvider *provider_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_GUIDE_DEBUG_GEOJSON_WRITTER_H
/* EOF */
