#include "guidance/src/maker/normalize/verbal_text_formatter_factory.h"
#include "guidance/src/maker/normalize/verbal_text_formatter.h"

namespace aurora {
namespace guide {

std::shared_ptr<VerbalTextFormatter>
VerbalTextFormatterFactory::Create(const std::string& country_code, const std::string& state_code) {
    return std::make_shared<VerbalTextFormatter>(country_code, state_code);
}

}  // namespace guide
}  // namespace aurora
/* EOF */
