#ifndef MAP_SRC_GUIDE_SRC_MAKER_NORMALIZE_VERBAL_TEXT_FORMATTER_FACTORY_H
#define MAP_SRC_GUIDE_SRC_MAKER_NORMALIZE_VERBAL_TEXT_FORMATTER_FACTORY_H

#include <string>
#include <memory>

#include "guidance/src/maker/normalize/verbal_text_formatter.h"

namespace aurora {
namespace guide {

class VerbalTextFormatterFactory {
public:
    VerbalTextFormatterFactory() = delete;

    static std::shared_ptr<VerbalTextFormatter> 
        Create(const std::string &coutry_code, const std::string &state_code);

}; // class

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_MAKER_NORMALIZE_VERBAL_TEXT_FORMATTER_FACTORY_H
/* EOF */
