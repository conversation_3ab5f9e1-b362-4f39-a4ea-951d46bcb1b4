#include "guidance/src/maker/narrative/narrative_builder_factory.h"
#include <unordered_map>
#include <filesystem>
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

static std::unordered_map<std::string, std::string> locales_json = {
    {"zh_CN", "zh-CN.json"},
    {"en_GB", "en-GB.json"},
};

static std::string locales_dir = "/home/<USER>/proj/map_engine/src/guidance/config/locales";

std::unique_ptr<NarrativeBuilder> 
NarrativeBuilderFactory::Create(GuidanceOptions *options, PathDataManagerPtr path_manager, const std::string &dir) {
    if (options == nullptr) {
        GUIDE_LOG_ERROR("options_ is nullptr .");
        return nullptr;
    }

    if (path_manager == nullptr) {
        GUIDE_LOG_ERROR("path_manager is nullptr .");
        return nullptr;
    }

    std::string language_tag = options->language_tag;
    if (locales_json.find(language_tag) == locales_json.end()) {
        // GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("{} narrative config file not found .", language_tag);
        return nullptr;
    }

    std::string json = dir + "/" + locales_json.find(language_tag)->second;
    std::filesystem::path file_path(json);
    if (!std::filesystem::exists(file_path)) {
        GUIDE_LOG_ERROR("{} narrative config file not exist.", json);
        return nullptr;
    }

    return std::make_unique<NarrativeBuilder>(options, json, path_manager);
}

}  // namespace guide
}  // namespace aurora
/* EOF */
