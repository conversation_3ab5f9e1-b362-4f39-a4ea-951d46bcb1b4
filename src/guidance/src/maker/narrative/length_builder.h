#ifndef MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_LENGTH_BUILDER_H
#define MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_LENGTH_BUILDER_H

#include <string>
#include <vector>
#include "guidance/src/common/maneuver.h"
#include "guidance/src/maker/narrative/narrative_dictionary.h"

namespace aurora {
namespace guide {

class LengthBuilder {
public:
    LengthBuilder(DistanceUnits units, NarrativeDictionary *dictionary);
    
    /**
     * Returns the length string of the specified maneuver.
     *
     * @param maneuver The maneuver to process.
     *
     * @return the length string of the specified maneuver.
     */
    std::string FormLength(Maneuver& maneuver,
                           const std::vector<std::string>& metric_lengths,
                           const std::vector<std::string>& us_customary_lengths);

    /**
     * Returns the length string of the specified distance.
     *
     * @param distance The distance in user units (miles or kilometers) to process.
     *
     * @return the length string of the specified distance.
     */
    std::string FormLength(float distance,
                           const std::vector<std::string>& metric_lengths,
                           const std::vector<std::string>& us_customary_lengths);

    /**
     * Returns the metric length string of the specified kilometer value.
     *
     * @param kilometers The length value to process.
     *
     * @return the metric length string of the specified length value.
     */
    std::string FormMetricLength(float kilometers, const std::vector<std::string>& metric_lengths);

    /**
     * Returns the US customary length string of the specified miles value.
     *
     * @param miles The length value to process.
     *
     * @return the US customary length string of the specified length value.
     */
    std::string FormUsCustomaryLength(float miles,
                                      const std::vector<std::string>& us_customary_lengths);


protected:
    DistanceUnits units_;
    NarrativeDictionary *dictionary_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_LENGTH_BUILDER_H
/* EOF */
