#ifndef MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_MULTI_CUE_BUILDER_H
#define MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_MULTI_CUE_BUILDER_H

#include <cstdint>
#include <string>
#include <list>
#include "guidance/src/common/maneuver.h"
#include "guidance/src/maker/narrative/narrative_dictionary.h"
#include "guidance/src/maker/narrative/length_builder.h"

namespace aurora {
namespace guide {

class NarrativeBuilder;

class MultiCueBuilder {
public:
    MultiCueBuilder(NarrativeDictionary *narrative_dictionary, LengthBuilder *length_builder, NarrativeBuilder * narrative_builder);
    ~MultiCueBuilder() = default;

    /**
     * Processes the specified maneuver list and creates verbal multi-cue
     * instructions based on quick maneuvers.
     *
     * @param maneuvers The maneuver list to process.
     */
    void FormVerbalMultiCue(std::list<Maneuver>& maneuvers);

protected:

    /**
     * Returns the verbal multi-cue instruction based on the specified maneuvers.
     *
     * @param maneuver The current quick maneuver that will be the first verbal
     *                 cue in the returned instruction.
     * @param next_maneuver The next maneuver that will be the second verbal cue
     *                      in the returned instruction.
     * @param process_succinct Flag to determine if we are processing the succinct instruction.
     *                         Defaulted to false.
     *
     * @return the verbal multi-cue instruction based on the specified maneuvers.
     */
    std::string
    FormVerbalMultiCue(Maneuver& maneuver, Maneuver& next_maneuver, bool process_succinct = false);

    /**
     * Returns the verbal multi-cue instruction based on the specified maneuver and strings.
     *
     * @param maneuver The current quick maneuver.
     * @param first_verbal_cue The first verbal cue in the returned instruction.
     * @param second_verbal_cue The second verbal cue in the returned instruction.
     *
     * @return the verbal multi-cue instruction based on the specified maneuver and strings.
     */
    std::string FormVerbalMultiCue(Maneuver& maneuver,
                                    const std::string& first_verbal_cue,
                                    const std::string& second_verbal_cue);

    /**
     * Returns true if a verbal multi-cue instruction should be formed for the
     * two specified maneuvers.
     *
     * @param maneuver The current maneuver that must be short based on time.
     * @param next_maneuver The next maneuver that must meet criteria to be used.
     *
     * @return true if a verbal multi-cue instruction should be formed for the
     *         two specified maneuvers.
     */
    bool IsVerbalMultiCuePossible(Maneuver& maneuver, Maneuver& next_maneuver);


    /**
     * Returns true if the specified maneuver is within the mulit-cue bounds.
     * The time bounds for a start maneuver is greater than other maneuver types.
     *
     * @param maneuver The current maneuver that must be short based on time.
     *
     * @return true if the specified maneuver is within the mulit-cue bounds.
     */
    bool IsWithinVerbalMultiCueBounds(Maneuver& maneuver);

private:
    NarrativeDictionary *dictionary_;
    LengthBuilder       *length_builder_;
    NarrativeBuilder    *narrative_builder_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_MULTI_CUE_BUILDER_H
/* EOF */
