#include "guidance/src/maker/narrative/multi_cue_builder.h"

#include "boost/algorithm/string/replace.hpp"

#include "guidance/src/maker/narrative/narrative_dictionary.h"
#include "guidance/src/maker/narrative/narrative_builder.h"
#include "guidance/src/common/guide_log.h"

namespace aurora {
namespace guide {

// Basic time threshold in seconds for creating a verbal multi-cue
constexpr auto kVerbalMultiCueTimeThreshold = 13;
constexpr auto kVerbalMultiCueTimeStartManeuverThreshold = kVerbalMultiCueTimeThreshold * 3;

MultiCueBuilder::MultiCueBuilder(NarrativeDictionary *narrative_dictionary, LengthBuilder *length_builder, NarrativeBuilder *narrative_builder)
: dictionary_(narrative_dictionary)
, length_builder_(length_builder)
, narrative_builder_(narrative_builder) {

    GUIDE_ASSERT(dictionary_ != nullptr);
    GUIDE_ASSERT(length_builder_ != nullptr);
    GUIDE_ASSERT(narrative_builder != nullptr);

    if (dictionary_ == nullptr) {
        GUIDE_LOG_ERROR("dictionary_ is nullptr .");
    }

    if (length_builder_ == nullptr) {
        GUIDE_LOG_ERROR("length_builder_ is nullptr .");
    }

    if (narrative_builder == nullptr) {
        GUIDE_LOG_ERROR("narrative_builder is nullptr .");
    }
}
    
void MultiCueBuilder::FormVerbalMultiCue(std::list<Maneuver>& maneuvers) {
    Maneuver* prev_maneuver = nullptr;
    for (auto& maneuver : maneuvers) {
        if (prev_maneuver && IsVerbalMultiCuePossible(*prev_maneuver, maneuver)) {
            // Determine if imminent or distant verbal multi-cue
            // if previous maneuver has an intersecting traversable outbound edge
            // in the same direction as the maneuver
            switch (maneuver.GetType()) {
            case ManeuverType::kTypeSlightRight:
            case ManeuverType::kTypeRight:
            case ManeuverType::kTypeSharpRight:
            case ManeuverType::kTypeRightUTurn:
            case ManeuverType::kTypeRightToRamp:
            case ManeuverType::kTypeExitRight:
            case ManeuverType::kTypeStayRight: {
                if (prev_maneuver->HasRightOutXEdge()) {
                    prev_maneuver->SetDistantVerbalMultiCue(true);
                } else {
                    prev_maneuver->SetImminentVerbalMultiCue(true);
                }
                break;
            }

            case ManeuverType::kTypeSlightLeft:
            case ManeuverType::kTypeLeft:
            case ManeuverType::kTypeSharpLeft:
            case ManeuverType::kTypeLeftUTurn:
            case ManeuverType::kTypeLeftToRamp:
            case ManeuverType::kTypeExitLeft:
            case ManeuverType::kTypeStayLeft: {
                if (prev_maneuver->HasLeftOutXEdge()) {
                    prev_maneuver->SetDistantVerbalMultiCue(true);
                } else {
                    prev_maneuver->SetImminentVerbalMultiCue(true);
                }
                break;
            }

            case ManeuverType::kTypeDestination:
            case ManeuverType::kTypeDestinationLeft:
            case ManeuverType::kTypeDestinationRight: {
                if (prev_maneuver->HasLeftOutXEdge() || prev_maneuver->HasRightOutXEdge()) {
                    prev_maneuver->SetDistantVerbalMultiCue(true);
                } else {
                    prev_maneuver->SetImminentVerbalMultiCue(true);
                }
                break;
            }

            default: {
                prev_maneuver->SetImminentVerbalMultiCue(true);
                break;
            }
        }

        if (prev_maneuver->HasVerbalAlertInstruction()) {
            prev_maneuver->SetVerbalAlertInstruction(
                FormVerbalMultiCue(*prev_maneuver, maneuver, true));
        }

        // Set verbal succinct transition instruction as a verbal multi-cue
        if (prev_maneuver->HasVerbalSuccinctInstruction()) {
            prev_maneuver->SetVerbalSuccinctInstruction(
                FormVerbalMultiCue(*prev_maneuver, maneuver, true));
        }

        // Set verbal pre transition instruction as a verbal multi-cue
        prev_maneuver->SetVerbalPreInstruction(
            FormVerbalMultiCue(*prev_maneuver, maneuver));
    }

    // Update previous maneuver
    prev_maneuver = &maneuver;
   }
}

std::string MultiCueBuilder::FormVerbalMultiCue(Maneuver& maneuver,
                                                 Maneuver& next_maneuver,
                                                 bool process_succinct) {
    // Set current verbal cue
    const std::string& current_verbal_cue =
        ((process_succinct && maneuver.HasVerbalSuccinctInstruction())
            ? maneuver.GetVerbalSuccinctInstruction()
            : maneuver.GetVerbalPreInstruction());

    // Set next verbal cue
    std::string next_verbal_cue = next_maneuver.HasVerbalAlertInstruction()
                                        ? next_maneuver.GetVerbalAlertInstruction()
                                        : next_maneuver.GetVerbalPreInstruction();

    return FormVerbalMultiCue(maneuver, current_verbal_cue, next_verbal_cue);
}

std::string MultiCueBuilder::FormVerbalMultiCue(Maneuver& maneuver,
                                                const std::string& first_verbal_cue,
                                                const std::string& second_verbal_cue) {

    if (length_builder_ == nullptr || dictionary_ == nullptr || narrative_builder_ == nullptr) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("parameter is nullptr .");
        return "";
    }

    // "0": "<CURRENT_VERBAL_CUE> Then <NEXT_VERBAL_CUE>"
    // "1": "<CURRENT_VERBAL_CUE> Then, in <LENGTH>, <NEXT_VERBAL_CUE>"

    std::string instruction;
    instruction.reserve(kInstructionInitialCapacity);

    // Set instruction to the proper verbal multi-cue
    uint8_t phrase_id = 0;
    if (maneuver.GetDistantVerbalMultiCue()) {
        phrase_id = 1;
    }
    instruction = dictionary_->verbal_multi_cue_subset.phrases.at(std::to_string(phrase_id));

    // Replace phrase tags with values
    boost::replace_all(instruction, kCurrentVerbalCueTag, first_verbal_cue);
    boost::replace_all(instruction, kNextVerbalCueTag, second_verbal_cue);
    boost::replace_all(instruction, kLengthTag,
                        length_builder_->FormLength(maneuver, dictionary_->post_transition_verbal_subset.metric_lengths,
                                    dictionary_->post_transition_verbal_subset.us_customary_lengths));

    // If enabled, form articulated prepositions
    if (narrative_builder_->IsArticulatedPrepositionEnabled()) {
        narrative_builder_->FormArticulatedPrepositions(instruction);
    }

    GUIDE_LOG_INFO("multi_cue: {}", instruction);
    return instruction;
}


bool MultiCueBuilder::IsVerbalMultiCuePossible(Maneuver& maneuver, Maneuver& next_maneuver) {
    // Current maneuver must have a verbal pre-transition instruction
    // Next maneuver must have a verbal transition alert or a verbal pre-transition instruction
    // Current maneuver must be within verbal multi-cue bounds
    // Next maneuver must not be a merge
    // Current is not a roundabout OR current maneuver has combined enter/exit roundabout instruction
    // Next maneuver must not be a roundabout
    // Current and next maneuvers must not be transit or transit connection

    // TODO: 针对环岛需要做优化
    GUIDE_ASSERT(!maneuver.GetRoundAbout());
    GUIDE_ASSERT(!next_maneuver.GetRoundAbout());
    
    if (maneuver.HasVerbalPreInstruction() &&
        (next_maneuver.HasVerbalAlertInstruction() ||
          next_maneuver.HasVerbalPreInstruction()) &&
        IsWithinVerbalMultiCueBounds(maneuver) && !next_maneuver.IsMergeType() &&
        (!maneuver.GetRoundAbout() || next_maneuver.GetRoundAbout())) {
        return true;
    }
    return false;
}


bool MultiCueBuilder::IsWithinVerbalMultiCueBounds(Maneuver& maneuver) {
  if (maneuver.IsStartType()) {
    return (maneuver.GetBasicTime() < kVerbalMultiCueTimeStartManeuverThreshold);
  }
  // Maneuver must be quick (basic time < 13 sec)
  return (maneuver.GetBasicTime() < kVerbalMultiCueTimeThreshold);
}

}  // namespace guide
}  // namespace aurora
/* EOF */
