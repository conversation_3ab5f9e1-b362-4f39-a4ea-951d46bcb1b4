#include "guidance/src/maker/narrative/narrative_dictionary.h"
#include "boost/property_tree/ptree.hpp"
#include "guidance/src/common/rapidjson_util.h"

#include "guidance/src/common/guide_log.h"
#include "guidance/src/maker/narrative/ptree_util.h"

#include "guidance/src/common/file_util.h"

namespace aurora {
namespace guide {

NarrativeDictionary::NarrativeDictionary(const std::string& language_tag, const std::string& json_file) {
    language_tag_ = language_tag;

    std::string content = FileUtil::ReadTextFile(json_file);
    boost::property_tree::ptree narrative_pt;
    std::stringstream ss;
    ss << content;
    // TODO: thorw exception
    rapidjson::read_json(ss, narrative_pt);
    Load(narrative_pt);
}

void NarrativeDictionary::Load(const boost::property_tree::ptree& narrative_pt) {
    posix_locale = narrative_pt.get<std::string>(kPosix<PERSON>oc<PERSON><PERSON>ey, "zh_CN.UTF-8");

    try {
        locale_ = std::locale(posix_locale.c_str());
    } catch (...) {
        GUIDE_LOG_ERROR("Using the default locale because a locale was not found for: " + posix_locale);
        locale_ = std::locale("");
    }

    // start
    GUIDE_LOG_INFO("populate start_subset ...");
    Load(start_subset, narrative_pt.get_child(kStartKey));

    GUIDE_LOG_INFO("Populate start_verbal_subset...");
    Load(start_verbal_subset, narrative_pt.get_child(kStartVerbalKey));

    // destination
    GUIDE_LOG_INFO("Populate destination_subset...");
    Load(destination_subset, narrative_pt.get_child(kDestinationKey));

    GUIDE_LOG_INFO("Populate destination_verbal_alert_subset...");
    Load(destination_verbal_alert_subset, narrative_pt.get_child(kDestinationVerbalAlertKey));

    GUIDE_LOG_INFO("Populate destination_verbal_subset...");
    Load(destination_verbal_subset, narrative_pt.get_child(kDestinationVerbalKey));

    // become
    GUIDE_LOG_INFO("Populate becomes_subset...");
    Load(becomes_subset, narrative_pt.get_child(kBecomesKey));

    GUIDE_LOG_INFO("Populate becomes_verbal_subset...");
    Load(becomes_verbal_subset, narrative_pt.get_child(kBecomesVerbalKey));

    // continue
    GUIDE_LOG_INFO("Populate continue_subset...");
    Load(continue_subset, narrative_pt.get_child(kContinueKey));

    GUIDE_LOG_INFO("Populate continue_verbal_alert_subset...");
    Load(continue_verbal_alert_subset, narrative_pt.get_child(kContinueVerbalAlertKey));

    GUIDE_LOG_INFO("Populate continue_verbal_subset...");
    Load(continue_verbal_subset, narrative_pt.get_child(kContinueVerbalKey));

    // bear
    GUIDE_LOG_INFO("Populate bear_subset...");
    Load(bear_subset, narrative_pt.get_child(kBearKey));

    GUIDE_LOG_INFO("Populate bear_verbal_subset...");
    Load(bear_verbal_subset, narrative_pt.get_child(kBearVerbalKey));

    // turn
    GUIDE_LOG_INFO("Populate turn_subset...");
    Load(turn_subset, narrative_pt.get_child(kTurnKey));

    GUIDE_LOG_INFO("Populate turn_verbal_subset...");
    Load(turn_verbal_subset, narrative_pt.get_child(kTurnVerbalKey));    

    // sharp turn
    GUIDE_LOG_INFO("Populate sharp_subset...");
    Load(sharp_subset, narrative_pt.get_child(kSharpKey));

    LOG_TRACE("Populate sharp_verbal_subset...");
    Load(sharp_verbal_subset, narrative_pt.get_child(kSharpVerbalKey));

    // uturn
    GUIDE_LOG_INFO("Populate uturn_subset...");
    Load(uturn_subset, narrative_pt.get_child(kUturnKey));

    GUIDE_LOG_INFO("Populate uturn_verbal_subset...");
    Load(uturn_verbal_subset, narrative_pt.get_child(kUturnVerbalKey));

    // ramp straight
    GUIDE_LOG_INFO("Populate ramp_straight_subset...");
    Load(ramp_straight_subset, narrative_pt.get_child(kRampStraightKey));

    GUIDE_LOG_INFO("Populate ramp_straight_verbal_subset...");
    Load(ramp_straight_verbal_subset, narrative_pt.get_child(kRampStraightVerbalKey));

    // ramp (left or right)
    GUIDE_LOG_INFO("Populate ramp_subset...");
    Load(ramp_subset, narrative_pt.get_child(kRampKey));

    GUIDE_LOG_INFO("Populate ramp_verbal_subset...");
    Load(ramp_verbal_subset, narrative_pt.get_child(kRampVerbalKey));

    // exit
    GUIDE_LOG_INFO("Populate exit_subset...");
    Load(exit_subset, narrative_pt.get_child(kExitKey));

    GUIDE_LOG_INFO("Populate exit_verbal_subset...");
    Load(exit_verbal_subset, narrative_pt.get_child(kExitVerbalKey));

    GUIDE_LOG_INFO("Populate exit_visual_subset...");
    Load(exit_visual_subset, narrative_pt.get_child(kExitVisualKey));

    // keep 
    GUIDE_LOG_INFO("Populate keep_subset...");
    Load(keep_subset, narrative_pt.get_child(kKeepKey));

    GUIDE_LOG_INFO("Populate keep_verbal_subset...");
    Load(keep_verbal_subset, narrative_pt.get_child(kKeepVerbalKey));

    // stay on
    GUIDE_LOG_INFO("Populate keep_to_stay_on_subset...");
    Load(keep_to_stay_on_subset, narrative_pt.get_child(kKeepToStayOnKey));

    GUIDE_LOG_INFO("Populate keep_to_stay_on_verbal_subset...");
    Load(keep_to_stay_on_verbal_subset, narrative_pt.get_child(kKeepToStayOnVerbalKey));

    // merge
    GUIDE_LOG_INFO("Populate merge_subset...");
    Load(merge_subset, narrative_pt.get_child(kMergeKey));

    GUIDE_LOG_INFO("Populate merge_verbal_subset...");
    Load(merge_verbal_subset, narrative_pt.get_child(kMergeVerbalKey));

    // enter roundabout
    GUIDE_LOG_INFO("Populate enter_roundabout_subset...");
    Load(enter_roundabout_subset, narrative_pt.get_child(kEnterRoundaboutKey));

    GUIDE_LOG_INFO("Populate enter_roundabout_verbal_subset...");
    Load(enter_roundabout_verbal_subset, narrative_pt.get_child(kEnterRoundaboutVerbalKey));

    // exit roundabout
    GUIDE_LOG_INFO("Populate exit_roundabout_subset...");
    Load(exit_roundabout_subset, narrative_pt.get_child(kExitRoundaboutKey));

    GUIDE_LOG_INFO("Populate exit_roundabout_verbal_subset...");
    Load(exit_roundabout_verbal_subset, narrative_pt.get_child(kExitRoundaboutVerbalKey));

    // Populate post_transition_verbal_subset
    GUIDE_LOG_INFO("Populate post_transition_verbal_subset...");
    Load(post_transition_verbal_subset, narrative_pt.get_child(kPostTransitionVerbalKey));

    // multi cue
    GUIDE_LOG_INFO("Populate verbal_multi_cue_subset...");
    Load(verbal_multi_cue_subset, narrative_pt.get_child(kVerbalMultiCueKey));

    // alert
    GUIDE_LOG_INFO("Populate approach_verbal_alert_subset...");
    Load(approach_verbal_alert_subset, narrative_pt.get_child(kApproachVerbalAlertKey));
}

void NarrativeDictionary::Load(PhraseSet& phrase_handle, const BoostPTree& phrase_pt) {
    phrase_handle.phrases = as_unordered_map<std::string, std::string>(phrase_pt, kPhrasesKey);
}

void NarrativeDictionary::Load(StartSubset& start_handle, const BoostPTree& start_subset_pt) {
    // Populate phrases
    Load(static_cast<PhraseSet&>(start_handle), start_subset_pt);

    // Populate cardinal_directions
    start_handle.cardinal_directions = as_vector<std::string>(start_subset_pt, kCardinalDirectionsKey);

    // Populate empty_street_name_labels
    // start_handle.empty_street_name_labels =
    // as_vector<std::string>(start_subset_pt, kEmptyStreetNameLabelsKey);
}

void NarrativeDictionary::Load(StartVerbalSubset& start_verbal_handle, const BoostPTree& start_verbal_subset_pt) {

  // Populate start_subset items
  Load(static_cast<StartSubset&>(start_verbal_handle), start_verbal_subset_pt);

  // Populate metric_lengths
  start_verbal_handle.metric_lengths =
      as_vector<std::string>(start_verbal_subset_pt, kMetricLengthsKey);

  // Populate us_customary_lengths
  start_verbal_handle.us_customary_lengths =
      as_vector<std::string>(start_verbal_subset_pt, kUsCustomaryLengthsKey);
}

void NarrativeDictionary::Load(DestinationSubset& destination_handle, const BoostPTree& dst_subset_pt) {
    // Populate phrases
    Load(static_cast<PhraseSet&>(destination_handle), dst_subset_pt);

    // Populate relative_directions
    destination_handle.relative_directions = as_vector<std::string>(dst_subset_pt, kRelativeDirectionsKey);
}

void NarrativeDictionary::Load(ContinueSubset& continue_handle, 
                               const boost::property_tree::ptree& continue_subset_pt) {

    // Populate phrases
    Load(static_cast<PhraseSet&>(continue_handle), continue_subset_pt);

    // Populate empty_street_name_labels
    continue_handle.empty_street_name_labels =
        as_vector<std::string>(continue_subset_pt, kEmptyStreetNameLabelsKey);

}

void NarrativeDictionary::Load(ContinueVerbalSubset& continue_verbal_handle, 
                               const boost::property_tree::ptree& continue_verbal_subset_pt) {
    
    // Populate continue_subset items
    Load(static_cast<ContinueSubset&>(continue_verbal_handle), continue_verbal_subset_pt);

    // Populate metric_lengths
    continue_verbal_handle.metric_lengths =
        as_vector<std::string>(continue_verbal_subset_pt, kMetricLengthsKey);

    // Populate us_customary_lengths
    continue_verbal_handle.us_customary_lengths =
        as_vector<std::string>(continue_verbal_subset_pt, kUsCustomaryLengthsKey);
}

void NarrativeDictionary::Load(TurnSubset& turn_handle, const boost::property_tree::ptree& turn_subset_pt) {
    // Populate phrases
    Load(static_cast<PhraseSet&>(turn_handle), turn_subset_pt);

    // Populate relative_directions
    turn_handle.relative_directions = as_vector<std::string>(turn_subset_pt, kRelativeDirectionsKey);

    // Populate empty_street_name_labels
    turn_handle.empty_street_name_labels =
        as_vector<std::string>(turn_subset_pt, kEmptyStreetNameLabelsKey);
}

void NarrativeDictionary::Load(RampSubset& ramp_handle, const boost::property_tree::ptree& ramp_subset_pt) {
    // Populate phrases
    Load(static_cast<PhraseSet&>(ramp_handle), ramp_subset_pt);

    // Populate relative_directions
    ramp_handle.relative_directions = as_vector<std::string>(ramp_subset_pt, kRelativeDirectionsKey);
}

void NarrativeDictionary::Load(KeepSubset& keep_handle, const boost::property_tree::ptree& keep_subset_pt) {
    // Populate ramp_subset items
    Load(static_cast<RampSubset&>(keep_handle), keep_subset_pt);

    // Populate empty_street_name_labels
    keep_handle.empty_street_name_labels =
        as_vector<std::string>(keep_subset_pt, kEmptyStreetNameLabelsKey);
}

void NarrativeDictionary::Load(EnterRoundaboutSubset& enter_roundabout_handle,
                               const boost::property_tree::ptree& enter_roundabout_subset_pt) {

    // Populate phrases
    Load(static_cast<PhraseSet&>(enter_roundabout_handle), enter_roundabout_subset_pt);

    // Populate ordinal_values
    enter_roundabout_handle.ordinal_values =
        as_vector<std::string>(enter_roundabout_subset_pt, kOrdinalValuesKey);

    // Populate empty_street_name_labels
    enter_roundabout_handle.empty_street_name_labels =
        as_vector<std::string>(enter_roundabout_subset_pt, kEmptyStreetNameLabelsKey);
}

void NarrativeDictionary::Load(PostTransitionVerbalSubset& post_transition_verbal_handle,
                               const boost::property_tree::ptree& post_transition_verbal_subset_pt) {
    // Populate phrases
    Load(static_cast<PhraseSet&>(post_transition_verbal_handle), post_transition_verbal_subset_pt);

    // Populate metric_lengths
    post_transition_verbal_handle.metric_lengths =
        as_vector<std::string>(post_transition_verbal_subset_pt, kMetricLengthsKey);

    // Populate us_customary_lengths
    post_transition_verbal_handle.us_customary_lengths =
        as_vector<std::string>(post_transition_verbal_subset_pt, kUsCustomaryLengthsKey);

    // Populate empty_street_name_labels
    post_transition_verbal_handle.empty_street_name_labels =
        as_vector<std::string>(post_transition_verbal_subset_pt, kEmptyStreetNameLabelsKey);
}

void NarrativeDictionary::Load(VerbalMultiCueSubset& verbal_multi_cue_handle,
                               const boost::property_tree::ptree& verbal_multi_cue_subset_pt) {

    // Populate phrases
    Load(static_cast<PhraseSet&>(verbal_multi_cue_handle), verbal_multi_cue_subset_pt);

    // Populate metric_lengths
    verbal_multi_cue_handle.metric_lengths =
        as_vector<std::string>(verbal_multi_cue_subset_pt, kMetricLengthsKey);

    // Populate us_customary_lengths
    verbal_multi_cue_handle.us_customary_lengths =
        as_vector<std::string>(verbal_multi_cue_subset_pt, kUsCustomaryLengthsKey);
}

void NarrativeDictionary::Load(ApproachVerbalAlertSubset& approach_verbal_alert_handle,
                               const boost::property_tree::ptree& approach_verbal_alert_subset_pt) {

    // Populate phrases
    Load(static_cast<PhraseSet&>(approach_verbal_alert_handle), approach_verbal_alert_subset_pt);

    // Populate metric_lengths
    approach_verbal_alert_handle.metric_lengths =
        as_vector<std::string>(approach_verbal_alert_subset_pt, kMetricLengthsKey);

    // Populate us_customary_lengths
    approach_verbal_alert_handle.us_customary_lengths =
        as_vector<std::string>(approach_verbal_alert_subset_pt, kUsCustomaryLengthsKey);
}

const std::locale& NarrativeDictionary::GetLocale() const {
    return locale_;
}
    
const std::string& NarrativeDictionary::GetLanguageTag() const {
    return language_tag_;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
