#ifndef MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_NARRATIVE_BUILDER_H
#define MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_NARRATIVE_BUILDER_H

#include <string>
#include <list>
#include "guidance/src/common/maneuver.h"
#include "guidance/src/data/enhance_path_result.h"
#include "guidance/src/data/path_data_manager.h"
#include "guidance/src/maker/narrative/narrative_dictionary.h"
#include "guidance/src/maker/narrative/length_builder.h"
#include "guidance/src/maker/narrative/multi_cue_builder.h"

namespace aurora {
namespace guide {

const bool kLimitByConseuctiveCount = true;
constexpr uint32_t kElementMaxCount = 4;
constexpr uint32_t kVerbalAlertElementMaxCount = 1;
constexpr uint32_t kVerbalPreElementMaxCount = 2;
constexpr uint32_t kVerbalPostElementMaxCount = 2;
const std::string kVerbalDelim = ", ";

class NarrativeBuilder {
public:
    friend class MultiCueBuilder;

    NarrativeBuilder(GuidanceOptions *option, const std::string &json_file, PathDataManagerPtr path_manager);
    virtual ~NarrativeBuilder() = default;

    int32_t Build(std::list<Maneuver> &maneuvers);

protected:
    bool IsArticulatedPrepositionEnabled() const { return articulated_preposition_enabled_;}

    std::string FormStartInstruction(Maneuver& maneuver);

    std::string FormDestinationInstruction(Maneuver& maneuver);
    std::string FormVerbalAlertDestinationInstruction(Maneuver& maneuver);
    std::string FormVerbalDestinationInstruction(Maneuver& maneuver);
    
    std::string FormTurnInstruction(Maneuver& maneuver,
                                  bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                  uint32_t element_max_count = kElementMaxCount);

    std::string FormVerbalAlertTurnInstruction(Maneuver& maneuver,
                                               bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                               uint32_t element_max_count = kVerbalAlertElementMaxCount,
                                               const std::string& delim = kVerbalDelim);

    std::string FormVerbalTurnInstruction(Maneuver& maneuver,
                                          bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                          uint32_t element_max_count = kVerbalPreElementMaxCount,
                                          const std::string& delim = kVerbalDelim);

    std::string FormVerbalSuccinctTurnTransitionInstruction(Maneuver& maneuver,
                                                            bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                                            uint32_t element_max_count = kVerbalPreElementMaxCount,
                                                            const std::string& delim = kVerbalDelim);

    std::string FormRelativeTwoDirection(ManeuverType type,
                                         const std::vector<std::string>& relative_directions);

    std::string FormRelativeThreeDirection(ManeuverType type,
                                           const std::vector<std::string>& relative_directions);

    std::string FormUturnInstruction(Maneuver& maneuver,
                                     bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                     uint32_t element_max_count = kElementMaxCount);

    std::string FormVerbalSuccinctUturnTransitionInstruction(Maneuver& maneuver,
                                                             bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                                             uint32_t element_max_count = kVerbalPreElementMaxCount,
                                                             const std::string& delim = kVerbalDelim);

    std::string FormVerbalAlertUturnInstruction(Maneuver& maneuver,
                                                bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                                uint32_t element_max_count = kVerbalAlertElementMaxCount,
                                                const std::string& delim = kVerbalDelim);

    std::string FormVerbalUturnInstruction(Maneuver& maneuver,
                                         bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                         uint32_t element_max_count = kVerbalPreElementMaxCount,
                                         const std::string& delim = kVerbalDelim);

    std::string FormVerbalUturnInstruction(uint8_t phrase_id,
                                           const std::string& relative_dir,
                                           const std::string& street_names,
                                           const std::string& cross_street_names,
                                           const std::string& junction_name,
                                           const std::string& guide_sign);

    std::string FormRampStraightInstruction(Maneuver& maneuver,
                                          bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                          uint32_t element_max_count = kElementMaxCount);

    std::string FormVerbalAlertRampStraightInstruction(Maneuver& maneuver,
                                                       bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                                       uint32_t element_max_count = kVerbalAlertElementMaxCount,
                                                       const std::string& delim = kVerbalDelim);

    std::string FormVerbalRampStraightInstruction(Maneuver& maneuver,
                                                 bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                                 uint32_t element_max_count = kVerbalPreElementMaxCount,
                                                 const std::string& delim = kVerbalDelim);

    std::string FormVerbalRampStraightInstruction(uint8_t phrase_id,
                                                  const std::string& exit_branch_sign,
                                                  const std::string& exit_toward_sign,
                                                  const std::string& exit_name_sign);


    std::string FormRampInstruction(Maneuver& maneuver,
                                    bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                    uint32_t element_max_count = kElementMaxCount);
    
    std::string FormVerbalAlertRampInstruction(Maneuver& maneuver,
                                               bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                               uint32_t element_max_count = kVerbalAlertElementMaxCount,
                                               const std::string& delim = kVerbalDelim);

    std::string FormVerbalRampInstruction(Maneuver& maneuver,
                                          bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                          uint32_t element_max_count = kVerbalPreElementMaxCount,
                                          const std::string& delim = kVerbalDelim);

    std::string FormVerbalRampInstruction(uint8_t phrase_id,
                                          const std::string& relative_dir,
                                          const std::string& exit_branch_sign,
                                          const std::string& exit_toward_sign,
                                          const std::string& exit_name_sign);

    std::string FormExitInstruction(Maneuver& maneuver,
                                    bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                    uint32_t element_max_count = kElementMaxCount);

    std::string
    FormVerbalAlertExitInstruction(Maneuver& maneuver,
                                   bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                   uint32_t element_max_count = kVerbalAlertElementMaxCount,
                                   const std::string& delim = kVerbalDelim);

    std::string FormVerbalExitInstruction(Maneuver& maneuver,
                                         bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                         uint32_t element_max_count = kVerbalPreElementMaxCount,
                                         const std::string& delim = kVerbalDelim);

    std::string FormVerbalExitInstruction(uint8_t phrase_id,
                                         const std::string& relative_dir,
                                         const std::string& exit_number_sign,
                                         const std::string& exit_branch_sign,
                                         const std::string& exit_toward_sign,
                                         const std::string& exit_name_sign);

    std::string FormKeepToStayOnInstruction(Maneuver& maneuver,
                                            bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                            uint32_t element_max_count = kElementMaxCount);

    std::string FormKeepInstruction(Maneuver& maneuver,
                                    bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                    uint32_t element_max_count = kElementMaxCount);

    std::string
    FormVerbalAlertKeepInstruction(Maneuver& maneuver,
                                  bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                  uint32_t element_max_count = kVerbalAlertElementMaxCount,
                                  const std::string& delim = kVerbalDelim);

    std::string FormVerbalKeepInstruction(Maneuver& maneuver,
                                        bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                        uint32_t element_max_count = kVerbalPreElementMaxCount,
                                        const std::string& delim = kVerbalDelim);

    std::string FormVerbalKeepInstruction(uint8_t phrase_id,
                                          const std::string& relative_dir,
                                          const std::string& street_name,
                                          const std::string& exit_number_sign,
                                          const std::string& toward_sign);

    std::string
    FormVerbalAlertKeepToStayOnInstruction(Maneuver& maneuver,
                                           uint32_t element_max_count = kVerbalAlertElementMaxCount,
                                           const std::string& delim = kVerbalDelim);

    std::string
    FormVerbalKeepToStayOnInstruction(Maneuver& maneuver,
                                      bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                      uint32_t element_max_count = kVerbalPreElementMaxCount,
                                      const std::string& delim = kVerbalDelim);

    std::string FormVerbalKeepToStayOnInstruction(uint8_t phrase_id,
                                                  const std::string& relative_dir,
                                                  const std::string& street_name,
                                                  const std::string& exit_number_sign = "",
                                                  const std::string& toward_sign = "");

    std::string FormMergeInstruction(Maneuver& maneuver,
                                    bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                    uint32_t element_max_count = kElementMaxCount);

    std::string
    FormVerbalAlertMergeInstruction(Maneuver& maneuver,
                                    bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                    uint32_t element_max_count = kVerbalAlertElementMaxCount,
                                    const std::string& delim = kVerbalDelim);

    std::string FormVerbalMergeInstruction(Maneuver& maneuver,
                                          bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                          uint32_t element_max_count = kVerbalPreElementMaxCount,
                                          const std::string& delim = kVerbalDelim);

    std::string FormVerbalSuccinctMergeTransitionInstruction(Maneuver& maneuver,
                                                            bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                                            uint32_t element_max_count = kVerbalPreElementMaxCount,
                                                            const std::string& delim = kVerbalDelim);

    std::string FormContinueInstruction(Maneuver& maneuver,
                                        bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                        uint32_t element_max_count = kElementMaxCount);

    std::string
    FormVerbalAlertContinueInstruction(Maneuver& maneuver,
                                       bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                       uint32_t element_max_count = kVerbalAlertElementMaxCount,
                                       const std::string& delim = kVerbalDelim);

    std::string
    FormVerbalContinueInstruction(Maneuver& maneuver,
                                bool limit_by_consecutive_count = kLimitByConseuctiveCount,
                                uint32_t element_max_count = kVerbalPreElementMaxCount,
                                const std::string& delim = kVerbalDelim);
                
    /**
     * Returns the street names string for the specified street name list.
     * The format is controlled via the optional parameters.
     *
     * @param street_names The list of street names to process.
     * @param max_count The maximum number of street names to process.
     *                  The default value is zero - meaning no limit.
     *                  This parameter is optional.
     * @param delim The specified delimiter to use between each street name.
     *              The default delimiter is a slash.
     *              This parameter is optional.
     * @param verbal_formatter A pointer to a verbal text formatter that prepares
     *                         strings for use with a text-to-speech engine.
     *                         The default is a nullptr.
     *                         This parameter is optional.
     *
     * @return the street names string for the specified street name list.
     */
    std::string FormStreetNames(const std::shared_ptr<StreetNames>& street_names,
                                uint32_t max_count = 0,
                                const std::string& delim = "/",
                                const VerbalTextFormatter* verbal_formatter = nullptr);

    std::string FormVerbalSuccinctStartTransitionInstruction(Maneuver& maneuver);
  
    std::string FormVerbalStartInstruction(Maneuver& maneuver,
                                           uint32_t element_max_count = kVerbalPreElementMaxCount,
                                           const std::string& delim = kVerbalDelim);

    std::string
    FormVerbalPostTransitionInstruction(Maneuver& maneuver,
                                        bool include_street_names = false,
                                        uint32_t element_max_count = kVerbalPostElementMaxCount,
                                        const std::string& delim = kVerbalDelim);



    /**
     * If begin_street_names exist, assign begin_street_names to street_names and clear the
     * begin_street_names.
     *
     * @param maneuver The current maneuver to process.
     * @param begin_street_names The begin street names string.
     * @param street_names The street names string.
     */
    void UpdateObviousManeuverStreetNames(const Maneuver& maneuver,
                                            std::string& begin_street_names,
                                            std::string& street_names);

    /**
     * Combines a simple preposition and a definite article for certain languages.
     */
    // 系统会对导航指令中的介词进行特殊处理，使其更符合特定语言的语法规则。
    // 例如，在意大利语中，介词 "a"（到）和定冠词 "la"（这个，阴性）会合并为 "alla"，而不是简单地并列为 "a la"。
    // 这种处理使得生成的导航指令更加自然，符合母语使用者的语言习惯。
    virtual void FormArticulatedPrepositions(std::string& instruction) {}

protected:
    bool articulated_preposition_enabled_;
    EnhancePathResultPtr enhance_path_;
    NarrativeDictionary dictionary_;
    LengthBuilder       length_builder_;
    PathDataManagerPtr  path_data_manager_;
}; // class NarrativeBuilder

class NarrativeBuilder_csCZ : public NarrativeBuilder {
public:
    NarrativeBuilder_csCZ();
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_NARRATIVE_BUILDER_H
/* EOF */
