#ifndef MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_NARRATIVE_DICTIONARY_H
#define MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_NARRATIVE_DICTIONARY_H

#include <string>
#include <unordered_map>
#include "boost/property_tree/ptree.hpp"

namespace {

// Subset keys
constexpr auto kStartKey = "instructions.start";
constexpr auto kStartVerbalKey = "instructions.start_verbal";
constexpr auto kDestinationKey = "instructions.destination";
constexpr auto kDestinationVerbalAlertKey = "instructions.destination_verbal_alert";
constexpr auto kDestinationVerbalKey = "instructions.destination_verbal";
constexpr auto kBecomesKey = "instructions.becomes";
constexpr auto kBecomesVerbalKey = "instructions.becomes_verbal";

// 生成口头"继续"指令，告诉用户继续沿当前道路行驶一段距离。这种指令通常在用户需要在同一条道路上行驶
// 较长距离时使用，或者在转弯之后确认用户已经成功完成转弯。
constexpr auto kContinueKey = "instructions.continue";
constexpr auto kContinueVerbalAlertKey = "instructions.continue_verbal_alert";
constexpr auto kContinueVerbalKey = "instructions.continue_verbal";

// bear指令在导航中用于描述角度较小的转向动作，区别于急转弯（turn）。
// 这种指令通常用于高速公路分叉、轻微的道路弯曲或保持在主要道路上的场景。
constexpr auto kBearKey = "instructions.bear";
constexpr auto kBearVerbalKey = "instructions.bear_verbal";
constexpr auto kTurnKey = "instructions.turn";
constexpr auto kTurnVerbalKey = "instructions.turn_verbal";
constexpr auto kSharpKey = "instructions.sharp";
constexpr auto kSharpVerbalKey = "instructions.sharp_verbal";
constexpr auto kUturnKey = "instructions.uturn";
constexpr auto kUturnVerbalKey = "instructions.uturn_verbal";
constexpr auto kRampStraightKey = "instructions.ramp_straight";
constexpr auto kRampStraightVerbalKey = "instructions.ramp_straight_verbal";
constexpr auto kRampKey = "instructions.ramp";
constexpr auto kRampVerbalKey = "instructions.ramp_verbal";
constexpr auto kExitKey = "instructions.exit";
constexpr auto kExitVerbalKey = "instructions.exit_verbal";
constexpr auto kExitVisualKey = "instructions.exit_visual";

// 用于生成"保持"指令，告诉用户在道路分叉或多车道道路上保持在特定的方向或车道上。
// 这种指令通常在高速公路出口、匝道或复杂的交叉路口使用，帮助用户选择正确的行驶路径。
constexpr auto kKeepKey = "instructions.keep";
constexpr auto kKeepVerbalKey = "instructions.keep_verbal";
constexpr auto kKeepToStayOnKey = "instructions.keep_to_stay_on";
constexpr auto kKeepToStayOnVerbalKey = "instructions.keep_to_stay_on_verbal";
constexpr auto kMergeKey = "instructions.merge";
constexpr auto kMergeVerbalKey = "instructions.merge_verbal";
constexpr auto kEnterRoundaboutKey = "instructions.enter_roundabout";
constexpr auto kEnterRoundaboutVerbalKey = "instructions.enter_roundabout_verbal";
constexpr auto kExitRoundaboutKey = "instructions.exit_roundabout";
constexpr auto kExitRoundaboutVerbalKey = "instructions.exit_roundabout_verbal";

constexpr auto kVerbalMultiCueKey = "instructions.verbal_multi_cue";
constexpr auto kApproachVerbalAlertKey = "instructions.approach_verbal_alert";
constexpr auto kPostTransitionVerbalKey = "instructions.post_transition_verbal";

constexpr auto kPosixLocaleKey = "posix_locale";

// Variable keys
constexpr auto kPhrasesKey = "phrases";
constexpr auto kCardinalDirectionsKey = "cardinal_directions"; // 基本方向，是指地理上的基本方向
constexpr auto kRelativeDirectionsKey = "relative_directions"; 
constexpr auto kOrdinalValuesKey = "ordinal_values"; // 是一个字符串数组，用于表示序数词（如"第一"、"第二"、"第三"等），主要用于导航指令中描述环岛出口的顺序。
constexpr auto kEmptyStreetNameLabelsKey = "empty_street_name_labels";
constexpr auto kMetricLengthsKey = "metric_lengths"; // 公制单位：meter
constexpr auto kUsCustomaryLengthsKey = "us_customary_lengths"; // 美制：英里
constexpr auto kFerryLabelKey = "ferry_label";
constexpr auto kStationLabelKey = "station_label";
constexpr auto kEmptyTransitNameLabelsKey = "empty_transit_name_labels";
constexpr auto kTransitStopCountLabelsKey = "transit_stop_count_labels";
constexpr auto kObjectLabelsKey = "object_labels";

constexpr auto kPluralCategoryZeroKey = "zero";
constexpr auto kPluralCategoryOneKey = "one";
constexpr auto kPluralCategoryTwoKey = "two";
constexpr auto kPluralCategoryFewKey = "few";
constexpr auto kPluralCategoryManyKey = "many";
constexpr auto kPluralCategoryOtherKey = "other";

// Empty street names label indexes
constexpr auto kWalkwayIndex = 0;
constexpr auto kCyclewayIndex = 1;
constexpr auto kMountainBikeTrailIndex = 2;
constexpr auto kPedestrianCrossingIndex = 3;
constexpr auto kStepsIndex = 4;
constexpr auto kBridgeIndex = 5;
constexpr auto kTunnelIndex = 6;

// object label indexes
constexpr auto kGateIndex = 0;
constexpr auto kBollardIndex = 1;
constexpr auto kStreetIntersectionIndex = 2;

// Metric length indexes
constexpr auto kKilometersIndex = 0;
constexpr auto kOneKilometerIndex = 1;
constexpr auto kMetersIndex = 2;
constexpr auto kSmallMetersIndex = 3;

// US Customary length indexes
constexpr auto kMilesIndex = 0;
constexpr auto kOneMileIndex = 1;
constexpr auto kHalfMileIndex = 2;
constexpr auto kQuarterMileIndex = 3;
constexpr auto kFeetIndex = 4;
constexpr auto kSmallFeetIndex = 5;

// Phrase tags
constexpr auto kCardinalDirectionTag = "<CARDINAL_DIRECTION>";
constexpr auto kRelativeDirectionTag = "<RELATIVE_DIRECTION>";
constexpr auto kOrdinalValueTag = "<ORDINAL_VALUE>";
constexpr auto kStreetNamesTag = "<STREET_NAMES>";
constexpr auto kPreviousStreetNamesTag = "<PREVIOUS_STREET_NAMES>";
constexpr auto kBeginStreetNamesTag = "<BEGIN_STREET_NAMES>";
constexpr auto kCrossStreetNamesTag = "<CROSS_STREET_NAMES>";

// 表示从环岛出口后将要行驶的道路名称。这是用户离开环岛后将要继续行驶的道路。
constexpr auto kRoundaboutExitStreetNamesTag = "<ROUNDABOUT_EXIT_STREET_NAMES>";

// 表示环岛出口处的道路名称，特别是当这个名称与用户将继续行驶的道路名称不同时。
// 这通常是环岛出口与主要道路连接的过渡部分的名称。
constexpr auto kRoundaboutExitBeginStreetNamesTag = "<ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>";

constexpr auto kRampExitNumbersVisualTag = "<EXIT_NUMBERS>";
constexpr auto kObjectLabelTag = "<OBJECT_LABEL>";
constexpr auto kLengthTag = "<LENGTH>";
constexpr auto kDestinationTag = "<DESTINATION>";

// 多语音提示，指当前即将执行的导航操作的语音指令。
// 这通常是用户需要立即执行的操作，例如"向右转"、"靠左行驶"等。
constexpr auto kCurrentVerbalCueTag = "<CURRENT_VERBAL_CUE>";

// 多语音提示,指在当前操作之后不久将要执行的下一个导航操作的语音指令。
// 这是用户在完成当前操作后需要准备的下一个操作。
constexpr auto kNextVerbalCueTag = "<NEXT_VERBAL_CUE>";

constexpr auto kKilometersTag = "<KILOMETERS>";
constexpr auto kMetersTag = "<METERS>";
constexpr auto kMilesTag = "<MILES>";

// 表示"英里的十分之一"或"十分之一英里"。它在生成导航指令时使用，
// 特别是在需要以美制单位（英里）告知用户较短距离信息时。
constexpr auto kTenthsOfMilesTag = "<TENTHS_OF_MILE>";
constexpr auto kFeetTag = "<FEET>";
constexpr auto kNumberSignTag = "<NUMBER_SIGN>";
constexpr auto kBranchSignTag = "<BRANCH_SIGN>";
constexpr auto kTowardSignTag = "<TOWARD_SIGN>";
constexpr auto kNameSignTag = "<NAME_SIGN>";
constexpr auto kJunctionNameTag = "<JUNCTION_NAME>";
constexpr auto kFerryLabelTag = "<FERRY_LABEL>";
constexpr auto kTransitPlatformTag = "<TRANSIT_STOP>";
constexpr auto kStationLabelTag = "<STATION_LABEL>";
constexpr auto kTimeTag = "<TIME>";
constexpr auto kTransitNameTag = "<TRANSIT_NAME>";
constexpr auto kTransitHeadSignTag = "<TRANSIT_HEADSIGN>";
constexpr auto kTransitPlatformCountTag = "<TRANSIT_STOP_COUNT>";
constexpr auto kTransitPlatformCountLabelTag = "<TRANSIT_STOP_COUNT_LABEL>";
constexpr auto kLevelTag = "<LEVEL>";


// Text instruction initial capacity
constexpr auto kInstructionInitialCapacity = 128;

// Text length initial capacity
constexpr auto kLengthStringInitialCapacity = 32;

// Basic time threshold in seconds for creating a verbal multi-cue
constexpr auto kVerbalMultiCueTimeThreshold = 13;
constexpr auto kVerbalMultiCueTimeStartManeuverThreshold = kVerbalMultiCueTimeThreshold * 3;

constexpr float kVerbalPostMinimumRampLength = 2.0f; // Kilometers
constexpr float kVerbalAlertMergePriorManeuverMinimumLength = kVerbalPostMinimumRampLength;

// Lower and upper bounds for roundabout_exit_count
constexpr uint32_t kRoundaboutExitCountLowerBound = 1;
constexpr uint32_t kRoundaboutExitCountUpperBound = 10;
}

namespace aurora {
namespace guide {

struct PhraseSet {
    std::unordered_map<std::string, std::string> phrases;
};

struct StartSubset : PhraseSet {
    std::vector<std::string> cardinal_directions;
};

struct StartVerbalSubset : StartSubset {
    std::vector<std::string> metric_lengths;
    std::vector<std::string> us_customary_lengths;
};

struct DestinationSubset : PhraseSet {
    std::vector<std::string> relative_directions;
};

struct ContinueSubset : PhraseSet {
    std::vector<std::string> empty_street_name_labels;
};

struct ContinueVerbalSubset : ContinueSubset {
    std::vector<std::string> metric_lengths;
    std::vector<std::string> us_customary_lengths;
};

struct TurnSubset : PhraseSet {
    std::vector<std::string> relative_directions;
    std::vector<std::string> empty_street_name_labels;
};

struct RampSubset : PhraseSet {
    std::vector<std::string> relative_directions;
};

struct KeepSubset : RampSubset {
    std::vector<std::string> empty_street_name_labels;
};

struct EnterFerrySubset : PhraseSet {
    std::vector<std::string> empty_street_name_labels;
    std::string ferry_label;
};

struct EnterRoundaboutSubset : PhraseSet {
  std::vector<std::string> ordinal_values;
  std::vector<std::string> empty_street_name_labels;
};

struct VerbalMultiCueSubset : PhraseSet {
  std::vector<std::string> metric_lengths;
  std::vector<std::string> us_customary_lengths;
};

struct ApproachVerbalAlertSubset : PhraseSet {
  std::vector<std::string> metric_lengths;
  std::vector<std::string> us_customary_lengths;
};

struct PostTransitionVerbalSubset : PhraseSet {
  std::vector<std::string> metric_lengths;
  std::vector<std::string> us_customary_lengths;
  std::vector<std::string> empty_street_name_labels;
};

using BoostPTree = boost::property_tree::ptree;
class NarrativeDictionary {
public:
    NarrativeDictionary(const std::string& language_tag, const std::string &json_file);
    
    const std::locale& GetLocale() const;
    const std::string& GetLanguageTag() const;

    // Start
    StartSubset start_subset;
    StartVerbalSubset start_verbal_subset;

    // Destination
    DestinationSubset destination_subset;
    DestinationSubset destination_verbal_alert_subset;
    DestinationSubset destination_verbal_subset;

    // Becomes<road name>
    PhraseSet becomes_subset;
    PhraseSet becomes_verbal_subset;

    // Continue
    ContinueSubset continue_subset;
    ContinueSubset continue_verbal_alert_subset;
    ContinueVerbalSubset continue_verbal_subset;

    // Bear
    // 通常用于高速公路分叉、轻微的道路弯曲或保持在主要道路上的场景。
    TurnSubset bear_subset;
    TurnSubset bear_verbal_subset;

    // Turn
    TurnSubset turn_subset;
    TurnSubset turn_verbal_subset;

    // Sharp
    TurnSubset sharp_subset;
    TurnSubset sharp_verbal_subset;

    // Uturn
    TurnSubset uturn_subset;
    TurnSubset uturn_verbal_subset;

    // RampStraight
    PhraseSet ramp_straight_subset;
    PhraseSet ramp_straight_verbal_subset;

    // Ramp
    RampSubset ramp_subset;
    RampSubset ramp_verbal_subset;

    // Exit
    RampSubset exit_subset;
    RampSubset exit_verbal_subset;
    PhraseSet exit_visual_subset;

    // Keep
    KeepSubset keep_subset;
    KeepSubset keep_verbal_subset;

    // KeepToStayOn
    KeepSubset keep_to_stay_on_subset;
    KeepSubset keep_to_stay_on_verbal_subset;

    // Merge
    TurnSubset merge_subset;
    TurnSubset merge_verbal_subset;

    // EnterRoundabout
    EnterRoundaboutSubset enter_roundabout_subset;
    EnterRoundaboutSubset enter_roundabout_verbal_subset;

    // ExitRoundabout
    ContinueSubset exit_roundabout_subset;
    ContinueSubset exit_roundabout_verbal_subset;

    // Post transition verbal
    PostTransitionVerbalSubset post_transition_verbal_subset;

    // Verbal multi-cue
    VerbalMultiCueSubset verbal_multi_cue_subset;

    // Approach verbal alert
    ApproachVerbalAlertSubset approach_verbal_alert_subset;

protected:
    // common
    void Load(const boost::property_tree::ptree& narrative_pt);
    void Load(PhraseSet& phrase_handle, const BoostPTree& phrase_pt);
    
    // start
    void Load(StartSubset& start_handle, const BoostPTree& start_subset_pt);
    void Load(StartVerbalSubset& start_verbal_handle, const BoostPTree& start_verbal_subset_pt);
    
    // destination
    void Load(DestinationSubset& destination_handle, const BoostPTree& destination_subset_pt);

    // continue
    void Load(ContinueSubset& continue_handle, const boost::property_tree::ptree& continue_subset_pt);
    void Load(ContinueVerbalSubset& continue_verbal_handle, 
              const boost::property_tree::ptree& continue_verbal_subset_pt);
    
    // turn
    void Load(TurnSubset& turn_handle, const boost::property_tree::ptree& turn_subset_pt);

    // ramp left or right
    void Load(RampSubset& ramp_handle, const boost::property_tree::ptree& ramp_subset_pt);
    
    // keep
    void Load(KeepSubset& keep_handle, const boost::property_tree::ptree& keep_subset_pt);
    
    
    void Load(EnterRoundaboutSubset& enter_roundabout_handle,
              const boost::property_tree::ptree& enter_roundabout_subset_pt);
    
    /**
     * Loads the specified 'post transition verbal' instruction subset with the
     * localized narrative instructions contained in the specified property tree.
     *
     * @param  post_transition_verbal_handle  The 'post transition verbal'
     *                                        structure to populate.
     * @param  post_transition_verbal_subset_pt  The 'post transition verbal'
     *                                           property tree.
     */
    void Load(PostTransitionVerbalSubset& post_transition_verbal_handle,
                const boost::property_tree::ptree& post_transition_verbal_subset_pt);

    // cue
    void Load(VerbalMultiCueSubset& verbal_multi_cue_handle,
              const boost::property_tree::ptree& verbal_multi_cue_subset_pt);
    
    // alert
    void Load(ApproachVerbalAlertSubset& approach_verbal_alert_handle,
              const boost::property_tree::ptree& approach_verbal_alert_subset_pt);

private:
    // Locale
    std::locale locale_;

    // Language tag
    std::string language_tag_;

    std::string posix_locale;

};

}  // namespace guide
}  // namespace aurora

#endif  // MAP_SRC_GUIDE_SRC_MAKER_NARRATIVE_NARRATIVE_DICTIONARY_H
/* EOF */
