#include "guidance/src/maker/narrative/length_builder.h"
#include "guidance/src/common/guide_log.h"
#include "boost/algorithm/string/replace.hpp"
#include "guidance/src/maker/narrative/narrative_dictionary.h"
#include "constants.h"

namespace aurora {
namespace guide {

LengthBuilder::LengthBuilder(DistanceUnits units, NarrativeDictionary *dictionary)
: units_(units) 
, dictionary_(dictionary) {

}

std::string LengthBuilder::FormLength(Maneuver& maneuver,
                                     const std::vector<std::string>& metric_lengths,
                                     const std::vector<std::string>& us_customary_lengths) {
    
    if (maneuver.IsRoundabout()) {
        GUIDE_ASSERT(false);
    }

    switch (units_) {
        case DistanceUnits::KDistanceMiles: {
            return FormUsCustomaryLength(maneuver.GetLengthMiles(), us_customary_lengths);
        }
    
        default: {
            return FormMetricLength(maneuver.GetLengthKilometers(), metric_lengths);
        }
    }

    return "";
}

std::string LengthBuilder::FormLength(float distance,
                                      const std::vector<std::string>& metric_lengths,
                                      const std::vector<std::string>& us_customary_lengths) {

    switch(units_) {
    case DistanceUnits::KDistanceMiles:
        return FormUsCustomaryLength(distance, us_customary_lengths);

    case DistanceUnits::kDistanceKilometers:
    default:
        return FormMetricLength(distance, metric_lengths);
    }
}

std::string LengthBuilder::FormMetricLength(float kilometers, 
                                            const std::vector<std::string>& metric_lengths) {
    // 0 "<KILOMETERS> kilometers"
    // 1 "1 kilometer"
    // 2 "<METERS> meters" (10-900 meters)
    // 3 "less than 10 meters"

    std::string length_string;
    length_string.reserve(kLengthStringInitialCapacity);

    std::stringstream distance;
    // Follow locale rules turning numbers into strings
    if (dictionary_ != nullptr) {
        distance.imbue(dictionary_->GetLocale());
    } else {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("dictionary is nullptr .");
    }

    float meters = std::round(kilometers * kMetersPerKm);
    float rounded = 0.f;

    // These will determine what we say
    // For distances that will round to 1km or greater
    if (meters > 949) {
        if (kilometers > 3) {
        // Round to integer for distances greater than 3km
        rounded = std::round(kilometers);
        } else {
        // Round to whole or half km for 1km to 3km distances
        rounded = std::round(kilometers * 2) / 2;
        }

        if (rounded == 1.f) {
        // "1 kilometer"
        length_string += metric_lengths.at(kOneKilometerIndex);
        } else {
        // "<KILOMETERS> kilometers"
        length_string += metric_lengths.at(kKilometersIndex);
        // 1 digit of precision for float and 0 for int
        distance << std::setiosflags(std::ios::fixed)
                << std::setprecision(rounded != static_cast<int>(rounded)) << rounded;
        }
    } else {
        if (meters > 94) {
        // "<METERS> meters" (100-900 meters)
        length_string += metric_lengths.at(kMetersIndex);
        distance << (std::round(meters / 100) * 100);
        } else if (meters > 9) {
        // "<METERS> meters" (10-90 meters)
        length_string += metric_lengths.at(kMetersIndex);
        distance << (std::round(meters / 10) * 10);
        } else {
        // "less than 10 meters"
        length_string += metric_lengths.at(kSmallMetersIndex);
        }
    }

    // TODO: why do we need separate tags for kilometers and meters?
    // Replace tags with length values
    boost::replace_all(length_string, kKilometersTag, distance.str());
    boost::replace_all(length_string, kMetersTag, distance.str());

    return length_string;
}

std::string LengthBuilder::FormUsCustomaryLength(float miles,
                                                 const std::vector<std::string>& us_customary_lengths) {

    // 0  "<MILES> miles"
    // 1  "1 mile"
    // 2  "a half mile"
    // 3  "a quarter mile"
    // 4  "<FEET> feet" (10-90, 100-500)
    // 5  "less than 10 feet"

    std::string length_string;
    length_string.reserve(kLengthStringInitialCapacity);

    // Follow locale rules turning numbers into strings
    std::stringstream distance;
    if (dictionary_ != nullptr) {
        distance.imbue(dictionary_->GetLocale());
    } else {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("dictionary is nullptr .");
    }

    float feet = std::round(miles * kFeetPerMile);
    float rounded = 0.f;

    // These will determine what we say
    if (feet > 1000) {
        if (miles > 2) {
        rounded = std::round(miles);
        } else if (miles >= 0.625) {
        rounded = std::round(miles * 2) / 2;
        } else {
        rounded = std::round(miles * 4) / 4;
        }

        if (rounded == 0.25f) {
        // "a quarter mile"
        length_string += us_customary_lengths.at(kQuarterMileIndex);
        } else if (rounded == 0.5f) {
        // "a half mile"
        length_string += us_customary_lengths.at(kHalfMileIndex);
        } else if (rounded == 1.f) {
        // "1 mile"
        length_string += us_customary_lengths.at(kOneMileIndex);
        } else {
        // "<MILES> miles"
        length_string += us_customary_lengths.at(kMilesIndex);
        distance << std::setiosflags(std::ios::fixed) << std::setprecision(rounded == 1.5f) << rounded;
        }
    } else {
        if (feet > 94) {
        // "<FEET> feet" (100-1000)
        length_string += us_customary_lengths.at(kFeetIndex);
        distance << (std::round(feet / 100) * 100);
        } else if (feet > 9) {
        // "<FEET> feet" (10-90)
        length_string += us_customary_lengths.at(kFeetIndex);
        distance << (std::round(feet / 10) * 10);
        } else {
        // "less than 10 feet"
        length_string += us_customary_lengths.at(kSmallFeetIndex);
        }
    }

    // TODO: why do we need separate tags for miles, tenths and feet?
    // Replace tags with length values
    boost::replace_all(length_string, kMilesTag, distance.str());
    boost::replace_all(length_string, kTenthsOfMilesTag, distance.str());
    boost::replace_all(length_string, kFeetTag, distance.str());

    return length_string;
}

}  // namespace guide
}  // namespace aurora
/* EOF */
