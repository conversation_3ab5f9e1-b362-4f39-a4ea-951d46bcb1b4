#ifndef MAP_SRC_GUIDE_SRC_MAKER_MANEUVER_TURN_DIRECTION_BUILDER_H
#define MAP_SRC_GUIDE_SRC_MAKER_MANEUVER_TURN_DIRECTION_BUILDER_H

#include "guidance/src/common/maneuver.h"
#include "guidance/src/data/enhance_path_result.h"

namespace aurora {
namespace guide {

class TurnDirectionBuilder {
public:
    TurnDirectionBuilder(EnhancePathResultPtr enhance_path);
    void Build(Maneuver &maneuver, DirectedEdge *prev_edge, DirectedEdge *curr_edge);

protected:
    void BuildStraight(Maneuver &maneuver);
    void BuildSlightRight(Maneuver &maneuver);
    void BuildRight(Maneuver &maneuver);
    void BuildSharpRight(Maneuver &maneuver);
    void BuildReverse(Maneuver &maneuver);
    void BuildSharpLeft(Maneuver &maneuver);
    void BuildLeft(Maneuver &Maneuver);
    void BuildSlightLeft(Maneuver &maneuver);

private:
    EnhancePathResultPtr enhance_path_;
    DirectedEdge *prev_edge_;
    DirectedEdge *curr_edge_;

}; // class 

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_MAKER_MANEUVER_TURN_DIRECTION_BUILDER_H
/* EOF */
