#ifndef MAP_SRC_GUIDE_SRC_MAKER_MANEUVER_ROUNDABOUTS_BUILDER_H
#define MAP_SRC_GUIDE_SRC_MAKER_MANEUVER_ROUNDABOUTS_BUILDER_H

#include <list>
#include "guidance/src/common/maneuver.h"
#include "guidance/src/data/enhance_path_result.h"

namespace aurora {
namespace guide {

class RoundaboutsBuilder {
public:
    RoundaboutsBuilder(EnhancePathResultPtr enhance_path);
    void BuildRoundabouts(std::list<Maneuver> &maneuvers);

protected:
    EnhancePathResultPtr enhance_path_;
};

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_MAKER_MANEUVER_ROUNDABOUTS_BUILDER_H
/* EOF */
