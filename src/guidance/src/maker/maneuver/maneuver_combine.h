#ifndef MAP_SRC_GUIDE_SRC_MAKER_MANEUVER_MANEUVER_COMBINE_H
#define MAP_SRC_GUIDE_SRC_MAKER_MANEUVER_MANEUVER_COMBINE_H

#include <list>
#include "guidance/src/common/maneuver.h"
#include "guidance/src/data/enhance_path_result.h"

namespace aurora {
namespace guide {

class ManeuversBuilder;
class ManeuverCombine {
public:
    ManeuverCombine(EnhancePathResultPtr enhance_path);

    void Combine(std::list<Maneuver> &maneuvers, ManeuversBuilder* maneuver_builder);

    static std::list<Maneuver>::iterator CombineManeuvers(std::list<Maneuver>& maneuvers,
                                                          std::list<Maneuver>::iterator curr_man,
                                                          std::list<Maneuver>::iterator next_man);

protected:
    bool PossibleUnspecifiedInternalManeuver(std::list<Maneuver>::iterator prev_man, 
                                             std::list<Maneuver>::iterator curr_man, 
                                             std::list<Maneuver>::iterator next_man);

    std::list<Maneuver>::iterator CombineUnspecifiedInternalManeuver(std::list<Maneuver>& maneuvers,
                                                                    std::list<Maneuver>::iterator prev_man,
                                                                    std::list<Maneuver>::iterator curr_man,
                                                                    std::list<Maneuver>::iterator next_man,
                                                                    const ManeuverType& maneuver_type);

    std::list<Maneuver>::iterator CombineInternalManeuver(std::list<Maneuver>& maneuvers,
                                                          std::list<Maneuver>::iterator prev_man,
                                                          std::list<Maneuver>::iterator curr_man,
                                                          std::list<Maneuver>::iterator next_man,
                                                          bool start_man);

    bool IsTurnChannelManeuverCombinable(std::list<Maneuver>::iterator prev_man,
                                         std::list<Maneuver>::iterator curr_man,
                                         std::list<Maneuver>::iterator next_man,
                                         bool start_man) const;

    std::list<Maneuver>::iterator CombineTurnChannelManeuver(std::list<Maneuver>& maneuvers,
                                                             std::list<Maneuver>::iterator prev_man,
                                                             std::list<Maneuver>::iterator curr_man,
                                                             std::list<Maneuver>::iterator next_man,
                                                             bool start_man);

  /**
   * Returns true if the current and next ramp maneuvers are able to be combined,
   * false otherwise.
   *
   * @param curr_man Current maneuver
   * @param next_man Next maneuver
   *
   * @return true if the current and next ramp maneuvers are able to be combined,
   * false otherwise.
   */
  bool AreRampManeuversCombinable(std::list<Maneuver>::iterator curr_man,
                                  std::list<Maneuver>::iterator next_man) const;

  /**
   * Returns true if the next maneuver is obvious and can be combined with the current maneuver,
   * false otherwise.
   *
   * @param maneuvers The list of maneuvers to process
   * @param curr_man Current maneuver
   * @param next_man Next maneuver
   *
   * @return true if the next maneuver is obvious and can be combined with the current maneuver,
   * false otherwise.
   */
  bool IsNextManeuverObvious(const std::list<Maneuver>& maneuvers,
                             std::list<Maneuver>::const_iterator curr_man,
                             std::list<Maneuver>::const_iterator next_man) const;
private:
    EnhancePathResultPtr enhance_path_;
    ManeuversBuilder *maneuver_builder_;
}; // class 
}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_MAKER_MANEUVER_MANEUVER_COMBINE_H
/* EOF */
