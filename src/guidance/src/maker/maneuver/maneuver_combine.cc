#include "guidance/src/maker/maneuver/maneuver_combine.h"
#include "guidance/src/maker/maneuver/maneuvers_builder.h"
#include "guidance/src/common/guide_log.h"
#include "guidance/src/common/guide_constant.h"
#include "guidance/src/common/geo_util.h"

namespace aurora {
namespace guide {

ManeuverCombine::Maneu<PERSON><PERSON><PERSON><PERSON>(EnhancePathResultPtr enhance_path)
: enhance_path_(enhance_path) {

}

void ManeuverCombine::Combine(std::list<Maneuver> &maneuvers, ManeuversBuilder *maneuver_builder) {
    bool maneuvers_have_been_combined = true;

    maneuver_builder_ = maneuver_builder;
    while (maneuvers_have_been_combined) {
        maneuvers_have_been_combined = false;

        auto prev_man = maneuvers.begin();
        auto curr_man = maneuvers.begin();
        auto next_man = maneuvers.begin();

        if (next_man != maneuvers.end()) {
            ++next_man;
        }

        while (next_man != maneuvers.end()) {

            // process common base names            
            std::shared_ptr<StreetNames> common_base_names = 
                    curr_man->GetStreetNames()->FindCommonStreetNames(*next_man->GetStreetNames());

            DirectedEdge *next_man_begin_edge = enhance_path_->GetCurrentEdge(next_man->GetBeginNodeIndex());
            bool is_first_man = (curr_man == maneuvers.begin());

            GUIDE_LOG_TRACE("+++ Combine TOP ++++++++++++++++++++++++++++++++++++++++++++");

            // Do not combine
            // if driving side is different
            if (curr_man->GetDriveOnRight() != next_man->GetDriveOnRight()) {
                GUIDE_LOG_TRACE("+++ Do Not Combine: if driving side is different +++");
                prev_man = curr_man;
                curr_man = next_man;
                ++next_man;
            }
            
            // Do not combine
            // if next maneuver is destination
            else if (next_man->GetType() == ManeuverType::kTypeDestination) {
                GUIDE_LOG_TRACE("+++ Do Not Combine: if travel mode is different or next maneuver is destination +++");
                // Update with no combine
                prev_man = curr_man;
                curr_man = next_man;
                ++next_man;
            }
            // Combine current left unspecified internal maneuver with next left maneuver
            // 用于识别那些在数据中没有被明确标记为内部交叉路口的潜在内部导航操作。
            // 这个函数的主要目的是改善导航指令，通过正确处理这些特殊情况使导航更加自然和直观
            else if (PossibleUnspecifiedInternalManeuver(prev_man, curr_man, next_man) && 
                     prev_man->HasSimilarName(&(*next_man)) &&
                     (geo::DetermineRelativeDirection(curr_man->GetTurnDegree()) == Maneuver::RelativeDirection::kLeft) &&
                     (geo::DetermineRelativeDirection(next_man->GetTurnDegree()) == Maneuver::RelativeDirection::kLeft) && 
                     (geo::DetermineRelativeDirection(geo::CalcTurnDegree360(prev_man->GetEndHeading(), 
                        next_man->GetBeginHeading())) == Maneuver::RelativeDirection::KReverse)) {

                GUIDE_LOG_TRACE("+++ Combine: double L turns with short unspecified internal intersection as ManeuverType=UTURN_LEFT +++");
                curr_man = CombineUnspecifiedInternalManeuver(maneuvers, prev_man, curr_man, next_man, ManeuverType::kTypeLeftUTurn);
                maneuvers_have_been_combined = true;
                ++next_man;
            }
            // Combine current right unspecified internal maneuver with next right maneuver
            // TODO: 右掉头
            else if (PossibleUnspecifiedInternalManeuver(prev_man, curr_man, next_man) && 
                     prev_man->HasSimilarName(&(*next_man)) &&
                     (geo::DetermineRelativeDirection(curr_man->GetTurnDegree()) == Maneuver::RelativeDirection::kRight) &&
                     (geo::DetermineRelativeDirection(next_man->GetTurnDegree()) == Maneuver::RelativeDirection::kRight) &&
                     (geo::DetermineRelativeDirection(geo::CalcTurnDegree360(prev_man->GetEndHeading(),
                        next_man->GetBeginHeading())) == Maneuver::RelativeDirection::KReverse)) {
                
                GUIDE_LOG_TRACE("+++ Combine: double R turns with short unspecified internal intersection as ManeuverType=UTURN_RIGHT +++");
                curr_man = CombineUnspecifiedInternalManeuver(maneuvers, prev_man, curr_man, next_man, ManeuverType::kTypeRightUTurn);
                maneuvers_have_been_combined = true;
                ++next_man;
            }
            // Do not combine
            // if next maneuver is a fork or a tee
            else if (next_man->GetFork() || next_man->GetTee()) {
                GUIDE_LOG_TRACE("+++ Do Not Combine: if next maneuver is a fork or a tee +++");
                // Update with no combine
                prev_man = curr_man;
                curr_man = next_man;
                ++next_man;
            }
            // Combine current internal maneuver with next maneuver
            // 路口场景机动点合并
            else if (curr_man->GetStreetIntersection() && 
                     (curr_man != next_man) && 
                     !next_man->IsDestinationType()) {
                GUIDE_LOG_TRACE("+++ Combine: current internal maneuver with next maneuver +++");
                curr_man = CombineInternalManeuver(maneuvers, prev_man, curr_man, next_man, is_first_man);
                if (is_first_man) {
                    prev_man = curr_man;
                }
                maneuvers_have_been_combined = true;
                ++next_man;
            }
            // Combine current turn channel maneuver with next maneuver
            else if (IsTurnChannelManeuverCombinable(prev_man, curr_man, next_man, is_first_man)) {
                GUIDE_LOG_TRACE("+++ Combine: current turn channel maneuver with next maneuver +++");
                curr_man = CombineTurnChannelManeuver(maneuvers, prev_man, curr_man, next_man, is_first_man);
                if (is_first_man) {
                    prev_man = curr_man;
                }
                maneuvers_have_been_combined = true;
                ++next_man;
            }
            // Do not combine
            // if next maneuver has an intersecting forward link
            // 下一个机动点必然不是直行
            else if (next_man->GetIntersectingForwardEdge()) { 
                GUIDE_LOG_TRACE("+++ Do Not Combine: if next maneuver has an intersecting forward link +++");
                // Update with no combine
                prev_man = curr_man;
                curr_man = next_man;
                ++next_man;
            }

            // NOTE: Logic may have to be adjusted depending on testing
            // Maybe not intersecting forward link
            // Maybe first edge in next is internal
            // Maybe no signs
            // Combine the 'same name straight' next maneuver with the current maneuver
            // if begin edge of next maneuver is not a turn channel
            // and the next maneuver is not an internal intersection maneuver
            // and the current maneuver is not a ramp
            // and the next maneuver is not a ramp
            // and current and next maneuvers have a common base name
            else if (next_man->GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepStraight &&
                     ((next_man_begin_edge != nullptr) && !next_man_begin_edge->IsTurnChannel()) &&
                     !next_man->GetStreetIntersection() && 
                     !curr_man->GetRamp() && !next_man->GetRamp() &&
                     !curr_man->GetRoundAbout() && !next_man->GetRoundAbout() && !common_base_names->empty()) {

                GUIDE_LOG_TRACE("+++ Combine: Several factors +++");

                if (!curr_man->HasBeginStreetNames() && !curr_man->GetPortionHighway() && 
                    (curr_man->GetStreetNames()->size() > common_base_names->size())) {
                    curr_man->SetBeginStreetNames(curr_man->GetStreetNames()->Clone());
                }

                // update current maneuver street names
                curr_man->SetStreetNames(common_base_names);
                next_man = CombineManeuvers(maneuvers, curr_man, next_man);
                maneuvers_have_been_combined = true;
            }
            // Combine unnamed straight maneuvers
            else if (next_man->GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepStraight &&
                     !curr_man->HasStreetNames() && !next_man->HasStreetNames() && 
                     ((next_man_begin_edge != nullptr) && !next_man_begin_edge->IsTurnChannel()) && 
                     !next_man->GetStreetIntersection() && !curr_man->GetRamp() && !next_man->GetRamp() &&
                     !curr_man->GetRoundAbout() && !next_man->GetRoundAbout()) {
                
                GUIDE_LOG_TRACE("+++ Combine: unnamed straight maneuvers +++");
                next_man = CombineManeuvers(maneuvers, curr_man, next_man);
                maneuvers_have_been_combined = true;
            }
            // Combine ramp maneuvers
            else if (AreRampManeuversCombinable(curr_man, next_man)) {
                GUIDE_LOG_TRACE("+++ Combine: ramp maneuvers +++");
                next_man = CombineManeuvers(maneuvers, curr_man, next_man);
                maneuvers_have_been_combined = true;
            }
            // Combine obvious maneuver
            else if (IsNextManeuverObvious(maneuvers, curr_man, next_man)) {
                // If current maneuver does not have street names then use the next maneuver street names
                if (!curr_man->HasStreetNames() && next_man->HasStreetNames()) {
                    curr_man->SetStreetNames(next_man->GetStreetNames()->Clone());
                }

                curr_man->SetContainObviousManeuver(true);
                curr_man->SetTurnChannel(false);
                GUIDE_LOG_TRACE("+++ Combine: obvious maneuver +++");
                next_man = CombineManeuvers(maneuvers, curr_man, next_man);
                maneuvers_have_been_combined = true;
            }
            // Combine current short length non-internal edges (left or right) with next maneuver that is a
            // kRampStraight NOTE: This should already be marked internal for OSM data so shouldn't happen
            // for OSM
            else if (PossibleUnspecifiedInternalManeuver(prev_man, curr_man, next_man) &&
                     ((geo::DetermineRelativeDirection(curr_man->GetTurnDegree()) == Maneuver::RelativeDirection::kLeft) ||
                      (geo::DetermineRelativeDirection(curr_man->GetTurnDegree()) == Maneuver::RelativeDirection::kRight)) &&
                     next_man->GetType() == ManeuverType::kTypeStraightToRamp) {
                
                GUIDE_LOG_TRACE("+++ Combine: current non-internal turn that is very short in length with the next straight ramp maneuver +++");
                curr_man = CombineUnspecifiedInternalManeuver(maneuvers, prev_man, curr_man, next_man, ManeuverType::kTypeNone);
                maneuvers_have_been_combined = true;
                ++next_man;
            } else {
                GUIDE_LOG_INFO("+++ Do Not Combine +++");
                prev_man = curr_man;
                curr_man = next_man;

                if (next_man != maneuvers.end()) {
                    ++next_man;
                }
            }

            GUIDE_LOG_TRACE("+++ Combine BOTTOM +++++++++++++++++++++++++++++++++++++++++");

        }  // while
    }
}

bool 
ManeuverCombine::PossibleUnspecifiedInternalManeuver(std::list<Maneuver>::iterator prev_man,
                                                     std::list<Maneuver>::iterator curr_man,
                                                     std::list<Maneuver>::iterator next_man) {
    
    if (!curr_man->GetStreetIntersection() && 
        !prev_man->GetRoundAbout() && 
        !curr_man->GetRoundAbout() && 
        !next_man->GetRoundAbout() &&
        curr_man->GetLengthMeter() < kMaxInternalLength && 
        curr_man != next_man && 
        !curr_man->IsStartType() && 
        !next_man->IsDestinationType()) {
            return true;
    }
    return false;
}

std::list<Maneuver>::iterator 
ManeuverCombine::CombineUnspecifiedInternalManeuver(std::list<Maneuver>& maneuvers,
                                                    std::list<Maneuver>::iterator prev_man,
                                                    std::list<Maneuver>::iterator curr_man,
                                                    std::list<Maneuver>::iterator next_man,
                                                    const ManeuverType& maneuver_type) {
                                
    next_man->SetTurnDegree(geo::CalcTurnDegree360(prev_man->GetEndHeading(), next_man->GetBeginHeading()));
    
    if (curr_man->HasStreetNames()) {
        next_man->SetCrossStreetNames(curr_man->GetStreetNames());
    }

    next_man->SetBeginRelativeDirection(geo::DetermineRelativeDirection(next_man->GetTurnDegree()));
    next_man->SetLengthMeter(curr_man->GetLengthMeter() + next_man->GetLengthMeter());
    next_man->SetBasicTime(curr_man->GetBasicTime() + next_man->GetBasicTime());
    next_man->SetBeginNodeIndex(curr_man->GetBeginNodeIndex());
    next_man->SetType(maneuver_type);

    if (curr_man->HasSignPost()) {
        auto& cur_sign_posts = curr_man->GetSignPosts();
        next_man->GetSignPosts().insert(next_man->GetSignPosts().begin(), cur_sign_posts.begin(), cur_sign_posts.end());
    }

    return maneuvers.erase(curr_man);
}

std::list<Maneuver>::iterator 
ManeuverCombine::CombineInternalManeuver(std::list<Maneuver>& maneuvers,
                                         std::list<Maneuver>::iterator prev_man,
                                         std::list<Maneuver>::iterator curr_man,
                                         std::list<Maneuver>::iterator next_man,
                                         bool start_man) {
                                            
    if (start_man) {
        // 起点在路口内
        next_man->SetTurnDegree(geo::CalcTurnDegree360(curr_man->GetEndHeading(), next_man->GetBeginHeading()));
    } else {
        next_man->SetTurnDegree(geo::CalcTurnDegree360(prev_man->GetEndHeading(), next_man->GetBeginHeading()));
    }

    if (curr_man->HasStreetNames()) {
        next_man->SetCrossStreetNames(curr_man->GetStreetNames()->Clone());
    }

    // TODO: valhalla error
    next_man->SetInternalLeftTurnCount(curr_man->GetInternalLeftTurnCount() + next_man->GetInternalLeftTurnCount());
    next_man->SetInternalRightTurnCount(curr_man->GetInternalRightTurnCount() + next_man->GetInternalRightTurnCount());

    // 
    next_man->SetBeginRelativeDirection(
        geo::DetermineRelativeDirection(
            geo::CalcTurnDegree360(prev_man->GetEndHeading(), next_man->GetBeginHeading())));

    // cur_man
    if ((next_man->GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepStraight) &&
        (curr_man->GetInternalLeftTurnCount() > 0) &&
        (curr_man->GetInternalRightTurnCount() > 0)) {
        
        GUIDE_LOG_TRACE("both left and right internal turn counts are > 0");
        next_man->SetBeginRelativeDirection(geo::DetermineRelativeDirection(
            geo::CalcTurnDegree360(prev_man->GetEndHeading(), curr_man->GetEndHeading())));
    }

    next_man->SetLengthMeter(curr_man->GetLengthMeter() + next_man->GetLengthMeter());
    next_man->SetBasicTime(curr_man->GetBasicTime() + next_man->GetBasicTime());

    next_man->SetBeginNodeIndex(curr_man->GetBeginNodeIndex());

    if (curr_man->HasSignPost()) {
        auto& cur_sign_posts = curr_man->GetSignPosts();
        next_man->GetSignPosts().insert(next_man->GetSignPosts().begin(), cur_sign_posts.begin(), cur_sign_posts.end());
    }

    if (start_man) {
        next_man->SetType(ManeuverType::kTypeStart);
    } else {
        next_man->SetType(ManeuverType::kTypeNone);
        GUIDE_ASSERT(maneuver_builder_ != nullptr);

        if (maneuver_builder_ != nullptr) {
            maneuver_builder_->SetManeuverType(*(next_man));
        }
    }

    return maneuvers.erase(curr_man);
}

bool 
ManeuverCombine::IsTurnChannelManeuverCombinable(std::list<Maneuver>::iterator prev_man,
                                                 std::list<Maneuver>::iterator curr_man,
                                                 std::list<Maneuver>::iterator next_man,
                                                 bool start_man) const {
#if 0
    if ((curr_man->GetTurnChannel()) && (curr_man != next_man) && !next_man->IsDestinationType()) {
        uint32_t new_turn_degree;
        if (start_man) {
            new_turn_degree = geo::CalcTurnDegree360(curr_man->GetEndHeading(), next_man->GetBeginHeading());
        } else {
            new_turn_degree = geo::CalcTurnDegree180(prev_man->GetEndHeading(), next_man->GetBeginHeading());
        }

        Turn::Type new_turn_type = Turn::GetType(new_turn_degree);
        int32_t turn_channel_end_node_index = curr_man->GetEndNodeIndex();
        EnhanceNode *node = enhance_path_->GetNode(turn_channel_end_node_index);
        DirectedEdge *prev_edge = enhance_path_->GetPrevEdge(turn_channel_end_node_index);
        DirectedEdge *curr_edge = enhance_path_->GetCurrentEdge(turn_channel_end_node_index);

        if (node == nullptr || prev_edge == nullptr || curr_edge == nullptr) {
            GUIDE_ASSERT(false);
            return false;
        }

        // Calculate this value in case next maneuver turn degree is modified
        // because of internal intersection collapsing
        auto post_turn_channel_turn_degree = geo::CalcTurnDegree360(prev_edge->GetEndHeading(), curr_edge->GetBeginHeading());
        
        // TODO: tbt
    }
    return false;
# else
    // TODO: TBT
    return false;
#endif 
}

std::list<Maneuver>::iterator
ManeuverCombine::CombineTurnChannelManeuver(std::list<Maneuver>& maneuvers,
                                            std::list<Maneuver>::iterator prev_man,
                                            std::list<Maneuver>::iterator curr_man,
                                            std::list<Maneuver>::iterator next_man,
                                            bool start_man) {
    if (start_man) {
        next_man->SetTurnDegree(geo::CalcTurnDegree360(curr_man->GetEndHeading(), next_man->GetBeginHeading()));
    } else {
        next_man->SetTurnDegree(geo::CalcTurnDegree360(prev_man->GetEndHeading(), next_man->GetBeginHeading()));
    }

    next_man->SetBeginRelativeDirection(curr_man->GetBeginRelativeDirection());
    next_man->SetLengthMeter(curr_man->GetLengthMeter() + next_man->GetLengthMeter());
    next_man->SetBasicTime(curr_man->GetBasicTime() + next_man->GetBasicTime());
    next_man->SetBeginNodeIndex(curr_man->GetBeginNodeIndex());

    if (curr_man->HasSignPost()) {
        auto& cur_sign_posts = curr_man->GetSignPosts();
        next_man->GetSignPosts().insert(next_man->GetSignPosts().begin(), cur_sign_posts.begin(), cur_sign_posts.end());
    }

    if (start_man) {
        next_man->SetType(ManeuverType::kTypeStart);
    } else {
        next_man->SetType(ManeuverType::kTypeNone);
        GUIDE_ASSERT(maneuver_builder_ != nullptr);
        if (maneuver_builder_ != nullptr) {
            maneuver_builder_->SetManeuverType(*next_man);
        }
    }

    return maneuvers.erase(curr_man);
}

std::list<Maneuver>::iterator 
ManeuverCombine::CombineManeuvers(std::list<Maneuver>& maneuvers,
                                  std::list<Maneuver>::iterator curr_man,
                                  std::list<Maneuver>::iterator next_man) {

    curr_man->SetLengthMeter(curr_man->GetLengthMeter() + next_man->GetLengthMeter());
    curr_man->SetBasicTime(curr_man->GetBasicTime() + next_man->GetBasicTime());
    curr_man->SetEndHeading(next_man->GetEndHeading());
    curr_man->SetEndNodeIndex(next_man->GetEndNodeIndex());

    if (next_man->GetRamp()) {
        curr_man->SetRamp(true);
    }

    if (next_man->GetRoundAbout()) {
        curr_man->SetRoundAbout(true);
    }

    if (next_man->GetPortionToll()) {
        curr_man->SetPortionToll(true);
    }

    if (next_man->GetTurnRestriction()) {
        curr_man->SetTurnRestriction(true);
    }

    if (next_man->GetPortionUnpaved()) {
        curr_man->SetPortionUnpaved(true);
    }

    if (next_man->GetPortionHighway()) {
        curr_man->SetPortionHighway(true);
    }

    if (next_man->GetContainObviousManeuver()) {
        curr_man->SetContainObviousManeuver(true);
    }

    if (next_man->HasSignPost()) {
        auto& next_sign_posts = next_man->GetSignPosts();
        curr_man->GetSignPosts().insert(curr_man->GetSignPosts().end(), next_sign_posts.begin(), next_sign_posts.end());
    }

    return maneuvers.erase(next_man);
}

bool ManeuverCombine::AreRampManeuversCombinable(std::list<Maneuver>::iterator curr_man,
                                                 std::list<Maneuver>::iterator next_man) const {
    if (curr_man->GetRamp() && next_man->GetRamp() && !next_man->GetFork() &&
         !curr_man->GetStreetIntersection() && !next_man->GetStreetIntersection()) {
        
        EnhanceNode *node = enhance_path_->GetNode(next_man->GetBeginNodeIndex());

        if (node == nullptr) {
            GUIDE_LOG_ERROR("node is nullptr .");
            return false;
        }

        if (!node->HasOutIntersectionEdge() || 
            node->IsStraightestIntersectingEdgeReversed(curr_man->GetEndHeading()) || // TODO: this need to be check
            next_man->GetType() == ManeuverType::kTypeStraightToRamp) {
            return true;
        }
    }
    return false;
}

// 判断下一个机动点是否是显而易见的，从而与当前机动点做合并，简化导航指令
bool ManeuverCombine::IsNextManeuverObvious(const std::list<Maneuver>& maneuvers,
                                            std::list<Maneuver>::const_iterator curr_man,
                                            std::list<Maneuver>::const_iterator next_man) const {

                                                // The next maneuver must be a continue maneuver
    if (next_man->GetType() == ManeuverType::kTypeContinue) {

        EnhanceNode *node = enhance_path_->GetNode(next_man->GetBeginNodeIndex());
        if (node != nullptr && !node->HasOutIntersectionEdge() && !node->HasInIntersectionEdge()) {
            return true;
        }

        // Return false if the maneuver has an exit number
        if (next_man->HasSignPost()) {
            return false;
        }

        // process ramp forks
        if (curr_man->GetRamp() && curr_man->GetFork() && !curr_man->GetContainObviousManeuver()) {
            // Obvious if Keep straight and continue
            if (curr_man->GetType() == ManeuverType::kTypeStayStraight) {
                return true;
            }
            else {
                if (node != nullptr) {
                    IntersectingEdgeCounts xedge_counts;
                    node->CalculateRightLeftIntersectingEdgeCounts(curr_man->GetEndHeading(), xedge_counts);

                    // TODO: we could enhance in the future
                    // Obvious if left fork and no left edges at intersection
                    if ((curr_man->GetType() == ManeuverType::kTypeStayLeft) && (xedge_counts.left == 0)) {
                        return true;
                    }

                    // Obvious if right fork and no right edges at intersection
                    if ((curr_man->GetType() == ManeuverType::kTypeStayRight) && (xedge_counts.right == 0)) {
                        return true;
                    }
                }
            }
            return false;
        }

        // Return true if a short continue maneuver
        // and the following maneuver is not a continue
        // TODO: 后续
#if 0
        if (next_man->GetLengthMeter() < kShortContinueThresholdMeter ) {
            auto next_next_man = std::next(next_man);
            if ((next_next_man != maneuvers.end()) && (next_next_man->GetType() != ManeuverType::kTypeContinue)) {
                return true;
            }
        }
#endif
        if ((node != nullptr) && node->IsMotorwayJunction()) {
            return false;
        }

#if 0
        if (node != nullptr && 
                node->HasNonBackwardTraversableSameNameRampIntersectingEdge(curr_man->GetEndHeading())) {
            return true;
        }
#endif
    }
    return false;
}


}  // namespace guide
}  // namespace aurora
/* EOF */
