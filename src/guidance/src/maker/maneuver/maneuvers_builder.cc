#include "guidance/src/maker/maneuver/maneuvers_builder.h"
#include "guidance/src/common/guide_constant.h"
#include "guidance/src/common/guide_log.h"
#include "guidance/src/maker/normalize/verbal_text_formatter_factory.h"
#include "guidance/src/maker/normalize/street_name_factory.h"
#include "guidance/src/common/geo_util.h"
#include "guidance/src/maker/maneuver/turn_direction_builder.h"
#include "guidance/src/maker/maneuver/maneuver_combine.h"
#include "guidance/src/maker/maneuver/roundabouts_builder.h"
#include "guidance/src/common/string_util.h"

namespace aurora {
namespace guide {

const bool kLimitByConseuctiveCount = true;
constexpr uint32_t kElementMaxCount = 4;
constexpr uint32_t kVerbalAlertElementMaxCount = 1;
constexpr uint32_t kVerbalPreElementMaxCount = 2;
constexpr uint32_t kVerbalPostElementMaxCount = 2;
const std::string kVerbalDelim = ", ";

// Thresholds for succinct phrase usage
constexpr uint32_t kMaxWordCount = 5;
constexpr uint32_t kMaxStreetNameLength = 25;

enum class SideOfStreet {
    kNone = 0,
    kLeft,
    kRight
};

static SideOfStreet CalcStreeSide(const PointLL pt, const std::vector<PointXY<double>>& points, bool forward = true) {
    std::vector<PointLL> geo;
    geo.reserve(points.size());
    for (const auto& e: points) {
        geo.emplace_back(e.first, e.second);
    }

    auto closest = pt.ClosestPoint(geo);

    if (std::get<1>(closest) < 5.0f) {
        return SideOfStreet::kNone;
    }

    int32_t idx = std::get<2>(closest);
    bool is_left = pt.IsLeft(geo[idx], geo[idx + 1]);

    if (is_left) {
        if (forward) {
            return SideOfStreet::kLeft;
        } else {
            return SideOfStreet::kRight;
        }
    } else {
        if (forward) {
            return SideOfStreet::kRight;
        } else {
            return SideOfStreet::kLeft;
        }
    }
}

ManeuversBuilder::ManeuversBuilder(EnhancePathResultPtr enhance_path) 
: enhance_path_(enhance_path) {

}

std::list<Maneuver> ManeuversBuilder::Build() {
    std::list<Maneuver> maneuvers = Produce();
    uint32_t before_maneuer_num = maneuvers.size();
    Combine(maneuvers);
    GUIDE_LOG_INFO("++++++++ ManeuverResult: before:{} after:{} ++++++++++++++++", before_maneuer_num, maneuvers.size());
    
    ConfirmManeuverTypeAssignment(maneuvers);
    
    SetTraversableOutboundIntersectingEdgeFlags(maneuvers);
    
    ProcessRoundabouts(maneuvers);
    
    SetToStayOnAttribute(maneuvers);

    // Enhance signless interchanges
    EnhanceSignlessInterchnages(maneuvers);
    
    UpdateManeuverPlacementForInternalIntersectionTurns(maneuvers);

    // Collapse small end ramp fork maneuvers to reduce verbose instructions
    // Must happen after updating maneuver placement for internal edges
    CollapseSmallEndRampFork(maneuvers);

    CollapseMergeManeuvers(maneuvers);
    
    ProcessGuidanceViews(maneuvers);
    ProcessFacilities(maneuvers);
    ProcessTollStations(maneuvers);
    ProcessLaneInfos(maneuvers);
    
    UpdateManeuverPoints(maneuvers);
    ProcessVerbalSuccinctTransitionInstruction(maneuvers);
    CheckManeuverValid(maneuvers);
    return maneuvers;
}

void ManeuversBuilder::CheckManeuverValid(std::list<Maneuver>& maneuvers) {
    // 校验 SignPost完整性
    for (const auto &maneuver : maneuvers) {
        std::vector<EnhanceSignPostPtr> sign_posts;
        // check signpost
        for (int32_t index = maneuver.GetBeginNodeIndex() + 1; index <= maneuver.GetEndNodeIndex(); ++index) {
            EnhanceNode *enhance_node = enhance_path_->GetNode(index);
            if (enhance_node->GetSignPostNum() > 0) {
                sign_posts.insert(sign_posts.end(), enhance_node->GetSignPosts().begin(), enhance_node->GetSignPosts().end());
            }
        }

        if (sign_posts != maneuver.GetSignPosts()) {
            GUIDE_ASSERT(false);
        }
    } 
}

void ManeuversBuilder::ProcessVerbalSuccinctTransitionInstruction(std::list<Maneuver>& maneuvers) {
    for (auto& maneuver : maneuvers) {
        uint32_t street_name_count = 0;
        for (const auto& street_name : *maneuver.GetStreetNames()) {
            if (street_name_count == kVerbalPreElementMaxCount) {
                break;
            }

            if (StringUtil::GetWordCount(street_name->Value()) > kMaxWordCount ||
                StringUtil::StrlenUtf8(street_name->Value()) > kMaxStreetNameLength) {
                maneuver.SetHasLongStreetName(true);
                break;
            }
            ++street_name_count;
        }

        if (maneuver.IsRoundabout()) {
            // TODO: 
            GUIDE_ASSERT(false);
            GUIDE_LOG_ERROR("Not realize ...");
        }
    }
}

std::list<Maneuver> ManeuversBuilder::Produce() {
    std::list<Maneuver> maneuvers;
    if (enhance_path_ == nullptr || enhance_path_->GetNodeNum() < 2) {
        GUIDE_LOG_ERROR("enhane path data exception .");
        return maneuvers;
    }

    maneuvers.emplace_front();
    CreateDestinationManeuver(maneuvers.front());

    maneuvers.emplace_front();
    InitializeManeuver(maneuvers.front(), enhance_path_->GetLastNodeIndex());

    for (int32_t i = (enhance_path_->GetLastNodeIndex() - 1); i > 0; --i) {
        EnhanceNode* enhance_node = enhance_path_->GetNode(i);

        if (CanManeuverIncludePrevEdge(maneuvers.front(), i)) {
            UpdateManeuver(maneuvers.front(), i);
        } else {
            FinalizeManeuver(maneuvers.front(), i);

            maneuvers.emplace_front();
            InitializeManeuver(maneuvers.front(), i);
        }
    }

    CreateStartManeuver(maneuvers.front());
    return maneuvers;
}

void ManeuversBuilder::Combine(std::list<Maneuver> &maneuvers) {
    ManeuverCombine combine(enhance_path_);
    combine.Combine(maneuvers, this);
}

void ManeuversBuilder::ConfirmManeuverTypeAssignment(std::list<Maneuver>& maneuvers) {
    for (auto& maneuver : maneuvers) {
        SetManeuverType(maneuver, false);
    }
}

void ManeuversBuilder::ProcessRoundabouts(std::list<Maneuver> &maneuvers) {
    RoundaboutsBuilder builder(enhance_path_);
    builder.BuildRoundabouts(maneuvers);
}

// 设置"to stay on"属性，优化导航播报指令，例如"保持在Main Street上"而不是"转向Main Street"。
// 这使得导航指令更加自然和符合驾驶员的预期，特别是在道路弯曲或分叉但名称保持不变的情况下。
// 使用场景：
//    道路弯曲：当一条命名道路做大转弯时，导航系统会说"保持在XX路上"而不是"转向XX路"
//    道路分叉：当一条命名道路分叉，但用户需要继续在主路上行驶时，导航系统会说"保持在XX路上"
//    U型转弯：当用户需要在同一条道路上做U型转弯时，导航系统会说"在XX路上掉头"而不是"掉头到XX路"
void ManeuversBuilder::SetToStayOnAttribute(std::list<Maneuver> &maneuvers) {
    auto prev_man = maneuvers.begin();

    auto curr_man = maneuvers.begin();
    auto next_man = maneuvers.begin();

    if (next_man != maneuvers.end()) {
        ++next_man;
        curr_man = next_man;
    }

    if (next_man != maneuvers.end()) {
        ++next_man;
    }

    while (next_man != maneuvers.end()) {
        switch (curr_man->GetType()) {
        case ManeuverType::kTypeSlightLeft:
        case ManeuverType::kTypeSlightRight:
        case ManeuverType::kTypeLeft:
        case ManeuverType::kTypeRight:
        case ManeuverType::kTypeSharpLeft:
        case ManeuverType::kTypeSharpRight:
            if (curr_man->HasSimilarName(&(*prev_man))) {
                curr_man->SetToStayOn(true);
            }
            break;

        case ManeuverType::kTypeStayLeft:
        case ManeuverType::kTypeStayRight:
        case ManeuverType::kTypeStayStraight:
            if (curr_man->HasSimilarName(&(*prev_man))) {
                if (!curr_man->GetRamp()) {
                    curr_man->SetToStayOn(true);
                } else if (curr_man->HasSimilarName(&(*next_man))) {
                    curr_man->SetToStayOn(true);
                }
            }
            break;

        case ManeuverType::kTypeLeftUTurn:
        case ManeuverType::kTypeRightUTurn:
            if (curr_man->HasSimilarName(&(*prev_man))) {
                curr_man->SetToStayOn(true);
            }
            break;
        
        default:
            break;
        }

        prev_man = curr_man;
        curr_man = next_man;
        ++next_man;
    }
}

void ManeuversBuilder::EnhanceSignlessInterchnages(std::list<Maneuver> &maneuvers) {
    // TODO: 
    // later do it
}

void ManeuversBuilder::ProcessGuidanceViews(std::list<Maneuver> &maneuvers) {
    for (auto &maneuver : maneuvers) {
        uint32_t begin_node_idx = maneuver.GetBeginNodeIndex();
        uint32_t end_node_index = maneuver.GetEndNodeIndex();

        for (uint32_t idx = begin_node_idx + 1; idx <= end_node_index; ++idx) {
            EnhanceNode *enhance_node = enhance_path_->GetNode(idx);
            GUIDE_ASSERT(enhance_node != nullptr);
            if (enhance_node == nullptr) {
                GUIDE_LOG_ERROR("enhance_node is nullptr .");
                continue;
            }

            if (enhance_node->GetJunctionViewNum() > 0) {
                const std::vector<EnhanceJunctionViewPtr>&  junction_views = enhance_node->GetJunctionViews();
                auto& man_junction_views = maneuver.GetJunctionViews();
                man_junction_views.insert(man_junction_views.end(), junction_views.begin(), junction_views.end());
            }
        } // for
    }
}

void ManeuversBuilder::ProcessFacilities(std::list<Maneuver> &maneuvers) {
    for (auto& maneuver : maneuvers) {
        uint32_t begin_node_idx = maneuver.GetBeginNodeIndex();
        uint32_t end_node_index = maneuver.GetEndNodeIndex();
        
        for (uint32_t idx = begin_node_idx; idx < end_node_index; ++idx) {
            DirectedEdge *edge = enhance_path_->GetEdge(idx);
            GUIDE_ASSERT(edge != nullptr);
            if (edge == nullptr) {
                GUIDE_LOG_ERROR("edge is nullptr .");
                continue;
            }

            if (edge->GetFacilityNum() > 0) {
                auto& edge_facilities = edge->GetFacilities();
                auto& man_facilities =  maneuver.GetFacilities();
                man_facilities.insert(man_facilities.end(), edge_facilities.begin(), edge_facilities.end());
            }
        }
    }
}

void ManeuversBuilder::ProcessTollStations(std::list<Maneuver> &maneuvers) {
    for (auto& maneuver : maneuvers) {
        uint32_t begin_node_idx = maneuver.GetBeginNodeIndex();
        uint32_t end_node_index = maneuver.GetEndNodeIndex();

        for (uint32_t idx = begin_node_idx + 1; idx <= end_node_index; ++idx) {
            EnhanceNode *enhance_node = enhance_path_->GetNode(idx);
            GUIDE_ASSERT(enhance_node != nullptr);
            if (enhance_node == nullptr) {
                GUIDE_LOG_ERROR("enhance node is nullptr .");
                continue;
            }

            if (enhance_node->HasTollStations()) {
                auto& toll_stations = maneuver.GetTollStations();
                toll_stations.insert(toll_stations.end(), enhance_node->GetTollStation());
            }
        } // for
    } // for
}

void ManeuversBuilder::ProcessLaneInfos(std::list<Maneuver> &maneuvers) {
    for (auto &maneuver : maneuvers) {
        uint32_t begin_node_idx = maneuver.GetBeginNodeIndex();
        uint32_t end_node_idx = maneuver.GetEndNodeIndex();

        for (uint32_t idx = begin_node_idx; idx < end_node_idx; ++idx) {
            DirectedEdge *edge = enhance_path_->GetEdge(idx);
            GUIDE_ASSERT(edge != nullptr);
            if (edge == nullptr) {
                GUIDE_LOG_ERROR("edge is nullptr .");
                continue;
            }

            auto lane_info_ptr = edge->GetLaneInfo();
            if (lane_info_ptr != nullptr) {
                maneuver.GetLaneInfos().insert(maneuver.GetLaneInfos().end(), lane_info_ptr);
            }
        }
    }
}

void ManeuversBuilder::UpdateManeuverPlacementForInternalIntersectionTurns(std::list<Maneuver> &maneuvers) {
    auto is_turn_maneuver = [](ManeuverType maneuver_type) -> bool {
        switch (maneuver_type) {
        case ManeuverType::kTypeSlightRight:
        case ManeuverType::kTypeRight:
        case ManeuverType::kTypeSharpRight:
        case ManeuverType::kTypeRightUTurn:
        case ManeuverType::kTypeLeftUTurn:
        case ManeuverType::kTypeSharpLeft:
        case ManeuverType::kTypeLeft:
        case ManeuverType::kTypeSlightLeft:
        case ManeuverType::kTypeStayLeft:
        case ManeuverType::kTypeStayRight:
        case ManeuverType::kTypeLeftToRamp:
        case ManeuverType::kTypeRightToRamp:
            return true;

        default:
            return false;
        }
    };

    Maneuver *prev_maneuver = nullptr;
    for (auto &maneuver : maneuvers) {
        if (prev_maneuver != nullptr) {
            if (maneuver.IsDestinationType()) {
                break;
            }

            if (is_turn_maneuver(maneuver.GetType())) {
                int32_t origin_maneuver_end_node_index = maneuver.GetEndNodeIndex();

                for (int32_t node_index = maneuver.GetBeginNodeIndex(); node_index < origin_maneuver_end_node_index; ++node_index) {
                    int32_t new_node_index = node_index + 1;
                    DirectedEdge *prev_edge = enhance_path_->GetPrevEdge(node_index);
                    DirectedEdge *edge = enhance_path_->GetCurrentEdge(node_index);

                    if (new_node_index < origin_maneuver_end_node_index && edge->IsInnerEdge() && 
                         geo::IsRelativeStraight(
                            geo::CalcTurnDegree360(prev_edge->GetEndHeading(), edge->GetBeginHeading()))) {

                        // Add straight internal edge to previous maneuver
                        MoveInternalEdgeToPreviousManeuver(*prev_maneuver, maneuver, new_node_index, prev_edge, edge);

                    } else {
                        break;
                    }
                } // for
            } // if
        } // if
        prev_maneuver = &maneuver;
    } // for
}

void ManeuversBuilder::MoveInternalEdgeToPreviousManeuver(Maneuver& prev_maneuver, 
                                                         Maneuver& maneuver, 
                                                         uint32_t new_node_index,
                                                         DirectedEdge *prev_edge,
                                                         DirectedEdge *edge) {
    GUIDE_ASSERT(prev_edge != nullptr);
    GUIDE_ASSERT(edge != nullptr);

    uint32_t prev_node_index = prev_maneuver.GetEndNodeIndex();
    EnhanceNode *enhance_node = enhance_path_->GetNode(prev_node_index);
    GUIDE_ASSERT(enhance_node != nullptr);
    if ((enhance_node != nullptr) && (enhance_node->GetSignPostNum() > 0)) {
        auto& prev_sign_posts = enhance_node->GetSignPosts();
        prev_maneuver.GetSignPosts().insert(prev_maneuver.GetSignPosts().end(), prev_sign_posts.begin(), prev_sign_posts.end());
        
        auto& curr_sign_posts = maneuver.GetSignPosts();
        GUIDE_ASSERT(prev_sign_posts.size() <= curr_sign_posts.size());
        GUIDE_ASSERT(!prev_sign_posts.empty());
        
        if (curr_sign_posts.size() >= prev_sign_posts.size()) {
            curr_sign_posts.erase(curr_sign_posts.begin(), curr_sign_posts.begin() + prev_sign_posts.size());
        }
    }

    prev_maneuver.SetLengthMeter(prev_maneuver.GetLengthMeter() + edge->GetLengthMeter());
    prev_maneuver.SetBasicTime(prev_maneuver.GetBasicTime() + edge->GetBasicTime());
    prev_maneuver.SetEndNodeIndex(new_node_index);

    
    maneuver.SetLengthMeter(maneuver.GetLengthMeter() - edge->GetLengthMeter());
    maneuver.SetBasicTime(maneuver.GetBasicTime() - edge->GetBasicTime());
    maneuver.SetBeginNodeIndex(new_node_index);

    if (edge->GetLaneInfo() == nullptr) {
        // If the internal edge does not have turn lanes
        // then copy the turn lanes from the previous edge
        edge->SetLaneInfo(prev_edge->GetLaneInfo());
    }
}

void ManeuversBuilder::CollapseSmallEndRampFork(std::list<Maneuver>& maneuvers) {
    auto prev_man = maneuvers.begin();

    auto curr_man = maneuvers.begin();
    auto next_man = maneuvers.begin();
    if (next_man != maneuvers.end()) {
        ++next_man;
        curr_man = next_man;
    }

    if (next_man != maneuvers.end()) {
        ++next_man;
    }

    auto is_fork_then_turn_same_direction = [](ManeuverType curr_man_type, ManeuverType next_man_type) -> bool {
        if ((curr_man_type == ManeuverType::kTypeStayRight) && 
            ((next_man_type == ManeuverType::kTypeSlightRight) || (next_man_type == ManeuverType::kTypeRight) || (next_man_type == ManeuverType::kTypeSharpRight))) {
            return true;
        }
        else if ((curr_man_type == ManeuverType::kTypeStayLeft) && 
            ((next_man_type == ManeuverType::kTypeSlightLeft) || (next_man_type == ManeuverType::kTypeLeft) || (next_man_type == ManeuverType::kTypeSharpLeft))) {
            return true;
        }
        return false;
    };

    while (next_man != maneuvers.end()) {
        if ((prev_man != curr_man) && !prev_man->GetHasCollapsedSmallEndRampFork() &&
             prev_man->GetRamp() && curr_man->GetRamp() && !next_man->GetRamp() && 
             (curr_man->GetLengthMeter() < kSmallEndRampForkThreshold) &&
             is_fork_then_turn_same_direction(curr_man->GetType(), next_man->GetType())) {
            
            curr_man = ManeuverCombine::CombineManeuvers(maneuvers, prev_man, curr_man);
            prev_man->SetHasCollapsedSmallEndRampFork(true);
            ++next_man;
        } else {
            prev_man = curr_man;
            curr_man = next_man;
            ++next_man;
        }
    }
}

void ManeuversBuilder::CollapseMergeManeuvers(std::list<Maneuver>& maneuvers) {
    // TODO: TBT
    GUIDE_LOG_WARN(" ++++ CollapseMergeManeuvers is not realize ++++");
}

void ManeuversBuilder::CreateDestinationManeuver(Maneuver& maneuver) {
    int32_t node_index = enhance_path_->GetLastNodeIndex();
    auto query = enhance_path_->GetEnhancePathQuery();

    DirectedEdge* edge = enhance_path_->GetPrevEdge(node_index);
    SideOfStreet side = SideOfStreet::kNone;
    if (edge != nullptr) {
        side = CalcStreeSide(query->GetDestination()->pt, edge->GetGeoPoints());
    }

    switch (side) {
    case SideOfStreet::kLeft:
        maneuver.SetType(ManeuverType::kTypeDestinationLeft);
        GUIDE_LOG_INFO("ManeuverType=DestionationLeft");
        break;

    case SideOfStreet::kRight:
        maneuver.SetType(ManeuverType::kTypeDestinationRight);
        GUIDE_LOG_INFO("ManeuverType=DestionationRight");
        break;
    
    default:
        maneuver.SetType(ManeuverType::kTypeDestination);
        GUIDE_LOG_INFO("ManeuverType=Destionation");
        break;
    }

    maneuver.SetBeginNodeIndex(node_index);
    maneuver.SetEndNodeIndex(node_index);
    
    maneuver.SetVerbalFormatter(
        VerbalTextFormatterFactory::Create(enhance_path_->GetCountryCode(),
                                           enhance_path_->GetStateCode()));
}

void ManeuversBuilder::CreateStartManeuver(Maneuver& maneuver) {
    int32_t node_index = 0;
    auto query = enhance_path_->GetEnhancePathQuery();

    DirectedEdge* edge = enhance_path_->GetCurrentEdge(node_index);
    GUIDE_ASSERT(edge != nullptr);
    SideOfStreet side = SideOfStreet::kNone;
    if (edge != nullptr) {
        side = CalcStreeSide(query->GetStart()->pt, edge->GetGeoPoints());
    }

    switch (side) {
    case SideOfStreet::kLeft:
        maneuver.SetType(ManeuverType::kTypeStartLeft);
        break;
    
    case SideOfStreet::kRight:
        maneuver.SetType(ManeuverType::kTypeStartRight);
        break;

    default:
        maneuver.SetType(ManeuverType::kTypeStart);
        break;
    }

    FinalizeManeuver(maneuver, node_index);
}

void ManeuversBuilder::InitializeManeuver(Maneuver& maneuver, int node_index) {
    DirectedEdge* prev_edge = enhance_path_->GetPrevEdge(node_index);
    // DirectedEdge* curr_edge = enhance_path_->GetCurrentEdge(node_index);

    GUIDE_ASSERT(prev_edge != nullptr);

    maneuver.SetEndHeading(prev_edge->GetEndHeading());
    maneuver.SetEndNodeIndex(node_index);

    if (prev_edge->IsRamp()) {
        maneuver.SetRamp(true);
    }

    if (prev_edge->IsTurnChannel()) {
        maneuver.SetTurnChannel(true);
    }

    if (prev_edge->IsRoundAbout()) {
        maneuver.SetRoundAbout(true);
        maneuver.SetRoundAboutExitCount(1);
    }

    if (prev_edge->IsInnerEdge() && !enhance_path_->IsLastNodeIndex(node_index) &&
            !enhance_path_->IsFirstNodeIndex(node_index-1)) {
        maneuver.SetStreetIntersection(true); 
    }

    maneuver.SetDriveOnRight(prev_edge->DriveOnRight());
    UpdateManeuver(maneuver, node_index);
}
   
void ManeuversBuilder::UpdateManeuver(Maneuver& maneuver, int node_index) {
    
    DirectedEdge* prev_edge = enhance_path_->GetPrevEdge(node_index);
    GUIDE_ASSERT(prev_edge != nullptr);
    
    if ((maneuver.GetStreetNames()->empty() && !maneuver.GetStreetIntersection()) ||
        UsableInternalIntersectionName(maneuver, node_index)) {
        
        if (!prev_edge->GetStreetNames()->empty()) {
            maneuver.SetStreetNames(prev_edge->GetStreetNames());
        }
    }

    // update internal left turn && right turn count
    UpdateInternalTurnCount(maneuver, node_index);


    maneuver.SetLengthMeter(maneuver.GetLengthMeter() + prev_edge->GetLengthMeter());
    maneuver.SetBasicTime(maneuver.GetBasicTime() + prev_edge->GetBasicTime());

    if (prev_edge->IsToll()) {
        maneuver.SetPortionToll(true);
    }

    if (prev_edge->HasTurnRestriction()) {
        maneuver.SetTurnRestriction(true);
    }

    if (prev_edge->IsUnpaved()) {
        maneuver.SetPortionUnpaved(true);
    }

    if (prev_edge->IsHighway()) {
        maneuver.SetPortionHighway(true);
    }

    if (prev_edge->IsRoundAbout()) {
        // TODO: 
        //  待补充
    }

    // process sign post
    EnhanceNode* enhance_node = enhance_path_->GetNode(node_index);
    GUIDE_ASSERT(enhance_node != nullptr);
    if ((enhance_node != nullptr) && (enhance_node->GetSignPostNum() > 0)) {
        auto& sign_posts = enhance_node->GetSignPosts();
        maneuver.GetSignPosts().insert(maneuver.GetSignPosts().begin(), sign_posts.begin(), sign_posts.end());
    }

}
    
void ManeuversBuilder::FinalizeManeuver(Maneuver& maneuver, int node_index) {
    DirectedEdge *prev_edge = enhance_path_->GetPrevEdge(node_index);
    DirectedEdge *curr_edge = enhance_path_->GetCurrentEdge(node_index);
    EnhanceNode  *enhance_node = enhance_path_->GetNode(node_index);

    maneuver.SetBeginCardinalDirection(
                    geo::DetermineCardinalDirection(curr_edge->GetBeginHeading()));
    maneuver.SetBeginHeading(curr_edge->GetBeginHeading());
    maneuver.SetBeginNodeIndex(node_index);

    // maneuver.SetBasicTime

    if (prev_edge != nullptr) {
        maneuver.SetTurnDegree(geo::CalcTurnDegree360(prev_edge->GetEndHeading(), 
                                                      curr_edge->GetBeginHeading()));
        
        // 优化转向信息
        DetermineRelativeDirection(maneuver);
    }

    maneuver.SetBeginIntersectingEdgeNameConsistency(enhance_node->HasXEdgeNameConssitency());

    // set begin street name
    // TODO: 此处策略涉及到调整
    if (!curr_edge->IsHighway() && !curr_edge->IsInnerEdge() && (curr_edge->GetStreetNames()->size() > 1)) {

        std::shared_ptr<StreetNames> common_base_names = 
            curr_edge->GetStreetNames()->FindCommonBaseNames(*maneuver.GetStreetNames());
        
        if (curr_edge->GetStreetNames()->size() > common_base_names->size()) {
            maneuver.SetBeginStreetNames(curr_edge->GetStreetNames());
        }
    }

    maneuver.SetVerbalFormatter(
        VerbalTextFormatterFactory::Create(enhance_path_->GetCountryCode(), enhance_path_->GetStateCode())
    );

    // set maneuver type
    SetManeuverType(maneuver);
}

void ManeuversBuilder::SetManeuverType(Maneuver& maneuver, bool none_type_allowed) {
    if (maneuver.GetType() != ManeuverType::kTypeNone) {
        return;
    }

    DirectedEdge *prev_edge = enhance_path_->GetPrevEdge(maneuver.GetBeginNodeIndex());
    DirectedEdge *curr_edge = enhance_path_->GetCurrentEdge(maneuver.GetBeginNodeIndex());

    if (maneuver.GetRoundAbout()) {
        // TODO: 
        //    环岛待补充
    } else if (prev_edge != nullptr && prev_edge->IsRoundAbout()) {
        // TODO:
        //   环岛待补充
    } else if (maneuver.GetFork()) {
        // process two fork, three fork
        switch (maneuver.GetBeginRelativeDirection()) {
        case Maneuver::RelativeDirection::kKeepRight:
        case Maneuver::RelativeDirection::kRight:
            maneuver.SetType(ManeuverType::kTypeStayRight);
            GUIDE_LOG_INFO("ManeuverType=ForkStayRight");
            break;
        
        case Maneuver::RelativeDirection::kKeepLeft:
        case Maneuver::RelativeDirection::kLeft:
            maneuver.SetType(ManeuverType::kTypeStayLeft);
            GUIDE_LOG_INFO("ManeuverType=ForkStayLeft");
            break;

        default:
            maneuver.SetType(ManeuverType::kTypeStayStraight);
            GUIDE_LOG_INFO("ManeuverType=ForkStayStraight");
            break;
        }
    } else if (none_type_allowed && maneuver.GetStreetIntersection()) { // 路口场景
        maneuver.SetType(ManeuverType::kTypeNone);
        GUIDE_LOG_INFO("ManeuverType=StreetIntersection, Update ManeuverType None");
    } else if (none_type_allowed && maneuver.GetTurnChannel()) {
        maneuver.SetType(ManeuverType::kTypeNone);
        GUIDE_LOG_INFO("ManeuverType=TurnChannel, Update ManeuverType None");
    } 
    // Process exit
    // if maneuver is ramp
    // and previous edge is a highway or maneuver has an exit number
    // or previous edge is not a ramp and ramp does not lead to a highway
    //    and the maneuver relative direction is a keep left or keep right
    // 高速出口
    else if (maneuver.GetRamp() && prev_edge != nullptr && 
                (prev_edge->IsHighway() || maneuver.HasSignPost() ||
                    (!prev_edge->IsRamp() && !RampLeadsToHighway(maneuver) &&
                        ((maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepRight) ||
                        (maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepRight))))) {

        switch(maneuver.GetBeginRelativeDirection()) {
        case Maneuver::RelativeDirection::kKeepRight:
        case Maneuver::RelativeDirection::kRight:
            maneuver.SetType(ManeuverType::kTypeExitRight);
            GUIDE_LOG_INFO("ManeuverType=kTypeExitRight");
            break;

        case Maneuver::RelativeDirection::kKeepLeft:
        case Maneuver::RelativeDirection::kLeft:
            maneuver.SetType(ManeuverType::kTypeExitLeft);
            GUIDE_LOG_INFO("ManeuverType=kTypeExirLeft");
            break;

        default:
            GUIDE_LOG_INFO("Exit RelativeDirection={}", 
                    std::to_string(static_cast<int32_t>(maneuver.GetBeginRelativeDirection())));
            if (maneuver.GetDriveOnRight()) {
                maneuver.SetType(ManeuverType::kTypeExitRight);
                GUIDE_LOG_INFO("ManeuverType=Exit Right");
            } else {
                maneuver.SetType(ManeuverType::kTypeLeft);
                GUIDE_LOG_INFO("ManeuverType=Exit Left");
            }
            break;
        }

    } 
    // 进入匝道
    else if (maneuver.GetRamp() && prev_edge != nullptr && !prev_edge->IsHighway()) {
        switch(maneuver.GetBeginRelativeDirection()) {
        case Maneuver::RelativeDirection::kKeepRight:
        case Maneuver::RelativeDirection::kRight:
            maneuver.SetType(ManeuverType::kTypeRightToRamp);
            GUIDE_LOG_INFO("ManeuverType=RightToRamp");
            break;

        case Maneuver::RelativeDirection::kKeepLeft:
        case Maneuver::RelativeDirection::kLeft:
            maneuver.SetType(ManeuverType::kTypeLeftToRamp);
            GUIDE_LOG_INFO("ManeuverType=LeftToRamp");
            break;

        case Maneuver::RelativeDirection::kKeepStraight:
            maneuver.SetType(ManeuverType::kTypeStraightToRamp);
            GUIDE_LOG_INFO("ManeuverType=StraightToRamp");
            break;

        case Maneuver::RelativeDirection::KReverse:
            if (maneuver.GetDriveOnRight()) {
                if (maneuver.GetTurnDegree() < 180) {
                    maneuver.SetType(ManeuverType::kTypeRightToRamp);
                    GUIDE_LOG_INFO("ManeuverType=RightToRamp");
                } else {
                    maneuver.SetType(ManeuverType::kTypeLeftToRamp);
                    GUIDE_LOG_INFO("ManeuverType=LeftToRamp");
                }
            } else {
                if (maneuver.GetTurnChannel() > 180) {
                    maneuver.SetType(ManeuverType::kTypeLeftToRamp);
                    GUIDE_LOG_INFO("ManeuverType=LeftToRamp");
                } else {
                    maneuver.SetType(ManeuverType::kTypeRightToRamp);
                    GUIDE_LOG_INFO("ManeuverType=RightToRamp");
                }
            }
            break;

        default:
            GUIDE_ASSERT(false);
            maneuver.SetType(ManeuverType::kTypeRightToRamp);
            GUIDE_LOG_INFO("impossiable case");
            break;
        }
    }
    // process merge
    else if (IsMergeManeuverType(maneuver, prev_edge, curr_edge)) {
        switch (maneuver.GetMergeToRelativeDirection()) {
        case Maneuver::RelativeDirection::kKeepLeft:
            maneuver.SetType(ManeuverType::kTypeMergeToLeft);
            GUIDE_LOG_INFO("ManeuverType=MergeToLeft");
            break;
        
        case Maneuver::RelativeDirection::kKeepRight:
            maneuver.SetType(ManeuverType::kTypeMergeToRight);
            GUIDE_LOG_INFO("ManeuverType=MergeToRight");
            break;

        default:
            maneuver.SetType(ManeuverType::kTypeMerge);
            break;
        }
    }
    // process simple direction
    else {
        GUIDE_LOG_INFO("ManeuverType=Simple");
        SetSimpleDirectionalManeuverType(maneuver, prev_edge, curr_edge);
    }
}

// 处理八方向
void ManeuversBuilder::SetSimpleDirectionalManeuverType(Maneuver& maneuver,
                                                        DirectedEdge *prev_edge,
                                                        DirectedEdge *curr_edge) {
    TurnDirectionBuilder turn_direction_builder(enhance_path_);
    turn_direction_builder.Build(maneuver, prev_edge, curr_edge);
}

void ManeuversBuilder::UpdateManeuverPoints(std::list<Maneuver> &maneuvers) {
    for (auto &maneuver : maneuvers) {
        int32_t start_node_idx = maneuver.GetBeginNodeIndex();
        int32_t end_node_idx = maneuver.GetEndNodeIndex();

        if (start_node_idx == end_node_idx) { // only one point
            if (enhance_path_->IsFirstNodeIndex(start_node_idx)) {
                DirectedEdge *edge = enhance_path_->GetCurrentEdge(start_node_idx);
                GUIDE_ASSERT(edge != nullptr);
                GUIDE_ASSERT(!edge->GetGeoPoints().empty());
                if (!edge->GetGeoPoints().empty()) {
                    maneuver.SetPoints({edge->GetGeoPoints().front()});                   
                }
            } else if (enhance_path_->IsLastNodeIndex(start_node_idx)) {
                DirectedEdge *edge = enhance_path_->GetPrevEdge(start_node_idx);
                GUIDE_ASSERT(edge != nullptr);
                GUIDE_ASSERT(!edge->GetGeoPoints().empty());
                if (!edge->GetGeoPoints().empty()) {
                    maneuver.SetPoints({edge->GetGeoPoints().back()});                   
                }
            }
        } else {
            std::vector<PointXY<double>> geo;
            geo.reserve(1024);
            for (int32_t index = start_node_idx; index < end_node_idx; ++index) {
                DirectedEdge *edge = enhance_path_->GetEdge(index);
                GUIDE_ASSERT(edge != nullptr);
                GUIDE_ASSERT(!edge->GetGeoPoints().empty());
                geo.insert(geo.end(), edge->GetGeoPoints().begin(), edge->GetGeoPoints().end());
            }

            maneuver.SetPoints(std::move(geo));
        }
    }
}

bool ManeuversBuilder::CanManeuverIncludePrevEdge(Maneuver& maneuver, int node_index) {
    DirectedEdge* prev_edge = enhance_path_->GetPrevEdge(node_index);
    DirectedEdge* curr_edge = enhance_path_->GetCurrentEdge(node_index);
    EnhanceNode* node = enhance_path_->GetNode(node_index);

    GUIDE_ASSERT(prev_edge != nullptr);
    GUIDE_ASSERT(curr_edge != nullptr);
    GUIDE_ASSERT(node != nullptr);

    if (maneuver.GetDriveOnRight() != prev_edge->DriveOnRight()) {
        return false;
    }
   
    // process roundabout
    if (maneuver.GetRoundAbout() && !prev_edge->IsRoundAbout()) {
        return false;
    } else if (!maneuver.GetRoundAbout() && prev_edge->IsRoundAbout()) {
        return false;
    } else if (maneuver.GetRoundAbout() && prev_edge->IsRoundAbout()) {
        return true;
    }

    // process fork
    if (IsFork(node_index, prev_edge, curr_edge)) {
        maneuver.SetFork(true);
        return false;
    }

    // process internal intersection
    // cann't be the first edge in the trip
    if (prev_edge->IsInnerEdge() && !maneuver.GetStreetIntersection()) {
        return false;
    } else if (!prev_edge->IsInnerEdge() && maneuver.GetStreetIntersection()) {
        return false;
    } else if (prev_edge->IsInnerEdge() && !enhance_path_->IsFirstNodeIndex(node_index-1) &&
               maneuver.GetStreetIntersection()) {
        return true;
    }

    // process simple turn channel
    if (prev_edge->IsTurnChannel() && !maneuver.GetTurnChannel()) {
        return false;
    } else if (!prev_edge->IsTurnChannel() && maneuver.GetTurnChannel()) {
        return false;
    } else if (prev_edge->IsTurnChannel() && maneuver.GetTurnChannel()) {
        return true;
    }

    // process exit sign
    if (maneuver.HasSignPost()) {
        return false;
    }

    // process ramp
    int32_t turn_degree = geo::CalcTurnDegree360(prev_edge->GetEndHeading(), curr_edge->GetBeginHeading());
    if (maneuver.GetRamp() && !prev_edge->IsRamp()) {
        return false;
    } else if (!maneuver.GetRamp() && prev_edge->IsRamp()) {
        return false;
    } else if (maneuver.GetRamp() && prev_edge->IsRamp()) {
        // don't combind if ramp to ramp is not forward
        if (!geo::IsForward(turn_degree)) {
            return false;
        }
        return false;
    }

    // process simple uturn
    if (turn_degree == 180) {
        if (prev_edge->DriveOnRight()) {
            maneuver.SetType(ManeuverType::kTypeLeftUTurn);
            GUIDE_LOG_INFO("ManeuverType=SIMPLE_LEFT_UTURN");
        } else {
            maneuver.SetType(ManeuverType::kTypeRightUTurn);
            GUIDE_LOG_INFO("ManeuverType=SIMPLE_RIGHT_UTURN");
        }
        return false;
    }

    // process pencil point u-turns
    if (IsLeftPencilPointUturn(node_index, prev_edge, curr_edge)) {
        maneuver.SetType(ManeuverType::kTypeLeftUTurn);
        GUIDE_LOG_INFO("ManeuverType=Pencil_point_Uturn_Left");
        return false;
    }

    if (IsRightPencilPointUturn(node_index, prev_edge, curr_edge)) {
        maneuver.SetType(ManeuverType::kTypeRightUTurn);
        GUIDE_LOG_INFO("ManeuverType=Pencil_point_Uturn_Right");
        return false;
    }

    // intersecting forward edge
    // 非直行场景，不合并
    if (IsIntersectingForwardEdge(node_index, prev_edge, curr_edge)) {
        maneuver.SetIntersectingForwardEdge(true);
        GUIDE_LOG_INFO("IsIntersectingForwardEdge");
        return false;
    }

    // determine previous edge names and common base names
    std::shared_ptr<StreetNames> prev_edge_names = prev_edge->GetStreetNames();
    std::shared_ptr<StreetNames> common_base_names = 
            prev_edge_names->FindCommonBaseNames(*maneuver.GetStreetNames());

    if (IsTee(node_index, prev_edge, curr_edge, !common_base_names->empty())) {
        maneuver.SetTee(true);
        return false;
    }

    // process no-forward transition with intersectiong traversable edge
    if (!curr_edge->IsStraightest(turn_degree, 
                                 node->GetStraightestIntersectionEdgeTurnDegree(prev_edge->GetEndHeading())) &&
            !node->HasForwardIntersectionEdge(prev_edge->GetEndHeading()) && 
            node->HasOutIntersectionEdge()) {
        return false;
    }

    // process common base names
    if (!common_base_names->empty()) {
        maneuver.SetStreetNames(common_base_names);
        return true;
    }

    // process unnamed edge
    if (!maneuver.HasStreetNames()&& prev_edge->GetStreetNames()->empty() &&
        IncludeUnnamedPrevEdge(node_index, prev_edge, curr_edge)) {
        return true;
    }

    return false;
}

bool ManeuversBuilder::IncludeUnnamedPrevEdge(int node_index, 
                                              DirectedEdge *prev_edge, 
                                              DirectedEdge *curr_edge) const {
    EnhanceNode *node = enhance_path_->GetNode(node_index);
    GUIDE_ASSERT(node != nullptr);

    if (!node->HasOutIntersectionEdge()) {
        return true;
    } else if (curr_edge->IsStraightest( // merge straight
        geo::CalcTurnDegree360(prev_edge->GetEndHeading(), curr_edge->GetBeginHeading()), 
        node->GetStraightestIntersectionEdgeTurnDegree(prev_edge->GetEndHeading()))) {
        return true;
    }
    return false;
}

void ManeuversBuilder::DetermineRelativeDirection(Maneuver &maneuver) {
    DirectedEdge *prev_edge = enhance_path_->GetPrevEdge(maneuver.GetBeginNodeIndex());
    DirectedEdge *curr_edge = enhance_path_->GetCurrentEdge(maneuver.GetBeginNodeIndex());

    IntersectingEdgeCounts xedge_counts;
    EnhanceNode *node = enhance_path_->GetNode(maneuver.GetBeginNodeIndex());
    node->CalculateRightLeftIntersectingEdgeCounts(prev_edge->GetEndHeading(), xedge_counts);

    // 标准四方向
    Maneuver::RelativeDirection relative_direction = 
        geo::DetermineRelativeDirection(maneuver.GetTurnDegree());
    
    maneuver.SetBeginRelativeDirection(relative_direction);

    // 复杂场景对转向做微调，避免用户产生困惑
    if (relative_direction == Maneuver::RelativeDirection::kKeepStraight) { // [-30, 30]
        if (xedge_counts.right_similar == 0 && xedge_counts.left_similar > 0) { 
            // 直行道路有多条，主路位于最右侧
            maneuver.SetBeginRelativeDirection(Maneuver::RelativeDirection::kKeepRight);
        } else if (xedge_counts.left_similar == 0 && xedge_counts.right_similar > 0) {
            // 直行道路有多条，主路位于最左侧
            maneuver.SetBeginRelativeDirection(Maneuver::RelativeDirection::kKeepLeft);
        } else if (xedge_counts.left_similar == 0 && xedge_counts.left > 0 && xedge_counts.right == 0) {
            // 当前道路左侧有大夹角分歧路，道路右侧无分歧路
            if (!curr_edge->IsStraightest(maneuver.GetTurnDegree(), 
                    node->GetStraightestIntersectionEdgeTurnDegree(prev_edge->GetEndHeading()))) {
                // 当前道路不是最直的
                maneuver.SetBeginRelativeDirection(Maneuver::RelativeDirection::kKeepRight);
            } else if (maneuver.GetTurnChannel() && 
                       (Turn::GetType(maneuver.GetTurnDegree()) != Turn::Type::kStraight)) {
                // 
                maneuver.SetBeginRelativeDirection(Maneuver::RelativeDirection::kKeepRight);
            } else if (maneuver.GetFork()) {
                maneuver.SetBeginRelativeDirection(Maneuver::RelativeDirection::kKeepRight);
            }

        } else if (xedge_counts.right_similar == 0 && xedge_counts.right > 0 && xedge_counts.left == 0) {
            // 当前道路右侧有大夹角分歧路，道路左侧无分歧路
            if (!curr_edge->IsStraightest(maneuver.GetTurnDegree(),
                        node->GetStraightestIntersectionEdgeTurnDegree(prev_edge->GetEndHeading()))) {
                maneuver.SetBeginRelativeDirection(Maneuver::RelativeDirection::kKeepLeft);
            } else if (maneuver.GetTurnChannel() && 
                      (Turn::GetType(maneuver.GetTurnDegree()) != Turn::Type::kStraight)) {
                maneuver.SetBeginRelativeDirection(Maneuver::RelativeDirection::kKeepLeft);
            } else if (maneuver.GetFork()) {
                maneuver.SetBeginRelativeDirection(Maneuver::RelativeDirection::kKeepLeft);
            }
        } else {
            // TODO: Straight方向下，主路左右都有edge,后续需要做进一步细分处理
        }

    } else if ((relative_direction == Maneuver::RelativeDirection::kLeft) &&
               (Turn::GetType(maneuver.GetTurnDegree()) == Turn::Type::kSlightLeft) &&
               node->HasSpecifiedTurnXEdge(Turn::Type::kLeft, prev_edge->GetEndHeading())) {
        // 
        maneuver.SetBeginRelativeDirection(Maneuver::RelativeDirection::kKeepLeft);
    } else if ((relative_direction == Maneuver::RelativeDirection::kRight) &&
               (Turn::GetType(maneuver.GetTurnDegree()) == Turn::Type::kSlightRight) &&
               node->HasSpecifiedTurnXEdge(Turn::Type::kRight, prev_edge->GetEndHeading())) {
        maneuver.SetBeginRelativeDirection(Maneuver::RelativeDirection::kKeepRight);
    }
}

bool ManeuversBuilder::IsFork(int32_t node_index, 
                              DirectedEdge *prev_edge, 
                              DirectedEdge *curr_edge) const {
    // TODO: heavy work later
    // TODO: be careful
    // ref: https://deepwiki.com/search/nodefork_767cba38-f99a-426a-bc8a-f76454b4522d
    // TODO: 待优化
    EnhanceNode *node = enhance_path_->GetNode(node_index);
    GUIDE_ASSERT(node != nullptr);

    if (node == nullptr) {
        return false;
    }

    if ((node->IsInternalIntesection()) || (node->GetOutIntersectionSize() > 2)) {
        return false;
    }

    // If node is fork
    // and prev to curr edge is relative straight
    // and there is a relative straight intersecting edge
    if (node->IsFork() && geo::IsWideForward(geo::CalcTurnDegree360(prev_edge->GetEndHeading(), curr_edge->GetBeginHeading())) && 
        node->HasWideForwardIntersectionEdge(prev_edge->GetEndHeading())) {

        IntersectingEdgeCounts xedge_counts;
        // TODO: update to pass similar turn threshold
        node->CalculateRightLeftIntersectingEdgeCounts(prev_edge->GetEndHeading(), xedge_counts);

        // if there is a similar traversable intersecting edge
        //   or there is a traversable intersecting edge and curr edge is link(ramp)
        //      and the straightest intersecting edge is not in the reversed direction
        //   or curr_edge is fork forward and xedge only has forward edge

        if ((xedge_counts.left_similar > 0) || (xedge_counts.right_similar > 0)) {
            return true;
        }

        if (((xedge_counts.left > 0) || (xedge_counts.right > 0)) && curr_edge->IsRamp() && 
            !node->IsStraightestIntersectingEdgeReversed(prev_edge->GetEndHeading())) {
            return true;
        }

        if (geo::IsWideForward(geo::CalcTurnDegree360(prev_edge->GetEndHeading(), curr_edge->GetBeginHeading())) && 
            node->HasOnlyForwardIntersectionEdge(prev_edge->GetEndHeading())) {
            return true;
        }
    }

    // Possibly move some logic to data processing in the future
    // Verify that both previous and current edges are highways
    // and the path is in the forward direction
    // and there is an intersecting highway edge in the forward direction
    else if (prev_edge->IsHighway() && curr_edge->IsHighway() &&
        geo::IsWideForward(geo::CalcTurnDegree360(prev_edge->GetEndHeading(), curr_edge->GetBeginHeading())) &&
           node->HasWideForwardIntersectionEdge(prev_edge->GetEndHeading())) {
        return true;
    }
    
    // Verify road class
    // and not turn channel, not ramp, not ferry, not rail ferry
    // and path is in forward direction
    // and only intersecting forward traversable edge
    else if (!prev_edge->IsRamp() && !prev_edge->IsTurnChannel() &&
        !curr_edge->IsRamp() && !curr_edge->IsTurnChannel() && 
        geo::IsForkForward(geo::CalcTurnDegree360(prev_edge->GetEndHeading(), curr_edge->GetBeginHeading())) &&
        node->HasOnlyForwardIntersectionEdge(prev_edge->GetEndHeading())) {
        // 路口场景，只有前方有多条直行
        return true;
    }

    else if (geo::IsForkForward(geo::CalcTurnDegree360(prev_edge->GetEndHeading(), curr_edge->GetBeginHeading())) && 
        !prev_edge->IsRamp() && !prev_edge->IsTurnChannel() && !curr_edge->IsRamp() && !curr_edge->IsTurnChannel() &&
        node->HasForwardIntersectionEdge(prev_edge->GetEndHeading())) {
        return true;
    }
    else if (node->GetOutIntersectionSize() == 1U) {
        const OutIntersectionBranch* xedge = node->GetOutIntersectionBranch(0);
        GUIDE_ASSERT(xedge != nullptr);

        if (xedge == nullptr) {
            return false;
        }

        auto has_lane_bifurcation = [](const DirectedEdge *prev_edge, const DirectedEdge *curr_edge, const OutIntersectionBranch *xedge) {
            // TODO: may need optimze for later
            uint32_t prev_lane_count = prev_edge->GetLaneNum();
            uint32_t curr_lane_count = curr_edge->GetLaneNum();

            uint32_t post_split_min_count = (prev_lane_count + 1) / 2;
            uint32_t xedge_lane_count = xedge->GetLaneNum();
            if ((prev_lane_count == 2) && (curr_lane_count == 1) && (xedge_lane_count == 1)) {
                return true;
            } else if ((prev_lane_count > 2) && (curr_lane_count == post_split_min_count) &&
                 (xedge_lane_count == post_split_min_count)) {
                return true;
            }
            return false;
        };
        
        if (prev_edge->IsHighway() && 
            ((curr_edge->IsHighway() && xedge->IsRamp()) || (curr_edge->IsRamp() && xedge->IsHighway())) &&
            geo::IsForkForward(geo::CalcTurnDegree360(prev_edge->GetEndHeading(), xedge->GetBeginHeading())) &&
            geo::IsForkForward(geo::CalcTurnDegree360(prev_edge->GetEndHeading(), curr_edge->GetBeginHeading())) &&
            has_lane_bifurcation(prev_edge, curr_edge, xedge)) {
            return true;
        }
    }
    return false;
}

bool ManeuversBuilder::IsTee(int32_t node_index, 
                             DirectedEdge *prev_edge, 
                             DirectedEdge *curr_edge, 
                             bool prev_has_common_name) const {
    
    EnhanceNode *node = enhance_path_->GetNode(node_index);
    if (node->GetOutIntersectionSize() == 1U) {
        Turn::Type turn_type = Turn::GetType(geo::CalcTurnDegree360(prev_edge->GetEndHeading(), curr_edge->GetBeginHeading()));

        Turn::Type xturn_type = Turn::GetType(geo::CalcTurnDegree360(prev_edge->GetEndHeading(), 
                                                node->GetOutIntersectionBranch(0)->GetBeginHeading()));
        
        if ((turn_type == Turn::Type::kLeft) && (xturn_type == Turn::Type::kRight)) {
            return true;
        }

        if ((turn_type == Turn::Type::kRight) && (xturn_type == Turn::Type::kLeft)) {
            return true;
        }
    }
    return false;
}

void ManeuversBuilder::UpdateInternalTurnCount(Maneuver& maneuver, int32_t node_index) const {
    DirectedEdge *prev_edge = enhance_path_->GetPrevEdge(node_index);
    DirectedEdge *prev_prev_edge = enhance_path_->GetPrevEdge(node_index, 2);

    uint32_t prev_prev_2prev_turn_degree = 0;
    if (prev_prev_edge != nullptr) {
        prev_prev_2prev_turn_degree = geo::CalcTurnDegree360(prev_prev_edge->GetEndHeading(), prev_edge->GetBeginHeading());
    }
    
    Maneuver::RelativeDirection relative_direction = geo::DetermineRelativeDirection(prev_prev_2prev_turn_degree);
    if (relative_direction == Maneuver::RelativeDirection::kLeft) {
        maneuver.SetInternalLeftTurnCount(maneuver.GetInternalLeftTurnCount() + 1);
    }

    if (relative_direction == Maneuver::RelativeDirection::kRight) {
        maneuver.SetInternalRightTurnCount(maneuver.GetInternalRightTurnCount() + 1);
    }
}

bool ManeuversBuilder::UsableInternalIntersectionName(Maneuver& maneuver, int32_t node_index) const {
    DirectedEdge *prev_edge = enhance_path_->GetPrevEdge(node_index);
    DirectedEdge *prev_prev_edge = enhance_path_->GetPrevEdge(node_index, 2);
    uint32_t prev_prev_2prev_turn_degree = 0;
    if (prev_prev_edge) {
        prev_prev_2prev_turn_degree = 
                geo::CalcTurnDegree360(prev_prev_edge->GetEndHeading(), prev_edge->GetBeginHeading());
    }

    Maneuver::RelativeDirection relative_direction = 
                geo::DetermineRelativeDirection(prev_prev_2prev_turn_degree);
    // Criteria for usable internal intersection name:
    // The maneuver is an internal intersection
    // Left turn for right side of the street driving
    // Right turn for left side of the street driving
    if (maneuver.GetStreetIntersection() && 
            (prev_edge->DriveOnRight() && (relative_direction == Maneuver::RelativeDirection::kLeft)) ||
            (!prev_edge->DriveOnRight() && (relative_direction == Maneuver::RelativeDirection::kRight))) {
        return true;
    }

    return false;
}

bool ManeuversBuilder::IsLeftPencilPointUturn(int32_t node_index, 
                                              DirectedEdge* prev_edge, 
                                              DirectedEdge* curr_edge) const {
    uint32_t turn_degree = geo::CalcTurnDegree360(prev_edge->GetEndHeading(), curr_edge->GetBeginHeading());
    // If drive on right
    // and the the turn is a sharp left (179 < turn < 226)
    // and oneway edges
    if (curr_edge->DriveOnRight() && (turn_degree > 179) && (turn_degree < 226) && 
        prev_edge->IsOneWay() && curr_edge->IsOneWay()) {
    
        IntersectingEdgeCounts xegde_counts;
        EnhanceNode *node = enhance_path_->GetNode(node_index);
        node->CalculateRightLeftIntersectingEdgeCounts(prev_edge->GetEndHeading(), xegde_counts);

        if (xegde_counts.left == 0) {
            return true;
        }
    }

    return false;

}


bool ManeuversBuilder::IsRightPencilPointUturn(int32_t node_index, 
                                               DirectedEdge* prev_edge, 
                                               DirectedEdge* curr_edge) const {
    uint32_t turn_degree = geo::CalcTurnDegree360(prev_edge->GetEndHeading(), curr_edge->GetBeginHeading());

    if (!curr_edge->DriveOnRight() && (turn_degree > 134) && (turn_degree < 181) &&
         prev_edge->IsOneWay() && curr_edge->IsOneWay()) {
        
        IntersectingEdgeCounts xedge_counts;
        EnhanceNode *node = enhance_path_->GetNode(node_index);
        node->CalculateRightLeftIntersectingEdgeCounts(prev_edge->GetEndHeading(), xedge_counts);

        if (xedge_counts.right == 0) {
            return true;
        }
    }

    return false;
}

bool ManeuversBuilder::IsIntersectingForwardEdge(int32_t node_index, 
                                                 DirectedEdge *prev_edge, 
                                                 DirectedEdge *curr_edge) const {
    EnhanceNode *node = enhance_path_->GetNode(node_index);
    uint32_t turn_degree = geo::CalcTurnDegree360(prev_edge->GetEndHeading(), curr_edge->GetBeginHeading());

    if (node->IsInternalIntesection() && !node->IsFork() && 
        !(prev_edge->IsHighway() && curr_edge->IsHighway())) {
        // if path edge is not forward
        // and forward intersecting edge exists
        // then return true
        // 当前路径不是前向方向，但是存在可前向通行的边
        if (!geo::IsForward(turn_degree) && node->HasForwardIntersectionEdge(prev_edge->GetEndHeading())) {
            return true;
        }

        // if path edge is forward
        // and forward traversable significant road class intersecting edge exists
        // and path edge is not the straightest
        // then return true
        // 当前路径是前向方向，但是当前路线不是最直的路径
        else if (geo::IsForward(turn_degree) && 
                 node->HasForwardIntersectionEdge(prev_edge->GetEndHeading()) && 
                !curr_edge->IsStraightest(turn_degree, node->GetStraightestIntersectionEdgeTurnDegree(
                    prev_edge->GetEndHeading()))) {
            return true;
        }
    }
    return false;
}

bool ManeuversBuilder::RampLeadsToHighway(Maneuver& maneuver) const {
    if (maneuver.GetRamp()) {
        for (uint32_t node_idx = maneuver.GetEndNodeIndex(); node_idx < enhance_path_->GetLastNodeIndex(); 
                ++node_idx) {
           
           // EnhanceNode *enhance_node = enhance_path_->GetNode(node_idx);
            DirectedEdge *curr_edge = enhance_path_->GetEdge(node_idx);
            if (curr_edge && (curr_edge->IsRamp() || curr_edge->IsTurnChannel() ||curr_edge->IsInnerEdge())) {
                continue;
            } else if (curr_edge && curr_edge->IsHighway()) {
                return true;
            } else {
                return false;
            }
        } // for
    } // if

    return false;
}

bool ManeuversBuilder::IsMergeManeuverType(Maneuver& maneuver, DirectedEdge *prev_edge, DirectedEdge *curr_edge) const {
    EnhanceNode *enhance_node = enhance_path_->GetNode(maneuver.GetBeginNodeIndex());

    GUIDE_ASSERT(enhance_node != nullptr);
    if (enhance_node == nullptr) {
        return false;
    }

    // Previous edge is ramp and current edge is not a ramp
    // Current edge is a highway OR
    // Current edge is a trunk or primary, oneway, forward turn degree, and
    // consistent name with intersecting edge
    if (prev_edge != nullptr && (prev_edge->IsRamp() || prev_edge->IsIC() || prev_edge->IsJct()) && 
            (!curr_edge->IsRamp() && !curr_edge->IsIC() && !curr_edge->IsJct()) &&
            (curr_edge->IsHighway() ||
             curr_edge->GetRoadClass() == parser::RoadClass::kRoadClassNationWay ||
             curr_edge->GetRoadClass() == parser::RoadClass::kRoadClassMainRoad) &&
             curr_edge->IsOneWay() && geo::IsForward(maneuver.GetTurnDegree()) &&
             enhance_node->HasXEdgeNameConssitency()) {

        maneuver.SetMergeToRelativeDirection(
            DetermineMergeToRelativeDirection(enhance_node, prev_edge));
        return true;
    }
    return false;
}

Maneuver::RelativeDirection 
ManeuversBuilder::DetermineMergeToRelativeDirection(EnhanceNode *node, DirectedEdge *prev_edge) const {
    
    // TODO: 需要测试验证
    IntersectingEdgeSimpleCounts xedge_simple_counts;
    node->CalculateInEdgeRightLeftForNonStreetIntersection(prev_edge->GetEndHeading(), xedge_simple_counts);

    if (xedge_simple_counts.left > 0 && xedge_simple_counts.right == 0) {
        return Maneuver::RelativeDirection::kKeepLeft;
    } 
    else if(xedge_simple_counts.right > 0 && xedge_simple_counts.left == 0) {
        return Maneuver::RelativeDirection::kKeepRight;
    }
    return Maneuver::RelativeDirection::kNone;
}

std::string ManeuversBuilder::FindCommonBaseName(const std::string & prev_name, 
                                                 const std::string &curr_name) {
    std::string common_name = Maneuver::GetCommonBaseNameUtf8(prev_name, curr_name);
    if (common_name == prev_name || common_name == curr_name) {
        return common_name;
    }
    return "";
}

void ManeuversBuilder::SetTraversableOutboundIntersectingEdgeFlags(std::list<Maneuver>& maneuvers) {
    // Process each maneuver for traversable outbound intersecting edges
    for (auto &maneuver : maneuvers) {
        bool found_first_edge_to_process = false;
        for (int node_index = maneuver.GetBeginNodeIndex(); node_index < maneuver.GetEndNodeIndex(); ++node_index) {
            if (!found_first_edge_to_process) {
                DirectedEdge *curr_edge = enhance_path_->GetEdge(node_index);
                GUIDE_ASSERT(curr_edge != nullptr);

                if (curr_edge == nullptr) {
                    continue;
                }

                if (curr_edge->IsInnerEdge() || curr_edge->IsTurnChannel()) {
                    continue;
                }

                found_first_edge_to_process = true;
                continue;
            }

            EnhanceNode *node = enhance_path_->GetNode(node_index);
            DirectedEdge *prev_edge = enhance_path_->GetPrevEdge(node_index);
            GUIDE_ASSERT(node != nullptr);
            GUIDE_ASSERT(prev_edge != nullptr);
            if (node != nullptr && prev_edge != nullptr) {
                IntersectingEdgeCounts xedge_counts;
                node->CalculateRightLeftIntersectingEdgeCounts(prev_edge->GetEndHeading(), xedge_counts);

                if (xedge_counts.left > 0) {
                    maneuver.SetHasLeftOutXEdge(true);
                }

                if (xedge_counts.right > 0) {
                    maneuver.SetHasRightOutXEdge(true);
                }

                if (maneuver.HasLeftOutXEdge() && maneuver.HasRightOutXEdge()) {
                    break;
                }
            } // if

        } // for
    } // for
}

}  // namespace guide
}  // namespace aurora
/* EOF */
