#include "guidance/src/maker/maneuver/turn_direction_builder.h"
#include "guidance/src/common/guide_log.h"
#include "guidance/src/common/guide_constant.h"

namespace aurora {
namespace guide {

TurnDirectionBuilder::TurnDirectionBuilder(EnhancePathResultPtr enhance_path)
: enhance_path_(enhance_path)
, prev_edge_(nullptr)
, curr_edge_(nullptr) {

}

void TurnDirectionBuilder::Build(Maneuver &maneuver, DirectedEdge *prev_edge, DirectedEdge *curr_edge) {
    prev_edge_ = prev_edge;
    curr_edge_ = curr_edge;

    switch (Turn::GetType(maneuver.GetTurnDegree())) {
    case Turn::Type::kStraight:
        BuildStraight(maneuver);
        break;
    
    case Turn::Type::kSlightRight:
        BuildSlightRight(maneuver);
        break;

    case Turn::Type::kRight:
        BuildRight(maneuver);
        break;

    case Turn::Type::kSharpRight:
        BuildSharpRight(maneuver);
        break;

    case Turn::Type::kReverse:
        BuildReverse(maneuver);
        break;

    case Turn::Type::kSharpLeft:
        BuildSharpLeft(maneuver);
        break;

    case Turn::Type::kLeft:
        BuildLeft(maneuver);
        break;

    case Turn::Type::kSlightLeft:
        BuildSlightLeft(maneuver);
        break;

    default:
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("unknown turn type .");
        break;
    }
}

void TurnDirectionBuilder::BuildStraight(Maneuver &maneuver) {
    maneuver.SetType(ManeuverType::kTypeContinue);

    if (enhance_path_ == nullptr) {
        GUIDE_LOG_ERROR("enhane_path is nullptr .");
        return;
    }

    DirectedEdge *man_begin_edge = enhance_path_->GetCurrentEdge(maneuver.GetBeginNodeIndex());
    EnhanceNode *node = enhance_path_->GetNode(maneuver.GetBeginNodeIndex());

    if ((node == nullptr) || (prev_edge_ == nullptr) || (curr_edge_ == nullptr)) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("param is nullptr .");
        return;
    }

    ////////////////////////////////////////////////////////////////////
    // If the maneuver begin edge is a turn channel
    // and the relative direction is not a keep straight
    // then set as slight right based on a relative keep right direction
    //  OR  set as slight left based on a relative keep left direction
    if ( (man_begin_edge != nullptr) && man_begin_edge->IsTurnChannel() && 
        maneuver.GetBeginRelativeDirection() != Maneuver::RelativeDirection::kKeepStraight) {
        
        if (maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepRight) {
            maneuver.SetType(ManeuverType::kTypeSlightRight);
            GUIDE_LOG_INFO("ManeuverType=SlightRight");
        } else if (maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepLeft) {
            maneuver.SetType(ManeuverType::kTypeSlightLeft);
            GUIDE_LOG_INFO("ManeuverType=SlightLeft");   
        }

    }

    // If internal intersection at beginning of maneuver
    else if (curr_edge_->IsInnerEdge()) {
        // Straight turn type but left relative direction
        if (maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kLeft ||
            maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepLeft) {
            maneuver.SetType(ManeuverType::kTypeSlightLeft);
            GUIDE_LOG_INFO("ManeuverType=SlightLeft");
        }
            
        // Straight turn type but right relative direction
        else if (maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kRight ||
            maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepRight) {
            maneuver.SetType(ManeuverType::kTypeSlightRight);
            GUIDE_LOG_INFO("ManeuverType=SlightRight");
        }
    }

    else if (curr_edge_->IsHighway() && maneuver.GetLengthMeter() < kShortContinueThresholdMeter) {
        // Keep as short continue - no adjustment needed
    }
    else if ((maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepRight) &&
             node->HasSimilarStraightRoadClassXEdge(maneuver.GetTurnDegree(), 
                    prev_edge_->GetEndHeading(), prev_edge_->GetRoadClass())) { // 前方直行道路存在多条
        // 
        if (curr_edge_->IsHighway() || node->HasForwardRampXEdge(prev_edge_->GetEndHeading())) {
            if (node->HasSimilarStraightNonRampOrSameNameRampXEdge(maneuver.GetTurnDegree(), 
                                                                   prev_edge_->GetEndHeading())) {
                maneuver.SetType(ManeuverType::kTypeStayRight);
                GUIDE_LOG_INFO("ManeuverType=kTypeStayRight");
            }
        } else {
            maneuver.SetType(ManeuverType::kTypeSlightRight);
            GUIDE_LOG_INFO("ManeuverType=kTypeSlightRight");
        }
    }
    else if ((maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepLeft) && 
            node->HasSimilarStraightRoadClassXEdge(maneuver.GetTurnDegree(), 
                prev_edge_->GetEndHeading(), prev_edge_->GetRoadClass())) {
        
         if (curr_edge_->IsHighway() || node->HasForwardRampXEdge(prev_edge_->GetEndHeading())) {
            if (node->HasSimilarStraightNonRampOrSameNameRampXEdge(maneuver.GetTurnDegree(), 
                                                                   prev_edge_->GetEndHeading())) {
                maneuver.SetType(ManeuverType::kTypeStayLeft);
                GUIDE_LOG_INFO("ManeuverType=kTypeStayLeft");
            }
        } else {
            maneuver.SetType(ManeuverType::kTypeSlightLeft);
            GUIDE_LOG_INFO("ManeuverType=kTypeSlightLeft");
        }
    }
}

void TurnDirectionBuilder::BuildSlightRight(Maneuver &maneuver) {
    maneuver.SetType(ManeuverType::kTypeSlightRight);
    GUIDE_LOG_INFO("ManeuverType=SlightRight");

    IntersectingEdgeCounts xedge_counts;
    EnhanceNode *node = enhance_path_->GetNode(maneuver.GetBeginNodeIndex());
    if ((node == nullptr) || (prev_edge_ == nullptr) || (curr_edge_ == nullptr)) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("param is nullptr .");
        return;
    }

    node->CalculateRightLeftIntersectingEdgeCounts(prev_edge_->GetEndHeading(), xedge_counts);

    // 
    if(maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepStraight &&
       !node->HasForwardIntersectionEdge(prev_edge_->GetEndHeading()) &&
      ((xedge_counts.right > 0) || ((xedge_counts.left == 0) && (xedge_counts.right == 0)))) {
        // Even though there is a slight right along the path - a continue maneuver is appropriate
        maneuver.SetType(ManeuverType::kTypeContinue);
        GUIDE_LOG_INFO("ManeuverType=kTypeContinue");
    }
}

void TurnDirectionBuilder::BuildRight(Maneuver &maneuver) {
    maneuver.SetType(ManeuverType::kTypeRight);
    GUIDE_LOG_INFO("ManeuverType=kRight");

    EnhanceNode *node = enhance_path_->GetNode(maneuver.GetBeginNodeIndex());
    if ((node == nullptr) || (prev_edge_ == nullptr) || (curr_edge_ == nullptr)) {
        GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("param is nullptr .");
        return;
    }

    if (!node->HasOutIntersectionEdge()) { // 
        return;
    }
    
    uint32_t right_most_turn_degree = 
        node->GetRightMostTurnDegree(maneuver.GetTurnDegree(), prev_edge_->GetEndHeading()); // y-axis positive
    if (maneuver.GetTurnDegree() == right_most_turn_degree) {
        maneuver.SetType(ManeuverType::kTypeRight);
        LOG_TRACE("ManeuverType=RIGHT");
    } else if (maneuver.GetTurnDegree() < right_most_turn_degree && 
        !node->HasSpecifiedTurnXEdge(Turn::Type::kSlightRight, prev_edge_->GetEndHeading())) {
        maneuver.SetType(ManeuverType::kTypeSlightRight);
    } else if (maneuver.GetTurnDegree() > right_most_turn_degree) { // 
        maneuver.SetType(ManeuverType::kTypeSharpRight);        
    }
}

void TurnDirectionBuilder::BuildSharpRight(Maneuver &maneuver) {
    maneuver.SetType(ManeuverType::kTypeSharpRight);
    GUIDE_LOG_INFO("ManeuverType=kSharpRight");
}
    
void TurnDirectionBuilder::BuildReverse(Maneuver &maneuver) { // TODO:
    if (maneuver.GetInternalLeftTurnCount() > maneuver.GetInternalRightTurnCount()) {
        maneuver.SetType(ManeuverType::kTypeLeftUTurn);
        GUIDE_LOG_INFO("ManeuverType=kTypeLeftUTurn");
    } else if (maneuver.GetInternalLeftTurnCount() < maneuver.GetInternalRightTurnCount()) {
        maneuver.SetType(ManeuverType::kTypeRightUTurn);
        GUIDE_LOG_INFO("ManeuverType=kTypeRightUTurn");        
    } else if (maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepLeft) {
        // TODO: need check
        maneuver.SetType(ManeuverType::kTypeLeftUTurn);
        GUIDE_LOG_INFO("ManeuverType=kTypeLeftUTurn");
    } else if (maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepRight) {
        // TODO: need check
        maneuver.SetType(ManeuverType::kTypeRightUTurn);
        GUIDE_LOG_INFO("ManeuverType=kTypeRightUTurn");
    } else if (enhance_path_->GetCurrentEdge(maneuver.GetBeginNodeIndex())->DriveOnRight()) {
        // TODO: need check
        if (maneuver.GetTurnDegree() < 180) {
            maneuver.SetType(ManeuverType::kTypeRightUTurn);
            GUIDE_LOG_INFO("ManeuverType=kTypeRightUTurn");
        } else {
            maneuver.SetType(ManeuverType::kTypeLeftUTurn);
            GUIDE_LOG_INFO("ManeuverType=kTypeLeftUTurn");
        }
    } else {
        // TODO：need check
        if (maneuver.GetTurnDegree() > 180) {
            maneuver.SetType(ManeuverType::kTypeLeftUTurn);
            GUIDE_LOG_INFO("ManeuverType=kTypeLeftUTurn");
        } else {
            maneuver.SetType(ManeuverType::kTypeRightUTurn);
            GUIDE_LOG_INFO("ManeuverType=kTypeRightUTurn");
        }
    }
}
    
void TurnDirectionBuilder::BuildSharpLeft(Maneuver &maneuver) {
    maneuver.SetType(ManeuverType::kTypeSharpLeft);
    GUIDE_LOG_INFO("ManeuverType=kSharpLeft");
}

void TurnDirectionBuilder::BuildLeft(Maneuver &maneuver) {
    maneuver.SetType(ManeuverType::kTypeLeft);

    EnhanceNode *node = enhance_path_->GetNode(maneuver.GetBeginNodeIndex());
    if ((node == nullptr) || (prev_edge_ == nullptr) || (curr_edge_ == nullptr)) {
        GUIDE_LOG_ERROR("param is nullptr .");
        return;
    }

    if (!node->HasOutIntersectionEdge()) {
        return;
    }

    uint32_t left_most_turn_degree = 
            node->GetLeftMostTurnDegree(maneuver.GetTurnDegree(), prev_edge_->GetEndHeading());

    if (left_most_turn_degree == maneuver.GetTurnDegree()) {
        maneuver.SetType(ManeuverType::kTypeLeft);
        GUIDE_LOG_INFO("ManeuverType=kManeuverTypeLeft");
    } else if ((maneuver.GetTurnDegree() > left_most_turn_degree) && 
                !node->HasSpecifiedTurnXEdge(Turn::Type::kSlightLeft, prev_edge_->GetEndHeading())) {
        maneuver.SetType(ManeuverType::kTypeSlightLeft);
        GUIDE_LOG_INFO("ManeuverType=kTypeSlightLeft");
    } else if ((maneuver.GetTurnDegree() < left_most_turn_degree) && 
        !node->HasSpecifiedTurnXEdge(Turn::Type::kSharpLeft, prev_edge_->GetBeginHeading())) {
        maneuver.SetType(ManeuverType::kTypeSharpLeft);
        GUIDE_LOG_INFO("ManeuverType=kTypeSharpLeft");
    }
}
    
void TurnDirectionBuilder::BuildSlightLeft(Maneuver &maneuver) {
    maneuver.SetType(ManeuverType::kTypeLeft);
    
    IntersectingEdgeCounts xedge_counts;
    EnhanceNode *node = enhance_path_->GetNode(maneuver.GetBeginNodeIndex());
    if ((node == nullptr) || (prev_edge_ == nullptr) || (curr_edge_ == nullptr)) {
        GUIDE_LOG_ERROR("param is nullptr");
        return;
    }

    node->CalculateRightLeftIntersectingEdgeCounts(prev_edge_->GetEndHeading(), xedge_counts);
    if ((maneuver.GetBeginRelativeDirection() == Maneuver::RelativeDirection::kKeepStraight) && 
        !node->HasForwardIntersectionEdge(prev_edge_->GetEndHeading() &&
        ((xedge_counts.left == 0) || (xedge_counts.right == 0 && xedge_counts.left == 0)))) {
        maneuver.SetType(ManeuverType::kTypeContinue);
        GUIDE_LOG_INFO("ManeuverType=kTypeContinue");
    }
}

}  // namespace guide
}  // namespace aurora
/* EOF */
