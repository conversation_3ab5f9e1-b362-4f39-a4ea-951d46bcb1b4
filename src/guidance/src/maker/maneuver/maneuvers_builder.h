#ifndef MAP_SRC_GUIDE_SRC_MAKER_MANEUVER_MANEUVERS_BUILDER_H
#define MAP_SRC_GUIDE_SRC_MAKER_MANEUVER_MANEUVERS_BUILDER_H

#include <cstdint>
#include <string>
#include <list>

#include "guidance/src/common/maneuver.h"
#include "guidance/src/data/enhance_path_result.h"

namespace aurora {
namespace guide {
class ManeuverCombine;

class ManeuversBuilder {
public:
    friend class ManeuverCombine;
    
    ManeuversBuilder(EnhancePathResultPtr enhance_path);

    std::list<Maneuver> Build();

protected:
    std::list<Maneuver> Produce();
    void Combine(std::list<Maneuver> &maneuvers);
    void ConfirmManeuverTypeAssignment(std::list<Maneuver>& maneuvers);

    void CreateDestinationManeuver(Maneuver& maneuver);
    void CreateStartManeuver(Maneuver& maneuver);

    void InitializeManeuver(Maneuver& maneuver, int node_index);
    void UpdateManeuver(Maneuver& maneuver, int node_index);
    void FinalizeManeuver(Maneuver& maneuver, int node_index);
    void SetManeuverType(Maneuver& maneuver, bool none_type_allowed = true);

    bool CanManeuverIncludePrevEdge(Maneuver& maneuver, int node_index);
    
    bool IsFork(int32_t node_index, DirectedEdge *prev_edge, DirectedEdge *curr_edge) const;
    bool IsTee(int32_t node_index, DirectedEdge *prev_edge, DirectedEdge *curr_edge, bool prev_has_common_name) const;
    bool UsableInternalIntersectionName(Maneuver& maneuver, int32_t node_index) const;
    void UpdateInternalTurnCount(Maneuver& maneuver, int32_t node_index) const;

    bool IsLeftPencilPointUturn(int32_t node_index, DirectedEdge *prev_edge, DirectedEdge *curr_edge) const;
    bool IsRightPencilPointUturn(int32_t node_index, DirectedEdge *prev_edge, DirectedEdge *curr_edge) const;
    bool IsIntersectingForwardEdge(int32_t node_index, DirectedEdge *prev_edge, DirectedEdge *curr_edge) const;
    bool IncludeUnnamedPrevEdge(int node_index, DirectedEdge *prev_edge, DirectedEdge *curr_edge) const;
    void DetermineRelativeDirection(Maneuver &maneuver);

    bool RampLeadsToHighway(Maneuver& maneuver) const;
    bool IsMergeManeuverType(Maneuver& maneuver, DirectedEdge *prev_edge, DirectedEdge *curr_edge) const;
    Maneuver::RelativeDirection DetermineMergeToRelativeDirection(EnhanceNode *node, DirectedEdge *prev_edge) const;
    void SetSimpleDirectionalManeuverType(Maneuver& maneuver, DirectedEdge *prev_edge, DirectedEdge *curr_edge);
    void UpdateManeuverPoints(std::list<Maneuver> &maneuvers);

    // Mark maneuvers that have traversable outbound intersecting edges.
    void SetTraversableOutboundIntersectingEdgeFlags(std::list<Maneuver> &maneuvers);
    void ProcessRoundabouts(std::list<Maneuver> &maneuvers);
    void SetToStayOnAttribute(std::list<Maneuver> &maneuvers);
    void EnhanceSignlessInterchnages(std::list<Maneuver> &maneuvers);
    void ProcessGuidanceViews(std::list<Maneuver> &maneuvers);
    void ProcessFacilities(std::list<Maneuver> &maneuvers);
    void ProcessTollStations(std::list<Maneuver> &maneuvers);

    void ProcessLaneInfos(std::list<Maneuver> &maneuvers);
    void UpdateManeuverPlacementForInternalIntersectionTurns(std::list<Maneuver> &maneuvers);
    
    // Update the transition point for internal intersection turns.
    void MoveInternalEdgeToPreviousManeuver(Maneuver& prev_maneuver, 
                                            Maneuver& maneuver, 
                                            uint32_t new_node_index,
                                            DirectedEdge *prev_edge,
                                            DirectedEdge *edge);

    void CollapseSmallEndRampFork(std::list<Maneuver>& maneuvers);
    void CollapseMergeManeuvers(std::list<Maneuver>& maneuvers);
    void ProcessVerbalSuccinctTransitionInstruction(std::list<Maneuver>& maneuvers);
    void CheckManeuverValid(std::list<Maneuver>& maneuvers);

private:
    std::string FindCommonBaseName(const std::string & prev_name, const std::string &curr_name);

private:
    EnhancePathResultPtr enhance_path_;
}; // class ManeuverBuilder


}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_MAKER_MANEUVER_MANEUVERS_BUILDER_H
/* EOF */
