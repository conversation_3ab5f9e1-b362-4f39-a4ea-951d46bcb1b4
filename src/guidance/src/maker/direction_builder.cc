#include "guidance/src/maker/direction_builder.h"
#include "guidance/src/maker/maneuver/maneuvers_builder.h"
#include "guidance/src/maker/narrative/narrative_builder.h"
#include "errorcode.h"
#include "Time.h"

#include "guidance/src/common/guide_log.h"
#include "guidance/src/maker/narrative/narrative_builder_factory.h"

namespace aurora {
namespace guide {

DirectionBuilder::DirectionBuilder() 
: thread_("direction_builder") {
    Stop();
}

void DirectionBuilder::SetOptions(GuidanceOptions *options) {
    options_ = options;
}

void DirectionBuilder::SetNarrativeDir(const std::string &dir) {
    narrative_dir_ = dir;
}


bool DirectionBuilder::Start() {
    thread_.Start();
    return true;
}
    
bool DirectionBuilder::Stop() {
    thread_.Stop();
    return true;
}

int32_t DirectionBuilder::Build(PathDataManagerPtr path_manager, std::list<Maneuver> &maneuver_list) {

    uint64_t start_ms = Time::Now().ToMillisecond();
    ManeuversBuilder maneuver_builder(path_manager->GetEnhancePathResult());
    maneuver_list = maneuver_builder.Build();
    uint64_t end_ms = Time::Now().ToMillisecond();
    
    GUIDE_LOG_INFO("+++++++ DirectionBuilder consume: {} ms", (end_ms - start_ms));
    std::unique_ptr<NarrativeBuilder> narrative_builder = 
            NarrativeBuilderFactory::Create(options_, path_manager, narrative_dir_);
    if (narrative_builder != nullptr) {
        narrative_builder->Build(maneuver_list);
        OutputNarratives(maneuver_list);
    } else {
        // GUIDE_ASSERT(false);
        GUIDE_LOG_ERROR("NarrativeBuilderFactory::Create(...) return nullptr .");
        return ErrorCode::kErrorCodeGuideCreateNarrativeError;
    }
    return ErrorCode::kErrorCodeOk;
}

int32_t DirectionBuilder::BuildAsync(PathDataManagerPtr path_manager, DirectionBuilderListenerPtr callback) {

    thread_.Post([this, path_manager, callback]() {
        ManeuversBuilder maneuver_builder(path_manager->GetEnhancePathResult());
        std::list<Maneuver> maneuver_list = maneuver_builder.Build();

        if (callback) {
            callback->OnResult(path_manager->GetEnhancePathResult()->GetPathId(), 
                               ErrorCode::kErrorCodeOk, 
                               std::move(maneuver_list));
        }
    });
    return ErrorCode::kErrorCodePending;
}

void DirectionBuilder::OutputNarratives(const std::list<Maneuver> &maneuver_list) {
    uint32_t index = 0;
    for (const auto &maneuver : maneuver_list) {
        GUIDE_LOG_INFO("Type[{}]: {}", index, ManeuverTypeToString(maneuver.GetType()));
        if (!maneuver.GetInstruction().empty()) {
            GUIDE_LOG_INFO("  Index [{}]: instruction -> {}", index, maneuver.GetInstruction());       
        }

        if (!maneuver.GetVerbalAlertInstruction().empty()) {
            GUIDE_LOG_INFO("  Index [{}]: alert -> {}", index, maneuver.GetVerbalAlertInstruction());
        }

        if (!maneuver.GetVerbalPreInstruction().empty()) {
            GUIDE_LOG_INFO("  Index [{}]: pre -> {}", index, maneuver.GetVerbalPreInstruction());  
        }

        if (!maneuver.GetVerbalSuccinctInstruction().empty()) {
            GUIDE_LOG_INFO("  Index [{}]: succinct -> {}", index, maneuver.GetVerbalSuccinctInstruction());   
        }

        if (!maneuver.GetVerbalPostInstruction().empty()) {
            GUIDE_LOG_INFO("  Index [{}]: post -> {}", index, maneuver.GetVerbalPostInstruction());
        }

        ++index;
    }
}

}  // namespace guide
}  // namespace aurora
/* EOF */
