#ifndef MAP_SRC_GUIDE_SRC_MAKER_DIRECTION_BUILDER_H
#define MAP_SRC_GUIDE_SRC_MAKER_DIRECTION_BUILDER_H

#include <cstdint>
#include <string>
#include <list>
#include <memory>

#include "guidance_def.h"
#include "guidance/src/common/maneuver.h"
#include "guidance/src/data/enhance_path_result.h"
#include "guidance/src/data/path_data_manager.h"
#include "loopthread.h"

namespace aurora {
namespace guide {

class DirectionBuilderListener {
public:
    virtual ~DirectionBuilderListener() = default;
    virtual void OnResult(uint64_t path_id, int32_t code, std::list<Maneuver> &&maneuver_list);
};
using DirectionBuilderListenerPtr = std::shared_ptr<DirectionBuilderListener>;

class DirectionBuilder {
public:
    DirectionBuilder();

    void SetOptions(GuidanceOptions *options);
    void SetNarrativeDir(const std::string &dir);
    bool Start();
    bool Stop();
    int32_t Build(PathDataManagerPtr path_manager, std::list<Maneuver> &maneuver_list);
    int32_t BuildAsync(PathDataManagerPtr path_manager, DirectionBuilderListenerPtr callback);

protected: 
    void OutputNarratives(const std::list<Maneuver> &maneuver_list);

protected:
    GuidanceOptions *options_;
    LoopThread thread_;
    std::string narrative_dir_;
}; // class DirectionBuilder

}  // namespace guide
}  // namespace aurora
#endif  // MAP_SRC_GUIDE_SRC_MAKER_DIRECTION_BUILDER_H
/* EOF */
