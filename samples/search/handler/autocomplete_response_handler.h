
#ifndef SAMPLES_SEARCH_AUTOCOMPLETE_RESPONSE_HANDLER_H
#define SAMPLES_SEARCH_AUTOCOMPLETE_RESPONSE_HANDLER_H

#include <memory>

#include "search_def.h"
#include "search_service.h"

class AutocompleteResponseHandlerSample final : public aurora::search::AutocompleteResponseHandler {
 public:
  void OnSuccess(const aurora::search::AutocompleteResponsePtr &response) override;

  void OnFailure(int error_code, const std::string &error_message) override;

  void OnCancel() override;
};

#endif