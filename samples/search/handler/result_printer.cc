#include "result_printer.h"

#include "logger.h"

void PrintPlaceBriefs(int task_id,
                      const std::vector<aurora::search::PlaceBrief>& places,
                      size_t max_num) {
  const int n = std::min(places.size(), max_num);
  for (int i = 0; i < n; ++i) {
    auto place = places[i];
    LOG_ERROR("[Task {}] id: {}, name: {}, address: {}, category: {}", task_id,
             place.id, place.name, place.address, place.category);
  }
}
void PrintPlaceDetail(int task_id, aurora::search::PlaceDetail& place) {
  LOG_ERROR("[Task {}] id: {}, name: {}, address: {}, category: {}", task_id,
           place.id, place.name, place.address, place.category);
}