
#ifndef SAMPLES_SEARCH_SEARCH_BY_TEXT_RESPONSE_HANDLER_H
#define SAMPLES_SEARCH_SEARCH_BY_TEXT_RESPONSE_HANDLER_H

#include <memory>

#include "search_def.h"

class SearchByTextResponseHandlerSample final : public aurora::search::SearchByTextResponseHandler {
 public:
  void OnSuccess(const aurora::search::SearchByTextResponsePtr& response) override;

  void OnFailure(int error_code, const std::string& error_message) override;

  void OnCancel() override;
};

#endif