
#ifndef SAMPLES_SEARCH_GET_DETAIL_RESPONSE_HANDLER_H
#define SAMPLES_SEARCH_GET_DETAIL_RESPONSE_HANDLER_H

#include <memory>

#include "search_def.h"

class GetDetailResponseHandlerSample final : public aurora::search::GetDetailResponseHandler {
 public:
  void OnSuccess(const aurora::search::GetDetailResponsePtr &response) override;

  void OnFailure(int error_code, const std::string &error_message) override;

  void OnCancel() override;
};

#endif