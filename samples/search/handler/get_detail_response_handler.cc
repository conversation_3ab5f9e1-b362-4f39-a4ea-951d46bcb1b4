#include "get_detail_response_handler.h"

#include "logger.h"
#include "result_printer.h"

void GetDetailResponseHandlerSample::OnSuccess(
    const aurora::search::GetDetailResponsePtr &response) {
  LOG_INFO(
      "****************** GetDetailResponseHandler  OnSuccess, taskId:[{}], "
      "name=[{}]",
      response->task_id, response->place_details[0].name);
  PrintPlaceDetail(response->task_id, response->place_details[0]);
}

void GetDetailResponseHandlerSample::OnFailure(int error_code, const std::string &error_message) {
  LOG_INFO(
      "******************  GetDetailResponseHandler -  OnFailure, "
      "errorCode:[{}], errorMessage:[{}]",
      error_code, error_message);
}

void GetDetailResponseHandlerSample::OnCancel() {
  LOG_INFO("****************** GetDetailResponseHandler -  OnCancel");
}