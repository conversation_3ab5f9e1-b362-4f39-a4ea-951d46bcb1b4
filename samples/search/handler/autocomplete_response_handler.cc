#include "autocomplete_response_handler.h"

#include "logger.h"
#include "result_printer.h"

void AutocompleteResponseHandlerSample::OnSuccess(
    const aurora::search::AutocompleteResponsePtr &response) {
  LOG_INFO(
      "****************** AutocompleteResponseHandler  OnSuccess, taskId:[{}], "
      "size=[{}]",
      response->task_id, response->place_briefs.size());
  PrintPlaceBriefs(response->task_id, response->place_briefs);
}

void AutocompleteResponseHandlerSample::OnFailure(int error_code,
                                                  const std::string &error_message) {
  LOG_INFO(
      "******************  AutocompleteResponseHandler -  OnFailure, "
      "errorCode:[{}], errorMessage:[{}]",
      error_code, error_message);
}

void AutocompleteResponseHandlerSample::OnCancel() {
  LOG_INFO("****************** AutocompleteResponseHandler -  OnCancel");
}