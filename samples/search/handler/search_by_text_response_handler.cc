#include "search_by_text_response_handler.h"

#include "logger.h"
#include "result_printer.h"

void SearchByTextResponseHandlerSample::OnSuccess(
    const aurora::search::SearchByTextResponsePtr& response) {
  LOG_INFO(
      "******************  SearchByTextResponse - OnSuccess, taskId:[{}], "
      "size=[{}]",
      response->task_id, response->place_briefs.size());
  PrintPlaceBriefs(response->task_id, response->place_briefs, 10);
}
void SearchByTextResponseHandlerSample::OnFailure(int error_code,
                                                  const std::string& error_message) {
  LOG_INFO(
      "******************  SearchByTextResponse -  OnFailure, errorCode:[{}], "
      "errorMessage:[{}]",
      error_code, error_message);
}

void SearchByTextResponseHandlerSample::OnCancel() {
  LOG_INFO("******************  SearchByTextResponse -  OnCancel");
}