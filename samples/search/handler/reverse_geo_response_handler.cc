#include "reverse_geo_response_handler.h"

#include "logger.h"
#include "result_printer.h"

void ReverseGeocodeResponseHandlerSample::OnSuccess(
    const aurora::search::ReverseGeocodeResponsePtr& response) {
  LOG_INFO(
      "****************** ReverseGeocodeResponseHandler  OnSuccess, "
      "taskId:[{}],  size=[{}]",
      response->task_id, response->place_briefs.size());
  PrintPlaceBriefs(response->task_id, response->place_briefs);
}

void ReverseGeocodeResponseHandlerSample::OnFailure(int error_code,
                                                    const std::string& error_message) {
  LOG_INFO(
      "******************  ReverseGeocodeResponseHandler -  OnFailure, "
      "errorCode:[{}], errorMessage:[{}]",
      error_code, error_message);
}

void ReverseGeocodeResponseHandlerSample::OnCancel() {
  LOG_INFO("****************** ReverseGeocodeResponseHandler -  OnCancel");
}