
#ifndef SAMPLES_SEARCH_REVERSE_GEO_RESPONSE_HANDLER_H
#define SAMPLES_SEARCH_REVERSE_GEO_RESPONSE_HANDLER_H

#include <memory>

#include "search_def.h"

class ReverseGeocodeResponseHandlerSample final
    : public aurora::search::ReverseGeocodeResponseHandler {
 public:
  void OnSuccess(const aurora::search::ReverseGeocodeResponsePtr& response) override;

  void OnFailure(int error_code, const std::string& error_message) override;

  void OnCancel() override;
};

#endif