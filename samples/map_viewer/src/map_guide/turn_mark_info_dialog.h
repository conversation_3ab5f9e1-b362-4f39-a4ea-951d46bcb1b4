﻿
#ifndef TURN_MARK_INFO_DIALOG_H
#define TURN_MARK_INFO_DIALOG_H

#include <QDockWidget>
#include <QGraphicsScene>
#include "ui_turn_mark_info_dialog.h"
#include "util_type_define.h"
#include "guidance_def.h"


struct tagRgeLaneInfo;

class TurnMarkInfoDialog : public QDockWidget
{
	Q_OBJECT

public:
    TurnMarkInfoDialog(QWidget *parent = 0);
    ~TurnMarkInfoDialog();
	void show();
private:
	void HideLaneInfoCtrl();
	void UpdateDistToGp(uint32 dist_to_gp);
	void UpdateNextName(int status, const char* next_road_name);
	void DoMsgNewGp();
	void DoMsgUpdateGp();
	void DoMsgSimArriveDest();
	USHORT GetViaPntMark(USHORT gp_mark);
	void UpdateLaneInfo();
	void UpdateDestInfo(ULONG ulDist, ULONG ultime);
//	unsigned char LaneTurnToArrow(uint turn_type);
	uint32 CalcHeight();
	void HideSapa();
	void UpdateTmcBar(uint32 dist);

    int LaneTurnToArrow(aurora::guide::LaneAction back_type, aurora::guide::LaneAction front_type );

public slots:
	void do_rge_gp_msg(long event_type, long param);
	void do_update_lane();
    void do_update_guide(QString text);
	void do_update_hwboards();
	void do_update_aircondition(int in_status);
	void do_update_smart_voice();
    void onUpdateLane(aurora::guide::NavigationLaneInfoPtr info);

private:
    Ui::TurnMarkInfoDialog ui;
	QPixmap m_laneDirImg;
	QPixmap m_laneDirLeftImg;
	QGraphicsScene* m_laneScene;
	const char* m_imgPath;
};

#endif // TURN_MARK_INFO_DIALOG_H
