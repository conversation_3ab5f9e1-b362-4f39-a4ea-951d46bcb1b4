

#include "guidance_control.h"
#include <QDebug>
#include "map_widget_manage.h"



GuidanceControl::GuidanceControl(QObject *parent)
{

}


GuidanceControl::~GuidanceControl()
{

}


void GuidanceControl::onGetTBTDetailsInfo()
{
    g_app->showActionListWidget(m_tbt_details);
}


void GuidanceControl::onUpdatePlayTTS(aurora::guide::SoundInfoPtr sound_tts)
{
    if (!sound_tts)
        return;

    qDebug() << "GuidanceControl SoundInfo : " << sound_tts->text.c_str();
    emit update_guide_msg(QString(sound_tts->text.c_str()));

}
