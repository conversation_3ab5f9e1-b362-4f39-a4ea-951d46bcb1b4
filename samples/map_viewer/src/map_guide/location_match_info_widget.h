﻿
#ifndef LOCATION_MATCH_INFO_WIDGET_H
#define LOCATION_MATCH_INFO_WIDGET_H

#include "ui_location_match_info_widget.h"
#include <QDockWidget>



namespace Ui { class LocationMatchInfoWidget; }

/**
	@brief 定位信息显示窗口
*/
class LocationMatchInfoWidget : public QDockWidget
{
	Q_OBJECT

public:
    LocationMatchInfoWidget(QWidget *parent = 0);
    ~LocationMatchInfoWidget();


private:
    Ui::LocationMatchInfoWidget *ui;

};

#endif // LOCATION_MATCH_INFO_WIDGET_H
