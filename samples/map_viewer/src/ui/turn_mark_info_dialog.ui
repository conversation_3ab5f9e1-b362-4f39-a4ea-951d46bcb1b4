<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TurnMarkInfoDialog</class>
 <widget class="QDockWidget" name="TurnMarkInfoDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>253</width>
    <height>468</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>58</width>
    <height>35</height>
   </size>
  </property>
  <property name="allowedAreas">
   <set>Qt::NoDockWidgetArea</set>
  </property>
  <property name="windowTitle">
   <string>Guidance View</string>
  </property>
  <widget class="QWidget" name="widget">
   <widget class="QLabel" name="label">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>91</width>
      <height>25</height>
     </rect>
    </property>
    <property name="text">
     <string>TTS Play</string>
    </property>
   </widget>
   <widget class="QTextBrowser" name="GuidancetextBrowser">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>25</y>
      <width>256</width>
      <height>100</height>
     </rect>
    </property>
   </widget>
   <widget class="UIGpnCtrl" name="widget_turnmark" native="true">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>125</y>
      <width>256</width>
      <height>121</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>0</width>
      <height>0</height>
     </size>
    </property>
    <property name="maximumSize">
     <size>
      <width>400</width>
      <height>800</height>
     </size>
    </property>
   </widget>
   <widget class="UIPicture" name="widget_distTogp" native="true">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>246</y>
      <width>256</width>
      <height>51</height>
     </rect>
    </property>
   </widget>
   <widget class="UIMutiLabel" name="widget_destinfo">
    <property name="geometry">
     <rect>
      <x>1</x>
      <y>348</y>
      <width>251</width>
      <height>51</height>
     </rect>
    </property>
    <property name="text">
     <string>PushButton</string>
    </property>
   </widget>
   <widget class="UIAirConditionerCtrl" name="widget_airconditioner">
    <property name="geometry">
     <rect>
      <x>1</x>
      <y>399</y>
      <width>256</width>
      <height>25</height>
     </rect>
    </property>
   </widget>
   <widget class="UIJamSmartVoiceCtrl" name="widget_jamsmartvoice">
    <property name="geometry">
     <rect>
      <x>1</x>
      <y>424</y>
      <width>256</width>
      <height>25</height>
     </rect>
    </property>
   </widget>
   <widget class="UILaneCtrl" name="widget_laneinfo">
    <property name="geometry">
     <rect>
      <x>1</x>
      <y>684</y>
      <width>256</width>
      <height>192</height>
     </rect>
    </property>
   </widget>
   <widget class="UISapaCtrl" name="widget_sapainfo">
    <property name="geometry">
     <rect>
      <x>1</x>
      <y>882</y>
      <width>256</width>
      <height>192</height>
     </rect>
    </property>
   </widget>
   <widget class="UIPicture" name="widget_nextroad" native="true">
    <property name="geometry">
     <rect>
      <x>1</x>
      <y>297</y>
      <width>256</width>
      <height>51</height>
     </rect>
    </property>
   </widget>
  </widget>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>UIGpnCtrl</class>
   <extends>QWidget</extends>
   <header location="global">uigpnctrl.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>UIPicture</class>
   <extends>QWidget</extends>
   <header location="global">uipicture.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>UILaneCtrl</class>
   <extends>QListView</extends>
   <header location="global">uilanectrl.h</header>
  </customwidget>
  <customwidget>
   <class>UISapaCtrl</class>
   <extends>QListView</extends>
   <header location="global">uisapactrl.h</header>
  </customwidget>
  <customwidget>
   <class>UIAirConditionerCtrl</class>
   <extends>QListView</extends>
   <header location="global">uiairconditionerctrl.h</header>
  </customwidget>
  <customwidget>
   <class>UIJamSmartVoiceCtrl</class>
   <extends>QListView</extends>
   <header location="global">uijamsmartvoicectrl.h</header>
  </customwidget>
  <customwidget>
   <class>UIMutiLabel</class>
   <extends>QPushButton</extends>
   <header location="global">uimutilabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
