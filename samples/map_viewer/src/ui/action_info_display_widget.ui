<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ActionInfoDisplayWidget</class>
 <widget class="QDockWidget" name="ActionInfoDisplayWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>411</width>
    <height>476</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Action Info Display</string>
  </property>
  <widget class="QWidget" name="widget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="DataTreeInfoWidget" name="treeWidget">
      <property name="contextMenuPolicy">
       <enum>Qt::CustomContextMenu</enum>
      </property>
      <property name="verticalScrollMode">
       <enum>QAbstractItemView::ScrollPerPixel</enum>
      </property>
      <property name="rootIsDecorated">
       <bool>true</bool>
      </property>
      <column>
       <property name="text">
        <string>Attribute</string>
       </property>
      </column>
      <column>
       <property name="text">
        <string>Value</string>
       </property>
      </column>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>DataTreeInfoWidget</class>
   <extends>QTreeWidget</extends>
   <header>data_tree_info_widget.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>treeWidget</sender>
   <signal>itemClicked(QTreeWidgetItem*,int)</signal>
   <receiver>ActionInfoDisplayWidget</receiver>
   <slot>onItemSelectionChanged()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>205</x>
     <y>248</y>
    </hint>
    <hint type="destinationlabel">
     <x>205</x>
     <y>237</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <slots>
  <slot>onShowFlexibleAttributesCheckBoxStateChanged(int)</slot>
 </slots>
</ui>
