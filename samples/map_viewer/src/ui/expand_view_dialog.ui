<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>expand_view_dlg</class>
 <widget class="QWidget" name="expand_view_dlg">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>480</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="UIPicture" name="pic_background" native="true">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>405</width>
     <height>392</height>
    </rect>
   </property>
   <property name="visible">
    <bool>true</bool>
   </property>
   <property name="imgname" stdset="0">
    <string>img_mm_00_00_16_01.png</string>
   </property>
  </widget>
  <widget class="UIGpbCtrl" name="uigpbctrl" native="true">
   <property name="geometry">
    <rect>
     <x>2</x>
     <y>2</y>
     <width>400</width>
     <height>421</height>
    </rect>
   </property>
  </widget>
  <widget class="UIProgressBar" name="uiprogressbar">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>430</y>
     <width>270</width>
     <height>48</height>
    </rect>
   </property>
   <property name="autoFillBackground">
    <bool>false</bool>
   </property>
   <property name="value">
    <number>50</number>
   </property>
   <property name="textVisible">
    <bool>false</bool>
   </property>
   <property name="invertedAppearance">
    <bool>true</bool>
   </property>
   <property name="progressRect" stdset="0">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>270</width>
     <height>48</height>
    </rect>
   </property>
   <property name="textRect" stdset="0">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>0</width>
     <height>0</height>
    </rect>
   </property>
   <property name="backgroundImageName" stdset="0">
    <string>progress_back.png</string>
   </property>
   <property name="progressImageName" stdset="0">
    <string>progress_image.png</string>
   </property>
  </widget>
  <widget class="UIButton" name="close_button" native="true">
   <property name="geometry">
    <rect>
     <x>2</x>
     <y>430</y>
     <width>48</width>
     <height>48</height>
    </rect>
   </property>
   <property name="visible">
    <bool>true</bool>
   </property>
   <property name="normalbackimage" stdset="0">
    <string>close_button.png</string>
   </property>
  </widget>
  <widget class="UIPicture" name="uIPicture" native="true">
   <property name="geometry">
    <rect>
     <x>320</x>
     <y>430</y>
     <width>80</width>
     <height>48</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>微软雅黑</family>
     <pointsize>16</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="visible">
    <bool>true</bool>
   </property>
   <property name="text" stdset="0">
    <string/>
   </property>
   <property name="iconrect" stdset="0">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>100</width>
     <height>52</height>
    </rect>
   </property>
   <property name="textrect" stdset="0">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>80</width>
     <height>52</height>
    </rect>
   </property>
   <property name="color" stdset="0">
    <color>
     <red>255</red>
     <green>255</green>
     <blue>255</blue>
    </color>
   </property>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>UIButton</class>
   <extends>QWidget</extends>
   <header>uibutton.h</header>
  </customwidget>
  <customwidget>
   <class>UIGpbCtrl</class>
   <extends>QWidget</extends>
   <header>uigpbctrl.h</header>
  </customwidget>
  <customwidget>
   <class>UIPicture</class>
   <extends>QWidget</extends>
   <header>uipicture.h</header>
  </customwidget>
  <customwidget>
   <class>UIProgressBar</class>
   <extends>QProgressBar</extends>
   <header>uiprogressbar.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>close_button</sender>
   <signal>clicked()</signal>
   <receiver>expand_view_dlg</receiver>
   <slot>closepopmenu()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>25</x>
     <y>365</y>
    </hint>
    <hint type="destinationlabel">
     <x>202</x>
     <y>195</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
