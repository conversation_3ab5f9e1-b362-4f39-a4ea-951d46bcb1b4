﻿
#ifndef ACTION_INFO_DISPLAY_WIDGET_H
#define ACTION_INFO_DISPLAY_WIDGET_H

#include "ui_action_info_display_widget.h"
#include <QDockWidget>
#include "guidance/include/guidance_def.h"
#include "map_widget.h"
#include "qt_ds.h"
#include "online_route.h"

namespace Ui { class ActionInfoDisplayWidget; }

/**
	@brief 定位信息显示窗口
*/
class ActionInfoDisplayWidget : public QDockWidget
{
	Q_OBJECT

public:
    ActionInfoDisplayWidget(QWidget *parent = 0);
    ~ActionInfoDisplayWidget();

	void clearSelectionItem();
    void showTBTInfo(std::vector<aurora::guide::ManeuverDetail> &tbtDetails);
    void updateDisplay(ENUM_MAPTYPE type);

    ENUM_MAPTYPE getMapType() {return m_map_type;}
    void clearTreeWidget() {ui->treeWidget->clear();}

private slots:
	void onItemSelectionChanged();



private:

	void showGpInfo();
    void showIpInfo();
	void showLaneInfos();
	void showHighwayBoardPoints();

    QIcon getTbtInfoIconByType(int &type);
    void showBmapActions();
    void showAmapActions();
    void showBmapOneAction(NaviAction& action, QTreeWidgetItem* item);
    void showAmapOneAction(NaviActionAmap action, QTreeWidgetItem* item);

private:
    Ui::ActionInfoDisplayWidget *ui;

    MapWidget * m_map_widget_ptr;

    std::vector<aurora::guide::ManeuverDetail> *m_tbtDetails_ptr;

    ENUM_MAPTYPE m_map_type;

};

#endif // ACTION_INFO_DISPLAY_WIDGET_H


