

#ifndef LOCATION_CONTROL_H
#define LOCATION_CONTROL_H

#include <QObject>
#include "location/include/location_module.h"
#include "geo_point.h"

class LocationLayer;

typedef std::shared_ptr<aurora::loc::ILocation> LocationPtr ;
typedef aurora::loc::SimulatorController::Config SimulConfig;

class LocationControl : public QObject
{
    Q_OBJECT
public:
    LocationControl(QObject *parent);
    ~LocationControl();

    void setMapEngineLoctionInterFace(LocationPtr interface_ptr);
    bool startSimulation(aurora::path::PathResultPtr path_result_ptr);
    void onClearDriveView();
    void setLoctionLayer(LocationLayer * loction_layer_ptr);

    void setSimulatorConfig(SimulConfig config);

public slots:
    void onUpdateSelectPathId(uint64_t);


private:
   LocationPtr m_loction_interface_ptr;
   LocationLayer * m_loction_layer_ptr;
   std::shared_ptr<aurora::path::PathInfo> m_select_path_ptr;
   SimulConfig m_simulator_config;

   uint64_t m_select_path_id;

};

#endif // LOCATION_CONTROL_H
