﻿
#ifndef MAP_WIDGET_MANAGE_H
#define MAP_WIDGET_MANAGE_H

#include <QApplication>
#include "utils/util_type_define.h"
#include "main_window.h"
#include "link_property_display_widget.h"
#include "view_property_info_widget.h"
#include "location_match_info_widget.h"
#include "dr_fusion_info_widget.h"
#include "action_info_display_widget.h"
#include "search_info_display_widget.h"
#include "traffic_property_info_widget.h"
#include "route_data/route_tile_reader.h"


class MapWidgetManage : public QApplication
{
	Q_OBJECT

public:
    MapWidgetManage(int &argc, char **argv);
    virtual ~MapWidgetManage();

    MainWindow* getMainWindow() { return m_mainWindowPtr; }
	void initWindows();

    void showLinkAttributeWidget(aurora::parser::AugmentEdge * link_ptr, const QString &strlayer_level);
	void hideLinkAttributeWidget();

    void showNodeAttributeWidget(aurora::parser::RouteNode * link_ptr, const QString &strlayer_level);
	void hideNodeAttributeWidget();


    void showTileAttributeWidget(uint64 tile_code);
	void hideTileAttributeWidget();

    void showTraceAttributeWidget();
	void hideTraceAttributeWidget();

	QTreeWidgetItem* showViewAttributeWidget(short type, void* viewData, uint8 i, uint16 j);
	void hideViewAttributeWidget();

    void showLocationInfoWidget();
	void hideLocationInfoWidget();

    void showActionListWidget(std::vector<aurora::guide::ManeuverDetail> &tbt_details);
    void showActionListWidget(ENUM_MAPTYPE type);
	void hideActionListWidget();
    void clearActionListWidget();
    ENUM_MAPTYPE getActionListWidgetMapType();

    void showSearchResultWidget(SearchByTextResponsePtr response);
	void hideSearchResultWidget();

    void showTmcAttributeWidget();
	void hideTmcAttributeWidget();

    void showDrAlgoInfoWidget();
	void hideDrAlgoInfoWidget();
	bool isDrAlgoInfoWidgetHiden();

	void destroy();
    void setRightWidgetWidth(int width);

    void hideAllWidget();

private:
    template<typename T> void initMapWidget(std::shared_ptr<T> &ptr);

private:
    MainWindow* m_mainWindowPtr;

    std::shared_ptr<LinkPropertyDisplayWidget> m_LinkAttributeWidgetPtr;
    std::shared_ptr<ViewPropertyInfoWidget> m_ViewAttributeWidgetPtr;
    std::shared_ptr<LocationMatchInfoWidget> m_LocationInfoWidgetPtr ;
    std::shared_ptr<ActionInfoDisplayWidget> m_ActionListWidgetPtr ;
    std::shared_ptr<SearchInfoDisplayWidget> m_SearchResultWidgetPtr;
    std::shared_ptr<TrafficPropertyInfoWidget> m_TmcAttributeWidgetPtr;
    std::shared_ptr<DrFusionInfoWidget> m_DrAlgoInfoWidgetPtr;

    std::vector<std::shared_ptr<QDockWidget>> m_attribute_widget_ptr_set;

    SearchInfoDisplayWidget * m_search_test;

};

MainWindow* AppGetMainWindow(void);

extern MapWidgetManage* g_app;

#endif // MAP_WIDGET_MANAGE_H
