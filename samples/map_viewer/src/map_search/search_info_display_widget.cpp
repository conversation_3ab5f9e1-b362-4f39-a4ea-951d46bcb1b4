﻿ 
#include "search_info_display_widget.h"
#include "map_widget_manage.h"
#include "main_window.h"
#include "map_engine_manage.h"
#include "route_plan_layer.h"
#include "search_poi_layer.h"

SearchInfoDisplayWidget::SearchInfoDisplayWidget(QWidget *parent)
	: QDockWidget(parent)
{
    ui = new Ui::SearchInfoDisplayWidget();
	ui->setupUi(this);

    m_map_widget_ptr = g_app->getMainWindow()->getMapWidget();
}

SearchInfoDisplayWidget::~SearchInfoDisplayWidget()
{
    if (ui) {
        delete ui;
        ui = nullptr;
    }
}


void SearchInfoDisplayWidget::updateDisplay(SearchByTextResponsePtr response)
{

    ui->treeWidget->clear();

    if (!response) {
        return;
    }

//   uint32 result_cnt = response->place_briefs.size();
//    QTreeWidgetItem* item = new QTreeWidgetItem(ui->treeWidget);
//    item->setText(0, QString("search_result %1").arg(result_cnt));
//    item->setText(1, QString::number(result_cnt));
    for (auto poi : response->place_briefs)
    {
        showPoiInfo(poi);
    }

    //ui->treeWidget->expandToDepth(2);
    ui->treeWidget->resizeColumnToContents(0);
}

void SearchInfoDisplayWidget::showPoiInfo(const aurora::search::PlaceBrief & poi)
{
    QTextCodec* code = QTextCodec::codecForLocale();
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
    QString text;

    QTreeWidgetItem* item = new QTreeWidgetItem(ui->treeWidget);
    text = QString::fromLocal8Bit(poi.name.c_str());
    item->setText(0, text);

    text = QString("ID:  %1").arg(QString::fromLocal8Bit(poi.id.c_str()));
    QTreeWidgetItem *sub_item = new QTreeWidgetItem(item);
    sub_item->setText(0, text);

    text = QString("Address:  %1").arg(QString::fromLocal8Bit(poi.address.c_str()));
    sub_item = new QTreeWidgetItem(item);
    sub_item->setText(0, text);

    text = QString("Category:  %1").arg(QString::fromLocal8Bit(poi.category.c_str()));
    sub_item = new QTreeWidgetItem(item);
    sub_item->setText(0, text);



    text = QString("Lat:  %1").arg(QString::number(poi.location.lat(), 'f', 12));
    sub_item = new QTreeWidgetItem(item);
    item->setData(0, Qt::UserRole + 1, poi.location.lat()); // Lat
    sub_item->setText(0, text);

    text = QString("Lng:  %1").arg(QString::number(poi.location.lng(), 'f', 12));
    sub_item = new QTreeWidgetItem(item);
    item->setData(0, Qt::UserRole + 2, poi.location.lng()); // Lng
    sub_item->setText(0, text);


    QTextCodec::setCodecForLocale(code);
}

void SearchInfoDisplayWidget::onItemSelectionChanged()
{
	QTreeWidgetItem* item = ui->treeWidget->currentItem(); // 获取当前节点
	if (item == NULL) // 没有选择节点
		return;

    bool isTopLevelItem = (item->parent() == nullptr);
    if (!isTopLevelItem)
        return;

    uint32 idx = ui->treeWidget->indexOfTopLevelItem(item);
    m_map_widget_ptr->getSearchPoiLayer()->setSelectIdx(idx);

}

void SearchInfoDisplayWidget::onTreeWidgetContextMenu(const QPoint& pos)
{
    QTreeWidgetItem* item = ui->treeWidget->itemAt(pos);
    if (item == NULL)
        return;

    // 判断是否是顶级项（主item）
     bool isTopLevelItem = (item->parent() == nullptr);

     // 如果需要仅处理顶级项，可添加以下逻辑
     if (!isTopLevelItem)
         return;

    if (item->text(0).isEmpty() && item->data(0, Qt::DecorationRole).isNull())
        return;

    QMenu menu(this);

 //   QAction* action = menu.addAction("&Copy Value");
//    connect(action, &QAction::triggered, this, [item]() {
//        int col = 1;
//        if (item->data(col, Qt::DecorationRole).isNull())
//            QApplication::clipboard()->setText(item->text(col));
//        else
//            QApplication::clipboard()->setPixmap(item->data(col, Qt::DecorationRole).value<QPixmap>());
//    });


    QAction* action = menu.addAction("&Set Start");
    connect(action, &QAction::triggered, this, [this, item]() {
        double lat = item->data(0, Qt::UserRole + 1).toDouble();
        double lng = item->data(0, Qt::UserRole + 2).toDouble();
        RGeoPoint pos(lat, lng);
        this->m_map_widget_ptr->getRoutePlanLayer()->onSetStartPoint(&pos);
    });


    action = menu.addAction("&Set Way");
    connect(action, &QAction::triggered, this, [this, item]() {
        double lat = item->data(0, Qt::UserRole + 1).toDouble();
        double lng = item->data(0, Qt::UserRole + 2).toDouble();
        RGeoPoint pos(lat, lng);
        this->m_map_widget_ptr->getRoutePlanLayer()->onSetWayPoint(&pos);
    });

    action = menu.addAction("&Set End");
    connect(action, &QAction::triggered, this, [this, item]() {
        double lat = item->data(0, Qt::UserRole + 1).toDouble();
        double lng = item->data(0, Qt::UserRole + 2).toDouble();
        RGeoPoint pos(lat, lng);
        this->m_map_widget_ptr->getRoutePlanLayer()->onSetEndPoint(&pos);
    });

    QPoint menu_pos;
    menu_pos.setX(pos.x());
    menu_pos.setY(pos.y() + 40);
    menu.exec(mapToGlobal(menu_pos));
}
