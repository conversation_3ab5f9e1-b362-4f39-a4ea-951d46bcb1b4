﻿
#ifndef UTIL_FUNTION_TOOLS_H
#define UTIL_FUNTION_TOOLS_H

#include <vector>
#include <QString>
#include <QStringList>
#include <QPointF>
#include "util_type_define.h"
#include "geo_point.h"
#include "geo_rect.h"

namespace util
{

    #define JUST_ANY_ANGLE	INVALID_UINT32 // free angle
    #define  _180_PI   0.0174532925199432954743716805978692718782
    #define _PI_180   57.2957795130823228646477218717336654663086

    #define  DegToRad(d)	  ((d)*_180_PI)
    #define  RadToDeg(r)	  ((r)*_PI_180)

enum ProjPointType
{
    kProjStart = 0,
    kProjMiddle = 1,
    kProjEnd = 2,
    kProjInvalid = 3
};

enum DirectionType
{
    kLinkDirectionFree = 0,
    kLinkDirectionBackward = 1,
    kLinkDirectionForward = 2,
    kLinkDirectionBothForbid = 3,
    kLinkDirectionCount,
};

	inline const char* GetResourceFolder() { return ":MainWindow/resources/"; }

    QString NumToHousMilSec(int numMilSec);
    QString NumToHousSec(int numSec);
	int HoursMinSecToNum(QString hoursMinSec);
	QString NumToKmM(int numMeter);
	int KmMToNum(QString kmM);

	QString PercentDiff(double num1, double num2);

	unsigned int GetZoomLevelByDistance(unsigned long distance);

	float DegreeToRadiant(float angle);

	float LatitudeToMeter(float distance);

	float GeodesicDistance(float lon1, float lat1, float lon2, float lat2);

	bool ClampInt(int& value, int min, int max);

    bool PointProjectToPolyline(const std::vector<map_engine::RGeoPoint> &Polyline,
                                  const uint16 &point_num,
                                  const map_engine::RGeoPoint &gps,
                                  DirectionType _dir_type,
                                  uint32 angle,
                                  ProjPointType &prj_type,
                                  double &prj_distance);

    double pointToPolylineDistance(const map_engine::GeoPoint& p, const std::vector<map_engine::GeoPoint>& polyline);
    double pointToPolylineDistance(const QPointF& p, const std::vector<QPointF>& polyline);

    int32 distance(const map_engine::GeoPoint& p1, const map_engine::GeoPoint& p2);
    double distance(const QPointF& p1, const QPointF& p2) ;

    bool isRectIntersecting(const map_engine::Rect& r1, const map_engine::Rect& r2);

    double projectPointToSegment(const QPointF& p, const QPointF& p1, const QPointF& p2);
    double distanceToSegment(const QPointF& p, const QPointF& p1, const QPointF& p2);


    template<class T>
    class RenderLinkNoObj
    {
    public:
        QPointF screen_point;
        T       no;
        int     no_len;
        int     is_render;

        RenderLinkNoObj() :screen_point(), no(), no_len(0), is_render(0)
        {}

        RenderLinkNoObj(QPointF& pt, T& num) :screen_point(pt),
            no(num), no_len(0), is_render(0)
        {}

        RenderLinkNoObj(QPointF& pt, T& num, int len) :screen_point(pt),
            no(num), no_len(len), is_render(0)
        {}


        map_engine::Rect GenerateRangeRect(int font_size)
        {
            return map_engine::Rect(screen_point.x(), screen_point.y(), screen_point.x() + no_len * font_size, screen_point.y() + font_size);
        }
    };

    typedef std::vector<RenderLinkNoObj<QString> > RenderLinkStringSet;


} // namespace util

#endif // UTIL_FUNTION_TOOLS_H
