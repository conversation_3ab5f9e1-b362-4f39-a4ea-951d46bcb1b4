﻿
#include "util_funtion_tools.h"
#include <cmath>
#include <QTextCodec>
#include <float.h>

using std::vector;

static const int LATITUDE_TO_METER = 111195;
const double REAL_PRECISION = 0.00000001;

namespace util
{

    QString NumToHousMilSec(int numMilSec) //毫秒
	{
#if defined(WIN32) || defined(_WIN32_WCE)
		QTextCodec *tc = QTextCodec::codecForName("GBK");
#else
		QTextCodec *tc = QTextCodec::codecForName("UTF-8");
#endif
        if (!numMilSec)
			//return QString::fromLocal8Bit("0秒");
			return tc->toUnicode(QByteArray("0毫秒"));

		QString ret;

        int numSec = numMilSec / 1000;
        int hour = numSec / 3600;
		int min = numSec / 60 - hour * 60;
		int sec = numSec - hour * 3600 - min * 60;
        int mini_sec = numMilSec - (hour * 3600 + min * 60 + sec) * 1000;

		//ret += (hour ? QString::number(hour) + QString::fromLocal8Bit("小时") : "");
		ret += (hour ? QString::number(hour) + tc->toUnicode(QByteArray("小时")) : "");
		//ret += (min ? QString::number(min) + QString::fromLocal8Bit("分钟") : "");
		ret += (min ? QString::number(min) + tc->toUnicode(QByteArray("分钟")) : "");
		//ret += (sec ? QString::number(sec) + QString::fromLocal8Bit("秒") : "");
		ret += (sec ? QString::number(sec) + tc->toUnicode(QByteArray("秒")) : "");

		ret += (mini_sec ? QString::number(mini_sec) + tc->toUnicode(QByteArray("毫秒")) : "");
		return ret;
	}

    QString NumToHousSec(int numSec) //秒
    {
#if defined(WIN32) || defined(_WIN32_WCE)
        QTextCodec *tc = QTextCodec::codecForName("GBK");
#else
        QTextCodec *tc = QTextCodec::codecForName("UTF-8");
#endif
        if (!numSec)
            //return QString::fromLocal8Bit("0秒");
            return tc->toUnicode(QByteArray("0秒"));

        QString ret;

        int hour = numSec / 3600;
        int min = numSec / 60 - hour * 60;
        int sec = numSec - hour * 3600 - min * 60;

        //ret += (hour ? QString::number(hour) + QString::fromLocal8Bit("小时") : "");
        ret += (hour ? QString::number(hour) + tc->toUnicode(QByteArray("小时")) : "");
        //ret += (min ? QString::number(min) + QString::fromLocal8Bit("分钟") : "");
        ret += (min ? QString::number(min) + tc->toUnicode(QByteArray("分钟")) : "");
        //ret += (sec ? QString::number(sec) + QString::fromLocal8Bit("秒") : "");
        ret += (sec ? QString::number(sec) + tc->toUnicode(QByteArray("秒")) : "");
        return ret;
    }

	int HoursMinSecToNum(QString hoursMinSec)
	{
#if defined(WIN32) || defined(_WIN32_WCE)
		QTextCodec *tc = QTextCodec::codecForName("GBK");
#else
		QTextCodec *tc = QTextCodec::codecForName("UTF-8");
#endif
		if (hoursMinSec.isNull() || hoursMinSec.isEmpty())
			return 0;

		//int hour_idx = hoursMinSec.indexOf(QString::fromLocal8Bit("小时"));
		int hour_idx = hoursMinSec.indexOf(tc->toUnicode(QByteArray("小时")));
		//int min_idx = hoursMinSec.indexOf(QString::fromLocal8Bit("分钟"));
		int min_idx = hoursMinSec.indexOf(tc->toUnicode(QByteArray("分钟")));
		//int sec_idx = hoursMinSec.indexOf(QString::fromLocal8Bit("秒"));
		int sec_idx = hoursMinSec.indexOf(tc->toUnicode(QByteArray("秒")));
		int hour = 0;
		int min = 0;
		int sec = 0;
		if (min_idx < 0)
		{
			if (hour_idx < 0)
			{
				sec = hoursMinSec.mid(0, sec_idx).toInt();
			}
			else
			{
				hour = hoursMinSec.mid(0, hour_idx).toInt();
				sec = hoursMinSec.mid(hour_idx + 2, sec_idx - hour_idx - 2).toInt();
			}		
		}
		else if (hour_idx < 0)
		{
			min = hoursMinSec.mid(0, min_idx).toInt();
			sec = hoursMinSec.mid(min_idx + 2, sec_idx - min_idx - 2).toInt();
		}
		else
		{
			hour = hoursMinSec.mid(0, hour_idx).toInt();
			min = hoursMinSec.mid(hour_idx + 2, min_idx - hour_idx - 2).toInt();
			sec = hoursMinSec.mid(min_idx + 2, sec_idx - min_idx - 2).toInt();
		}

		return hour * 3600 + min * 60 + sec;
	}

	QString NumToKmM(int numMeter)
	{
#if defined(WIN32) || defined(_WIN32_WCE)
		QTextCodec *tc = QTextCodec::codecForName("GBK");
#else
		QTextCodec *tc = QTextCodec::codecForName("UTF-8");
#endif
		if (!numMeter)
			//return QString::fromLocal8Bit("0米");
			return tc->toUnicode(QByteArray("0米"));

		QString ret;

		int km = numMeter / 1000;
		int m = numMeter % 1000;

		//ret += (km ? QString::number(km) + QString::fromLocal8Bit("公里") : "");
		ret += (km ? QString::number(km) + tc->toUnicode(QByteArray("公里")) : "");
		//ret += (m ? QString::number(m) + QString::fromLocal8Bit("米") : "");
		ret += (m ? QString::number(m) + tc->toUnicode(QByteArray("米")) : "");

		return ret;
	}

	int KmMToNum(QString kmM)
	{
#if defined(WIN32) || defined(_WIN32_WCE)
		QTextCodec *tc = QTextCodec::codecForName("GBK");
#else
		QTextCodec *tc = QTextCodec::codecForName("UTF-8");
#endif
		if (kmM.isNull() || kmM.isEmpty())
			return 0;

		//int km_idx = kmM.indexOf(QString::fromLocal8Bit("公里"));
		int km_idx = kmM.indexOf(tc->toUnicode(QByteArray("公里")));
		//int m_idx = kmM.indexOf(QString::fromLocal8Bit("米"));
		int m_idx = kmM.indexOf(tc->toUnicode(QByteArray("米")));
		int km = 0;
		int m = 0;
		if (km_idx < 0)
		{
			km = kmM.mid(0, km_idx).toInt();
		}
		else
		{
			km = kmM.mid(0, km_idx).toInt();
			m = kmM.mid(km_idx + 2, m_idx - km_idx - 2).toInt();
		}

		return km * 1000 + m;
	}

	QString PercentDiff(double num1, double num2)
	{
		QString ret("(");

		if (num1 == 0.0)
		{
			if (num2 == 0.0)
				ret += "0.0%";
			else
				ret += "Inf%";
		}
		else
		{
			ret += QString::number((num2 - num1) / num1 * 100, 'g', 4) + "%";
		}
		ret += ")";

		return ret;
	}

	unsigned int GetZoomLevelByDistance(unsigned long distance)
	{
		if (distance < 500)
		{
			return 18;
		}
		else if (distance < 1000)
		{
			return 17;
		}
		else if (distance < 2000)
		{
			return 16;
		}
		else if (distance < 4000)
		{
			return 15;
		}
		else if (distance < 8000)
		{
			return 14;
		}
		else if (distance < 16000)
		{
			return 13;
		}
		else if (distance < 32000)
		{
			return 12;
		}
		else if (distance < 64000)
		{
			return 11;
		}
		else if (distance < 128000)
		{
			return 10;
		}
		else if (distance < 256000)
		{
			return 9;
		}
		else if (distance < 512000)
		{
			return 8;
		}
		else if (distance < 1024000)
		{
			return 7;
		}
		else if (distance < 2048000)
		{
			return 6;
		}
		else
		{
			return 5;
		}
	}

	float DegreeToRadiant(float angle)
	{
		return angle * 0.01745329252;
	}

	float LatitudeToMeter(float distance)
	{
		return distance * LATITUDE_TO_METER;
	}

	float GeodesicDistance(float lon1, float lat1, float lon2, float lat2)
	{
		float dLon = lon2 - lon1;
		float dLat = lat2 - lat1;
		float sx1 = cos(DegreeToRadiant(lat1));

		if (dLat < 0.1)
		{
			return LatitudeToMeter(sqrt((dLon * dLon * sx1 * sx1) + (dLat * dLat)));
		}
		else
		{
			float sx2 = cos(DegreeToRadiant(lat2));
			float sx = (sx1 + sx2) / 2;
			return LatitudeToMeter(sqrt((dLon * dLon * sx * sx) + (dLat * dLat)));
		}
	}

	bool ClampInt(int& value, int min, int max)
	{
		if (value < min)
		{
			value = min;
			return true;
		}

		if (value > max)
		{
			value = max;
			return true;
		}

		return false;
	}


    /*
    * calculate the score of match
    */
    inline static float64 ObtainProjectScore(const float64 proj_bearing, const float64 angle_deviation)
    {
        return proj_bearing + angle_deviation;
    }

    /// Get the vector norm
    /**
    @param [in]v The vector
    @return the vector norm
    */
    inline static double Norm(const map_engine::RGeoPoint &v)
    {
        return sqrt((double)v.CoordX_ * v.CoordX_ + (double)v.CoordY_ * v.CoordY_);
    }

    inline static double Norm(const map_engine::RGeoPoint &pt0, const map_engine::RGeoPoint &pt1)
    {
        double cx = pt0.CoordX_ - pt1.CoordX_;
        double cy = pt0.CoordY_ - pt1.CoordY_;
        return sqrt(cx * cx + cy * cy);
    }

    /// Get the vector norm's square
    /**
    @param [in]v The vector
    @return the vector norm's square
    */
    inline static double Norm2(const map_engine::RGeoPoint &v)
    {
        return (double)v.CoordX_ * v.CoordX_ + (double)v.CoordY_ * v.CoordY_;
    }

    inline static double Unit(const map_engine::RGeoPoint &first, const map_engine::RGeoPoint &second, double &x, double &y)
    {
        double norm = Norm(first, second);
        if (norm <= 1e-06)
        {
            norm = x = y = 0.;
        }
        else
        {
            x = 1.0*(second.CoordX_ - first.CoordX_) / norm;
            y = 1.0*(second.CoordY_ - first.CoordY_) / norm;
        }
        return norm;
    }

    inline static double UnitAngle(const double &x, const double &y)
    {
        if (y < 0)
            return (2.*M_PI - acos(x));
        else
            return acos(x);
    }

    inline static double Angle(const map_engine::RGeoPoint &first, const map_engine::RGeoPoint &second)
    {
        double x, y;
        Unit(first, second, x, y);
        return UnitAngle(x, y);
    }

    inline static double BearingAngle(const map_engine::RGeoPoint &first, const map_engine::RGeoPoint &second)
    {
        double angle = 360 - RadToDeg(Angle(first, second)) + 90;
        while (angle >= 360)
        {
            angle -= 360;
        }
        return angle;
    }

    inline static double Dot(const map_engine::RGeoPoint &first, const map_engine::RGeoPoint &second)
    {
        return ((double)first.CoordX_ * second.CoordX_ + (double)first.CoordY_ * second.CoordY_);
    }

    inline double PointProjLine(const map_engine::RGeoPoint &start,
                                const map_engine::RGeoPoint &end,
                                const map_engine::RGeoPoint &point,
                                ProjPointType &type,
                                map_engine::RGeoPoint &proj_point,
                                map_engine::RGeoPoint &proj_point_dm6)
    {
        double factor;
        map_engine::RGeoPoint first = point - start;
        map_engine::RGeoPoint second = end - start;

        double length_square = Norm2(start - end);
        if (length_square <= REAL_PRECISION && length_square >= -REAL_PRECISION)
        {
            factor = 0.0;
        }
        else
        {
            factor = Dot(first, second) / length_square;
        }

        if (factor < 0.0)
        {
            proj_point = start;
            proj_point_dm6.CoordX_ = start.CoordX_ * 10;
            proj_point_dm6.CoordY_ = start.CoordY_ * 10;
            type = kProjStart;
        }
        else if (factor > 1.0)
        {
            proj_point = end;
            proj_point_dm6.CoordX_ = end.CoordX_ * 10;
            proj_point_dm6.CoordY_ = end.CoordY_ * 10;
            type = kProjEnd;
        }
        else
        {
            proj_point.CoordX_ = (int32)(start.CoordX_ + second.CoordX_ * factor);
            proj_point.CoordY_ = (int32)(start.CoordY_ + second.CoordY_ * factor);
            proj_point_dm6.CoordX_ = (start.CoordX_ + second.CoordX_ * factor) * 10;
            proj_point_dm6.CoordY_ = (start.CoordY_ + second.CoordY_ * factor) * 10;
            type = kProjMiddle;
        }

        first = point - proj_point;
        return Norm(first);
    }

    bool PointProjectToPolyline(const std::vector<map_engine::RGeoPoint> &Polyline,
                                  const uint16 &point_num,
                                  const map_engine::RGeoPoint &gps,
                                  DirectionType _dir_type,
                                  uint32 angle,
                                  ProjPointType &prj_type,
                                  double &prj_distance)
    {
        float64 max_score = DBL_MAX;
        float64 score;
        DirectionType dir_type = kLinkDirectionBothForbid;
        ProjPointType type = kProjInvalid;
        map_engine::RGeoPoint pt;
        map_engine::RGeoPoint pt_dm6;
        float64 bearing = 0.0;


        float64 angle_deviation = 0.0;
        for (uint16 idx = 0; idx < point_num - 1; idx++)
        {
            float64 distance = 0.0;
            map_engine::RGeoPoint cur_point(Polyline[idx].CoordX_, Polyline[idx].CoordY_);
            map_engine::RGeoPoint next_point(Polyline[idx + 1].CoordX_, Polyline[idx + 1].CoordY_);
            distance += PointProjLine(cur_point, next_point, gps, type, pt, pt_dm6);

            //obtain angle
            dir_type = _dir_type;
            if (kLinkDirectionForward == _dir_type)
            {
                bearing = BearingAngle(cur_point, next_point);
            }
            else if (kLinkDirectionBackward == _dir_type)
            {
                bearing = BearingAngle(next_point, cur_point);
            }
            else
            {
                //assert(0);
            }

            //angle_deviation = AngleDiff(bearing, gps.bearing);
            angle_deviation = 0.0; //待有角度再补充

            //obtain score
            if (angle == JUST_ANY_ANGLE)
            {
                score = ObtainProjectScore(distance, 0);
            }
            else
            {
                score = ObtainProjectScore(distance, angle_deviation);
            }

            if (type != kProjMiddle)
            {
                score += distance;
            }

            if (score < max_score)
            {
                prj_distance = distance;
                prj_type = type;
            }
        }
        return true;

    }

    struct Point {
        Point(double x, double y) : x(x),y(y)
        {}
        double x, y;
    };
    // 计算点到线段的距离
    double pointToSegmentDistance(const Point& p, const Point& a, const Point& b) {
        // 线段向量 ab
        double abX = b.x - a.x;
        double abY = b.y - a.y;

        // 点到起点向量 ap
        double apX = p.x - a.x;
        double apY = p.y - a.y;

        // 线段长度的平方
        double abLen2 = abX * abX + abY * abY;

        // 计算投影比例 t
        double t = std::max(0.0, std::min(1.0, (apX * abX + apY * abY) / abLen2));

        // 计算投影点
        double projectionX = a.x + t * abX;
        double projectionY = a.y + t * abY;

        // 计算点到投影点的距离
        double dx = p.x - projectionX;
        double dy = p.y - projectionY;

        return std::sqrt(dx * dx + dy * dy);
    }

    // 计算点到Polyline的距离
    double pointToPolylineDistance(const map_engine::GeoPoint& p, const std::vector<map_engine::GeoPoint>& polyline)
    {
        if (polyline.size() < 2) return -1.0; // 至少需要两个点形成线段

        double minDist = DBL_MAX;
        Point cur_pos(p.CoordX_, p.CoordY_);

        // 遍历每条线段
        for (size_t i = 0; i < polyline.size() - 1; ++i) {
            const Point a(polyline[i].CoordX_, polyline[i].CoordY_);
            const Point b(polyline[i+1].CoordX_, polyline[i+1].CoordY_);

            double dist = pointToSegmentDistance(cur_pos, a, b);
            if (dist < minDist) {
                minDist = dist;
            }
        }

        return minDist;
    }

    double pointToPolylineDistance(const QPointF& p, const std::vector<QPointF>& polyline)
    {
        if (polyline.size() < 2) return -1.0; // 至少需要两个点形成线段

        double minDist = DBL_MAX;
        Point cur_pos(p.x(), p.y());

        // 遍历每条线段
        for (size_t i = 0; i < polyline.size() - 1; ++i) {
            const Point a(polyline[i].x(), polyline[i].y());
            const Point b(polyline[i+1].x(), polyline[i+1].y());

            double dist = pointToSegmentDistance(cur_pos, a, b);
            if (dist < minDist) {
                minDist = dist;
            }
        }

        return minDist;
    }

    // 计算平面上两点间的欧几里得距离
    int32 distance(const map_engine::GeoPoint& p1, const map_engine::GeoPoint& p2)
    {
        int32 dx = p2.CoordX_ - p1.CoordX_;
        int32 dy = p2.CoordY_ - p1.CoordY_;
        return std::sqrt(dx * dx + dy * dy);
    }

    double distance(const QPointF& p1, const QPointF& p2)
    {
        double dx = p2.x() - p1.x();
        double dy = p2.y() - p1.y();
        return std::sqrt(dx * dx + dy * dy);
    }

    bool isRectIntersecting(const map_engine::Rect& r1, const map_engine::Rect& r2)
    {
        // 直接判断边界是否重叠（无间隙）
        return !(r1.CoordX2 < r2.CoordX1 ||   // r1在r2左侧
                 r1.CoordX1 > r2.CoordX2 ||   // r1在r2右侧
                 r1.CoordY2 < r2.CoordY1 ||   // r1在r2上方
                 r1.CoordY1 > r2.CoordY2);    // r1在r2下方
    }

    // 计算点p到线段p1-p2的投影点，并返回投影点在p1-p2上的参数t(0≤t≤1表示投影在线段内)
    double projectPointToSegment(const QPointF& p, const QPointF& p1, const QPointF& p2)
    {
        double dx = p2.x() - p1.y();
        double dy = p2.y() - p1.y();
        double lenSq = dx * dx + dy * dy;

        // 如果线段长度为0，直接返回p1
        if (lenSq == 0) return 0.0;

        // 计算投影参数t
        double t = ((p.x() - p1.x()) * dx + (p.y() - p1.y()) * dy) / lenSq;
        return t;
    }

    // 计算点p到线段p1-p2的最短距离
    double distanceToSegment(const QPointF& p, const QPointF& p1, const QPointF& p2)
    {
        double t = projectPointToSegment(p, p1, p2);

        // 限制t在[0,1]范围内，表示只考虑线段本身，而非延长线
        if (t < 0) t = 0;
        if (t > 1) t = 1;

        // 计算投影点
        QPointF projection = {
            p1.x() + t * (p2.x() - p1.x()),
            p1.y() + t * (p2.y() - p1.y())
        };

        // 返回投影点到p的距离
        double dx = p.x() - projection.x();
        double dy = p.y() - projection.y();
        return std::sqrt(dx * dx + dy * dy);
    }

} // namespace util
