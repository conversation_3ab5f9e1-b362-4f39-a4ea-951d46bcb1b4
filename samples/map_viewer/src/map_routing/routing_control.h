
#ifndef ROUTING_CONTROL_H
#define ROUTING_CONTROL_H

#include <QObject>
#include <QFile>
#include "utils/util_type_define.h"
#include "geo_point.h"
#include "geo_rect.h"
#include "path/include/path_module.h"
#include "path/include/path_def.h"
#include "guidance/include/guidance_def.h"
#include "qt_ds.h"
#include "online_route.h"



#define DEST_NUM_MAX 5 // 最多选点个数，注意，包括起点
#define AVOID_RECT_CNT_MAX 3  // 最多回避区域个数
#define AVOID_LINK_CNT_MAX 10 // 最多回避link个数
#define TIME_SECTION_MAX_CNT 10 // 算路耗时区间段个数
static uint32 s_time_section[TIME_SECTION_MAX_CNT] = { 100, 200, 300, 500, 1000, 1500, 2000, 3000, 5000, 8000 };

class RoutePathTestListener;
class RoutePlanLayer;
class RenderEngineLayer;
class MapWidget;

struct RouteLocationInfo
{

    RouteLocationInfo& operator = (const RouteLocationInfo& rhs)
    {
        if (this != &rhs)
        {
            prov_city_name = rhs.prov_city_name;
            dist_city_name = rhs.dist_city_name;
            dist_Code = rhs.dist_Code;
            dist_latlng = rhs.dist_latlng;
        }

        return *this;
    }

    QString prov_city_name;
    QString dist_city_name;
    QString dist_Code;
    map_engine::RGeoPoint dist_latlng;

};

struct RouteFileTestInfo
{
    RouteFileTestInfo()
    {
        path_cacl_success = false;
        path_dis = 0.0;
        path_time = 0.0;
        cacl_time = 0.0;
        paths_count = 0;
    }
    std::string path_uuid;
    RouteLocationInfo start_loc;
    RouteLocationInfo end_loc;
    double path_dis;
    double path_time;
    uint64_t cacl_time;
    bool path_cacl_success;
    int paths_count;
    std::string path_tag;

};

class RoutingControl : public QObject
{
    Q_OBJECT

public:
    RoutingControl(QObject *parent);
    ~RoutingControl();

//    void setStart(const map_engine::RGeoPoint* pos);
//    void setEnd(const map_engine::RGeoPoint* pos);
//    void addWayPoint(const map_engine::RGeoPoint* pos);
    void StartPathCalculation();
    aurora::path::PathResultPtr getRoutePathResult() {return m_path_result_ptr;}

    void clearRoutePath();
    void clearRoutePathAndPoints();

    void setMapLayer(MapWidget* map_widget);
    void setCompareWithBaidu(bool compare);
    bool getCompareWithBaidu(){ return m_compareWithBaidu; }
    void setCompareWithAmap(bool compare);
    bool getCompareWithAmap(){ return m_compareWithAmap; }
    const std::vector<float64>& getCompRouteBmap() { return m_comp_route_bmap; }
    const std::vector<float64>& getCompRouteAmap() { return m_comp_route_amap; }
    const QString& getRouteInfoBmap(){ return m_route_info_bmap; }
    const QString& getRouteInfoAmap(){ return m_route_info_amap; }
    void clearCompRouteBmap(){ m_comp_route_bmap.clear(); m_route_info_bmap.clear(); }
    void clearCompRouteAmap(){ m_comp_route_amap.clear(); m_route_info_amap.clear(); }
    aurora::path::PathStrategy getRule();
    std::shared_ptr<OnlineRoute> getOnlineRoute() {return m_online_Route_ptr; }

    bool setFileTestData(QString &fileName);
    void setRouteRule(uint8 rule_idx);

signals:
    void locationChanged();
    void avoidAreaChanged(map_engine::Rect* rect);
    void addTmcLink(uint64 uuid);
    void updataGuideInfo(int point, QString type);
    void simulationInfoChanged();
    void selectedFileChanged();
    void selectedLocChanged(int idx);
    void updateFileTestResultInfo(QString info);
    void updateFileTestLableInfo(QString info);
    void routePlanChanged();
    void routeResultChanged();
    void updateSelectPathId(uint64_t);

public slots:
    void onGetRoutePathResult(const aurora::path::PathQueryPtr& query, const aurora::path::PathResultPtr& result);
    void updateMapRoute(vector<float64>& route, QString routeInfo, ENUM_MAPTYPE type);

private:
    void setFileTestRoutePlan(std::vector<RouteLocationInfo> &file_test_input_set);
    void initTestOutputFile(int type = 0);
    void closeTestFile(int sucess_count, int fail_count);
    void emitSignalAndStartRouting();
    void onlineMapRoute(ENUM_MAPTYPE type);
    aurora::path::PathStrategy getRouteRule(uint8 index);
    void setMapRoute(std::vector<float64>& route, QString routeInfo, ENUM_MAPTYPE type);

public:
    bool m_is_file_test_stop;

private:
    std::shared_ptr<aurora::path::PathQuery> m_path_query_ptr;
    std::shared_ptr<aurora::path::PathLandmark> m_start_point_ptr;
    std::shared_ptr<aurora::path::PathLandmark> m_end_point_ptr;

    std::vector<RouteLocationInfo> m_file_test_input_data_set;
    std::vector<RouteFileTestInfo> m_file_test_output_result_set;

    uint8 m_route_rule_idx;
    RoutePlanLayer * m_route_path_layer_ptr;
    aurora::path::PathResultPtr m_path_result_ptr;
    std::shared_ptr<OnlineRoute> m_online_Route_ptr;



    ///////////////////////////////////////////////////////////////////////////// for test

    //std::shared_ptr<aurora::path::PathModule> m_path_module_test_ptr[10];
    std::shared_ptr<aurora::path::PathModule> m_path_module_test_ptr;
    std::shared_ptr<RoutePathTestListener> m_path_test_listener_ptr;
    std::shared_ptr<aurora::path::PathInterface> m_path_test_interface_ptr;
    aurora::path::PathQueryPtr m_file_test_path_query_ptr;
    QFile m_input_file;
    QFile m_result_file;
    uint64_t m_average_cacl_time;
    uint64_t m_max_cacl_time;
    RouteFileTestInfo * m_max_cacl_time_data_ptr;
    ///////////////////////////////////////////////////////////////////////////// for test

    bool m_compareWithBaidu;
    bool m_compareWithAmap;
    QString m_amap_key;
    QString m_bmap_key;

    std::vector<float64> m_comp_route_bmap;
    QString m_route_info_bmap;

    std::vector<float64> m_comp_route_amap;
    QString m_route_info_amap;






};


#endif //ROUTING_CONTROL_H
