

#include "routing_path.h"


RoutingPath::RoutingPath(MapWidget * map_widget)
    : m_map_widget_ptr(map_widget)
{
   reset();

}

RoutingPath::~RoutingPath()
{

}

void RoutingPath::reset()
{
    m_select_index = -1;
    m_select_index_last = -1;
    m_path_set.clear();
    m_start_point.clear();
    m_end_point.clear();
    m_way_point.clear();
}

void RoutingPath::setPathLableName(RoutePathType type, const QString &info, std::shared_ptr<QLabel> name_flag)
{
    initPathFlag(type, m_map_widget_ptr, name_flag_w, name_flag_h, info, name_flag);
}

void RoutingPath::highlightNameFlag(std::shared_ptr<QLabel> name_flag, bool is_highlight)
{
    if (!name_flag)
        return;

    QPalette palette = name_flag->palette();
    if (is_highlight) {
        QPixmap pixmap(":/MainWindow/resources/Routing_flag_highlight.png");
        if (pixmap.isNull())
            return;

        palette.setBrush(QPalette::Window, QBrush(
              pixmap.scaled(
                  name_flag->size(),
                  Qt::KeepAspectRatio,
                  Qt::SmoothTransformation
              )
          ));
    } else {
        QPixmap pixmap(":/MainWindow/resources/Routing_flag.png");
        if (pixmap.isNull())
            return;

        palette.setBrush(QPalette::Window, QBrush(
              pixmap.scaled(
                  name_flag->size(),
                  Qt::KeepAspectRatio,
                  Qt::SmoothTransformation
              )
          ));

    }

    name_flag->setPalette(palette);
}


void RoutingPath::initPathFlag(RoutePathType type,  MapWidget * map_widget_ptr, const int &w, const int &h, const QString info, std::shared_ptr<QLabel> name_flag)
{
    if (!name_flag)
        return;

    name_flag->setCursor(Qt::PointingHandCursor);
    name_flag->setParent(map_widget_ptr);
    name_flag->setFixedSize(w, h);

    // 加载图片并等比例缩放至Label大小
    QPixmap pixmap(":/MainWindow/resources/Routing_flag.png");
    if (pixmap.isNull())
        return;

    // 使用QPalette设置背景图片
    QPalette palette = name_flag->palette();
    palette.setBrush(QPalette::Window, QBrush(
        pixmap.scaled(w, h, Qt::KeepAspectRatio, Qt::SmoothTransformation)
    ));
    name_flag->setPalette(palette);

    // 启用自动填充背景
    name_flag->setAutoFillBackground(true);

    // 设置字体（通过QFont类）
     QFont font;
     font.setPointSize(18);  // 设置字体大小为
     //font.setBold(true);     // 粗体
     name_flag->setFont(font);

     //label.setStyleSheet("color: red;")

     if (type == RPT_PATH_BMAP) {
         QString color = QString("color: ") + bmap_color;
         name_flag->setStyleSheet(color);
     } else if (type == RPT_PATH_AMAP) {
         QString color = QString("color: ") + amap_color;
         name_flag->setStyleSheet(color);
     }


    name_flag->setText(info);
    name_flag->setAlignment(Qt::AlignCenter);
    name_flag->setContentsMargins(0, 0, 0, 20);  // 底部增加边距，文字上移
    name_flag->setVisible(true);

}

uint64_t RoutingPath::getSelectPathID()
{
    int cout = m_path_set.size();
    if (m_select_index < 0 && m_select_index >= cout && m_path_set.size() == 0)
        return 0;

    auto &path_item = m_path_set.at(m_select_index);

    if (path_item.type != RPT_PATH_ENGINE)
        return 0;
    else
        return path_item.path_id;

}

int RoutingPath::getSelectPathIndex()
{
    return m_select_index;
}

bool RoutingPath::isPathSelectChange(int index)
{
    if (index != m_select_index_last) {
        m_select_index = index;
        m_select_index_last = index;
        return true;
    }

    return false;
}

RoutePointInfo * RoutingPath::addRoutePoint(map_engine::RGeoPoint *latlng_pos, const RoutePointType type)
{
    if (!latlng_pos)
        return nullptr;

    RoutePointInfo point;
    point.type = type;
    point.latlon_pos = *latlng_pos;

    switch (point.type) {
    case RPT_START_POINT:
    {
        m_start_point.clear();

        point.image_flag_ptr = std::make_shared<QImage>();
        point.image_flag_ptr->load(":/MainWindow/resources/s.png");
        m_start_point.push_back(point);
        return &m_start_point.back();

    }break;
    case RPT_END_POINT:
    {
        m_end_point.clear();

        point.image_flag_ptr = std::make_shared<QImage>();
        point.image_flag_ptr->load(":/MainWindow/resources/e.png");
        m_end_point.push_back(point);
        return &m_end_point.back();

    }break;
    case RPT_WAY_POINT:
    {
        point.image_flag_ptr = std::make_shared<QImage>();
        point.image_flag_ptr->load(":/MainWindow/resources/w.png");
        m_way_point.push_back(point);
        return &m_way_point.back();
    }break;
    default:
    {

    }
    }

    return nullptr;

}

void RoutingPath::removeRoutePoint(const RoutePointType type, const int index)
{
    switch (type) {
    case RPT_START_POINT:
    {
        m_start_point.clear();

    }break;
    case RPT_END_POINT:
    {
        m_end_point.clear();

    }break;
    case RPT_WAY_POINT:
    {
        int cout = m_way_point.size();
        if (index < cout)
        {
            auto itr = m_way_point.begin() + index;
            m_way_point.erase(itr);
        }
    }break;
    default:
    {

    }
    }

}

RoutePointSet * RoutingPath::getRoutePoint( const RoutePointType type)
{
    switch (type) {
    case RPT_START_POINT:
    {
        return &m_start_point;

    }break;
    case RPT_END_POINT:
    {
        return &m_end_point;

    }break;
    case RPT_WAY_POINT:
    {
        return &m_way_point;

    }break;
    default:
    {

    }
    }

    return nullptr;
}

void RoutingPath::removeAllRoutePoint()
{
    m_start_point.clear();
    m_end_point.clear();
    m_way_point.clear();

}

void RoutingPath::removeAllRoutePath()
{
    m_path_set.clear();
    m_select_index = -1;
    m_select_index_last = -1;
}

void RoutingPath::removeRoutePath(RoutePathType type, uint64_t id)
{
    int index = -1;
    for (size_t i = 0; i < m_path_set.size(); i++) {
        auto item = m_path_set.at(i);
        if (type == RPT_PATH_ENGINE && item.type == type) {
            if (item.path_id == id) {
                index = i;
                break;
            }

        } else if (type == RPT_PATH_BMAP && item.type == type) {
            index = i;
            break;
        } else if (type == RPT_PATH_AMAP && item.type == type) {
            index = i;
            break;
        }

    }

    if (index != -1) {
       m_path_set.erase(m_path_set.begin() + index);
    }

}

void RoutingPath::addRoutePath(RoutePathInfo &path)
{
    int index = -1;
    for (size_t i = 0; i < m_path_set.size(); i++) {
        auto item = m_path_set.at(i);
        if (path.type == RPT_PATH_BMAP && item.type == path.type) {
            index = i;
            break;
        } else if (path.type == RPT_PATH_AMAP && item.type == path.type) {
            index = i;
            break;
        }

    }

    if (index != -1) {
       m_path_set.erase(m_path_set.begin() + index);
    }

    m_path_set.push_back(path);
}

RoutePathInfo * RoutingPath::getRoutePath(int index)
{
    int cout = m_path_set.size();
    if (index < 0 ||index >= cout)
       return nullptr;

    return &m_path_set.at(index);

}
