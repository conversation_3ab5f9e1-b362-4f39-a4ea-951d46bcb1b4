

#include "routing_control.h"
#include <sstream>
#include "iostream"
#include "base/include/pointll.h"
#include "path/include/path_def.h"
#include "path/include/path_module.h"
#include "map_engine_manage.h"
#include "map_widget_manage.h"
#include "render_engine_layer.h"
#include "route_plan_layer.h"

using namespace aurora::path;

const QString ROUTE_TEST_RESULT_FILE_NAME("route_test_result.txt");


RoutingControl::RoutingControl(QObject *parent)
    : m_route_path_layer_ptr(nullptr)
{
    m_average_cacl_time = 0.0;
    m_max_cacl_time = 0.0;
    m_route_rule_idx = 0;
    m_is_file_test_stop = false;
    m_max_cacl_time_data_ptr = nullptr;
    m_path_query_ptr = std::make_shared<PathQuery>();
    m_path_query_ptr->strategy = PathStrategy::kTimeFirst;
    m_start_point_ptr = std::make_shared<PathLandmark>();
    m_end_point_ptr = std::make_shared<PathLandmark>();

    QSettings s((MapEngineManage::PREFERENCES_FILE_NAME), QSettings::IniFormat);
    s.beginGroup("map");
    m_compareWithBaidu = s.value("compare_with_baidu", false).toBool();
    m_compareWithAmap = s.value("compare_with_amap", false).toBool();
    s.endGroup();

    m_online_Route_ptr = std::make_shared<OnlineRoute>();
    connect(m_online_Route_ptr.get(), &OnlineRoute::mapRoute, this, &RoutingControl::updateMapRoute);

    s.beginGroup("baidu_route");
    m_amap_key = s.value("amap_key", 0).toString();
    m_bmap_key = s.value("bmap_key", 0).toString();
    s.endGroup();

    m_online_Route_ptr->setAmapKey(m_amap_key);
    m_online_Route_ptr->setBmapKey(m_bmap_key);


}

RoutingControl::~RoutingControl()
{

}


void RoutingControl::setMapLayer(MapWidget* map_widget)
{
    if (map_widget) {
        m_route_path_layer_ptr = map_widget->getRoutePlanLayer();
    }
}

void RoutingControl::onGetRoutePathResult(const aurora::path::PathQueryPtr& query, const aurora::path::PathResultPtr& result)
{
    m_path_result_ptr = result;

    if (m_route_path_layer_ptr) {
        m_route_path_layer_ptr->setRoutePathResult(query, result);
    }

    emit routePlanChanged();
    emit routeResultChanged();
}


PathStrategy RoutingControl::getRouteRule(uint8 index)
{
    if (index == 0) {
        return PathStrategy::kTimeFirst;
    } else if (index == 1) {
        return PathStrategy::kDistanceFirst;
    } else if (index == 2) {
        return PathStrategy::KHighWayFirst;
    } else if (index == 3) {
        return PathStrategy::kAvoidToll;
    }

    return PathStrategy::kDistanceFirst;
}

void RoutingControl::StartPathCalculation()
{
    if (!m_route_path_layer_ptr)
        return;

    RoutePointSet *start_point = m_route_path_layer_ptr->getRoutePoint(RPT_START_POINT);
    RoutePointSet *end_point = m_route_path_layer_ptr->getRoutePoint(RPT_END_POINT);
    RoutePointSet *way_point = m_route_path_layer_ptr->getRoutePoint(RPT_WAY_POINT);

    //int des_cout = end_point->size() + way_point->size();
    if (start_point->size() ==0 || end_point->size() == 0)
        return; //没有有效的起点和终点


    //start route path
    m_path_query_ptr->path_points.clear();

    m_path_query_ptr->strategy = getRouteRule(m_route_rule_idx);

    if (start_point->size() > 0) {
        auto &pos = start_point->at(0).latlon_pos;
        m_start_point_ptr->valid = true;
        m_start_point_ptr->waypoint_type = WayPointType::kStartPoint;
        m_start_point_ptr->pt = aurora::PointLL(pos.CoordX_, pos.CoordY_);
        m_path_query_ptr->path_points.push_back(m_start_point_ptr);
    }

    if (way_point->size() > 0) {
        for (size_t i = 0; i < way_point->size(); i++) {
            auto &pos = way_point->at(i).latlon_pos;
            std::shared_ptr<aurora::path::PathLandmark> waypoint_ptr = std::make_shared<aurora::path::PathLandmark>();
            waypoint_ptr->valid = true;
            waypoint_ptr->waypoint_type = WayPointType::kViaPoint;
            waypoint_ptr->pt = aurora::PointLL(pos.CoordX_, pos.CoordY_);
            m_path_query_ptr->path_points.push_back(waypoint_ptr);
        }

    }

    if (end_point->size() > 0) {
        auto &pos = end_point->at(0).latlon_pos;
        m_end_point_ptr->valid = true;
        m_end_point_ptr->waypoint_type = WayPointType::kEndPoint;
        m_end_point_ptr->pt = aurora::PointLL(pos.CoordX_, pos.CoordY_);
        m_path_query_ptr->path_points.push_back(m_end_point_ptr);
    }

    if (MapEngineManage::GetInstance() && MapEngineManage::GetInstance()->getRouterPathProvider()) {
           MapEngineManage::GetInstance()->getRouterPathProvider()->RequestPath(m_path_query_ptr);
    }

    emitSignalAndStartRouting();

}

void RoutingControl::clearRoutePathAndPoints()
{
    g_app->clearActionListWidget();
    g_app->hideActionListWidget();
    m_route_path_layer_ptr->clearRoutePathAndPoints();
    m_path_result_ptr = nullptr;
}

void RoutingControl::clearRoutePath()
{
    g_app->clearActionListWidget();
    m_route_path_layer_ptr->clearRoutePath();
}


RouteFileTestInfo * g_get_path_result_ptr = nullptr;

class RoutePathTestListener : public PathListener {
public:
    RoutePathTestListener() {
      ready_ = false;
    }

    void Reset() {
      std::unique_lock<std::mutex> l(mutex_);
      ready_ = false;
    }

    void Wait() {
      std::unique_lock<std::mutex> l(mutex_);
      while (!ready_) {
        cv_.wait(l);
      }
    }

    void OnPathResult(const PathQueryPtr& query, const PathResultPtr& result) override
    {
        if (result && g_get_path_result_ptr)
        {
            if (g_get_path_result_ptr->path_uuid == result->uuid)
            {
                if (result->paths.size() > 0) {
                    auto &item = result->paths.at(0);
                    g_get_path_result_ptr->path_cacl_success = true;
                    g_get_path_result_ptr->paths_count = result->paths.size();
                    g_get_path_result_ptr->path_dis = item.length;
                    g_get_path_result_ptr->path_time = item.travel_time;
                    g_get_path_result_ptr->cacl_time = result->metadata.query_time_ms;
                    g_get_path_result_ptr->path_tag = result->tag;
                }
            }
        }

        {
            std::lock_guard<std::mutex> lock(mutex_);
            ready_ = true;
            cv_.notify_one();
        }

    }

private:
  bool ready_;
  std::mutex mutex_;
  std::condition_variable cv_;

 };

bool RoutingControl::setFileTestData(QString &fileName)
{

    if (!m_path_test_listener_ptr)
    {
        connect(this , &RoutingControl::updateFileTestResultInfo, g_app->getMainWindow(), &MainWindow::onUpdateRouteFileTestResultInfo, Qt::QueuedConnection);
        connect(this , &RoutingControl::updateFileTestLableInfo, g_app->getMainWindow(), &MainWindow::onUpdateRouteFileTestLableInfo, Qt::QueuedConnection);

        m_path_test_listener_ptr = std::make_shared<RoutePathTestListener>();
        m_path_module_test_ptr = std::make_shared<aurora::path::PathModule>();
        m_path_module_test_ptr->Prepare(MapEngineManage::GetInstance()->m_route_cacl_config_path.toStdString());
        m_path_module_test_ptr->SetParams({{"data_dir", MapEngineManage::GetInstance()->m_route_data_path.toStdString()}});
        auto module_finder = [](aurora::ModuleId id) {
                return nullptr;
            };
        m_path_module_test_ptr->Init(module_finder);
        m_path_module_test_ptr->Start();

        // get path interface
        m_path_test_interface_ptr = std::dynamic_pointer_cast<PathInterface>(m_path_module_test_ptr->GetInterface());
        m_path_test_interface_ptr->AddPathListener(m_path_test_listener_ptr);
        m_file_test_path_query_ptr = std::make_shared<PathQuery>();
    }

    QString localFileName = fileName;
    std::thread([this, localFileName]
    {

      QFile file(localFileName);
      if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
          emit updateFileTestLableInfo(QString("Can't open the file!"));
          return false;
      }

      m_file_test_input_data_set.clear();

      emit updateFileTestLableInfo(QString("Reading file ..."));

      QTextStream in(&(file));
      QString line;
      if (in.readLine().isEmpty()) {
          qDebug() << "文件为空或第一行已跳过";
       }

      while (!in.atEnd())
      {
          if (m_is_file_test_stop)
          {
              file.close();
              return false;
          }

          line = in.readLine();
          if (line.trimmed().isEmpty()) continue;  // 跳过空行

          QStringList fields = line.split(",");
          if (fields.size() < 12) {
              qDebug() << "数据格式错误，跳过该行:" << line;
              continue;
          }

          RouteLocationInfo info;
          info.prov_city_name = fields[0];
          info.dist_city_name = fields[8];
          info.dist_Code = fields[9];
          info.dist_latlng.CoordX_ = fields[10].toDouble();
          info.dist_latlng.CoordY_ = fields[11].toDouble();

          m_file_test_input_data_set.push_back(info);
      }

      file.close();

      if (m_file_test_input_data_set.size() == 0) {
          emit updateFileTestLableInfo(QString("File is empty!"));
          return false;
      }

         this->setFileTestRoutePlan(m_file_test_input_data_set);
       }).detach();

    return true;
}



void RoutingControl::setFileTestRoutePlan(std::vector<RouteLocationInfo> &file_test_input_set)
{
    if (file_test_input_set.size() == 0)
        return;

    int sucess_count = 0;
    int fail_count = 0;
    initTestOutputFile();
    m_average_cacl_time = 0.0;
    m_max_cacl_time = 0.0;
    uint64_t total_cacl_time = 0.0;
    m_max_cacl_time_data_ptr = nullptr;
    m_file_test_output_result_set.clear();
    for (size_t i = 0 ; i < file_test_input_set.size(); i++)
    {
        if (m_is_file_test_stop)
            return;

        QString lable_info = QString("Test data is being prepared: %1/%2")
                .arg(i)
                .arg(file_test_input_set.size());
        emit updateFileTestLableInfo(lable_info);

        RouteLocationInfo &start_loc = file_test_input_set.at(i);
        for (size_t j = 0; j < file_test_input_set.size(); j++)
        {
            RouteLocationInfo &end_loc = file_test_input_set.at(j);
            if (start_loc.dist_Code == end_loc.dist_Code)
                continue;

           RouteFileTestInfo test_data;
           test_data.start_loc = start_loc;
           test_data.end_loc = end_loc;
           m_file_test_output_result_set.push_back(test_data);

        }
    }

    size_t totel_count = m_file_test_output_result_set.size();
    for (size_t i = 0; i < totel_count; i++)
    {
        if (m_is_file_test_stop) {
             closeTestFile(sucess_count, fail_count);
             return;
        }

        auto &item = m_file_test_output_result_set.at(i);

        m_file_test_path_query_ptr->path_points.clear();
        PathLandmarkPtr start_land_mark = std::make_shared<PathLandmark>();;
        PathLandmarkPtr end_land_mark = std::make_shared<PathLandmark>();;

        start_land_mark->valid = true;
        start_land_mark->waypoint_type = WayPointType::kStartPoint;
        start_land_mark->pt = aurora::PointLL(item.start_loc.dist_latlng.CoordX_, item.start_loc.dist_latlng.CoordY_);

        end_land_mark->valid = true;
        end_land_mark->waypoint_type = WayPointType::kEndPoint;
        end_land_mark->pt = aurora::PointLL(item.end_loc.dist_latlng.CoordX_, item.end_loc.dist_latlng.CoordY_);

        m_file_test_path_query_ptr->path_points.push_back(start_land_mark);
        m_file_test_path_query_ptr->path_points.push_back(end_land_mark);

        g_get_path_result_ptr = &item;
        m_path_test_listener_ptr->Reset();

        item.path_uuid = m_path_test_interface_ptr->RequestPath(m_file_test_path_query_ptr);

        {
            m_path_test_listener_ptr->Wait();

            QString start_name = item.start_loc.prov_city_name + item.start_loc.dist_city_name;
            QString end_name = item.end_loc.prov_city_name + item.end_loc.dist_city_name;
            QString result_info = QString("%1->%2,dis:%3m,time:%4s")
                           .arg(start_name)
                           .arg(end_name)
                           .arg(item.path_dis)
                           .arg(item.path_time);

            if (item.path_cacl_success)
                sucess_count++;
            else
               fail_count ++;

            QString lable_info = QString("Paths: %1/%2, success: %3, fail:%4")
                    .arg(i)
                    .arg(totel_count)
                    .arg(sucess_count)
                    .arg(fail_count);

            emit updateFileTestResultInfo(result_info);
            emit updateFileTestLableInfo(lable_info);

            {
                total_cacl_time += item.cacl_time;
                if (sucess_count > 0)
                    m_average_cacl_time = total_cacl_time/sucess_count;

                if (item.cacl_time > m_max_cacl_time) {
                    m_max_cacl_time = item.cacl_time;
                    m_max_cacl_time_data_ptr = &item;
                }

                char route_result[2048] = {0};
                sprintf(route_result, "%22s(%f,%f) %22s(%f,%f) %22s %13f %17f %10llu\n",
                        start_name.toStdString().c_str(),
                        item.start_loc.dist_latlng.CoordX_,
                        item.start_loc.dist_latlng.CoordY_,
                        end_name.toStdString().c_str(),
                        item.end_loc.dist_latlng.CoordX_,
                        item.end_loc.dist_latlng.CoordY_,
                        item.path_tag.c_str(),
                        item.path_dis,
                        item.path_time,
                        (unsigned long long)item.cacl_time);

                m_result_file.write(QByteArray(route_result));
                m_result_file.flush();

            }

        }
    }
    closeTestFile(sucess_count, fail_count);

}

void RoutingControl::initTestOutputFile(int type )
{
    m_result_file.setFileName(ROUTE_TEST_RESULT_FILE_NAME);
    if (!m_result_file.open(QIODevice::ReadWrite | QIODevice::Truncate | QIODevice::Text))
    {
        qDebug("Can't open the route test result file!");
    }
    char head[2048] = { 0 };
    sprintf(head, "%22s %22s %22s %13s %17s %10s\n", "起点(lng，lat)", "终点(lng，lat)", "算路策略", "距离(米)", "时间(秒)", "耗时(毫秒)");
    m_result_file.write(QByteArray(head));
    m_result_file.flush();
}

void RoutingControl::closeTestFile(int sucess_count, int fail_count)
{
    if (!m_result_file.isOpen())
        return;

    uint32 total_cnt = sucess_count + fail_count;
    float32 success_rate =  (total_cnt == 0) ? 0 : ((float32)sucess_count / total_cnt) * 100;

    char stat[2048] = {0};
    sprintf(stat, "\n算路%d次, 成功%d次, 失败%d次, 成功率%.2f%%\n平均耗时%llu毫秒, \n最大耗时%llu毫秒:",
            total_cnt,
            sucess_count,
            fail_count,
            success_rate,
            (unsigned long long)m_average_cacl_time,
            (unsigned long long)m_max_cacl_time);
    m_result_file.write(stat);
    m_result_file.flush();

    if (m_max_cacl_time_data_ptr) {
        char max_data[2048] = {0};
        QString start_name = m_max_cacl_time_data_ptr->start_loc.prov_city_name + m_max_cacl_time_data_ptr->start_loc.dist_city_name;
        QString end_name = m_max_cacl_time_data_ptr->end_loc.prov_city_name + m_max_cacl_time_data_ptr->end_loc.dist_city_name;
        sprintf(max_data, "%22s(%f,%f) %22s(%f,%f) %22s %13f %17f %10llu\n",
                start_name.toStdString().c_str(),
                m_max_cacl_time_data_ptr->start_loc.dist_latlng.CoordX_,
                m_max_cacl_time_data_ptr->start_loc.dist_latlng.CoordY_,
                end_name.toStdString().c_str(),
                m_max_cacl_time_data_ptr->end_loc.dist_latlng.CoordX_,
                m_max_cacl_time_data_ptr->end_loc.dist_latlng.CoordY_,
                m_max_cacl_time_data_ptr->path_tag.c_str(),
                m_max_cacl_time_data_ptr->path_dis,
                m_max_cacl_time_data_ptr->path_time,
                (unsigned long long)m_max_cacl_time_data_ptr->cacl_time);

        m_result_file.write(max_data);
        m_result_file.flush();
    }

    m_result_file.close();
}



void RoutingControl::setRouteRule(uint8 rule_idx)
{
    m_route_rule_idx = rule_idx;}


void RoutingControl::setCompareWithBaidu(bool compare)
{
    m_compareWithBaidu = compare;
    if (compare) {
       emitSignalAndStartRouting();
    } else {
        if (!m_route_path_layer_ptr)
            return;

        m_route_path_layer_ptr->removeRoutePath(RPT_PATH_BMAP);
    }
}

void RoutingControl::setCompareWithAmap(bool compare)
{
    m_compareWithAmap = compare;
    if (compare) {
        emitSignalAndStartRouting();
    } else {
        if (!m_route_path_layer_ptr)
            return;

        m_route_path_layer_ptr->removeRoutePath(RPT_PATH_AMAP);
    }
}

void RoutingControl::emitSignalAndStartRouting()
{
    if (m_compareWithBaidu)
    {
        // 百度地图路线比较
        onlineMapRoute(BAIDU_MAP);
    }

    if (m_compareWithAmap)
    {
        // 高德地图路线比较
        onlineMapRoute(AMAP);
    }
}

void RoutingControl::onlineMapRoute(ENUM_MAPTYPE type)
{
    if (!m_route_path_layer_ptr)
        return;

    RoutePointSet *start_point = m_route_path_layer_ptr->getRoutePoint(RPT_START_POINT);
    RoutePointSet *end_point = m_route_path_layer_ptr->getRoutePoint(RPT_END_POINT);
    RoutePointSet *way_point = m_route_path_layer_ptr->getRoutePoint(RPT_WAY_POINT);

    //int des_cout = end_point->size() + way_point->size();
    if (start_point->size() ==0 || end_point->size() == 0)
        return; //没有有效的起点和终点

    if (start_point->size() > 0) {
        auto &pos = start_point->at(0).latlon_pos;
        m_online_Route_ptr->setOriginX(pos.CoordX_);
        m_online_Route_ptr->setOriginY(pos.CoordY_);
    }

    if (end_point->size() > 0) {
        auto &pos = end_point->at(0).latlon_pos;
        m_online_Route_ptr->setDestinationX(pos.CoordX_);
        m_online_Route_ptr->setDestinationY(pos.CoordY_);
    }

    m_online_Route_ptr->clearWaypoints();
    int way_cout = way_point->size() > 10 ? 10 : way_point->size(); //在线算路最多支持10个
    for (int i = 0; i < way_cout; i++) {
        auto &pos = way_point->at(i).latlon_pos;
        m_online_Route_ptr->setWaypointsX(pos.CoordX_);
        m_online_Route_ptr->setWaypointsY(pos.CoordY_);
    }

    if (getRouteRule(m_route_rule_idx) == PathStrategy::kTimeFirst) // time
    {
        m_online_Route_ptr->setTactics(10); // 最少时间
        m_online_Route_ptr->setStrategy(0);
    }
    else if (getRouteRule(m_route_rule_idx) == PathStrategy::KHighWayFirst) //  highway first
    {
        m_online_Route_ptr->setTactics(13); // 高速优先
        m_online_Route_ptr->setStrategy(6);
    }
    else if (getRouteRule(m_route_rule_idx) == PathStrategy::kDistanceFirst) // distance
    {
        m_online_Route_ptr->setTactics(12); // 最短路径
        m_online_Route_ptr->setStrategy(2);
    } else if (getRouteRule(m_route_rule_idx) == PathStrategy::kAvoidToll)
    {
        m_online_Route_ptr->setTactics(11); // 少收费
        m_online_Route_ptr->setStrategy(9);

    } else // other
    {
        m_online_Route_ptr->setTactics(10);
        m_online_Route_ptr->setStrategy(0);
    }

    m_online_Route_ptr->makeRequest(type);
}

void RoutingControl::updateMapRoute(vector<float64>& route, QString routeInfo, ENUM_MAPTYPE type)
{
    static bool baidu_route_over = false;
    static bool amap_route_over = false;

    if (type == BAIDU_MAP)
    {
        baidu_route_over = true;
    }
    else if (type == AMAP)
    {
        amap_route_over = true;
    }

    setMapRoute(route, routeInfo, type);
    emit routePlanChanged();
    emit routeResultChanged();

}

void RoutingControl::setMapRoute(std::vector<float64>& route, QString routeInfo, ENUM_MAPTYPE type)
{
    if (type == BAIDU_MAP)
    {
        m_comp_route_bmap.clear();
        m_comp_route_bmap.insert(m_comp_route_bmap.begin(), route.begin(), route.end());
        m_route_info_bmap = routeInfo;
    }
    else if (type == AMAP)
    {
        m_comp_route_amap.clear();
        m_comp_route_amap.insert(m_comp_route_amap.begin(), route.begin(), route.end());
        m_route_info_amap = routeInfo;
    }

    if (m_route_path_layer_ptr && (route.size() > 0)) {
        m_route_path_layer_ptr->setOnlineMapRoute(route, type);
    }

}

aurora::path::PathStrategy RoutingControl::getRule()
{

    return getRouteRule(m_route_rule_idx);

}

