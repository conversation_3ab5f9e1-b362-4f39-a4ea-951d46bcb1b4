
#ifndef ROUTING_PATH_H
#define ROUTING_PATH_H

#include <QLabel>
#include <QPainter>
#include "map_widget.h"
#include "geo_point.h"
#include "map_camera.h"

const int flag_w = 64;
const int flag_h = 64;
const int name_flag_w = 100;
const int name_flag_h = 100;

const QString amap_color = "#BA55D3";
const QString bmap_color = "#FFA500";

enum RoutePointType {
    RPT_START_POINT = 0,
    RPT_END_POINT = 1,
    RPT_WAY_POINT = 2,
    RPT_POINT_INVAID
};

enum RoutePathType {
    RPT_PATH_ENGINE = 0,
    RPT_PATH_BMAP = 1,
    RPT_PATH_AMAP ,
    RPT_PATH_INVAID
};

struct RoutePathInfo {
    RoutePathInfo()
    {
        path_id = 0;
        type = RPT_PATH_INVAID;
        length= 0.0;
        travel_time = 0.0;
        traffic_light_num = 0;
    }

    uint64_t path_id;
    RoutePathType type;
    double length;                   // 路径总长度，单位m
    double travel_time;              // 时间花费，单位s
    uint32_t traffic_light_num;      // 途经红绿灯数量
    std::shared_ptr<QLabel> label_name_ptr;
    std::vector<map_engine::RGeoPoint> path_latlon_set;
};

struct RoutePointInfo {
    RoutePointInfo()
    {
        type = RPT_POINT_INVAID;
        mark_id = -1;
    }

    void drawFlag(QPainter& painter, MapCamera& camera)
    {
        int w,h;
        camera.getScreenSize(w,h);

        QPointF screenPos;
        camera.worldToScreen(&latlon_pos, screenPos);

        if (screenPos.x() < 0 || screenPos.x() > w || screenPos.y() < 0 || screenPos.y() > h)
            return;

         painter.drawImage(screenPos.x() - (flag_w/2), screenPos.y() - flag_h, *image_flag_ptr.get());
    }

    RoutePointType type;
    map_engine::RGeoPoint latlon_pos;
    std::shared_ptr<QImage> image_flag_ptr;
    int mark_id;
};

typedef  std::vector<RoutePointInfo> RoutePointSet;

class RoutingPath
{
public:
    RoutingPath(MapWidget * map_widget);
    ~RoutingPath();

    void reset();

    void setPathLableName(RoutePathType type, const QString &info, std::shared_ptr<QLabel> name_flag);
    void highlightNameFlag(std::shared_ptr<QLabel> name_flag, bool is_highlight);

    bool isPathSelectChange(int index);
    int getSelectPathIndex();
    uint64_t getSelectPathID();

    RoutePointInfo * addRoutePoint(map_engine::RGeoPoint *point, const RoutePointType type);
    void removeAllRoutePoint();
    void removeRoutePoint(const RoutePointType type, const int index = 0);
    RoutePointSet * getRoutePoint( const RoutePointType type);

    void removeAllRoutePath();
    void removeRoutePath(RoutePathType type, uint64_t id = 0);
    void addRoutePath(RoutePathInfo &path);
    RoutePathInfo * getRoutePath(int index);


private:
    void initPathFlag(RoutePathType type,  MapWidget * map_widget_ptr, const int &w, const int &h, const QString info, std::shared_ptr<QLabel> name_flag);

public:

    MapWidget * m_map_widget_ptr;

    RoutePointSet m_start_point;
    RoutePointSet m_end_point;
    RoutePointSet m_way_point;

    std::vector<RoutePathInfo> m_path_set;

private:
    int m_select_index;
    int m_select_index_last;


};


#endif //ROUTING_PATH_H
