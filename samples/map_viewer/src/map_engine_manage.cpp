
#include "map_engine_manage.h"

#include <iostream>
#include "utils/util_type_define.h"
#include <QCoreApplication>
#include <QDir>
#include <QString>
#include <QSettings>
#include "search/include/search_def.h"
#include "map_widget_manage.h"
#include "location_layer.h"

const double DEF_LON = 121.3160468637943;
const double DEF_LAT = 31.189158148023324;

using namespace aurora;
using namespace aurora::path;
using namespace aurora::loc;

MapEngineManage* MapEngineManage::_instance = nullptr;
const QString MapEngineManage::PREFERENCES_FILE_NAME("preferences.ini");

class RoutePathListener : public PathListener {
public:
    void OnPathResult(const PathQueryPtr& query, const PathResultPtr& result) override {
        emit MapEngineManage::GetInstance()->getRoutePathResult(query, result);
     }
 };

class GetSearchByTextResponseHandler : public aurora::search::SearchByTextResponseHandler {
 public:

  void OnSuccess(
          const std::shared_ptr<aurora::search::SearchByTextResponse> &response)
  {
     MapEngineManage::GetInstance()->m_poi_search_response_ptr = response;

     int cout = response->place_briefs.size();
     QString info = QString("%1 %2 %3")
             .arg("finished , get")
             .arg(QString::number(cout))
             .arg("results");

     emit MapEngineManage::GetInstance()->updatePoiSearchStatusInfo(info);
     emit MapEngineManage::GetInstance()->getPoiResultList();
  }

  void OnFailure(int errorCode, const std::string &errorMessage)
  {

      QString info = QString("%1 %2 %3 %4%5")
              .arg("fail, error(code:")
              .arg(QString::number(errorCode))
              .arg(",message:")
              .arg(errorMessage.c_str())
              .arg(")");

      emit MapEngineManage::GetInstance()->updatePoiSearchStatusInfo(info);
  }

  void OnCancel(){}
};

class MyGuidanceListener : public guide::INavigationListener
{
public:
    virtual ~MyGuidanceListener() = default;
    virtual void OnPathManeuverDetail(int64_t path_id, const std::vector<guide::ManeuverDetail> &details)
    {
        std::shared_ptr<GuidanceControl> gc_ptr = MapEngineManage::GetInstance()->m_guidance_control_ptr;
        if (gc_ptr) {

            gc_ptr->m_tbt_details.clear();
            for (auto item : details) {
                guide::ManeuverDetail TbtItem;
                TbtItem.name = item.name;
                TbtItem.type = item.type;
                TbtItem.action = item.action;
                TbtItem.length = item.length;
                TbtItem.time = item.time;
                TbtItem.begin_name = item.begin_name;
                TbtItem.end_name = item.end_name;
                for (auto pos : item.geos) {
                    TbtItem.geos.push_back(pos);
                }
                gc_ptr->m_tbt_details.push_back(TbtItem);
            }

        }
      emit MapEngineManage::GetInstance()->getTBTDetailsInfo();
      std::cout  << "receive OnPathManeuverDetail : " << details.size() << std::endl;
    }

    /**
     * @brief 导航到达目的地
     */
    virtual void OnNavigationArrive(const guide::NavigationMode type)
    {
        qDebug() << "MyGuidanceListener OnNavigationArrive : type = " <<  static_cast<int>(type);
        emit MapEngineManage::GetInstance()->navigationArrive();
    }

    /**
     * @brief 导航开始
     */
    virtual void OnNavigationStart(const guide::NavigationMode type, uint64_t path_id)
    {
         qDebug() << "MyGuidanceListener OnNavigationStart : type = " <<  static_cast<int>(type) << ", path_id = "  << path_id;
    }

    /**
     * @brief 导航结束
     */
    virtual void OnNavigationStop(const guide::NavigationMode type, const guide::NavigationStopCode code)
    {
        qDebug() << "MyGuidanceListener OnNavigationStop : type = " <<  static_cast<int>(type) << ", code = "  << static_cast<int>(code);
    }

    /**
     * @brief 路口放大图信息显示、隐藏
     */
    virtual void OnShowJunctionView(guide::JunctionViewInfoPtr info)
    {
       qDebug() << "MyGuidanceListener OnShowJunctionView ";
       emit MapEngineManage::GetInstance()->updateExpandView(info);
    }

    /**
     * @brief LaneInfo信息显示、隐藏
     */
    virtual void OnShowLaneInfo(guide::NavigationLaneInfoPtr lane_info)
    {
        qDebug() << "MyGuidanceListener OnShowLaneInfo ";
        emit MapEngineManage::GetInstance()->updateLane(lane_info);
    }

    /**
     * @brief 显示camera相关信息
    */
};

class MySoundListener : public guide::ISoundListener {
public:
    virtual ~MySoundListener() = default;

    virtual void OnPlayTTS(guide::SoundInfoPtr info)
    {
        qDebug() << "MySoundListener OnPlayTTS : " << info->text.c_str();
        std::shared_ptr<GuidanceControl> gc_ptr = MapEngineManage::GetInstance()->m_guidance_control_ptr;
        if (gc_ptr) {

            emit MapEngineManage::GetInstance()->updatePlayTTS(info);

        }
    }

    virtual void OnPlayRing(const guide::TTSScenePlay scene_id)
    {
    }

    virtual bool IsPlaying()
    {
        return false;
    }
};

class LoctionMatchListner : public aurora::loc::MapMatchingListener {
 public:
  LoctionMatchListner() = default;
  ~LoctionMatchListner() override = default;

  void OnMapMatchingResult(const aurora::loc::MatchResult& result) override {

       emit MapEngineManage::GetInstance()->sendLoctionMatchResult(result);
  }
};


MapEngineManage::MapEngineManage()
  : m_is_render_engine_ready(false)
  , m_is_data_engine_ready(false)
  , m_is_route_cacl_engine_ready(false)
{
    m_cur_map_mode = MM_INVALID;
    m_routingCenter_ptr = std::make_shared<RoutingControl>(nullptr);
    m_loction_control_ptr = std::make_shared<LocationControl>(nullptr);
    m_guidance_control_ptr = std::make_shared<GuidanceControl>(nullptr);


    connect(m_routingCenter_ptr.get(), &RoutingControl::updateSelectPathId, m_loction_control_ptr.get(), &LocationControl::onUpdateSelectPathId, Qt::QueuedConnection);

    connect(this, &MapEngineManage::getPoiResultList, this, &MapEngineManage::onGetPoiResultList, Qt::QueuedConnection);
    connect(this, &MapEngineManage::updatePoiSearchStatusInfo, this, &MapEngineManage::onUpdatePoiSearchStatusInfo, Qt::QueuedConnection);

    qRegisterMetaType<aurora::path::PathQueryPtr>("aurora::path::PathQueryPtr");
    qRegisterMetaType<aurora::path::PathResultPtr>("aurora::path::PathResultPtr");
    connect(this, &MapEngineManage::getRoutePathResult, m_routingCenter_ptr.get(), &RoutingControl::onGetRoutePathResult, Qt::QueuedConnection);

    qRegisterMetaType<aurora::path::PathResultPtr>("aurora::guide::SoundInfoPtr");
    connect(this, &MapEngineManage::updatePlayTTS, m_guidance_control_ptr.get(), &GuidanceControl::onUpdatePlayTTS, Qt::QueuedConnection);
    connect(this, &MapEngineManage::getTBTDetailsInfo, m_guidance_control_ptr.get(), &GuidanceControl::onGetTBTDetailsInfo, Qt::QueuedConnection);

    qRegisterMetaType<aurora::loc::MatchResult>("aurora::loc::MatchResult");
    connect(this, &MapEngineManage::sendLoctionMatchResult, this, &MapEngineManage::onGetLoctionMatchResult, Qt::QueuedConnection);
    connect(this, &MapEngineManage::navigationArrive, this, &MapEngineManage::onNavigationArrive, Qt::QueuedConnection);

    m_def_longitude = DEF_LON;
    m_def_latitude = DEF_LAT;
    m_def_scale = 15;
    initDefaultDataPath();


}

MapEngineManage::~MapEngineManage()
{

    // uninit module
    if (m_path_interface_ptr)
            m_path_interface_ptr->RemovePathListener(m_path_listener_ptr);

    if (m_path_module_ptr) {
        m_path_module_ptr->Stop();
        m_path_module_ptr->UnInit();
    }

    clearMapEngine();

    //Close();
}

MapEngineManage* MapEngineManage::GetInstance()
{
    if (_instance == nullptr)
    {
        _instance = new MapEngineManage();
    }
    return _instance;
}


void MapEngineManage::Close()
{
    if (_instance)
    {
        delete _instance;
        _instance = NULL;
    }
}

bool MapEngineManage::CheckDataPath(const QString *DataPath)
{
    if (!DataPath || DataPath->isEmpty())
        return false;

    bool exists = QFile(*DataPath).exists();

    if (!exists) {
        std::cout << "dataPath isn't exit:" << DataPath->toStdString() << std::endl;
        return false;
    }
    return true;
}

bool MapEngineManage::initDefaultDataPath()
{
    QString appPath = QCoreApplication::applicationDirPath();
    std::cout << "App path:" << appPath.toStdString() << std::endl;

    QString config_file_Path;
    if (1) {
       config_file_Path = appPath + "/../map_engine_config.ini";
    } else {
       config_file_Path = appPath + "/../../samples/map_viewer/release/map_engine_config_debug.ini";
    }

    if (CheckDataPath(&config_file_Path)) {
        return InitMapEngineConfig(&appPath , &config_file_Path);
    }
    return false;
}

 bool MapEngineManage::InitMapEngineConfig(const QString *appPath, const QString *configPath)
 {
     if (!appPath || appPath->isEmpty())
         return false;

     QString bese_path = appPath + QString("/../");

     // 读取配置文件
     QSettings settings(*configPath, QSettings::IniFormat);

     //for base data path
     QString map_data_path = settings.value("BaseData/data_path", "").toString();

     //for search data path
     m_search_data_path = bese_path + settings.value("Search/data_path", "").toString();
     std::cout << "search data path : " << m_search_data_path.toStdString() << std::endl;

     //for route cacl path
     m_route_data_path = bese_path + map_data_path + "/route";
     std::cout << "route data path : " << m_route_data_path.toStdString() << std::endl;
     m_route_cacl_config_path = bese_path + settings.value("Route/config_file", "").toString();
     std::cout << "route config path : " << m_route_cacl_config_path.toStdString() << std::endl;

     //for render display
     m_render_data_path = bese_path + map_data_path + "/display";
     std::cout << "render data path : " << m_render_data_path.toStdString() << std::endl;
     m_render_config_path = bese_path + settings.value("Render/config_file", "").toString();
     std::cout << "render config path : " << m_render_config_path.toStdString() << std::endl;
     m_def_scale = settings.value("Render/def_scale", "").toDouble();
     m_render_fonts_path = bese_path + settings.value("Render/fonts_path", "").toString();
     std::cout << "fonts path : " << m_render_fonts_path.toStdString() << std::endl;

     //for map view
     m_def_longitude = settings.value("map_viewer/def_longitude", "").toDouble();
     m_def_latitude = settings.value("map_viewer/def_latitude", "").toDouble();
     m_res_data_path = bese_path + settings.value("map_viewer/res_path", "").toString();
     std::cout << "res path : " << m_res_data_path.toStdString() << std::endl;

     //for guidance
     m_guidance_conf_path = bese_path + settings.value("Guidance/config_path", "").toString();
     std::cout << "guidance config path : " << m_guidance_conf_path.toStdString() << std::endl;

     initMapEngineDataProvider(&m_route_data_path);
     initMapEngineRoutePathCacl(&m_route_cacl_config_path, &m_route_data_path);
     initMapEngineSearch(&m_search_data_path);
     initMapEngineLoction(&m_route_data_path);
     initMapEngineGuidance(&m_guidance_conf_path);
     //initMapEngineRenderMap(&m_render_data_path);
     return true;
 }

bool MapEngineManage::initMapEngineRoutePathCacl(const QString * ConfigPath, const QString * DataPath)
{
    if (!ConfigPath || ConfigPath->isEmpty() || !DataPath || DataPath->isEmpty())
        return false;

    m_path_module_ptr = std::make_shared<aurora::path::PathModule>();
    m_path_module_ptr->Prepare(ConfigPath->toStdString());
    m_path_module_ptr->SetParams({{"data_dir", DataPath->toStdString()}});
    auto module_finder = [](aurora::ModuleId id) {
        return nullptr;
    };
    m_path_module_ptr->Init(module_finder);
    m_path_module_ptr->Start();

    // get path interface
    m_path_interface_ptr = std::dynamic_pointer_cast<PathInterface>(m_path_module_ptr->GetInterface());
    m_path_listener_ptr = std::make_shared<RoutePathListener>();
    m_path_interface_ptr->AddPathListener(m_path_listener_ptr);
    m_is_route_cacl_engine_ready = true;

    return true;
}

bool MapEngineManage::initMapEngineGuidance(const QString * DataPath)
{
    m_engine_guidance_module.Prepare(DataPath->toStdString());
    m_engine_guidance_module.Init(nullptr);
    ModuleInitStatus status = m_engine_guidance_module.IsInit();
    std::cout << "guidance engine status: " << static_cast<int>(status)  << std::endl;
    m_engine_guidance_module.Start();

    m_guide_interface_ptr = std::dynamic_pointer_cast<guide::IGuidance>(m_engine_guidance_module.GetInterface());
    m_guidance_listener = std::make_shared<MyGuidanceListener>();
    m_sound_listener = std::make_shared<MySoundListener>();
    m_guide_interface_ptr->AddListener(m_guidance_listener);
    m_guide_interface_ptr->AddListener(m_sound_listener);

    return true;
}

bool MapEngineManage::initMapEngineDataProvider(const QString * DataPath)
{
    if (!DataPath || DataPath->isEmpty())
        return false;

    m_is_data_engine_ready = m_data_provider.InitRouteParser(DataPath->toStdString().c_str());
    return m_is_data_engine_ready;
}

bool MapEngineManage::initMapEngineSearch(const QString * DataPath)
{
    if (!DataPath || DataPath->isEmpty())
        return false;

    m_search_listener_ptr = std::make_shared<GetSearchByTextResponseHandler>();
    m_search_service_ptr = std::make_shared<aurora::search::SearchService>();
    return m_search_service_ptr->Init(DataPath->toStdString()) == 0;
}

bool MapEngineManage::initMapEngineRenderMap(const QString * DataPath)
{
    if (!DataPath || DataPath->isEmpty())
        return false;


    m_map_render_thema_ptr = std::make_shared<aurora::parser::CfgManager>();
    m_map_render_thema_ptr->Init(MapEngineManage::GetInstance()->m_render_config_path.toStdString());
    m_map_render_service_ptr = std::make_shared<aurora::IMapRenderService>();
    AuroraMapConfig config;
    config.data_path_ = DataPath->toStdString();
    auto font_path = DataPath->toStdString() + "/../fonts";
    config.default_fontPath_ = font_path;
    config.default_fontName_ = "Arial Unicode.ttf";
    m_is_render_engine_ready = m_map_render_service_ptr->Init(config, m_map_render_thema_ptr.get()) == 0;
    m_map_render_service_ptr->SetMapCenter(m_def_longitude, m_def_latitude);
    m_map_render_service_ptr->SetMapScale(m_def_scale);
    return m_is_render_engine_ready;
}

bool MapEngineManage::initMapEngineLoction(const QString * DataPath)
{
    if (!DataPath)
        return false;

    m_loction_nodule.Prepare(DataPath->toStdString());
    auto module_finder = [](aurora::ModuleId id) {
      return nullptr; //std::dynamic_pointer_cast<aurora::Interface>(path_module);
    };
    m_loction_nodule.Init(module_finder);
    m_loction_nodule.Start();

    m_loc_match_listener = std::make_shared<LoctionMatchListner>();
    m_loction_interface_ptr = std::dynamic_pointer_cast<aurora::loc::ILocation>(m_loction_nodule.GetInterface());
    m_loction_interface_ptr->AddMapMatchingListener(m_loc_match_listener);

    m_loction_control_ptr->setMapEngineLoctionInterFace(m_loction_interface_ptr);

    return true;
}

void MapEngineManage::onRequestPoiSearch(std::shared_ptr<aurora::search::SearchByTextRequest> request)
{
    int ret = m_search_service_ptr->SearchByText(request, m_search_listener_ptr);

    if (ret < 0) {
        QString info = QString("%1 %2")
                .arg("fail, return")
                .arg(QString::number(ret));
        onUpdatePoiSearchStatusInfo(info);
    }
}

void MapEngineManage::onGetPoiResultList()
{
    if (!m_poi_search_response_ptr)
        return;

    std::cout << "setPoiResultList ret: " << m_poi_search_response_ptr->place_briefs.size() << std::endl;
    MainWindow* main_window = AppGetMainWindow();
    if (main_window != nullptr) {
        main_window->updatePoiResultList(m_poi_search_response_ptr);
    }
}

void MapEngineManage::clearPoiResult()
{
    m_poi_search_response_ptr = nullptr;
}

void MapEngineManage::clearMapEngine()
{
    // Stop and uninitialize the loction
    m_loction_interface_ptr->RemoveMapMatchingListener(m_loc_match_listener);
    m_loction_nodule.Stop();
    m_loction_nodule.UnInit();


    // Stop and uninitialize the tbt manager
    m_guide_interface_ptr->RemoveListener(m_guidance_listener);
    m_engine_guidance_module.Stop();
    m_engine_guidance_module.UnInit();

    // Stop and uninitialize the route path manager
    m_path_interface_ptr->RemovePathListener(m_path_listener_ptr);
    m_path_module_ptr->Stop();
    m_path_module_ptr->UnInit();
}


MapMode MapEngineManage::getCurMapMode()
{
    return m_cur_map_mode;
}

void MapEngineManage::setCurMapMode(MapMode map_mode)
{
    m_cur_map_mode = map_mode;
}

void MapEngineManage::onUpdatePoiSearchStatusInfo(QString info)
{
    MainWindow* main_window = AppGetMainWindow();
    if (main_window != nullptr) {
        main_window->onUpdatePoiSearchStatusInfo(info);
    }
}

void MapEngineManage::setNavigationMode(guide::NavigationMode type)
{
    if(m_path_interface_ptr) {
        m_guide_interface_ptr->StartNavigation(type);
    }
}

void MapEngineManage::updateMapMatchingPos(const aurora::loc::MatchResult &map_result)
{
    if(m_path_interface_ptr) {
        m_guide_interface_ptr->UpdateMapMatchingPos(map_result);
    }
}

void MapEngineManage::stopNavigation()
{
    if(m_path_interface_ptr) {
        m_guide_interface_ptr->StopNavigation();
    }
}

void MapEngineManage::stopSimulation()
{
    std::shared_ptr<SimulatorController> Simulation_ptr =  m_loction_interface_ptr->GetSimulatorController();
    if (Simulation_ptr) {
        Simulation_ptr->Stop();
    }
}

void MapEngineManage::onGetLoctionMatchResult(aurora::loc::MatchResult result)
{
    MainWindow* main_window = AppGetMainWindow();
    if (main_window != nullptr) {
        main_window->getMapWidget()->getLocationLayer()->onGetLoctionMatchResult(result);
    }

    updateMapMatchingPos(result);
}


void MapEngineManage::onNavigationArrive()
{
    MainWindow* main_window = AppGetMainWindow();
    if (main_window != nullptr) {
        main_window->onNavigationArrive();
    }
}
