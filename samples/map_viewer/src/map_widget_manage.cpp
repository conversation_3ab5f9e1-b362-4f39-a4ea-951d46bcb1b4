﻿
#include "map_widget_manage.h"
#include "preferences_dialog.h"

MapWidgetManage* g_app = NULL;


MapWidgetManage::MapWidgetManage(int &argc, char **argv)
	: QApplication(argc, argv)
    , m_mainWindowPtr(nullptr)
{

}

MapWidgetManage::~MapWidgetManage()
{
    if (m_mainWindowPtr) {
        delete m_mainWindowPtr;
        m_mainWindowPtr = nullptr;
    }
}

template<typename T> void MapWidgetManage::initMapWidget(std::shared_ptr<T> &ptr)
{
    ptr = std::make_shared<T>(m_mainWindowPtr);
    if (ptr) {
        ptr->setAllowedAreas(Qt::LeftDockWidgetArea | Qt::RightDockWidgetArea);
        m_mainWindowPtr->addDockWidget(Qt::RightDockWidgetArea, ptr.get());
        ptr->hide();
        m_attribute_widget_ptr_set.push_back(ptr);
    }
}

void MapWidgetManage::initWindows()
{
    if (m_mainWindowPtr != NULL)
        return;

    m_mainWindowPtr = new MainWindow();

    initMapWidget<LinkPropertyDisplayWidget>(m_LinkAttributeWidgetPtr);
    initMapWidget<ViewPropertyInfoWidget>(m_ViewAttributeWidgetPtr);
    initMapWidget<LocationMatchInfoWidget>(m_LocationInfoWidgetPtr);
    initMapWidget<ActionInfoDisplayWidget>(m_ActionListWidgetPtr);
    initMapWidget<SearchInfoDisplayWidget>(m_SearchResultWidgetPtr);
    initMapWidget<TrafficPropertyInfoWidget>(m_TmcAttributeWidgetPtr);
    initMapWidget<DrFusionInfoWidget>(m_DrAlgoInfoWidgetPtr);

    QSettings s((MapEngineManage::PREFERENCES_FILE_NAME), QSettings::IniFormat);
    s.beginGroup("right_widget");
    int right_widget_width = s.value("width").toInt();
    s.endGroup();

    if (right_widget_width > 0) {
        setRightWidgetWidth(right_widget_width);
    } else {
       setRightWidgetWidth(RIGHT_WIDGET_WIDTH);
    }
}

void MapWidgetManage::hideAllWidget()
{
    for (auto itr : m_attribute_widget_ptr_set) {
        if (itr)
            itr->hide();
    }

}

void MapWidgetManage::showLinkAttributeWidget(aurora::parser::AugmentEdge * link_ptr, const QString &strlayer_level)
{
    if (m_LinkAttributeWidgetPtr) {
        m_LinkAttributeWidgetPtr->LinkupdateDisplay(link_ptr, strlayer_level);

        if (!m_LinkAttributeWidgetPtr->isVisible()) {
            m_LinkAttributeWidgetPtr->show();
        }
    }
}


void MapWidgetManage::hideLinkAttributeWidget()
{
    if (m_LinkAttributeWidgetPtr) {
        m_LinkAttributeWidgetPtr->hide();
    }
}

void MapWidgetManage::showNodeAttributeWidget(aurora::parser::RouteNode * link_ptr, const QString &strlayer_level)
{
    if (m_LinkAttributeWidgetPtr) {
        m_LinkAttributeWidgetPtr->nodeUpdateDisplay(link_ptr, strlayer_level);

        if (!m_LinkAttributeWidgetPtr->isVisible()) {
            m_LinkAttributeWidgetPtr->show();
        }
    }
}

void MapWidgetManage::hideNodeAttributeWidget()
{
    if (m_LinkAttributeWidgetPtr) {
        m_LinkAttributeWidgetPtr->hide();
    }
}

void MapWidgetManage::showTileAttributeWidget(uint64 tile_code)
{
    if (m_LinkAttributeWidgetPtr) {
        m_LinkAttributeWidgetPtr->tileUpdateDisplay(tile_code);
        m_LinkAttributeWidgetPtr->show();
    }
}

void MapWidgetManage::hideTileAttributeWidget()
{
    if (m_LinkAttributeWidgetPtr) {
        m_LinkAttributeWidgetPtr->hide();
    }
}

void MapWidgetManage::showTraceAttributeWidget()
{
    if (m_LinkAttributeWidgetPtr) {
        m_LinkAttributeWidgetPtr->show();
    }
}

void MapWidgetManage::hideTraceAttributeWidget()
{
    if (m_LinkAttributeWidgetPtr) {
        m_LinkAttributeWidgetPtr->hide();
    }
}

QTreeWidgetItem* MapWidgetManage::showViewAttributeWidget(short type, void* viewData, uint8 i, uint16 j)
{
    //m_ViewAttributeWidgetPtr->show();
    return NULL;
}

void MapWidgetManage::hideViewAttributeWidget()
{
    if (m_ViewAttributeWidgetPtr) {
        m_ViewAttributeWidgetPtr->hide();
    }
}

void MapWidgetManage::showLocationInfoWidget()
{
    if (m_LocationInfoWidgetPtr) {
        m_LocationInfoWidgetPtr->show();
    }
}



void MapWidgetManage::hideLocationInfoWidget()
{
    if (m_LocationInfoWidgetPtr) {
        m_LocationInfoWidgetPtr->hide();
    }
}

void MapWidgetManage::showActionListWidget(std::vector<aurora::guide::ManeuverDetail> &tbt_details)
{
    if (m_ActionListWidgetPtr) {
        m_ActionListWidgetPtr->showTBTInfo(tbt_details);
        m_ActionListWidgetPtr->show();
    }
}

void MapWidgetManage::showActionListWidget(ENUM_MAPTYPE type)
{
    m_ActionListWidgetPtr->updateDisplay(type);
    m_ActionListWidgetPtr->show();
}

void MapWidgetManage::hideActionListWidget()
{
    if (m_ActionListWidgetPtr) {
        m_ActionListWidgetPtr->hide();
    }
}

void MapWidgetManage::clearActionListWidget()
{
    if (m_ActionListWidgetPtr) {
        m_ActionListWidgetPtr->clearTreeWidget();
    }
}

ENUM_MAPTYPE MapWidgetManage::getActionListWidgetMapType()
{
    if (m_ActionListWidgetPtr) {
        return m_ActionListWidgetPtr->getMapType();
    }

    return MAP_INVAID;
}

void MapWidgetManage::showSearchResultWidget(SearchByTextResponsePtr response)
{
    if (m_SearchResultWidgetPtr) {
        m_SearchResultWidgetPtr->updateDisplay(response);
        m_SearchResultWidgetPtr->show();
    }
}

void MapWidgetManage::hideSearchResultWidget()
{
    if (m_SearchResultWidgetPtr) {
        m_SearchResultWidgetPtr->hide();
    }
}

void MapWidgetManage::showTmcAttributeWidget()
{
    if (m_TmcAttributeWidgetPtr) {
        //m_TmcAttributeWidgetPtr->updateDisplay();
        m_TmcAttributeWidgetPtr->show();
    }
}

void MapWidgetManage::hideTmcAttributeWidget()
{
    if (m_TmcAttributeWidgetPtr) {
        m_TmcAttributeWidgetPtr->hide();
    }
}

void MapWidgetManage::showDrAlgoInfoWidget()
{
    if (m_DrAlgoInfoWidgetPtr){
        m_DrAlgoInfoWidgetPtr->show();
	}
}

void MapWidgetManage::hideDrAlgoInfoWidget()
{
    if (m_DrAlgoInfoWidgetPtr){
        m_DrAlgoInfoWidgetPtr->hide();
	}
}

bool MapWidgetManage::isDrAlgoInfoWidgetHiden()
{
    if (m_DrAlgoInfoWidgetPtr){
        return m_DrAlgoInfoWidgetPtr->isHidden();
	}
	return true;
}

MainWindow* AppGetMainWindow(void)
{
    return g_app ? g_app->getMainWindow() : NULL;
}

void MapWidgetManage::destroy()
{
    g_app->exit();
}

void MapWidgetManage::setRightWidgetWidth(int width)
{
    for (auto ptr : m_attribute_widget_ptr_set) {
        m_mainWindowPtr->resizeDocks({ptr.get()}, {width}, Qt::Horizontal);
    }
}

