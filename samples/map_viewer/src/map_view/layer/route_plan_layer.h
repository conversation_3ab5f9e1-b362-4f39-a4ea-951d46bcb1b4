
#ifndef ROUTE_PLAN_LAYER_H
#define ROUTE_PLAN_LAYER_H

#include "map_layer.h"
#include <qmenu.h>
#include <QLabel>
#include <QPen>
#include <mutex>
#include "path/include/path_module.h"
#include "guidance/include/guidance_def.h"
#include "pointll.h"
#include "routing_path.h"

class RenderEngineLayer;

class RoutePlanLayer : public MapLayer
{
    Q_OBJECT

public:

    RoutePlanLayer(MapWidget *parent);
    ~RoutePlanLayer();

    /** 绘图 */
    virtual void paint(QPainter& painter, MapCamera& camera);

    /** 快捷菜单 */
    virtual void onContextMenuRequested(const QPoint& pos);

    /*  鼠标动作 */
    virtual void onMouseDown(QMouseEvent* event, MapCamera& camera);

    void onSetStartPoint(RGeoPoint *point);
    void onSetEndPoint(RGeoPoint *point);
    void onSetWayPoint(RGeoPoint *point);

    void setSelectTbtLink(std::vector<aurora::PointXY<double>> &m_link_geos);
    void clearRoutePath();
    void clearRoutePathAndPoints();
    void setRoutePathResult(const aurora::path::PathQueryPtr& query, const aurora::path::PathResultPtr& result);
    void setOnlineMapRoute(std::vector<float64>& route, ENUM_MAPTYPE type);
    void setOnlineSelectActionCoords(std::vector<float64>* coords, ENUM_MAPTYPE type);

    void removeRoutePoint(RoutePointType type, int index = 0);
    void removeRoutePath(RoutePathType type);
    RoutePointSet * getRoutePoint(RoutePointType type);

    std::vector<RoutePathInfo> * getRoutePaths(RoutePathType type);

    void setRenderMapLayer(std::shared_ptr<RenderEngineLayer> layer_ptr) { m_render_engine_layer_ptr = layer_ptr;}

signals:
    void updatePoiSearchPos(double lng, double lat);

private slots:
    void onAddStartPoint();
    void onAddWayPoint();
    void onAddEndPoint();

    void onSetPoiSearchCenter();

private:
    void setupContextMenu();
    void initPathFlag(QLabel &flag, QPixmap &pixmap);
    QPen getPenByLevel(int level);

    int onCheckSelectRoutePath(const QPointF& screenPt);

    void drawPathNameFlag();
    void drawRouteFlag(QPainter& painter, MapCamera& camera);

    void pos2Rect(const QPointF &screen_pos, const int &w, const int &h, map_engine::Rect& rect);
    void initPathFlag(const char *flag_path, QLabel &path_name_flag) ;

    void setStartPoint(RGeoPoint *point);
    void setEndPoint(RGeoPoint *point);
    void setWayPoint(RGeoPoint *point);
    void updateSelectPathId();
    void updateRouteFlag(QPainter& painter, QImage &flag, QPointF &screenPos);

private:

    std::mutex m_mutex;

    QImage m_search_poi_flag;

    bool is_show_search_poi_flag;
    QMenu m_contextMenu;
    std::shared_ptr<aurora::path::PathQuery> m_path_query;
    RGeoPoint m_search_point;


    QPoint m_screen_pos;
    MapWidget * m_map_widget_ptr;
    //aurora::path::PathResultPtr m_route_paths_result_ptr;

    std::vector<map_engine::RGeoPoint> m_select_link_geos;

    std::shared_ptr<RoutingPath> m_route_data_ptr;
    std::shared_ptr<RenderEngineLayer> m_render_engine_layer_ptr;


};

#endif // ROUTE_PLAN_LAYER_H
