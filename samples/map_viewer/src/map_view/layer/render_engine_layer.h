
#ifndef RENDER_ENGINE_LAYER_H
#define RENDER_ENGINE_LAYER_H

#include <qtimer.h>
#include <QPushButton>
#include <QLabel>
#include "map_layer.h"
#include "map_render_module.h"
#include "config_data/cfg_manager.h"
#include "map_render_engine_wrapper.h"
#include "routing_path.h"


class RenderEngineLayer : public MapLayer
{
    Q_OBJECT
public:
    RenderEngineLayer(MapWidget *parent = NULL);
	virtual ~RenderEngineLayer();
	virtual void paint(QPainter& painter, MapCamera& camera);

signals:
    void updateMapWidget();

public slots:
    void cleanup();
    void onRefresh();
public:
    virtual void initializeGL(QOpenGLWidget *container, QOpenGLFunctions *openGLFunctions);
    virtual void resizeGL(int width, int height);
    virtual void onMouseDown(QMouseEvent* event, MapCamera& camera);
    virtual void onMouseMove(QMouseEvent* event, MapCamera& camera);
    virtual void onMouseUp(QMouseEvent* event, MapCamera& camera);
    virtual void setVisible(bool visibility);

    aurora::IMapRenderService * getMapEngineRenderService() {return m_map_render_service_ptr.get();}


    float getRenderScale() {return m_map_render_service_ptr->GetMapScale();}
    void onUpdateZoom(double &level, bool is_animation);

    int setRoutePointToEngine(const RGeoPoint* pos, const RoutePointType type);
    void deleteRoutePointFromEngine(const RoutePointType type, const int index = 0);
    void deleteAllRoutePointFromEngine();

    void setRoutePathResult(const aurora::path::PathQueryPtr& query, const aurora::path::PathResultPtr& result);
    void deleteRoutePath(uint32_t type);
    void deleteAllRoutePathFromEngine();

private:
    bool initRenderMapengine();
    void addZoomButton(QPushButton & zoom_btn, std::string &image_path, std::string &image_click_path);
    void updataZoomBtnPos(MapCamera& camera);
    void updateScaleDisLabelPos(MapCamera& camera);
    void onZoomInPush();
    void onZoomOutPush();
    bool initMapEngineRenderMap(MapEngineManage * engine_manage);
    void initScaleDisLabel();
    void updateScaleDisLabel(int &dis);

private:
    QLabel m_scale_label;
    QTimer *m_refreshTimer;
    bool m_core;
    QPoint m_lastPos;
    bool m_isDragging;
    bool m_transparent;

    QOpenGLWidget *m_mapContainer;
    QOpenGLFunctions *m_openGLFunctions;

    MapWidget * m_map_widget_ptr;

    QPushButton  m_zoom_in_btn;
    QPushButton  m_zoom_out_btn;
    double m_render_engine_scale;


    std::shared_ptr<aurora::IMapRenderService> m_map_render_service_ptr;
    std::shared_ptr<aurora::parser::CfgManager> m_map_render_thema_ptr;

    std::vector<int> m_route_path_id_set;

    std::vector<int> m_engine_start_mark_id;
    std::vector<int> m_engine_end_mark_id;
    std::vector<int> m_engine_way_mark_id_set;


};

#endif // RENDER_ENGINE_LAYER_H
