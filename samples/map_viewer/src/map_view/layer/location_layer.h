
#ifndef LOCATION_LAYER_H
#define LOCATION_LAYER_H

#include "map_layer.h"
#include "map_widget.h"
#include <mutex>

class LocationLayer : public MapLayer
{
    Q_OBJECT

public:
    LocationLayer(MapWidget *parent);
    ~LocationLayer();

    /** 绘图 */
    virtual void paint(QPainter& painter, MapCamera& camera);
    virtual void onMouseDown(QMouse<PERSON>vent* event, MapCamera& camera);
    virtual void onMouseUp(QMouseEvent* event, MapCamera& camera);
    virtual void onMouseMove(QMouseEvent* event, MapCamera& camera);

    void onClearDriveView();
    void onStartDrive(std::shared_ptr<aurora::path::PathInfo> path_info);
    void onGetLoctionMatchResult(aurora::loc::MatchResult result);

private:
    void drawCarIcon(QPainter& painter, MapCamera& camera);
    QPen getPenByLevel(int level);   
    void onClearData();
    std::pair<size_t, size_t> findProjectionSegment(const std::vector<map_engine::RGeoPoint>& curve, const map_engine::RGeoPoint& projection);
    void onRefreshCenterPosition();

private:
    MapWidget * m_map_widget_ptr;
    QImage m_car_flag;
    RGeoPoint m_car_point;
    double m_car_dir;
    bool m_is_show_car_flag;

    std::vector<map_engine::RGeoPoint> m_original_latlon_set;
    std::vector<map_engine::RGeoPoint> m_match_latlon_set;

    std::vector<map_engine::RGeoPoint> m_path_latlon_set;
    std::vector<map_engine::RGeoPoint> m_gray_path_latlon_set;
    std::vector<map_engine::RGeoPoint> m_gray_offset_latlon_set;

    std::shared_ptr<aurora::path::PathInfo> m_path_info_ptr;
    int32_t m_last_match_path_index;
    bool m_is_move;
    bool m_is_update_center_pos;
    std::shared_ptr<QTimer> m_refreshTimer_ptr;
    std::mutex m_mutex;






};

#endif // LOCATION_LAYER_H
