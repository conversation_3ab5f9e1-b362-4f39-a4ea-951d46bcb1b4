﻿

#include "map_widget.h"
#include <QWheelEvent>
#include <QPainter>
#include <iostream>
#include "map_layer.h"
#include "render_engine_layer.h"
#include "route_data_layer.h"
#include "route_data_node_layer.h"
#include "util_funtion_tools.h"
#include "geo_point.h"
#include "route_plan_layer.h"
#include "search_poi_layer.h"
#include "location_layer.h"

#define MAP_VIEW_KEY_UP 0x13
#define MAP_VIEW_KEY_DOWN 0x15
#define MAP_VIEW_KEY_LEFT 0x12
#define MAP_VIEW_KEY_RIGHT 0x14
#define MAP_VIEW_KEY_N 0x4e
#define MAP_VIEW_KEY_SPACE 0x20

#define ROUTE_MARK_WIDTH     24
#define ROUTE_MARK_HEIGHT    38
#define ROUTE_MARK_OFFSET_X  1
#define ROUTE_MARK_OFFSET_Y  16

#define SYMBOL_CURSOR_SIZE  27

MapWidget::MapWidget(QWidget *parent)
    : QOpenGLWidget(parent)
    , x_(0)
    , y_(0)
    , is_follow_car_(false)
    , is_head_up_(false)
    , m_mapCacheData_(NULL)
    , m_layer_type_select(kMapLayer_Invalid)
    , m_route_plane_Layer_ptr(nullptr)
    , m_search_poi_Layer_ptr(nullptr)

{
    m_mouseDown = false;
    m_camera = new MapCamera();
    setProjection("plate carree");

    QSurfaceFormat format;
    format.setDepthBufferSize(24);
    format.setSamples(8);
    setFormat(format);

    m_search_poi_Layer_ptr = std::make_shared<SearchPoiLayer>(this);
    addBackgroundMapLayer(m_search_poi_Layer_ptr.get());

    m_route_plane_Layer_ptr = std::make_shared<RoutePlanLayer>(this);
    addBackgroundMapLayer(m_route_plane_Layer_ptr.get());

    m_location_Layer_ptr = std::make_shared<LocationLayer>(this);
    addBackgroundMapLayer(m_location_Layer_ptr.get());
}

MapWidget::~MapWidget()
{
    if (m_camera){
        delete m_camera;
        m_camera = NULL;
    }

    onClearMaplayer();

}

void MapWidget::onClearMaplayer()
{
    for (auto itr : m_layers)
    {
        if (itr) {
           delete itr;
           itr = nullptr;
        }
    }
    m_layers.clear();
}

void MapWidget::SetMapDataCache(MapDataAdapter* cache)
{
    m_mapCacheData_ = cache;
}


void MapWidget::ForceUpdateWidget()
{
	update();
}


void MapWidget::initializeGL()
{
    is_follow_car_ = true;
    is_head_up_ = false;

    //setAttribute(Qt::WA_TranslucentBackground);
    initializeOpenGLFunctions();

    for (auto layer : m_layers){
        layer->initializeGL(this, (QOpenGLFunctions*)this);
    }

}

void MapWidget::resizeGL(int w, int h)
{
    m_camera->resetScreenSize(w, h);

    for (auto layer : m_layers){
        layer->resizeGL(w, h);
    }

    h_ = h;

    update();

    updataTurnMarkDialogPos(*m_camera);
}

void MapWidget::paintGL()
{
   // glClearColor(1.0f, 0.0f, 0.0f, 0.0f);
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
    //std::cout << "paintEvent **************************" <<    std::endl;

    QPainter painter(this);
    painter.fillRect(rect(), Qt::white);

    for (MapLayer *layer : m_layers)
    {
        if (!layer->isVisible())
            continue;

       // std::cout << "layer type : " << layer->getMapLayerType() << std::endl;

        painter.save();
        layer->paint(painter, *m_camera);
        painter.restore();
    }

    for (MapLayer *layer : m_background_layers) {
         if (layer) {
             painter.save();
             layer->paint(painter, *m_camera);
             painter.restore();
         }
    }
}


void MapWidget::wheelEvent(QWheelEvent *e)
{

//    std::cout << "Scale : " <<  m_camera->getScale() <<  std::endl;

    if (e->delta() > 0){
        if (m_camera->isScaleOutRange(false))
            return;

       //m_camera->zoomOut(e->posF());
        m_camera->zoomOut(e);
    } else {
        if (m_camera->isScaleOutRange(true))
            return;

       //m_camera->zoomIn(e->posF());
        m_camera->zoomIn(e);
    }
    emit cameraChanged(m_camera);

    update();
}

void MapWidget::mousePressEvent(QMouseEvent *e)
{

    e->setAccepted(false);

    for (auto &layer : m_background_layers)
    {
        if (!layer->isVisible())
            continue;

        layer->onMouseDown(e, *m_camera);
        if (e->isAccepted())
            return;
    }


    for (auto layer : m_layers)
    {
        if (!layer->isVisible())
            continue;

        layer->onMouseDown(e, *m_camera);
        if (e->isAccepted())
            return;
    }

    if (e->button() != Qt::LeftButton)
        return;

    m_mouseDown = true;

    m_camera->screenToWorldLatLon(e->windowPos(), &m_mousePressWorldPos);
}

void MapWidget::mouseMoveEvent(QMouseEvent *e)
{

    e->setAccepted(false);
    for (auto &layer : m_background_layers)
    {
        if (!layer->isVisible())
            continue;

        layer->onMouseMove(e, *m_camera);
        if (e->isAccepted())
            return;
    }

    for (auto layer : m_layers)
    {
        if (!layer->isVisible())
            continue;

        layer->onMouseMove(e, *m_camera);
        if (e->isAccepted())
            return;
    }

    if (m_mouseDown)
    {
        m_camera->pinTo(&m_mousePressWorldPos, e->windowPos());
        emit cameraChanged(m_camera);
        update();
    }
}

void MapWidget::mouseReleaseEvent(QMouseEvent *e)
{

    m_mouseDown = false;

    e->setAccepted(false);
    for (auto &layer : m_background_layers)
    {
        if (!layer->isVisible())
            continue;

        layer->onMouseUp(e, *m_camera);
        if (e->isAccepted())
            return;
    }

    for (auto layer : m_layers)
    {
        if (!layer->isVisible())
            continue;

        layer->onMouseUp(e, *m_camera);
        if (e->isAccepted())
            return;
    }
}

void MapWidget::mouseDoubleClickEvent(QMouseEvent *e)
{
    e->setAccepted(false);
    for (auto layer : m_layers)
    {
        if (!layer->isVisible())
            continue;

        layer->onMouseDoubleClick(e, *m_camera);
        if (e->isAccepted())
            return;
    }
}

void MapWidget::keyPressEvent(QKeyEvent *keyevent)
{
    int key = keyevent->key() & 0xFFFF;
    switch (key) {
    case MAP_VIEW_KEY_UP:
        update();
        break;
    case MAP_VIEW_KEY_DOWN:
        update();
        break;
    case MAP_VIEW_KEY_LEFT:
        update();
        break;
    case MAP_VIEW_KEY_RIGHT:
        update();
        break;
    case MAP_VIEW_KEY_SPACE:
    case MAP_VIEW_KEY_N:
    {
    }
    default:
        break;
    }
}

void MapWidget::initRouteDataMapLayer()
{    
    onClearMaplayer();

    MapEngineManage *mm = MapEngineManage::GetInstance();
    //if (mm->isRenderEngineReady())
    {
        m_render_map_layer_ptr = std::make_shared<RenderEngineLayer>(this);
        m_render_map_layer_ptr->setMapLayerType(kMapLayer_Render_map);
        addMapLayer(m_render_map_layer_ptr.get());
        if (m_route_plane_Layer_ptr) {
            m_route_plane_Layer_ptr->setRenderMapLayer(m_render_map_layer_ptr);
        }
    }


    if (mm->isDataEngineReady())
    {
        for (int i = kMapLayer_Route_Link_level_0; i < map_layer_count; i++) {

            if (i == kMapLayer_Route_Link_level_0)
            {
                RouteDataLayer * layer_ptr = new RouteDataLayer();
                layer_ptr->setMapLayerType(kMapLayer_Route_Link_level_0);
                addMapLayer(layer_ptr);
            } else if (i == kMapLayer_Route_Link_level_1)
            {
                RouteDataLayer * layer_ptr = new RouteDataLayer();
                layer_ptr->setMapLayerType(kMapLayer_Route_Link_level_1);
                addMapLayer(layer_ptr);
            } else if (i == kMapLayer_Route_Link_level_2)
            {
                RouteDataLayer * layer_ptr = new RouteDataLayer();
                layer_ptr->setMapLayerType(kMapLayer_Route_Link_level_2);
                addMapLayer(layer_ptr);

            } else if (i == kMapLayer_Route_Link_Node_0)
            {
                RouteDataNodeLayer * layer_ptr = new RouteDataNodeLayer();
                layer_ptr->setMapLayerType(kMapLayer_Route_Link_Node_0);
                addMapLayer(layer_ptr);
            }else if (i == kMapLayer_Route_Link_Node_1)
            {
                RouteDataNodeLayer * layer_ptr = new RouteDataNodeLayer();
                layer_ptr->setMapLayerType(kMapLayer_Route_Link_Node_1);
                addMapLayer(layer_ptr);
            }else if (i == kMapLayer_Route_Link_Node_2)
            {
                RouteDataNodeLayer * layer_ptr = new RouteDataNodeLayer();
                layer_ptr->setMapLayerType(kMapLayer_Route_Link_Node_2);
                addMapLayer(layer_ptr);
            }
        }
    }

}

void MapWidget::addMapLayer(MapLayer *layer)
{
    layer->setParent(this);
    layer->setMapCamera(m_camera);
    m_layers.append(layer);
    connect(layer, SIGNAL(needRedraw()), this, SLOT(update()));

    emit layerListChanged(&m_layers);

    update();
}

void MapWidget::addBackgroundMapLayer(MapLayer *layer)
{
    layer->setParent(this);
    layer->setMapCamera(m_camera);
    connect(layer, SIGNAL(needRedraw()), this, SLOT(update()));
    m_background_layers.append(layer);

}

void MapWidget::setMapLayerVisibilityByIndex(int index, bool visibility)
{
    if (index < 0 || m_layers.size() <= index)
        return;

    MapLayer* layer = m_layers[index];
    if (layer->isVisible() == visibility)
        return;

    layer->setVisible(visibility);

    update();

}

bool MapWidget::setProjection(const QString &projectionName)
{
    bool succ = true;
    QString lowerName = projectionName.toLower();

    if (lowerName == "plate carree")
    {
        m_camera->setProjection(MapCamera::PROJECTION_PLATE_CARREE);
    }
    else if (lowerName == "equirectangular@40")
    {
        m_camera->setProjection(MapCamera::PROJECTION_EQUIRECTANGULAR_40);
    }
    else if (lowerName == "mercator")
    {
        m_camera->setProjection(MapCamera::PROJECTION_MERCATOR);
    }
    else
    {
        succ = false;
    }

    if (succ){
        m_projectionName = projectionName;
        emit cameraChanged(m_camera);
        update();
    }

    return succ;
}


MapCamera * MapWidget::getMapCamera()
{
    return m_camera;
}

void MapWidget::setWorldCenter(const RGeoPoint* pos , bool set_Scale)
{

    if (m_render_map_layer_ptr && m_render_map_layer_ptr->isVisible()) {
        aurora::IMapRenderService * render_service_ptr = m_render_map_layer_ptr->getMapEngineRenderService();
        if (render_service_ptr) {
            render_service_ptr->SetMapCenter(pos->CoordX_, pos->CoordY_);

            if (set_Scale)
               render_service_ptr->SetMapScale(15, 500);
        }
    }

    m_camera->setWorldCenter(pos);
    emit cameraChanged(m_camera);
    update();
}

void MapWidget::getWorldCenter(RGeoPoint * center) const
{
    m_camera->getWorldCenter(center);
}

void MapWidget::updateScale(double &scale)
{
    m_camera->setScale(scale);
    update();
}

void MapWidget::onFlyTo(const RGeoPoint* pos)
{
    if (m_render_map_layer_ptr && m_render_map_layer_ptr->isVisible()) {
        aurora::IMapRenderService * render_service_ptr = m_render_map_layer_ptr->getMapEngineRenderService();
        if (render_service_ptr) {
            render_service_ptr->FlyTo(pos->CoordX_, pos->CoordY_, 12);
        }
    } else {
        m_camera->setWorldCenter(pos);
        update();
    }


}

void MapWidget::updataTurnMarkDialogPos(MapCamera& camera)
{
    int w,h;
    camera.getScreenSize(w, h);

    int x = w ;
    int y = 200;

    qDebug() << "updataTurnMarkDialogPos : w = " << w << "h = " << h;
    qDebug() << "updataTurnMarkDialogPos : x = " << x << "y = " << y;

    emit updataTurnMarkInfoDialogPos(x, y);



}

