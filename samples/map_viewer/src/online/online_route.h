﻿
#ifndef ONLINE_ROUTE_H
#define ONLINE_ROUTE_H
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QFile>
#include <QTextStream>
#include <vector>
#include "common/qt_ds.h"


using std::vector;

#pragma pack(push, 1)

#define BMAP_ACTION_OFFSET 15
#define BMAP_MAX_WAYPOINTS 10 //百度支持10个，高德支持16个

struct NaviPoi
{
	QString instruction; 
	uint8 position;
	float64 lng;
	float64 lat;
	QString name;
	int16 type;
};

struct NaviAction
{
	uint8 turn; // 机动转向点，包括基准八个方向、环岛、分歧等 枚举值，返回0-16之间的一个值，共17个枚举值；
						// 分别代表的含义是：0-无效；1-直行；2-右前方转弯；3-右转；4-右后方转弯；5-掉头；6-左后方转弯；7-左转；
						// 8-左前方转弯；9-左侧；10-右侧；11-分歧左；12-分歧中央；13-分歧右；14-环岛；15-进渡口；16-出渡口
	uint8 type; // 未知，不知道表示什么意思，百度api没有介绍
	uint8 traffic_condition; // 该路段的路况评价 0：无路况；1：畅通；2：缓行；3：拥堵
	uint8 area; // 标示该路段是否在城市内部：0：不在城市内部；1：在城市内部 默认为 0
	uint8 direction; // 进入道路的角度 枚举值，返回值在0-11之间的一个值，共12个枚举值，以30度递进，即每个值代表角度范围为30度；
							 // 其中返回"0"代表345度到15度，以此类推，返回"11"代表315度到345度"
	uint32 distance; // 路段距离 单位：米
	uint32 duration; // 路段耗时 单位：秒
	QString instruction; // 路段描述
	NaviPoi pois; // 途径兴趣点poi信息
	//QString roadName;
	//QString postSign;
	QString stepOriginInstruction; // 路段起点经度及纬度
	QString stepDestinationInstruction; // 路段终点经度及纬度
	std::vector<float64> coords;
};

struct NaviActionAmap
{
	uint32 distance; // 路段距离 单位：米
	uint32 tolls; // 路段收费 单位：元
	uint32 toll_distance; // 收费路段距离 单位：米
	uint32 toll_road; // 主要收费道路
	QString action; // 导航主要动作
	QString assistant_action; // 导航辅助动作
	QString instruction; // 行驶指示
	QString orientation; // 方向
	QString road_name; // 道路名称
	std::vector<float64> coords;
};

const char* Bmap_turn_toString(uint8 o);
const char* Bmap_direction_toString(uint8 o);

class QXmlStreamReader;

class OnlineRoute : public QObject
{
	Q_OBJECT
public:
    OnlineRoute(float64 sX = 0.0, float64 sY = 0.0, float64 eX = 0.0, float64 eY = 0.0, int8 cnt = 0, int8 tactics = 12, int8 strategy = 0);
    ~OnlineRoute();

    void makeRequest(ENUM_MAPTYPE type);

	void setOriginX(float64 x) { originX = x; }
	void setOriginY(float64 y) { originY = y; }
	void setDestinationX(float64 x) { destinationX = x; }
	void setDestinationY(float64 y) { destinationY = y; }
	void setWaypointsX(float64 x) { waypointsX[wayCount] = x; }
	void setWaypointsY(float64 y) { waypointsY[wayCount++] = y; }
	void clearWaypoints() { wayCount = 0; }
	void setTactics(int8 t) { tactics = t; }
	void setStrategy(int8 s) { strategy = s; }
	void setAmapKey(QString key);
	void setBmapKey(QString key);

	QByteArray& getAMapResponseData() { return amapResponseData; }
	QByteArray& getBMapResponseData() { return bmapResponseData; }

	QString generateAMapUrl();
	QString generateBMapUrl();

	QString getRouteInfo() const { return routeInfo; }

	vector<NaviActionAmap>* getAMapActions() { return &amapActions; }
	vector<NaviAction>* getBMapActions() { return &bmapActions; }

	void parseContent(QXmlStreamReader& xmlReader, NaviAction& action);
	void parseStep(QXmlStreamReader& xmlReader, NaviActionAmap& action);

	void reset();

public slots:
	void amapRequestFinished(QNetworkReply* reply);
	void bmapRequestFinished(QNetworkReply* reply);

private:
	QNetworkAccessManager* amapNetworkManager;
	QNetworkAccessManager* bmapNetworkManager;
	QByteArray amapResponseData;
	QByteArray bmapResponseData;
	float64 originX;
	float64 originY;
	float64 destinationX;
	float64 destinationY;
	float64 waypointsX[BMAP_MAX_WAYPOINTS];
	float64 waypointsY[BMAP_MAX_WAYPOINTS];
	int8 wayCount; // 最多支持5个途经点
	int8 tactics; // 百度地图导航策略。导航路线类型，10，不走高速；11、最少时间；12、最短路径。默认为12
	int8 strategy; // 高德地图导航策略。 0速度优先 1费用优先 2距离优先 3不走快速路 4躲避拥堵 5多策略 6不走高速 7不走高速且避免收费 8躲避收费和拥堵 9不走高速且躲避收费和拥堵

	vector<float64> bmapCoords;
	vector<NaviAction> bmapActions;

	vector<float64> amapCoords;
	vector<NaviActionAmap> amapActions;

	QString routeInfo;
	QString amap_key;
	QString bmap_key;

signals:
    void mapRoute(vector<float64>& route, QString routeInfo, ENUM_MAPTYPE type);
};

#pragma pack(pop)

#endif // ONLINE_ROUTE_H
