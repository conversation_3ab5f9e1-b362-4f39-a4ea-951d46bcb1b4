﻿
#include "online_route.h"
#include <QXmlStreamReader>
#include <QRegExp>
#include <iostream>
#include "util_funtion_tools.h"
#include  <stdio.h>
#include "gps_encry.h"

const char* Bmap_turn_toString(uint8 o)
{
	switch (o)
	{
	case 0:
		return "无效";
	case 1:
		return "直行";
	case 2:
		return "右前方转弯";
	case 3:
		return "右转";
	case 4:
		return "右后方转弯";
	case 5:
		return "掉头";
	case 6:
		return "左后方转弯";
	case 7:
		return "左转";
	case 8:
		return "左前方转弯";
	case 9:
		return "左侧";
	case 10:
		return "右侧";
	case 11:
		return "分歧左";
	case 12:
		return "分歧中央";
	case 13:
		return "分歧右";
	case 14:
		return "环岛";
	case 15:
		return "进渡口";
	case 16:
		return "出渡口";
	default:
		return "";
	}
}

const char* Bmap_direction_toString(uint8 o)
{
	switch (o)
	{
	case 0:
		return "345-15";
	case 1:
		return "15-45";
	case 2:
		return "45-75";
	case 3:
		return "75-105";
	case 4:
		return "105-135";
	case 5:
		return "135-165";
	case 6:
		return "165-195";
	case 7:
		return "195-225";
	case 8:
		return "225-255";
	case 9:
		return "255-285";
	case 10:
		return "285-315";
	case 11:
		return "315-345";
	default:
		return "";
	}
}

OnlineRoute::OnlineRoute(float64 sX, float64 sY, float64 eX, float64 eY, int8 cnt, int8 t, int8 s) :
originX(sX),
originY(sY),
destinationX(eX),
destinationY(eY),
wayCount(cnt),
tactics(t),
strategy(s)
{
	bmapNetworkManager = new QNetworkAccessManager(this);
	connect(bmapNetworkManager, &QNetworkAccessManager::finished,
        this, &OnlineRoute::bmapRequestFinished);

	amapNetworkManager = new QNetworkAccessManager(this);
	connect(amapNetworkManager, &QNetworkAccessManager::finished,
        this, &OnlineRoute::amapRequestFinished);
}

OnlineRoute::~OnlineRoute()
{
	delete amapNetworkManager;
	delete bmapNetworkManager;
}

QString OnlineRoute::generateAMapUrl()
{
	QString url("http://restapi.amap.com/v3");
	url.append("/direction/driving?origin=");
	url.append(QString::number(originX, 10, 6));
	url.append(',');
	url.append(QString::number(originY, 10, 6));
	url.append("&destination=");
	url.append(QString::number(destinationX, 10, 6));
	url.append(',');
	url.append(QString::number(destinationY, 10, 6));
    if (wayCount > 0)
    {
        url.append("&waypoints=");
        for (size_t i = 0; i < wayCount; i++)
        {
            url.append(QString::number(waypointsX[i], 10, 6));
            url.append(',');
            url.append(QString::number(waypointsY[i], 10, 6));
            if (i != wayCount - 1)
            {
                url.append(";");
            }
        }
    }
	url.append("&strategy=");
    url.append(QString::number(strategy)); // 0 距离时间  2 最短路径 6 高速优先； 9 少收费

	url.append("&output=xml&key=");
    url.append("1b8270afabb12ff106e7dd267c2cb5ab");
	return url;

}

QString OnlineRoute::generateBMapUrl()
{
	QString url("http://api.map.baidu.com");
    url.append("/direction/v1/driving?origin=");
    //double lng = 0.0;
    //double lat = 0.0;
    //bd_encrypt(originY, originX, lat, lng);
    url.append(QString::number(originY, 10, 6));
	url.append(',');
    url.append(QString::number(originX, 10, 6));
	url.append("&destination=");
    //bd_encrypt(destinationY, destinationX, lat, lng);
    url.append(QString::number(destinationY, 10, 6));
	url.append(',');
    url.append(QString::number(destinationX, 10, 6));
    if (wayCount > 0)
    {
        url.append("&waypoints=");
        for (size_t i = 0; i < wayCount; i++)
        {
            url.append(QString::number(waypointsY[i], 10, 6));
            url.append(',');
            url.append(QString::number(waypointsX[i], 10, 6));
            if (i != wayCount - 1)
            {
                url.append("|");
            }
        }
    }
	url.append("&coord_type=");
    url.append("gcj02"); // 坐标类型，可选参数，默认为bd09ll。允许的值为：bd09ll（百度经纬度坐标）、bd09mc（百度摩卡托坐标）、gcj02（国测局加密坐标）、wgs84（gps设备获取的坐标）。
	url.append("&tactics=");
    url.append(QString::number(tactics)); // 可选值：10 （默认）最短时间；12 最短路径；13 高速优先；11 少收费;
    url.append("&origin_region=beijing&destination_region=beijing&output=xml&ak=");
    //url.append("&output=xml&ak=");
    url.append("WxDrLRYFQal5jAQujqyWo4swEIgOvH8L");

    qDebug() << "百度地图API url：" << url;
	return url;
}

void OnlineRoute::makeRequest(ENUM_MAPTYPE type)
{
	QUrl url;
	switch (type)
	{
	case BAIDU_MAP:
		url = QUrl(generateBMapUrl());
		bmapNetworkManager->get(QNetworkRequest(url));
		break;
	case AMAP:
		url = QUrl(generateAMapUrl());
		amapNetworkManager->get(QNetworkRequest(url));
		break;
	default:
		break;
	}
}

void OnlineRoute::amapRequestFinished(QNetworkReply* reply)
{
	amapResponseData = reply->readAll();

	int32 distance = 0;
	int32 duration = 0;
	int32 traffic_lights = 0;

	QXmlStreamReader xmlReader(amapResponseData);
	amapCoords.clear();
	amapActions.clear();

	while (!xmlReader.atEnd())
	{
		xmlReader.readNext();

		if (xmlReader.name() == "status" && xmlReader.isStartElement())
		{
			if (xmlReader.readElementText().toInt() != 1)
				return;
		}

		if (xmlReader.name() == "path" && xmlReader.isStartElement())
		{
			while (!(xmlReader.name() == "steps" && xmlReader.isStartElement()))
			{
				xmlReader.readNext();
				if (xmlReader.name() == "distance" && xmlReader.isStartElement())
				{
					distance = xmlReader.readElementText().toInt();
				}
				if (xmlReader.name() == "duration" && xmlReader.isStartElement())
				{
					duration = xmlReader.readElementText().toInt();
				}
			}
		}

		if (xmlReader.name() == "step" && xmlReader.isStartElement())
		{
			NaviActionAmap action;
			parseStep(xmlReader, action);
			amapActions.push_back(action);
		}

		if (xmlReader.name() == "traffic_lights" && xmlReader.isStartElement())
		{
			traffic_lights = xmlReader.readElementText().toInt();
		}
	}

	routeInfo.clear();
	routeInfo.append(util::NumToKmM(distance));
	routeInfo.append("; ");
    routeInfo.append(util::NumToHousSec(duration));
	routeInfo.append("; ");
	routeInfo.append(QString::number(traffic_lights));

    qDebug() << "online_route amapCoords size = " << amapCoords.size();

	emit(mapRoute(amapCoords, routeInfo, AMAP));

	reply->deleteLater();

}

void OnlineRoute::parseContent(QXmlStreamReader& xmlReader, NaviAction& action)
{
	vector<float64> bmapPathCoords;
	QStringList lonlats;
	float64 coord;

	xmlReader.readNext();
	while (!(xmlReader.isEndElement() && xmlReader.name() == "content"))
	{
		if (xmlReader.isStartElement())
		{
			if (xmlReader.name() == "area")
			{
				action.area = xmlReader.readElementText().toInt();
			}
			else if (xmlReader.name() == "direction")
			{
				action.direction = xmlReader.readElementText().toInt();
			}
			else if (xmlReader.name() == "distance")
			{
				action.distance = xmlReader.readElementText().toInt();
			}
			else if (xmlReader.name() == "duration")
			{
				action.duration = xmlReader.readElementText().toInt();
			}
			else if (xmlReader.name() == "instructions")
			{
				action.instruction = xmlReader.readElementText().remove(QRegExp("[a-z<>\/=\"]+|0x[0-9]+"));
			}
			else if (xmlReader.name() == "path")
			{
				bmapPathCoords.clear();
				lonlats = xmlReader.readElementText().split(';');
                for (int i = 0; i < lonlats.size(); ++i)
				{
					bmapPathCoords.push_back(lonlats[static_cast<int>(i)].split(',').at(0).toDouble());
					bmapPathCoords.push_back(lonlats[static_cast<int>(i)].split(',').at(1).toDouble());
				}
			}
			else if (xmlReader.name() == "pois")
			{
				while (!(xmlReader.isEndElement() && xmlReader.name() == "pois"))
				{
					xmlReader.readNext();
				}
			}
			else if (xmlReader.name() == "turn")
			{
				action.turn = xmlReader.readElementText().toInt();
			}
			else if (xmlReader.name() == "type")
			{
				action.type = xmlReader.readElementText().toInt();
			}
			else if (xmlReader.name() == "stepOriginLocation" && xmlReader.isStartElement())
			{
				xmlReader.readNextStartElement();
				if (xmlReader.name().toString() == "lng")
				{
					coord = xmlReader.readElementText().toDouble();
					bmapCoords.push_back(coord);
					action.coords.push_back(coord);
				}
				xmlReader.readNextStartElement();
				if (xmlReader.name().toString() == "lat")
				{
					coord = xmlReader.readElementText().toDouble();
					bmapCoords.push_back(coord);
					action.coords.push_back(coord);
				}

				// 添加path点
				bmapCoords.insert(bmapCoords.end(), bmapPathCoords.begin(), bmapPathCoords.end());
				action.coords.insert(action.coords.end(), bmapPathCoords.begin(), bmapPathCoords.end());
			}
			else if (xmlReader.name() == "stepDestinationLocation" && xmlReader.isStartElement())
			{
				xmlReader.readNextStartElement();
				if (xmlReader.name().toString() == "lng")
				{
					coord = xmlReader.readElementText().toDouble();
					bmapCoords.push_back(coord);
					action.coords.push_back(coord);
				}
				xmlReader.readNextStartElement();
				if (xmlReader.name().toString() == "lat")
				{
					coord = xmlReader.readElementText().toDouble();
					bmapCoords.push_back(coord);
					action.coords.push_back(coord);
				}
			}
			else if (xmlReader.name() == "stepOriginInstruction")
			{
				action.stepOriginInstruction = xmlReader.readElementText();
			}
			else if (xmlReader.name() == "stepDestinationInstruction")
			{
				action.stepDestinationInstruction = xmlReader.readElementText();
			}
		}
		xmlReader.readNext();
	}
}

void OnlineRoute::parseStep(QXmlStreamReader& xmlReader, NaviActionAmap& action)
{
	QStringList lonlats;

	xmlReader.readNext();
	while (!(xmlReader.isEndElement() && xmlReader.name() == "step"))
	{
		if (xmlReader.isStartElement())
		{
			if (xmlReader.name() == "instructions")
			{
				action.instruction = xmlReader.readElementText().remove(QRegExp("[a-z<>\/=\"]+|0x[0-9]+"));
			}
			else if (xmlReader.name() == "orientation")
			{
				action.orientation = xmlReader.readElementText();
			}
			else if (xmlReader.name() == "road")
			{
				action.road_name = xmlReader.readElementText();
			}
			else if (xmlReader.name() == "distance")
			{
				action.distance = xmlReader.readElementText().toInt();
			}
			else if (xmlReader.name() == "tolls")
			{
				action.tolls = xmlReader.readElementText().toInt();
			}
			else if (xmlReader.name() == "toll_distance")
			{
				action.toll_distance = xmlReader.readElementText().toInt();
			}
			else if (xmlReader.name() == "toll_road")
			{
				action.toll_road = xmlReader.readElementText().toInt();
			}
			else if (xmlReader.name() == "polyline")
			{
				lonlats = xmlReader.readElementText().split(';');
                for (int i = 0; i < lonlats.size(); ++i)
				{
					amapCoords.push_back(lonlats[static_cast<int>(i)].split(',').at(0).toDouble());
					amapCoords.push_back(lonlats[static_cast<int>(i)].split(',').at(1).toDouble());
					action.coords.push_back(lonlats[static_cast<int>(i)].split(',').at(0).toDouble());
					action.coords.push_back(lonlats[static_cast<int>(i)].split(',').at(1).toDouble());
				}
			}
			else if (xmlReader.name() == "action")
			{
				action.action = xmlReader.readElementText();
			}
			else if (xmlReader.name() == "assistant_action")
			{
				action.assistant_action = xmlReader.readElementText();
			}
			else if (xmlReader.name() == "tmcs")
			{
				while (!(xmlReader.isEndElement() && xmlReader.name() == "tmcs"))
				{
					xmlReader.readNext();
				}
			}
		}
		xmlReader.readNext();
	}
}

void OnlineRoute::bmapRequestFinished(QNetworkReply* reply)
{
	bmapResponseData = reply->readAll();
    qDebug() << "百度地图API响应：" << QString::fromUtf8(bmapResponseData);

	int distance = 0;
	int duration = 0;

	QXmlStreamReader xmlReader(bmapResponseData);
	bmapCoords.clear();
	bmapActions.clear();

    bool is_read_status = false;
	while (!xmlReader.atEnd())
	{
        xmlReader.readNext();

        //qDebug() << "百度地图API响应：" << xmlReader.name() << "isStartElement: " << xmlReader.isStartElement() << "readElementText" << xmlReader.readElementText();

        if (!is_read_status && xmlReader.name() == "status" && xmlReader.isStartElement())
		{
            is_read_status = true;
            int status = xmlReader.readElementText().toInt();
            if (status != 0) {
                std::cout << "url status = " << status << std::endl;
				return;
            }
		}

		//if (xmlReader.name() == "routes" && xmlReader.isStartElement())
		//{
		//while (!(xmlReader.name() == "steps" && xmlReader.isStartElement()))
		//{
		//xmlReader.readNext();
		if (xmlReader.name() == "distance" && xmlReader.isStartElement())
		{
			distance += xmlReader.readElementText().toInt();
		}
		if (xmlReader.name() == "duration" && xmlReader.isStartElement())
		{
			duration += xmlReader.readElementText().toInt();
		}
		//}
		//}

		if (xmlReader.name() == "content" && xmlReader.isStartElement())
		{
			NaviAction action;
			parseContent(xmlReader, action);
			bmapActions.push_back(action);
		}
	}

	routeInfo.clear();
	routeInfo.append(util::NumToKmM(distance));
	routeInfo.append("; ");
    routeInfo.append(util::NumToHousSec(duration));

#if 1 // 用接口进行坐标转换
	vector<float64> route;
	float64 mars_lon, mars_lat;
	for (size_t i = 0; i < bmapCoords.size(); i += 2)
	{
		bd_decrypt(bmapCoords.at(i + 1), bmapCoords.at(i), mars_lat, mars_lon);
		route.push_back(mars_lon);
		route.push_back(mars_lat);
	}
    qDebug() << "online_route bmapCoords size = " << route.size();
	emit(mapRoute(route, routeInfo, BAIDU_MAP));
#else
	QUrl url(generateAMapUrl());
	amapNetworkManager->get(QNetworkRequest(url));
#endif

	reply->deleteLater();
}

void OnlineRoute::reset()
{
	originX = 0.0;
	originY = 0.0;
	destinationX = 0.0;
	destinationY = 0.0;

	bmapCoords.clear();
	bmapActions.clear();

	routeInfo.clear();
}

void OnlineRoute::setAmapKey(QString key)
{
	amap_key = QString(key);
}

void OnlineRoute::setBmapKey(QString key)
{
	bmap_key = QString(key);
}
