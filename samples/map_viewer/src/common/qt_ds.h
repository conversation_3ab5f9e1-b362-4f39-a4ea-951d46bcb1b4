﻿

#ifndef QT_DS_H
#define QT_DS_H

#include "util_type_define.h"
#include <QString>
#include <sys/times.h>
#include <time.h>	/* clock_gettime(), time() */
#include <sys/time.h>	/* gethrtime(), gettimeofday() */

//// 地图类型
typedef enum
{
    MAP_ENGINE = 0 ,
    BAIDU_MAP = 1 ,	   // 百度地图
    AMAP,          // 高德地图
    //MAPBAR,        // 图吧地图
    //QQ_MAP,        // 腾讯地图
    //GOOGLE_MAP     // google地图
    MAP_INVAID
}ENUM_MAPTYPE;

//typedef struct StateLog{
//	LinkId link_id;
//	int32 state; // 状态 1-绿色 2-黄色 3-红色 4-深红色
//}StateLog;

static uint8 g_ruleList[] = { 0, 1, 2, 4, 8, 16 };
#define	ROUTE_STRATEGY_TIME					0x00								// 推荐，时间最短
#define	ROUTE_STRATEGY_HIGHWAY				0x01								// 高速优先
#define	ROUTE_STRATEGY_AVOID_HIGHWAY		0x02								// 少走高速
#define	ROUTE_STRATEGY_AVOID_TOLL			0x04								// 少收费
#define	ROUTE_STRATEGY_AVOID_JAM			0x08								// 回避拥堵
#define	ROUTE_STRATEGY_DISTANCE				0x10								// 距离最短

const char* Direction_toString(uint8 o);
const char* LinkType_toString(uint32 o);
const char* LinkForm_toString(uint32 o);
const char* SpeedGrade_toString(uint32 o);
const char* SpeedLimit_toString(uint32 o);
const char* FuncClass_toString(uint8 o);
const char* RoadRank_toString(uint8 o);
uint8 getIdxByRule(QString rule);
const char* Rule_toString(uint8 o);
const char* TurnType_toString(uint8 o);
QString LaneDesc_toString(uint16 o);
QString EnterExitDirection_toString(uint8 o);
const char* PostType_toString(uint8 o);
const char* JunctionViewType_toString(uint8 o);
const char* CameraType_toString(uint8 o);
const char* Slope_toString(uint8 o);
const char* Flag_toString(uint8 o);
const char* LeftDriving_toString(uint8 o);
const char* SapaType_toString(uint8 o);
const char* Resting_toString(uint8 o);
const char* Voice_toString(uint8 o);
QString TollShape_toString(void* o);
QString ModuleId_toString(uint8 o);
const char* ProtoCrossTurnType_toString(uint8 o);
const char* ProtoFerryType_toString(uint8 o);
const char* ProtoDestinationDirect_toString(uint8 o);
const char* ProtoMainSideChangeType_toString(uint8 o);
QString ProtoLaneTurnType_toString(uint8 o);
const char* ProtoLaneKind_toString(uint8 o);
const char* ProtoCrossSlopeType_toString(uint8 o);
int32 BrightLaneIdx_toLaneIdx(uint32 o);
const char* ProtoInfoType_toString(uint8 o);
const char* ProtoTrafficDir_toString(uint8 o);
const char* ProtoTurnRoadType_toString(uint8 o);
const char* ProtoTurnTimeType_toString(uint8 o);
const char* ProtoAdditionalInfo_toString(uint8 o);
const char* ProtoEnRingOutType_toString(uint8 o);
const char* ProtoExpandType_toString(uint8 o);
const char* ProtoHighwayBoardType_toString(uint8 o);
const char* ProtoHighwayBoardTurnType_toString(uint8 o);
QString ProtoSAPAProperty_toString(uint8 o);
const char* RoadCondition_toString(uint8 o);
const char* Impassable_toString(uint8 o);
const char* ImpassType_toString(uint8 o);
const char* LaneCnt_toString(uint8 o);
const char* AccessCtrlType_toString(uint8 o);

static uint32 _GetTickCount_() {
#if defined(WIN32)
    return ::GetTickCount();
#elif YUNOS
    struct timespec time = {0, 0};
        clock_gettime(CLOCK_THREAD_CPUTIME_ID, &time);
        return double(time.tv_sec * 1e6 + time.tv_nsec/1000.0);
#else
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    return (double)tv.tv_sec * 1000 + (double)tv.tv_usec / 1000;
#endif
}

bool GetGpsFixByStatus(uint32 status);
//bool CompareLinkId(const LinkId& left, const LinkId& right);
//bool CompareStateLog(const StateLog& left, const StateLog& right);
int CompareStateLog2(const void* left, const void* right);

//static const int32 MAPDATA_CONVERSION_DM6 = 1000000;
//inline static int32 ConvertCoord2(float64 lon)
//{
//	return (int32)(lon * MAPDATA_CONVERSION_DM6);
//}

//inline static float64 InvConvertCoord2(int32 lon)
//{
//	return (float64)lon / MAPDATA_CONVERSION_DM6;
//}

#endif // QT_DS_H
