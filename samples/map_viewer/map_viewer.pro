QT  += core gui network widgets

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += debug c++17

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0


#定义基础路径
BASE_PATH = $$PWD

INCLUDEPATH += \
               $${BASE_PATH}/src \ 
               $${BASE_PATH}/src/map_view \
               $${BASE_PATH}/src/map_view/layer \
               $${BASE_PATH}/src/map_traffic \
               $${BASE_PATH}/src/map_search \
               $${BASE_PATH}/src/map_routing \
               $${BASE_PATH}/src/map_location \
               $${BASE_PATH}/src/map_data \
               $${BASE_PATH}/src/file_manage \
               $${BASE_PATH}/src/custom_widgets \
               $${BASE_PATH}/src/utils \
               $${BASE_PATH}/src/online_engine \
               $${BASE_PATH}/src/map_guide \
               $${BASE_PATH}/src/map_engine \
               $${BASE_PATH}/src/preferences \
               $${BASE_PATH}/src/online \
               $${BASE_PATH}/src/common \
               $${BASE_PATH}/map_engine/common \
               $${BASE_PATH}/../../src/map/include \
               $${BASE_PATH}/../../src/data_provider/include \
               $${BASE_PATH}/../../src/base/include \
               $${BASE_PATH}/../../src/path/include \
               $${BASE_PATH}/../../src/guidance/include \
               $${BASE_PATH}/../../src/location/include \
               $${BASE_PATH}/../../src


SOURCES += \
           $$files(src/*.cpp) \
                   $$files(src/utils/*.cpp) \
                   $$files(src/custom_widgets/*.cpp) \
                   $$files(src/map_view/*.cpp) \
                   $$files(src/map_view/layer/*.cpp) \
                   $$files(src/map_traffic/*.cpp) \
                   $$files(src/map_search/*.cpp) \
                   $$files(src/map_routing/*.cpp) \
                   $$files(src/map_location/*.cpp) \
                   $$files(src/map_data/*.cpp) \
                   $$files(src/online_engine/*.cpp) \
                   $$files(src/map_guide/*.cpp) \
                   $$files(src/map_engine/*.cpp) \
                   $$files(src/file_manage/*.cpp) \
                   $$files(src/preferences/*.cpp) \
                   $$files(src/online/*.cpp) \
                   $$files(src/common/*.cpp)

HEADERS += \
           $$files(src/*.h) \
                   $$files(src/utils/*.h) \
                   $$files(src/custom_widgets/*.h) \
                   $$files(src/map_view/*.h) \
                   $$files(src/map_view/layer/*.h) \
                   $$files(src/map_traffic/*.h) \
		   $$files(src/map_search/*.h) \
		   $$files(src/map_routing/*.h) \
		   $$files(src/map_location/*.h) \
		   $$files(src/map_data/*.h) \
                   $$files(src/file_manage/*.h) \
		   $$files(src/online_engine/*.h) \
		   $$files(src/map_guide/*.h) \
		   $$files(src/map_engine/*.h) \
                   $$files(src/preferences/*.h) \
                   $$files(src/online/*.h) \
                   $$files(src/common/*.h) \
		   $$files(map_engine/common/*.h) \
                   $$files(../../src/map/include/*.h) \
		   $$files(../../src/data_provider/include/route_data/*.h) \
		   $$files(../../src/base/include/*.h)

FORMS += \
         $$files(src/ui/*.ui)

		 
RESOURCES += \
             $$files(src/res/main_window.qrc)
			 
RC_ICONS = $$files(src/res/map_viewer.ico)


LIBS += -L$$PWD/../../distribution/lib -laurora_base -ldata_provider -laurora_path -laurora_search -lyaml-cppd -laurora_guide -lmaprenderservice -laurora_location


# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target


